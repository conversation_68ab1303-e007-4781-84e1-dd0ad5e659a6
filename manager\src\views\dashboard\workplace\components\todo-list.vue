<template>
  <a-grid :cols="24" :row-gap="16" class="panel">
    <a-grid-item
      cursor-pointer
      v-for="(item, index) in panelList.data"
      :key="index"
      class="panel-col"
      :span="{ xs: 4, sm: 4, md: 4, lg: 4, xl: 4, xxl: 4 }"
    >
      <a-space @click="$router.push({ name: item.name ,query:item.query || ''})">
        <a-statistic
          :title="item.label"
          :value="item.value"
          :precision="0"
          :value-from="0"
          animation
          show-group-separator
        >
        </a-statistic>
      </a-space>
    </a-grid-item>

    <a-grid-item :span="24">
      <a-divider class="panel-border" />
    </a-grid-item>
  </a-grid>
</template>

<script lang="ts" setup>
  import { reactive, watch } from 'vue';

  const props = defineProps({
    res: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });
  const panelList = reactive<any>({
    data: {},
  });
  const getPanelList = (data:any) => {
    panelList.data = {
      goodsNums: {
        label: '待审核商品',
        value: data.goods || 0,
        pecs: '件',
        name: 'auth-goods-list'
      },
      shopAuthNums: {
        label: '待审核店铺',
        value: data.store || 0,
        pecs: '人',
        name:'shop-audit'
      },
      complainNums: {
        label: '待审核投诉',
        value: data.complain || 0,
        pecs: '人',
        name: 'order-complaint'
      },
      refundNums: {
        label: '待审核售后',
        value: data.refund || 0,
        pecs: '人',
        name: 'after-sale',
        query:{
          serviceStatus: 'APPLY'
        }
      },
      distributionCashNums: {
        label: '待审核分销提现',
        value: data.distributionCash || 0,
        pecs: '人',
        name: 'distrbution-withdrawal',
        query:{
          distributionCashStatus: 'APPLY'
        }
      },
      waitPayBillNums: {
        label: '待审核分账',
        value: data.waitPayBill || 0,
        pecs: '人',
        name: 'shop-reconciliation'
      }
    }
  };
  watch(() => props.res, (newValue, oldValue) => {
      getPanelList(newValue);
    }, { deep: true, immediate: true }
  );
</script>

<style lang="less" scoped>
  .arco-grid.panel {
    margin-bottom: 0;
    padding: 16px 20px 0 20px;
  }
  .panel-col {
    // padding-left: 43px;
    border-right: 1px solid rgb(var(--gray-2));
    text-align: center;
  }
  .col-avatar {
    margin-right: 12px;
    background-color: var(--color-fill-2);
  }
  .up-icon {
    color: rgb(var(--red-6));
  }
  .unit {
    margin-left: 8px;
    color: rgb(var(--gray-8));
    font-size: 12px;
  }
  :deep(.panel-border) {
    margin: 4px 0 0 0;
  }
</style>
