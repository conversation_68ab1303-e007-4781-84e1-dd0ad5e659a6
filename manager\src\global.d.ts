// 请求返回约束
interface ResponseRule {
  success: boolean;
  code: number;
  result: any;
  message: string;
  timestamp: number;
}
// table-pages的表格方法封装约束
interface ColumnsDataRule {
  width?:  number;
  title: string; // 表格显示的title
  dataIndex: string; // 表格显示的数据
  currency?: boolean; // 是否将显示的数据进行货币化处理
  slot?: boolean; // 是否插槽形式
  empty?: string; // 如果没有数据展示的空内容
  ellipsis?: boolean; // 是否展示省略号 默认展示
  slotTemplate?: string;
  slotData?: {
    tag?: Array<{ value: string | boolean | number; color: string,[key:string]:any }>; // tag形式写color https://arco.design/vue/component/tag
    goods?: {
      goodsImage: string;
      goodsName: string;
      linkTo?: boolean;
      goodsSpecs?: string;
      [key: string]: unknown;
    }; // 商品展示
    link?: string; // 链接
    badge?: Array<{ value: string | boolean | number; color: string }>; // 状态点
  }; // 插槽内容
  sortable?: {
    sortDirections: Array<string>
  } // 排序
}
// table-pages的表格方法封装约束
interface MethodsRule {
  title: string; // 表格显示的title
  fixed?: string; // 在arco文档中写到如果设置fixed必须设置宽度。如果scroll x轴的宽度不够，也是不会显示的 具体可看arco的文档
  methods: Array<{
    title?: string;
    callback?: string;
    type?: string;
    status?: string;
    slot?: boolean; // 是否插槽形式
    slotTemplate?: string;
    disabled?: any// 是否禁用
  }>; // 方法按钮的title, callback是回调的方法名
  width?: number | string;
}
// search-column 搜索列约束
interface SearchRule {
  label: string; // 显示的label
  model?: string; // v-model绑定内容
  disabled?: boolean; // 是否禁用
  input?: boolean; // 是否是input框
  select?: {
    defaultValue?: string;
    options: Array<{ label: string | number; value: string | number ,[key:string]:any}>;
  }; // 是否是选择框
  datePicker?: {
    type: string; // 类型 date 日期 range 范围
  }; // 是否是时间框
  category?: boolean; // 是否是选择分类

}
// 表格/分页发送请求约束
interface ParamsRule {
  pageNumber: number;
  pageSize: number;
  sort?: string;
  order?: string;
  [key: string]: unknown;
}

// 统计请求的约束
interface PreViewParamsRule {
  searchType: string;
  // year: string | number;
  // storeId?: string | number | undefined;
  month?: string | number;
  type?: string;
}

interface AnyObject {
  [key: string]: unknown;
}
