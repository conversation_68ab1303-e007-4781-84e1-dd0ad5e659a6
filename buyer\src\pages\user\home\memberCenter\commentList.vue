<template>
<div>
  <Card _Title="评论/晒单" :_Size="16"></Card>

  <div class="order-title border-bg-color">
    <a-row class="pl_20 pr_20">
      <a-col :span="12">订单详情</a-col>
      <a-col :span="3">收货人</a-col>
      <a-col :span="3">评价</a-col>
      <a-col :span="6"></a-col>
    </a-row>
  </div>

  <div class="order-item mt_10 mb_10" v-for="(item, index) in commentList" :key="index">
    <div>
      <div class="header pl_20 pr_20">
        <span>订单号：{{item.orderNo}}</span>
        <span class="create-time ml_20">{{item.createTime}}</span>
        <span class="eval-detail hover-pointer hover-color fontsize-12" @click="evaluateDetail(item.id)">评价详情</span>
      </div>
      <a-row class="body pl_20 pr_20">
        <a-col :span="12" class="body-goods mt_10 mb_10">
          <div class="hover-pointer" @click="goGoodsDetail(item.skuId, item.goodsId)"><img :src="item.goodsImage" alt="" width="60" height="60" /></div>
          <div class="hover-pointer hover-color" @click="goGoodsDetail(item.skuId, item.goodsId)">{{item.goodsName}}</div>
        </a-col>
        <a-col :span="3" class="text-center">{{ secrecyMobile(item.createBy) }}</a-col>
        <a-col :span="3" class="text-center"> {{item.grade==='GOOD'?'好评' : item.grade === 'WORSE'?'差评' : '中评'}}</a-col>
        <a-col :span="6" class="text-center">
          <a-tooltip :content="item.content" position="bottom">
            <div class="ellipsis ellipsis-1 hover-pointer">{{ item.content }}</div>
          </a-tooltip>
        </a-col>
      </a-row>
    </div>
  </div>

  <!-- 分页 -->
  <div class="paginationBox">
    <a-pagination :total="total" :current="apiParams.pageNumber" :page-size="apiParams.pageSize" show-page-size
                  @change="(number) => {apiParams.pageNumber = number;}" @page-size-change="(number) => {apiParams.pageSize = number; apiParams.pageNumber = 1;}" >
    </a-pagination>
  </div>



</div>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router';
  import { ref, onMounted, watch } from 'vue';
  import { evaluationList } from '@/api/order';
  import { secrecyMobile } from '@/utils/filters';

  const router = useRouter();
  // 请求参数
  const apiParams = ref({
    pageNumber: 1,
    pageSize: 10,
  });
  const commentList = ref<Array<any>>([]);
  const total = ref(0);


  const getList = () => { // 获取评价列表
    evaluationList(apiParams.value).then(res => {
      if (res.data.success) {
        const list = res.data.result.records;
        list.forEach((element: any) => {
          element.descriptionScore = Number(element.descriptionScore);
        });
        commentList.value = list;
        total.value = res.data.result.total
      }
    })
  };
  // 跳转评价详情
  const evaluateDetail = (id: any) => {
    router.push({path: '/user/home/<USER>/commentDetail', query: { id }})
  };
  // 跳转商品详情
  const goGoodsDetail = (skuId: any, goodsId: any) => {
    let routeUrl = router.resolve({path: "/goodsDetail", query: { skuId, goodsId },});
    window.open(routeUrl.href, "_blank");
  };


  onMounted(() => {
    getList();
  });
  watch(() => [apiParams],
    (val) => {
      getList();
    }, { deep: true }
  );
</script>

<style scoped lang="less">
  .order-title {
    background-color: @light_background_color;
    height: 38px;
    line-height: 38px;
    text-align: center;
    color: @text_color;
  }
  .order-item {
    border: 1px solid @border_color;
    .header {
      height: 30px;
      line-height: 30px;
      background-color: @light_background_color;
      position: relative;
      .create-time {
        color: @light_text_color;
      }
      .eval-detail {
        position: absolute;
        right: 20px;
      }
    }
    .body {
      align-items: center;
      .body-goods {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        > div:nth-of-type(1) {
          width: 60px;
          height: 60px;
          border: 1px solid @border_color;
          margin-right: 10px;
        }
      }
      .ellipsis {
         color: @light_text_color;
       }
    }
  }
</style>
