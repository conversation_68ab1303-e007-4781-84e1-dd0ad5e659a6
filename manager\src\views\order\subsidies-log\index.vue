<template>
  <a-card class="general-card" title="补差记录" :bordered="false">
    <!-- 搜索 -->
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <!-- 表格 -->
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="subsidiesLog"
      :api-params="apiParams"
      :bordered="true"
    >
      <template #subsidiesBtn="{ data }">
        <a-button
          type="primary"
          v-if="data.result == 'ERROR'"
          @click="onSubsidies(data.id)"
          >补差</a-button
        >
      </template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { subsidiesLog, subsidies } from '@/api/order';
  import { subsidiesSearch } from '@/utils/tools';
  import { ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import useCurrentInstance from '@/hooks/useCurrentInstance';

  const apiParams = ref({});
  const tablePageRef = ref<any>();
  // 获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;
  const columnsSearch: Array<SearchRule> = [
    {
      label: '补差单号',
      model: 'outSubsidyNo',
      disabled: false,
      input: true,
    },
    {
      label: '流水单号',
      model: 'storeFlowId',
      disabled: false,
      input: true,
    },
    {
      label: '补差单结果',
      model: 'result',
      disabled: false,
      select: {
        options: subsidiesSearch,
      },
    },
  ];

  // 表格搜索列表
  const columnsTable: ColumnsDataRule[] = [
    {
      title: '补差单号',
      dataIndex: 'outSubsidyNo',
    },

    {
      title: '流水单号ID',
      dataIndex: 'storeFlowId',
    },

    {
      title: '微信订单号',
      dataIndex: 'transactionId',
    },
    {
      title: '电商平台二级商户号',
      dataIndex: 'subMchId',
    },
    {
      title: '补差金额',
      dataIndex: 'amount',
      currency: true,
    },
    {
      title: '分账单状态',
      width: 120,
      dataIndex: 'result',
      slot: true,
      slotData: {
        badge: subsidiesSearch,
      },
    },
    {
      title: '错误消息',
      width: 200,
      dataIndex: 'message',
    },
  ];
  // 操作列表
  const sortMethods: MethodsRule = {
    title: '操作',
    width: 100,
    methods: [
      {
        title: '补差',
        slot: true,
        slotTemplate: 'subsidiesBtn',
      },
    ],
  };
  // 补差
  function onSubsidies(id: string) {
    modal.confirm({
      title: '进行补差',
      content: '确定进行补差吗？',
      alignCenter: false,
      onOk: async () => {
        const res = await subsidies(id);
        if (res.data.success) {
          Message.success('补差成功!');
          tablePageRef.value.init();
        }
      },
    });
  }
</script>

<style scoped></style>
