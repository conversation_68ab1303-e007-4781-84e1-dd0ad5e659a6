{"name": "lili-shop-buyer", "type": "module", "version": "1.0.0", "description": "Lili-shop Pro for Vue", "author": "LiLi-shop ui", "scripts": {"build": "vite build", "dev": "vite --port 3333 --open", "lint": "eslint .", "preview": "vite preview", "preview-https": "serve dist", "typecheck": "vue-tsc --noEmit", "up": "taze major -I", "sizecheck": "npx vite-bundle-visualizer"}, "peerDependencies": {"@arco-design/web-vue": "^2.55.0"}, "dependencies": {"@ant-design/colors": "^7.0.2", "@arco-design/web-vue": "^2.55.0", "@chenfengyuan/vue-qrcode": "2", "@unhead/vue": "^1.9.5", "@unocss/reset": "^0.59.4", "@vueuse/core": "^10.9.0", "@vueuse/head": "^2.0.0", "axios": "^1.6.7", "js-cookie": "^3.0.5", "nprogress": "^0.2.0", "pinia": "^2.1.7", "qrcode": "1", "qs": "^6.11.2", "query-string": "^8.1.0", "swiper": "^11.0.7", "uuid": "^9.0.1", "vue": "^3.4.23", "vue-demi": "^0.14.7", "vue-i18n": "^9.13.0", "vue-router": "^4.3.2"}, "devDependencies": {"@antfu/eslint-config": "^2.15.0", "@iconify-json/carbon": "^1.1.31", "@intlify/unplugin-vue-i18n": "^4.0.0", "@shikijs/markdown-it": "^1.3.0", "@types/markdown-it-link-attributes": "^3.0.5", "@types/nprogress": "^0.2.3", "@unocss/eslint-config": "^0.59.4", "@vitejs/plugin-vue": "^5.0.4", "@vue-macros/volar": "^0.18.18", "@vue/test-utils": "^2.4.5", "critters": "^0.0.22", "cross-env": "^7.0.3", "dayjs": "^1.11.11", "eslint": "^9.0.0", "eslint-plugin-format": "^0.1.1", "https-localhost": "^4.7.1", "less": "^4.2.0", "markdown-it-link-attributes": "^4.0.1", "rollup": "^4.14.3", "shiki": "^1.3.0", "taze": "^0.13.6", "typescript": "^5.4.5", "unocss": "^0.59.4", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "unplugin-vue-macros": "^2.9.1", "unplugin-vue-markdown": "^0.26.2", "unplugin-vue-router": "^0.8.6", "vite": "^5.2.9", "vite-bundle-visualizer": "^1.1.0", "vite-plugin-inspect": "^0.8.3", "vite-plugin-pwa": "^0.19.8", "vite-plugin-vue-devtools": "^7.0.27", "vite-plugin-vue-layouts": "^0.11.0", "vite-plugin-webfont-dl": "^3.9.3", "vite-ssg": "^0.23.6", "vite-ssg-sitemap": "^0.6.1", "vitest": "^1.5.0", "vue-tsc": "^2.0.13"}}