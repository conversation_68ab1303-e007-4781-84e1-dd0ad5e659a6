<template>
  <a-space>
    <a-radio-group v-model="recent" type="button" @change="handleClickRadio">
      <a-radio value="TODAY">今天</a-radio>
      <a-radio value="YESTERDAY">昨天</a-radio>
      <a-radio value="LAST_SEVEN">最近7天</a-radio>
      <a-radio value="LAST_THIRTY">最近30天</a-radio>
    </a-radio-group>
    <a-month-picker
      v-model="month"
      placeholder="年月查询"
      style="width: 200px"
      :disabled-date="disabledTime"
      @change="handleClickMonth"
    />
  </a-space>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import { ref, onMounted } from 'vue';

  function disabledTime(current: any) {
    return dayjs(current).isAfter(dayjs());
  }
  const props = defineProps({
    dateType: {
      type: Object,
      default: () => {
        return {
          recent: 'LAST_SEVEN',
          month: '',
        };
      },
    },
  });
  const emit = defineEmits<{ (e: 'onChange', obj: object): void }>();

  // 月份
  const month = ref<string>('');
  // 最近时间
  const recent = ref<string>(props.dateType.recent);

  const handleClickEmit = () => {
    emit('onChange', {
      // eslint-disable-next-line no-nested-ternary
      month: month.value,
      recent: recent.value,
    });
  };
  const handleClickMonth = (time: string) => {
    time ? (recent.value = '') : '';
    handleClickEmit();
  };
  const handleClickRadio: any = (val: string) => {
    val ? (month.value = '') : '';
    handleClickEmit();
  };

  onMounted(() => {
    month.value = '';
    recent.value = 'LAST_SEVEN';
    handleClickEmit();
  })
</script>

<style lang="scss" scoped></style>
