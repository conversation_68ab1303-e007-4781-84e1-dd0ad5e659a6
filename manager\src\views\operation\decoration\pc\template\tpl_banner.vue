<template>
  <div w-1184px>
    <div line-height-85px text-center h-85px bg-gray>
      顶部栏
    </div>

    <div v-if="props.res" flex w-full flex-j-sb h-386px>
      <div w-262px flex flex-a-c flex-j-c class="skeleton">分类</div>
      <div>
        <top_nav :res="userDesign.pinPc[1]" />
        <div flex>
          <div w-646px>
            <a-carousel @click="handleClickCarousel" :style="{
              height: '340px',
            }">
              <a-carousel-item :key="index" v-for="(item, index) in props.res.data.list">
                <img :src="item.img" :style="{
                  width: '100%',
                }" />
              </a-carousel-item>
            </a-carousel>
          </div>
          <div w-262px flex flex-a-c flex-j-c class="skeleton">个人中心</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import top_nav from '@/views/operation/decoration/pc/template/tpl_top_nav.vue';
import { useDesign } from '@/store'
const userDesign = useDesign();
const design = useDesign()
const props = defineProps<{
  res: any
}>()

function handleClickCarousel() {
  design.setCurrentPcDesign(props.res)
}
</script>

<style scoped>
.skeleton {
  box-sizing: border-box;
  border: 3px dotted #6d6d6d;
}
</style>
