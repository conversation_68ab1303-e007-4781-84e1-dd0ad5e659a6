import qs from 'query-string';
// eslint-disable-next-line import/no-cycle
import { ParamsRule } from '@/types/global';
import request, { Method } from '@/utils/axios';

interface loginParams {
  username: string | number;
  password: string;
}

/**
 * 登录
 * @returns
 * @param params
 */
export function login(params: loginParams) {
  return request({
    url: '/passport/login/userLogin',
    method: Method.POST,
    needToken: false,
    data: qs.stringify(params),
    headers: {
      'clientType': 'PC',
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 * 登出
 */
export function logoutUser() {
  return request({
    url: '/passport/login/logout',
    method: Method.POST,
    needToken: true,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 * 获取店铺信息
 */
export function getStoreInfo() {
  return request({
    url: '/settings/storeSettings',
    method: Method.GET,
    needToken: true,
  });
}

/**
 * 保存店铺信息
 */
export function setStoreInfo(params: any) {
  return request({
    url: '/settings/storeSettings',
    method: Method.PUT,
    data: qs.stringify(params),
    needToken: true,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 * 刷新token
 */
export function refreshTokenMethod(token: string) {
  return request({
    url: `/passport/login/refresh/${token}`,
    method: Method.GET,
    params: token,
  });
}

/**
 *
 * 个人中心修改密码
 */
export function changePassword(params: any) {
  return request({
    url: `/passport/login/modifyPass`,
    method: Method.POST,
    needToken: true,
    data: qs.stringify(params),
  });
}

/**
 *
 * 获取权限菜单
 */
export function getPermissionMenu() {
  return request({
    url: `/store/menu/memberMenu`,
    method: Method.GET,
    needToken: true,
  });
}
