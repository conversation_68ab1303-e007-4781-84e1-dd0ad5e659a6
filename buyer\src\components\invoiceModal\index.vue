<template>
  <a-modal v-model:visible="visible" :align-center="false" title-align="start" :width="700" draggable>
    <template #title>发票信息</template>
    <a-form ref="invoiceFormRef" :model="invoiceForm" :style="{ width: '600px' }">
      <a-form-item field="" label="发票类型" :rules="[REQUIRED]">
        <a-radio-group v-model="invoice" type="button" @change="changeInvoice">
          <a-radio :value="1">电子普通发票</a-radio>
          <a-radio :value="2" :disabled="true">增值税专用发票</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item field="" label="发票抬头" :rules="[REQUIRED]">
        <a-radio-group v-model="type" type="button" @change="changeInvoice">
          <a-radio :value="1">个人</a-radio>
          <a-radio :value="2">单位</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item field="receiptTitle" label="个人名称" v-if="type === 1" :rules="[REQUIRED]">
        <a-input style="width: 300px;" v-model="invoiceForm.receiptTitle" placeholder=""></a-input>
      </a-form-item>
      <a-form-item field="receiptTitle" label="单位名称" v-if="type === 2" :rules="[REQUIRED]">
        <a-input style="width: 300px;" v-model="invoiceForm.receiptTitle" placeholder=""></a-input>
      </a-form-item>
      <a-form-item field="taxpayerId" label="纳税人识别号" v-if="type === 2" :rules="[REQUIRED]">
        <a-input style="width: 300px;" v-model="invoiceForm.taxpayerId" placeholder=""></a-input>
      </a-form-item>
      <a-form-item field="receiptTitle" label="发票内容" :rules="[REQUIRED]">
        <a-radio-group v-model="invoiceForm.receiptContent" type="button">
          <a-radio value="商品明细">商品明细</a-radio>
          <a-radio value="商品类别">商品类别</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
    <template #footer>
      <div style="text-align: right">
        <a-button @click="close">取消</a-button>
        <a-button class="ml_10" type="primary" @click="handleSubmit" :loading="loading">保存发票信息</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
  import { useRoute } from 'vue-router';
  import { ref, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { REQUIRED } from '@/utils/validator';
  import { receiptSelect } from '@/api/cart';

  const route = useRoute();
  const loading = ref(false);
  const invoiceFormRef = ref();
  // 普票表单
  const invoiceForm = ref<any>({
    receiptTitle: '', // 发票抬头
    taxpayerId: '', // 纳税人识别号
    receiptContent: '商品明细', // 发票内容
  });
  const invoice = ref(1); // 发票类型
  const visible = ref(false); // 模态框显隐
  const type = ref(1); // 1 个人 2 单位

  const props = defineProps(['invoiceData']);
  const emit = defineEmits<{
    (e: 'callback', val: any): void;
  }>();

  // 选择发票抬头
  const changeInvoice = (val: any) => {
    type.value = val;
  };
  // 保存发票信息
  const handleSubmit = async () => {
    let flage = true;
    // 保存分为两种类型，个人以及企业
    const { receiptTitle } = JSON.parse(JSON.stringify(invoiceForm.value));
    // 判断是否填写发票抬头
    if (!receiptTitle) {Message.error('请填写发票抬头!');flage = false;}
    if (type.value === 2) {
      const auth = await invoiceFormRef.value?.validate();
      if (auth) {flage = false;}
    } else {
      delete invoiceForm.value.taxpayerId;
    }
    if (flage) {
      loading.value = true;
      let submit = {
        way: route.query.way,
        ...invoiceForm.value
      };
      let receipt = await receiptSelect(submit);
      if (receipt.data.success) {
        emit('callback', true);
      }
      loading.value = false;
    }
  };
  // 取消
  const close = () => {
    visible.value = false;
  };
  // 初始化内容
  const init = () => {
    visible.value = true;
  };

  // 回显发票信息
  watch(() => props.invoiceData, (val) => {
    invoiceForm.value = { ...invoiceForm.value, ...val };
    if (val.taxpayerId) {
      type.value = 2;
    } else {
      type.value = 1;
    }
  }, {deep: true,immediate: true,},);
  // 暴露方法变量
  defineExpose({
    init,
    close,
    visible
  });
</script>

<style scoped lang="less">




</style>
