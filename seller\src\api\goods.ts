import { ParamsRule } from '@/types/global';
import request, { Method } from '@/utils/axios';

// 商品列表
export function getGoodsListDataSeller(params: ParamsRule) {
  return request({
    url: '/goods/goods/list',
    method: Method.GET,
    needToken: true,
    params,
  });
}
//  获取商品分页列表
export function getGoodsSkuListDataSeller(params: any) {
  return request({
    url: '/goods/goods/sku/list',
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 下架商品
export function lowGoods(params: { goodsId: string }) {
  return request({
    url: '/goods/goods/under',
    method: Method.PUT,
    needToken: true,
    params,
  });
}
// 上架商品
export function upGoods(params: { goodsId: string }) {
  return request({
    url: '/goods/goods/up',
    method: Method.PUT,
    needToken: true,
    params,
  });
}
// 删除商品
export function deleteGoods(params: { goodsId: string }) {
  return request({
    url: '/goods/goods/delete',
    method: Method.PUT,
    needToken: true,
    params,
  });
}
// 批量设置运费模板
export function batchShipTemplate(params: ParamsRule) {
  return request({
    url: '/goods/goods/freight',
    method: Method.PUT,
    needToken: true,
    params,
  });
}
// 查询运费模板
export function getShipTemplate() {
  return request({
    url: '/setting/freightTemplate',
    method: Method.GET,
    needToken: true,
  });
}
// 更新商品库存
export function updateGoodsSkuStocks(params: ParamsRule) {
  return request({
    url: '/goods/goods/update/stocks',
    method: Method.PUT,
    needToken: true,
    data: params,
    headers: { 'Content-Type': 'application/json' },
  });
}
// 下载模板
export function downLoadGoods() {
  return request({
    url: '/goods/import/downLoad',
    method: Method.GET,
    needToken: true,
    responseType: 'blob',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  });
}
// 上传商品列表
export function uploadGoodsExcel(params:any) {
  return request({
    url: '/goods/import/import',
    method: Method.POST,
    needToken: true,
    params,
  });
}
// 获取草稿商品分页列表
export function getDraftGoodsListData(params: ParamsRule) {
  return request({
    url: '/goods/draftGoods/page',
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 删除草稿商品
export function deleteDraftGoods(id: string | number) {
  return request({
    url: `/goods/draftGoods/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}

// 获取商品sku分页列表
export function getGoodsSkuData(params: ParamsRule) {
  return request({
    url: '/goods/goods/sku/list',
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 获取分类
export function getCategory() {
  return request({
    url: '/goods/category/all',
    method: Method.GET,
    needToken: true,
  });
}

// 获取分类、品牌
export function getLabelData() {
  return request({
    url: '/goods/label',
    method: Method.GET,
    needToken: true,
  });
}

// 获取全部经营类目
export function getGoodsCategoryAll() {
  return request({
    url: '/goods/category/all',
    method: Method.GET,
    needToken: true,
  });
}

// 添加当前店铺分类
export function addShopGoodsLabel(params: ParamsRule) {
  return request({
    url: '/goods/label',
    method: Method.POST,
    needToken: true,
    params,
  });
}

// 修改当前店铺分类
export function editShopGoodsLabel(params: ParamsRule) {
  return request({
    url: '/goods/label',
    method: Method.PUT,
    needToken: true,
    params,
  });
}

// 删除当前店铺分类
export function delCateShopGoodsLabel(id: string | number) {
  return request({
    url: `/goods/label/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}

// 根据分类id获取关联品牌
export function getCategoryBrandListDataSeller(categoryId: string | number) {
  return request({
    url: `/goods/category/${categoryId}/brands`,
    method: Method.GET,
    needToken: true,
  });
}

// 获取商品单位列表
export function getGoodsUnitList(params: any) {
  return request({
    url: '/goods/goodsUnit',
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 查询分类绑定参数信息
export function getCategoryParamsListDataSeller(id: string | number) {
  return request({
    url: `/goods/categoryParameters/${id}`,
    method: Method.GET,
    needToken: true,
  });
}
// 保存草稿商品
export function saveDraftGoods(params: any) {
  return request({
    url: `/goods/draftGoods/save`,
    method: Method.POST,
    needToken: true,
    headers: { 'Content-Type': 'application/json' },
    data: params,
  });
}
// 编辑商品
export function editGoods(goodsId: string | number, params: ParamsRule) {
  return request({
    url: `/goods/goods/update/${goodsId}`,
    method: Method.PUT,
    needToken: true,
    headers: { 'Content-Type': 'application/json' },
    data: params,
  });
}

// 添加商品
export function createGoods(params: ParamsRule) {
  return request({
    url: '/goods/goods/create',
    method: Method.POST,
    needToken: true,
    headers: { 'Content-Type': 'application/json' },
    data: params,
  });
}

// 保存获取关联规格
export function getGoodsSpecInfoSeller(categoryId: string | number) {
  return request({
    url: `/goods/spec/${categoryId}`,
    method: Method.GET,
    needToken: true,
  });
}

// 获取草稿商品详情
export function getDraftGoodsDetail(id: string | number) {
  return request({
    url: `/goods/draftGoods/${id}`,
    method: Method.GET,
    needToken: true,
  });
}

// 获取商品详情
export function getGoods(id: string | number) {
  return request({
    url: `/goods/goods/get/${id}`,
    method: Method.GET,
    needToken: true,
  });
}

// 获取商品告警分页列表
export function getGoodsListDataByStockSeller(params: ParamsRule) {
  return request({
    url: `/goods/goods/list/stock`, params,
    method: Method.GET,
    needToken: true
  })
}

// 获取审核数量
export function getGoodsAuthNum() {
  return request({
    url: `/goods/goods/authNum`,
    method: Method.GET,
    needToken: true,
  });
}

// 获取包月商品列表
export function getSubGoodsList(params: Record<string, any>) {
  return request({
    url: `/goods/sub-goods/page`,
    method: Method.GET,
    needToken: true,
    params,
  });
}

export function addSubGoods(data: Record<string, any>) {
  return request({
    url: `/goods/sub-goods/create`,
    method: Method.POST,
    needToken: true,
    data,
  });
}

export function updateSubGoods(subGoodsId: string, data: Record<string, any>) {
  return request({
    url: `/goods/sub-goods/update/${subGoodsId}`,
    method: Method.PUT,
    needToken: true,
    data,
  });
}

export function upperSubGoods(subGoodsId: string) {
  return request({
    url: `/goods/sub-goods/up/${subGoodsId}`,
    method: Method.PUT,
    needToken: true,
  });
}

export function underSubGoods(subGoodsId: string) {
  return request({
    url: `/goods/sub-goods/under/${subGoodsId}`,
    method: Method.PUT,
    needToken: true,
  });
}

export function getSubGoodsDetail(subGoodsId: string) {
  return request({
    url: `/goods/sub-goods/${subGoodsId}`,
    method: Method.GET,
    needToken: true,
  });
}
