<template>
  <a-row style="height: 600px;">
    <a-col :span="4">
      <div style="padding: 10px 0 10px;display: flex;">
        <uploadPicInput :api="() => { }" :accessToken="props.accessToken" @onSuccess="() => init()"
          :dataParams="{ directoryPath: apiParams.fileDirectoryId }" />
        <a-button @click="() => init()" style="margin-left: 10px;">刷新</a-button>
      </div>
      <a-tree :blockNode="true" :data="treeData" @select="treeSelect">
        <template #extra="nodeData">
          <a-dropdown>
            <!-- @select="onIconClick($event, nodeData)" -->
            <icon-more style="position: absolute; right: 16px; font-size: 22px; top: 5px; color: #999999;" />
            <template #content>
              <a-doption value="edit">编辑</a-doption>
              <a-doption value="delete">删除</a-doption>
            </template>
          </a-dropdown>
        </template>
        <template #switcher-icon></template>
      </a-tree>
    </a-col>
    <a-col :span="20">
      <div class="oss-wrapper" :style="{ height: isNormal ? '560px' : 'auto' }">
        <a-card v-for="(item, index) in ossFileList" :key="index" class="oss-card"
          v-if="ossFileList && ossFileList.length">
          <div class="content">
            {{ item.selected }}
            <img class="img" :src="item.url" @click="selectedParams(item)" />
            <div class="actions">
              <div class="btn"><a-tooltip content="下载"><icon-cloud-download @click="download(item)" /></a-tooltip></div>
              <div class="btn"><a-tooltip content="预览"><icon-eye @click="look(item)" /></a-tooltip></div>
              <div class="btn"><a-tooltip content="删除"><icon-delete @click="remove(item)" /></a-tooltip></div>
            </div>
          </div>
        </a-card>
        <div v-else style="width: 100%;color: #a6a6a6;text-align: center;line-height: 560px;">暂无资源</div>
      </div>
      <a-row style="display: flex;justify-content: end;width: 100%;margin-right: 25px;">
        <a-pagination :current="apiParams.current" :total="apiParams.total" :pageSize="apiParams.pageSize"
          @change="change"></a-pagination>
      </a-row>
      <a-modal v-model:visible="picVisible" title="查看图片" draggable>
        <a-image :src="file.url" width="100%" alt="无效的图片链接"></a-image>
        <template #footer><span>文件类型：{{ file.fileType }} 文件大小：{{ file.msize }}</span></template>
      </a-modal>
    </a-col>
  </a-row>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, reactive, withDefaults, getCurrentInstance } from 'vue';
import { Message } from '@arco-design/web-vue';
import uploadPicInput from '../uploader/index.vue';
const ossFileList = ref<any>([]); //

const useCurrentInstance = () => {
  const { appContext } = getCurrentInstance() as ComponentInternalInstance;
  const { globalProperties } = appContext.config;
  return {
    globalProperties,
  };
}
//  获取modal
const modal = useCurrentInstance().globalProperties?.$modal;
const selectedWay = ref<Array<any>>([]); // 选择数据
const picVisible = ref(false); // 图片的modal
const file = ref<any>({}); // 文件数据
// 传递的参数
const apiParams = reactive<any>({
  // 搜索框对应data对象
  fileType: 'image',
  pageSize: 12, // 页面大小
  sort: 'createTime', // 默认排序字段
  order: 'desc', // 默认排序方式
  startDate: '', // 起始时间
  endDate: '', // 终止时间
  title: '',
  total: 0,
  current: 1,
  fileDirectoryId: ''
});
const emit: any = defineEmits<{
  (e: 'selected', val: any, type: string): void;
}>();
const treeData = ref<Array<any>>([]); // 树结构
const treeDataDefault = ref([]);

const props = withDefaults(defineProps<{
  isSingle?: boolean
  templateModel?: string
  initialize?: boolean
  accessToken: string
  apis: {
    init(): any // 初始化
    rename(): any // 重定义文件名
    delete(): any // 删除文件
    initDir(): any // 初始化目录
    addDir(): any // 新增目录
    updateDir(): any // 更新目录
    delDir(): any // 删除目录
  }
}>(), {
  // 是否单选
  isSingle: false,
  // 组件模式
  templateModel: "normal",
  initialize: false,
  accessToken: ""
})

const isNormal: boolean = props.templateModel === 'normal';

// 选择
const selectedParams = (item: any) => {
  if (props.isSingle) {
    ossFileList.value.forEach((e: any) => {
      e.selected = false;
    });
    item.selected = true;
    selectedWay.value.push(item);
  } else {
    if (item.selected == false) {
      item.selected = true;
      selectedWay.value.push(item);
    } else {
      item.selected = false;
      for (let i = 0; i < selectedWay.value.length; i += 1) {
        if (selectedWay.value[i].id === item.id) {
          selectedWay.value.splice(i, 1);
          break;
        }
      }
    }
  }
  emit('selected', selectedWay.value);
};
// 下载
const download = (v: any) => {
  window.open(
    `${v.url}?attname=&response-content-type=application/octet-stream`
  );
};
// 查看
const look = (v: any) => {
  file.value = v;
  file.value.msize = `${((v.fileSize * 1.0) / (1024 * 1024)).toFixed(2)}MB`;
  picVisible.value = true;
};

// 缩略图请求接口
const init = async (params = apiParams) => {
  const res = await props.apis.init(params);
  if (res.data.success) {
    res.data.result.records.forEach((item: any) => {
      item.selected = false;
    });
    ossFileList.value = res.data.result.records;
    const { current, size, total } = res.data.result;
    apiParams.current = current;
    apiParams.pageSize = size;
    apiParams.total = total;
  }
};
// 删除
const remove = (v: any) => {
  modal.confirm({
    title: '确认删除',
    content: `您确认要删除文件${v.name}?`,
    alignCenter: false,
    onOk: async () => {
      const res = await props.apis.delete(v.id);
      if (res.data.success) {
        Message.success(`删除文件${v.name}成功`);
        // tablePageRef.value.init();
        init();
      }
    },
  });
};
// 分页改变事件
const change = (v: any) => {
  const params = {
    ...apiParams,
    pageNumber: v,
  };
  init(params);
};

// 文件目录格分类式化方法
const getTree = (tree = []) => {
  const arr: any = [];
  if (!!tree && tree.length !== 0) {
    // :field-names="{ children: 'children', title: 'directoryName', key: 'id' }"
    tree.forEach((item: any) => {
      const obj: any = {};
      obj.title = item.directoryName;
      obj.key = item.id; // 拥有者id
      obj.type = item.directoryType; // 用户类型
      obj.label = item.directoryName;
      obj.level = item.level;
      obj.expand = false;
      obj.selected = false;
      obj.contextmenu = true;
      obj.parentId = item.parentId;
      obj.children = getTree(item.children);
      arr.push(obj);
    })
  }
  return arr;
};
// 获取文件目录列表
const getAllList = () => {
  props.apis.initDir().then(res => {
    if (res.data.success) {
      // treeData.value = res.data.result;
      treeData.value = getTree(res.data.result);
      treeDataDefault.value = getTree(res.data.result);
      treeData.value.unshift({
        title: "全部分类",
        label: "全部分类",
        value: "0",
        level: 0,
        children: [],
        id: "0",
        categoryId: 0,
        key: '0'
      });
    }
  })
};
// 选择目录回调
const treeSelect = (selectedKeys: any, data: any) => {
  let id = selectedKeys[selectedKeys.length - 1];
  if (id == '0') {
    delete apiParams.fileDirectoryId;
  } else {
    apiParams.fileDirectoryId = id;
  }
  change(1);
};

onMounted(() => {
  init();
  getAllList();
});
// 初始化监听 是否清空所选图片
watch(() => props.initialize, (val) => {
  if (val) {
    selectedWay.value = [];
    // 获取文件列表
    getAllList();
    init();
  }
}, { deep: true, immediate: true })
</script>

<style scoped lang="less">
.none {
  display: none;
}

.oss-wrapper {
  display: flex;
  flex-wrap: wrap;
  position: relative;
  justify-content: flex-start;
  align-content: start;
}

.oss-card {
  // display: flex;
  position: relative;
  margin: 10px;
  width: 200px;

  :hover {
    .content .other .name {
      color: rgb(var(--arcoblue-6));
      transition: color 0.3s;
    }
  }

  cursor: pointer;

  .content {
    display: flex;
    flex-direction: column;

    :hover {
      .play {
        transition: opacity 0.3s;
        opacity: 1 !important;
      }
    }

    .img {
      height: 100px;
      object-fit: cover;
    }

    .actions {
      display: flex;
      align-items: center;
      height: 30px;
      background: #f7f9fa;
      border-top: 1px solid #e8e8e8;

      i:hover {
        color: rgb(var(--arcoblue-6));
      }

      .btn {
        display: flex;
        justify-content: center;
        width: 33.33%;
        border-right: 1px solid #e8e8e8;
      }

      .btn-no {
        display: flex;
        justify-content: center;
        width: 33.33%;
      }
    }
  }
}

.active {
  width: 25px;
  position: absolute;
  top: 3px;
  right: 5px;
}
</style>
