// eslint-disable-next-line import/no-cycle
import request, { Method, commonUrl } from '@/utils/axios';
import { ParamsRule } from '@/types/global';

// 店铺分页列表
export function shopList (params: ParamsRule) {
  return request({
    url: '/store/store',
    needToken: true,
    method: Method.GET,
    params
  })
}

// 申请店铺第一步-填写企业信息
export function applyFirst (params: ParamsRule) {
  return request({
    url: '/store/store/apply/first',
    needToken: true,
    method: Method.PUT,
    params,
    headers: {'Content-Type': 'application/json'},
  })
}

// 申请店铺第一步-填写企业信息
export function applySecond (params: ParamsRule) {
  return request({
    url: '/store/store/apply/second',
    needToken: true,
    method: Method.PUT,
    params
  })
}

// 申请店铺第一步-填写企业信息
export function applyThird (params: ParamsRule) {
  return request({
    url: '/store/store/apply/third',
    needToken: true,
    method: Method.PUT,
    params
  })
}

// 店铺详情
export function getDetailById (id: any) {
  return request({
    url: `/store/store/get/detail/${id}`,
    needToken: true,
    method: Method.GET
  })
}
// 店铺分类
export function getCateById (id: any) {
  return request({
    url: `/store/store/label/get/${id}`,
    needToken: true,
    method: Method.GET
  })
}
//  店铺入驻协议
export function agreement () {
  return request({
    url: `/other/article/type/STORE_REGISTER`,
    needToken: true,
    method: Method.GET
  })
}

//  获取当前登录会员的店铺信息
export function applyStatus () {
  return request({
    url: `/store/store/apply`,
    needToken: true,
    method: Method.GET
  })
}

/**
 * 获取自提点信息
 * @param storeId
 */
export function getStoreAddress(storeId: number | string,params: ParamsRule) {
  return request({
    url: `/store/address/page/${storeId}`,
    method: Method.GET,
    params
  });
}
