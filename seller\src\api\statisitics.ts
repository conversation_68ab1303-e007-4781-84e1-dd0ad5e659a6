import { ParamsRule } from '@/types/global';
import request, { commonUrl, Method } from '@/utils/axios';

/**
 * 商品统计排行前一百的数据
 */
export function goodsStatistics(params: ParamsRule) {
  return request({
    url: '/statistics/goods',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 *  获取商家首页数据
 */
export function getDashboardData() {
  return request({
    url: `/statistics/index`,
    method: Method.GET,
    needToken: true,
  });
}

// 获取订单统计图表
export function getOrderChart(params: any) {
  return request({
    url: '/statistics/order',
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 获取流量统计图表
export function getPreviewChart(params: any) {
  return request({
    url: '/statistics/view/list',
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 订单统计概括
export function getOrderOverView(params: any) {
  return request({
    url: '/statistics/order/overview',
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 统计相关订单统计
export function statisticsOrderList(params: any) {
  return request({
    url: '/statistics/order/order',
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 获取首页统计数量
export function getSellerHomeData(params?: ParamsRule) {
  return request({
    url: '/statistics/index',
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 统计相关退单统计
export function statisticsOrderRefundList(params: any) {
  return request({
    url: '/statistics/order/refund',
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 获取所有city
export function getAllCity() {
  return request({
    url: `${commonUrl}/common/common/region/allCity`,
    method: Method.GET,
    needToken: true,
  });
}
