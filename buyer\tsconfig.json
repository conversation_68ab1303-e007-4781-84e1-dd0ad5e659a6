{
  "compilerOptions": {
    "target": "ESNext",
    "jsx": "preserve",
    "lib": ["DOM", "ESNext"],
    "baseUrl": ".",
    "module": "ESNext",
    "moduleResolution": "node",
    "paths": {
      "@/*": ["src/*"]
    },
    "resolveJsonModule": true,
    "types": [
      "vitest",
      "vite/client",
      "vue/ref-macros",
      "vite-plugin-vue-layouts/client",
      "vite-plugin-pwa/client",
      "unplugin-vue-macros/macros-global",
      "naive-ui/volar"
    ],
    "allowJs": true,
    "strict": true,
    "strictNullChecks": true,
    "noUnusedLocals": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    "skipLibCheck": true
  },
  "vueCompilerOptions": {
    "plugins": [
      "@vue-macros/volar/define-models",
      "@vue-macros/volar/define-slots"
    ]
  },
  "exclude": ["dist", "node_modules",]
}
