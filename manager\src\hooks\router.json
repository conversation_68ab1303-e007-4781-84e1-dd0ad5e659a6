{"data": {"success": true, "message": "success", "code": 200, "timestamp": 1705655177257, "result": [{"id": "1367038467288072191", "createBy": "admin", "createTime": "2021-03-03 09:05:44", "updateBy": "admin", "updateTime": "2021-03-03 09:09:27", "deleteFlag": false, "title": "工作台", "icon": "icon-dashboard", "name": "dashboard", "path": "dashboard", "level": 0, "frontRoute": "null", "parentId": "0", "sortOrder": 0.0, "permission": "", "children": [{"id": "1367041332861730816", "createBy": "admin", "createTime": "2021-03-03 09:17:07", "updateBy": "admin", "updateTime": "2023-06-16 16:30:21", "deleteFlag": false, "title": "工作台", "name": "Workplace", "path": "workplace", "level": 1, "frontRoute": "views/dashboard/workplace/index", "parentId": "1367038467288072192", "sortOrder": 0.0}]}, {"id": "1367038467288072192", "createBy": "admin", "createTime": "2021-03-03 09:05:44", "updateBy": "admin", "updateTime": "2021-03-03 09:09:27", "deleteFlag": false, "title": "会员", "icon": "icon-user", "name": "member", "path": "member", "level": 0, "frontRoute": "null", "parentId": "0", "sortOrder": 0.0, "permission": "/manager/member*,/manager/wallet/log*,/manager/passport*,/manager/common*,/manager/order/*,/manager/wallet/wallet*,/manager/trade/receipt*", "children": [{"id": "1367041332861730816", "createBy": "admin", "createTime": "2021-03-03 09:17:07", "updateBy": "admin", "updateTime": "2023-06-16 16:30:21", "deleteFlag": false, "title": "会员列表", "name": "member-list", "path": "member-list", "level": 1, "frontRoute": "views/member/member-list/list", "parentId": "1367038467288072192", "sortOrder": 0.0}, {"id": "1367041575619657728", "createBy": "admin", "createTime": "2021-03-03 09:17:07", "updateBy": "admin", "updateTime": "2023-06-16 16:30:21", "deleteFlag": false, "title": "回收站", "name": "member-recycle", "path": "member-recycle", "level": 1, "frontRoute": "views/member/member-recycle/index", "parentId": "1367038467288072192", "sortOrder": 1.0}, {"id": "1367042917113266176", "createBy": "admin", "createTime": "2021-03-03 09:23:25", "updateBy": null, "updateTime": null, "deleteFlag": false, "title": "会员评价", "name": "member-comment", "path": "member-comment", "level": 1, "frontRoute": "views/member/member-comment/index", "parentId": "1367038467288072192", "sortOrder": 2.0, "permission": "/manager/memberEvaluation*"}, {"id": "1373166892465782784", "createBy": "admin", "createTime": "2021-03-20 01:57:54", "updateBy": "admin", "updateTime": "2021-03-22 20:13:48", "deleteFlag": false, "title": "历史积分", "name": "point", "path": "point", "level": 1, "frontRoute": "views/member/point/index", "parentId": "1367038467288072192", "sortOrder": 3.0, "permission": "/manager/member/memberP<PERSON><PERSON><PERSON>istory*"}, {"id": "1367042490443497472", "createBy": "admin", "createTime": "2021-03-03 09:21:43", "updateBy": null, "updateTime": null, "deleteFlag": false, "title": "会员资金", "name": "member-capital", "path": "member-capital", "level": 1, "frontRoute": "views/member/member-capital/index", "parentId": "1367038467288072192", "sortOrder": 4.0, "permission": "/manager/wallet/log*"}, {"id": "1367042804944994304", "createBy": "admin", "createTime": "2021-03-03 09:21:43", "updateBy": null, "updateTime": null, "deleteFlag": false, "title": "充值记录", "name": "recharge-record", "path": "recharge-record", "level": 1, "frontRoute": "views/member/recharge-record/index", "parentId": "1367038467288072192", "sortOrder": 5.0, "permission": "/manager/wallet/recharge*"}, {"id": "1367042804944994305", "createBy": "admin", "createTime": "2021-03-03 09:21:43", "updateBy": null, "updateTime": null, "deleteFlag": false, "title": "提现申请", "name": "withdraw-apply", "path": "withdraw-apply", "level": 1, "frontRoute": "views/member/withdraw-apply/index", "parentId": "1367038467288072192", "sortOrder": 6.0, "permission": "/manager/wallet/withdrawApply*"}]}, {"id": "1367039534616805376", "createBy": "admin", "createTime": "2021-03-03 09:09:58", "updateBy": "admin", "updateTime": "2021-05-18 10:51:12", "deleteFlag": false, "title": "订单", "icon": "icon-unordered-list", "name": "order-module", "path": "order-module", "level": 0, "frontRoute": "null", "parentId": "0", "sortOrder": 1.0, "permission": "/manager/order*", "children": [{"id": "1367043443917848576", "createBy": "admin", "createTime": "2021-03-03 09:25:30", "updateBy": "admin", "updateTime": "2023-03-17 20:18:57", "deleteFlag": false, "title": "商品订单", "name": "order-list", "path": "order-list", "level": 1, "frontRoute": "views/order/order-list/list", "parentId": "1367039534616805376", "sortOrder": 0.0, "permission": "/manager/order*"}, {"id": "1367043791105556480", "createBy": "admin", "createTime": "2021-03-03 09:25:30", "updateBy": "admin", "updateTime": "2023-03-17 20:18:57", "deleteFlag": false, "title": "虚拟订单", "name": "fictitious-order", "path": "fictitious-order", "level": 1, "frontRoute": "views/order/fictitious-order/index", "parentId": "1367039534616805376", "sortOrder": 1.0, "permission": "/manager/order*"}, {"id": "1367043505771249664", "createBy": "admin", "createTime": "2021-03-03 09:25:45", "updateBy": "admin", "updateTime": "2023-03-17 20:19:17", "deleteFlag": false, "title": "售后管理", "name": "after-sale", "path": "after-sale", "level": 1, "frontRoute": "views/order/after-sale/index", "parentId": "1367039534616805376", "sortOrder": 2.0, "permission": "/manager/order/afterSale*"}, {"id": "1367044121163726848", "createBy": "admin", "createTime": "2021-03-03 09:25:45", "updateBy": "admin", "updateTime": "2023-03-17 20:19:17", "deleteFlag": false, "title": "交易投诉", "name": "order-complaint", "path": "order-complaint", "level": 1, "frontRoute": "views/order/order-complaint/list", "parentId": "1367039534616805376", "sortOrder": 3.0, "permission": "/manager/order/complain*"}, {"id": "1367044247978508288", "createBy": "admin", "createTime": "2021-03-03 09:25:45", "updateBy": "admin", "updateTime": "2023-03-17 20:19:17", "deleteFlag": false, "title": "售后原因", "name": "after-sale-reason", "path": "after-sale-reason", "level": 1, "frontRoute": "views/order/after-sale-reason/index", "parentId": "1367039534616805376", "sortOrder": 4.0, "permission": "/manager/order/afterSaleReason*"}, {"id": "1372807928452481024", "createBy": "admin", "createTime": "2021-03-19 02:11:30", "updateBy": "admin", "updateTime": "2023-03-17 20:19:12", "deleteFlag": false, "title": "收款记录", "name": "payment-log", "path": "payment-log", "level": 1, "frontRoute": "views/order/payment-log/index", "parentId": "1367039534616805376", "sortOrder": 4.0, "permission": "/manager/payment/paymentLog*"}, {"id": "1372808352295288832", "createBy": "admin", "createTime": "2021-03-19 02:11:30", "updateBy": "admin", "updateTime": "2023-03-17 20:19:12", "deleteFlag": false, "title": "退款流水", "name": "refund-log", "path": "refund-log", "level": 1, "frontRoute": "views/order/refund-log/index", "parentId": "1367039534616805376", "sortOrder": 4.0, "permission": "/manager/payment/refundLog*"}, {"id": "1634834716266446849", "createBy": "admin", "createTime": "2021-03-19 02:11:30", "updateBy": "admin", "updateTime": "2023-03-17 20:19:12", "deleteFlag": false, "title": "提现记录", "name": "withdraw-log", "path": "withdraw-log", "level": 1, "frontRoute": "views/order/withdraw-log/index", "parentId": "1367039534616805376", "sortOrder": 4.0, "permission": "/manager/payment/withdrawLog*"}, {"id": "1704426715304906754", "createBy": "admin", "createTime": "2021-03-19 02:11:30", "updateBy": "admin", "updateTime": "2023-03-17 20:19:12", "deleteFlag": false, "title": "补差记录", "name": "subsidies-log", "path": "subsidies-log", "level": 1, "frontRoute": "views/order/subsidies-log/index", "parentId": "1367039534616805376", "sortOrder": 4.0, "permission": "/manager/order/subsidiesLog*"}, {"id": "1634835112821112834", "createBy": "admin", "createTime": "2021-03-19 02:11:30", "updateBy": "admin", "updateTime": "2023-03-17 20:19:12", "deleteFlag": false, "title": "分账记录", "name": "out-order-log", "path": "out-order-log", "level": 1, "frontRoute": "views/order/out-order-log/index", "parentId": "1367039534616805376", "sortOrder": 4.0, "permission": "/manager/payment/outOrderLog*"}]}, {"id": "1367039950368800768", "createBy": "admin", "createTime": "2021-03-03 09:11:37", "updateBy": null, "updateTime": null, "deleteFlag": false, "title": "商品", "name": "goods-module", "icon": "icon-email", "path": "goods-module", "level": 0, "frontRoute": null, "parentId": "0", "sortOrder": 0.2, "permission": "/manager/goods*,/manager/common*", "children": [{"id": "1367044376391319552", "createBy": "admin", "createTime": "2021-03-03 09:29:12", "updateBy": "admin", "updateTime": "2023-03-17 20:20:29", "deleteFlag": false, "title": "平台商品", "name": "goods-list", "path": "goods-list", "level": 1, "frontRoute": "views/goods/goods-list/list", "parentId": "1367039950368800768", "sortOrder": 0.0, "permission": "/manager/goods*,/manager/common*"}, {"id": "1367045630710513664", "createBy": "admin", "createTime": "2021-03-03 09:29:12", "updateBy": "admin", "updateTime": "2023-03-17 20:20:29", "deleteFlag": false, "title": "商品审核", "name": "auth-goods-list", "path": "auth-goods-list", "level": 1, "frontRoute": "views/goods/auth-goods-list/list", "parentId": "1367039950368800768", "sortOrder": 1.0, "permission": "/manager/goods*,/manager/common*"}, {"id": "1367044657296441344", "createBy": "admin", "createTime": "2021-03-03 09:30:19", "updateBy": "admin", "updateTime": "2023-03-17 20:21:05", "deleteFlag": false, "title": "商品分类", "name": "category-list", "path": "category-list", "level": 1, "frontRoute": "views/goods/category/list", "parentId": "1367039950368800768", "sortOrder": 1.0, "permission": "/manager/goods/category*,/manager/goods/brand*,/manager/goods/spec*,/manager/goods/parameters*"}, {"id": "1367045921434501120", "createBy": "admin", "createTime": "2021-03-03 09:30:19", "updateBy": "admin", "updateTime": "2023-03-17 20:21:05", "deleteFlag": false, "title": "品牌列表", "name": "goods-brand", "path": "goods-brand", "level": 1, "frontRoute": "views/goods/goods-brand/list", "parentId": "1367039950368800768", "sortOrder": 2.0, "permission": "/manager/goods/brand*"}, {"id": "1367046266214678528", "createBy": "admin", "createTime": "2021-03-03 09:30:19", "updateBy": "admin", "updateTime": "2023-03-17 20:21:05", "deleteFlag": false, "title": "规格列表", "name": "goods-sku", "path": "goods-sku", "level": 1, "frontRoute": "views/goods/goods-sku/list", "parentId": "1367039950368800768", "sortOrder": 2.0, "permission": "/manager/goods/goodsSku*"}, {"id": "1367046266214678529", "createBy": "admin", "createTime": "2021-03-03 09:30:19", "updateBy": "admin", "updateTime": "2023-03-17 20:21:05", "deleteFlag": false, "title": "计量单位", "name": "goods-unit", "path": "goods-unit", "level": 1, "frontRoute": "views/goods/goods-unit/list", "parentId": "1367039950368800768", "sortOrder": 2.0, "permission": "/manager/goods/goodsUnit*"}]}, {"id": "1367039534616805377", "createBy": "admin", "createTime": "2021-03-03 09:09:58", "updateBy": "admin", "updateTime": "2021-05-18 10:51:12", "deleteFlag": false, "title": "财务", "icon": "icon-file", "name": "finance-module", "path": "finance-module", "level": 0, "frontRoute": "null", "parentId": "0", "sortOrder": 0.4, "permission": "/manager/finance*", "children": [{"id": "1367043443917848555", "createBy": "admin", "createTime": "2021-03-03 09:25:30", "updateBy": "admin", "updateTime": "2023-03-17 20:18:57", "deleteFlag": false, "title": "发票管理", "name": "receipt-list", "path": "receipt-list", "level": 1, "frontRoute": "views/finance/receipt/list", "parentId": "1367039534616805377", "sortOrder": 0.0, "permission": "/manager/finance*"}]}, {"id": "1367040067201138688", "createBy": "admin", "createTime": "2021-03-03 09:12:05", "updateBy": "admin", "updateTime": "2021-12-02 19:45:22", "deleteFlag": false, "title": "促销", "icon": "icon-tag", "name": "promotion-module", "path": "promotion-module", "level": 0, "frontRoute": "null", "parentId": "0", "sortOrder": 0.3, "permission": "/manager/goods/category/allChildren,/manager/goods/goods/sku/list*", "children": [{"id": "1367049214198022144", "createBy": "admin", "createTime": "2021-03-03 09:48:26", "updateBy": "admin", "updateTime": "2023-06-16 16:50:12", "deleteFlag": false, "title": "优惠券", "name": "coupon", "path": "coupon", "level": 1, "frontRoute": "views/promotion/coupon/list", "parentId": "1367040067201138688", "sortOrder": 0.0, "permission": "/manager/promotion/coupon*"}, {"id": "1403988156444962818", "createBy": "admin", "createTime": "2021-03-03 09:48:26", "updateBy": "admin", "updateTime": "2023-06-16 16:50:12", "deleteFlag": false, "title": "券活动", "name": "coupon-activity", "path": "coupon-activity", "level": 1, "frontRoute": "views/promotion/coupon-activity/list", "parentId": "1367040067201138688", "sortOrder": 1.0, "permission": "/manager/promotion/couponActivity*"}, {"id": "1367049500782231552", "createBy": "admin", "createTime": "2021-03-03 09:48:26", "updateBy": "admin", "updateTime": "2023-06-16 16:50:12", "deleteFlag": false, "title": "满额活动", "name": "full-discount", "path": "full-discount", "level": 1, "frontRoute": "views/promotion/full-discount/list", "parentId": "1367040067201138688", "sortOrder": 1.0, "permission": "/manager/promotion/fullDiscount*"}, {"id": "1367049611578966016", "createBy": "admin", "createTime": "2021-03-03 09:48:26", "updateBy": "admin", "updateTime": "2023-06-16 16:50:12", "deleteFlag": false, "title": "秒杀活动", "name": "seckill", "path": "seckill", "level": 1, "frontRoute": "views/promotion/seckill/list", "parentId": "1367040067201138688", "sortOrder": 1.0, "permission": "/manager/promotion/seckill*,/manager/setting/setting/get/SECKILL_SETTING*,/manager/setting/setting/get/SECKILL_SETTING"}, {"id": "1367049712657498112", "createBy": "admin", "createTime": "2021-03-03 09:48:26", "updateBy": "admin", "updateTime": "2023-06-16 16:50:12", "deleteFlag": false, "title": "拼团活动", "name": "pin<PERSON>an", "path": "pin<PERSON>an", "level": 1, "frontRoute": "views/promotion/pintuan/list", "parentId": "1367040067201138688", "sortOrder": 1.0, "permission": "/manager/promotion/pintuan*"}, {"id": "1430799171593535490", "createBy": "admin", "createTime": "2021-03-03 09:48:26", "updateBy": "admin", "updateTime": "2023-06-16 16:50:12", "deleteFlag": false, "title": "砍价活动", "name": "bargain-activity", "path": "bargain-activity", "level": 1, "frontRoute": "views/promotion/bargain-activity/list", "parentId": "1367040067201138688", "sortOrder": 1.0, "permission": "/manager/promotion/kan<PERSON><PERSON>G<PERSON>s*"}, {"id": "1407602441964244994", "createBy": "admin", "createTime": "2021-06-23 15:32:29", "updateBy": "admin", "updateTime": "2023-06-16 16:53:30", "deleteFlag": false, "title": "积分商品", "name": "integrate-goods", "path": "integrate-goods", "level": 1, "frontRoute": "views/promotion/integrate-goods/list", "parentId": "1367040067201138688", "sortOrder": 3.0, "permission": "/manager/promotion/points*"}, {"id": "1407602673334636546", "createBy": "admin", "createTime": "2021-06-23 15:32:29", "updateBy": "admin", "updateTime": "2023-06-16 16:53:30", "deleteFlag": false, "title": "积分分类", "name": "integrate-classify", "path": "integrate-classify", "level": 1, "frontRoute": "views/promotion/integrate-classify/list", "parentId": "1367040067201138688", "sortOrder": 3.0, "permission": "/manager/promotion/pointsGoodsCategory*"}]}, {"id": "1367048084701315072", "createBy": "admin", "createTime": "2021-03-03 09:43:57", "updateBy": "admin", "updateTime": "2021-03-03 09:52:17", "deleteFlag": false, "title": "店铺", "name": "shop", "icon": "icon-home", "path": "shop", "level": 0, "frontRoute": "null", "parentId": "0", "sortOrder": 0.4, "permission": "/manager/order*,/manager/store*,/manager/goods/category*,/manager/common/file*,/manager/passport/member*,/manager/statistics/index/notice*", "children": [{"id": "1367048684339986432", "createBy": "admin", "createTime": "2021-03-03 09:46:20", "updateBy": "admin", "updateTime": "2023-06-16 17:00:36", "deleteFlag": false, "title": "店铺列表", "name": "shop-list", "path": "shop-list", "level": 1, "frontRoute": "views/shop/shop-list/index", "parentId": "1367048084701315072", "sortOrder": 0.0, "permission": ""}, {"id": "1367048967635861504", "createBy": "admin", "createTime": "2021-03-03 09:46:20", "updateBy": "admin", "updateTime": "2023-06-16 17:00:36", "deleteFlag": false, "title": "店铺审核", "name": "shop-audit", "path": "shop-audit", "level": 1, "frontRoute": "views/shop/shop-audit/index", "parentId": "1367048084701315072", "sortOrder": 0.0, "permission": ""}, {"id": "1367048754229673984", "createBy": "admin", "createTime": "2021-03-03 09:46:36", "updateBy": "admin", "updateTime": "2023-06-16 17:02:23", "deleteFlag": false, "title": "店铺结算", "name": "shop-settlement", "path": "shop-settlement", "level": 1, "frontRoute": "views/shop/shop-settlement/index", "parentId": "1367048084701315072", "sortOrder": 0.0, "permission": "/manager/order*,/manager/payment/bill*"}, {"id": "1373791578371391488", "createBy": "admin", "createTime": "2021-03-03 09:46:20", "updateBy": "admin", "updateTime": "2023-06-16 17:00:36", "deleteFlag": false, "title": "商家对账", "name": "shop-reconciliation", "path": "shop-reconciliation", "level": 1, "frontRoute": "views/shop/shop-reconciliation/index", "parentId": "1367048084701315072", "sortOrder": 0.0, "permission": ""}, {"id": "1628586821962895362", "createBy": "admin", "createTime": "2023-02-24 02:45:31", "updateBy": "admin", "updateTime": "2023-08-23 16:47:21", "deleteFlag": false, "title": "进件", "name": "progress-form-list ", "path": "progress-form-list", "level": 1, "frontRoute": "views/shop/construction/applicationFormList", "parentId": "1367048084701315072", "sortOrder": 0.0, "permission": "/manager/store/Application/*,/manager/common/file*"}]}, {"id": "1367040599596728320", "createBy": "admin", "createTime": "2021-03-03 09:14:12", "updateBy": "admin", "updateTime": "2021-03-03 09:52:13", "deleteFlag": false, "title": "运营", "icon": "icon-user", "name": "decoration-module", "path": "decoration-module", "level": 0, "frontRoute": "null", "parentId": "0", "sortOrder": 0.5, "permission": "/manager/other/pageData*,/manager/file*,/manager/other/article*,/manager/promotion*,/manager/goods*,/manager/store*", "children": [{"id": "1367050320584114176", "createBy": "admin", "createTime": "2021-03-03 09:52:50", "updateBy": "admin", "updateTime": "2023-06-16 17:32:37", "deleteFlag": false, "title": "PC端", "name": "pc", "path": "pc", "level": 1, "frontRoute": "views/operation/decoration/pc/index", "parentId": "1367040599596728320", "sortOrder": 0.0, "permission": ""}, {"id": "1367050673312497664", "createBy": "admin", "createTime": "2021-03-03 09:52:50", "updateBy": "admin", "updateTime": "2023-06-16 17:32:37", "deleteFlag": false, "title": "APP装修", "name": "app", "path": "app", "level": 1, "frontRoute": "views/operation/decoration/app/index", "parentId": "1367040599596728320", "sortOrder": 0.0, "permission": ""}, {"id": "1526768613934030850", "createBy": "admin", "createTime": "2022-05-18 11:36:18", "updateBy": "admin", "updateTime": "2023-06-16 17:34:57", "deleteFlag": false, "title": "隐私协议", "name": "privacy-policy", "path": "privacy-policy", "level": 1, "frontRoute": "views/operation/privacy-policy/index", "parentId": "1367040599596728320", "sortOrder": 0.0, "permission": "/manager/common*,/manager/other/articleCategory*"}, {"id": "1374173575405109248", "createBy": "admin", "createTime": "2021-03-22 20:38:06", "updateBy": "admin", "updateTime": "2021-03-22 20:52:58", "deleteFlag": false, "title": "分销设置", "name": "distribution-setting", "path": "distribution-setting", "level": 1, "frontRoute": "views/operation/distribution/distribution-setting/index", "parentId": "1367040599596728320", "sortOrder": 1.0, "permission": "/manager/setting/setting*"}, {"id": "1374178303975358464", "createBy": "admin", "createTime": "2022-05-18 11:36:18", "updateBy": "admin", "updateTime": "2023-06-16 17:34:57", "deleteFlag": false, "title": "分销员", "name": "distrbution-staff", "path": "distrbution-staff", "level": 1, "frontRoute": "views/operation/distribution/distrbution-staff/index", "parentId": "1367040599596728320", "sortOrder": 0.0, "permission": "/manager/distribution*"}, {"id": "1374178079181635584", "createBy": "admin", "createTime": "2022-05-18 11:36:18", "updateBy": "admin", "updateTime": "2023-06-16 17:34:57", "deleteFlag": false, "title": "分销申请", "name": "distribution-apply", "path": "distribution-apply", "level": 1, "frontRoute": "views/operation/distribution/distribution-apply/index", "parentId": "1367040599596728320", "sortOrder": 0.0, "permission": "/manager/distribution/distribution*"}, {"id": "1374177789581721600", "createBy": "admin", "createTime": "2022-05-18 11:36:18", "updateBy": "admin", "updateTime": "2023-06-16 17:34:57", "deleteFlag": false, "title": "分销商品", "name": "distrbution-goods", "path": "distrbution-goods", "level": 1, "frontRoute": "views/operation/distribution/distrbution-goods/index", "parentId": "1367040599596728320", "sortOrder": 0.0, "permission": "/manager/distribution/goods*"}, {"id": "1374177910411231232", "createBy": "admin", "createTime": "2022-05-18 11:36:18", "updateBy": "admin", "updateTime": "2023-06-16 17:34:57", "deleteFlag": false, "title": "分销订单", "name": "distrbution-order", "path": "distrbution-order", "level": 1, "frontRoute": "views/operation/distribution/distrbution-order/index", "parentId": "1367040599596728320", "sortOrder": 0.0, "permission": "/manager/distribution/order*,/manager/store*"}, {"id": "1410862675914764290", "createBy": "admin", "createTime": "2022-05-18 11:36:18", "updateBy": "admin", "updateTime": "2023-06-16 17:34:57", "deleteFlag": false, "title": "分销提现", "name": "distrbution-withdrawal", "path": "distrbution-withdrawal", "level": 1, "frontRoute": "views/operation/distribution/distrbution-withdrawal/index", "parentId": "1367040599596728320", "sortOrder": 0.0, "permission": "/manager/distribution/cash*"}, {"id": "1367050250249830400", "createBy": "admin", "createTime": "2021-03-03 09:52:33", "updateBy": "admin", "updateTime": "2023-06-16 17:35:03", "deleteFlag": false, "title": "搜索热词", "name": "hot-key-word", "path": "hot-key-word", "level": 1, "frontRoute": "views/operation/article/hot-key-word/index", "parentId": "1367040599596728320", "sortOrder": 2.0, "permission": "/manager/hotwords*,/manager/store/store*"}, {"id": "1367050939084570624", "createBy": "admin", "createTime": "2021-03-03 09:52:33", "updateBy": "admin", "updateTime": "2023-06-16 17:35:03", "deleteFlag": false, "title": "文章分类", "name": "article-category", "path": "article-category", "level": 1, "frontRoute": "views/operation/article/article-category/index", "parentId": "1367040599596728320", "sortOrder": 2.0, "permission": "/manager/article-category*"}, {"id": "1367051048232943616", "createBy": "admin", "createTime": "2021-03-03 09:52:33", "updateBy": "admin", "updateTime": "2023-06-16 17:35:03", "deleteFlag": false, "title": "文章管理", "name": "article-list", "path": "article-list", "level": 1, "frontRoute": "views/operation/article/article-list/index", "parentId": "1367040599596728320", "sortOrder": 2.0, "permission": "/manager/other/articleCategory*,/manager/other/article*"}, {"id": "1419926569920536578", "createBy": "admin", "createTime": "2021-03-03 09:52:33", "updateBy": "admin", "updateTime": "2023-06-16 17:35:03", "deleteFlag": false, "title": "ES分词", "name": "custom-words", "path": "custom-words", "level": 1, "frontRoute": "views/operation/article/custom-words/index", "parentId": "1367040599596728320", "sortOrder": 2.0, "permission": "/manager/other/customWords*"}, {"id": "1374154349697040384", "createBy": "admin", "createTime": "2021-03-22 19:21:42", "updateBy": "admin", "updateTime": "2023-06-16 17:37:03", "deleteFlag": false, "title": "意见反馈", "name": "feedback", "path": "feedback", "level": 1, "frontRoute": "views/operation/feedback/index", "parentId": "1367040599596728320", "sortOrder": 3.0, "permission": "/manager/other/feedback*"}, {"id": "1376450531517530112", "createBy": "admin", "createTime": "2021-03-29 03:25:55", "updateBy": "admin", "updateTime": "2023-06-16 17:39:57", "deleteFlag": false, "title": "站内信", "name": "notice-message-template", "path": "notice-message-template", "level": 1, "frontRoute": "views/operation/notice-message-template/index", "parentId": "1367040599596728320", "sortOrder": 5.0, "permission": "/manager/setting/noticeMessage*,/manager/other/*,/manager/passport/member*,/manager/store/store*"}, {"id": "1376450766817984512", "createBy": "admin", "createTime": "2021-03-29 03:26:51", "updateBy": "admin", "updateTime": "2021-03-29 03:27:25", "deleteFlag": false, "title": "短信管理", "name": "sms", "path": "sms", "level": 1, "frontRoute": "views/operation/sms/index", "parentId": "1367040599596728320", "sortOrder": 6.0, "permission": "manager/sms*,/manager/passport/member*"}]}, {"id": "1367040819248234496", "createBy": "admin", "createTime": "2021-03-03 09:15:04", "updateBy": null, "updateTime": null, "deleteFlag": false, "title": "统计", "name": "statistic-module", "icon": "icon-computer", "path": "statistic-module", "level": 0, "frontRoute": null, "parentId": "0", "sortOrder": 0.7, "permission": "/manager/store*,/manager/member*,/manager/statistics*", "children": [{"id": "1367052616634204160", "createBy": "admin", "createTime": "2021-03-03 10:01:57", "updateBy": "admin", "updateTime": "2023-06-16 17:41:13", "deleteFlag": false, "title": "会员统计", "name": "member-statistics", "path": "member-statistics", "level": 1, "frontRoute": "views/statistics/member", "parentId": "1367040819248234496", "sortOrder": 0.0, "permission": "/manager/store*,/manager/member*,/manager/statistics*"}, {"id": "1367052915314786304", "createBy": "admin", "createTime": "2021-03-03 10:01:57", "updateBy": "admin", "updateTime": "2023-06-16 17:41:13", "deleteFlag": false, "title": "商品统计", "name": "goods", "path": "goods", "level": 1, "frontRoute": "views/statistics/goods", "parentId": "1367040819248234496", "sortOrder": 0.0, "permission": "/manager/store*,/manager/member*,/manager/statistics*"}, {"id": "1367052805503713280", "createBy": "admin", "createTime": "2021-03-03 10:01:57", "updateBy": "admin", "updateTime": "2023-06-16 17:41:13", "deleteFlag": false, "title": "订单统计", "name": "order", "path": "order", "level": 1, "frontRoute": "views/statistics/order", "parentId": "1367040819248234496", "sortOrder": 0.0, "permission": "/manager/store*,/manager/member*,/manager/statistics*"}, {"id": "1367053087121866752", "createBy": "admin", "createTime": "2021-03-03 10:01:57", "updateBy": "admin", "updateTime": "2023-06-16 17:41:13", "deleteFlag": false, "title": "流量统计", "name": "preview", "path": "preview", "level": 1, "frontRoute": "views/statistics/preview", "parentId": "1367040819248234496", "sortOrder": 0.0, "permission": "/manager/store*,/manager/member*,/manager/statistics*"}]}, {"id": "1348810750596767744", "createBy": "admin", "createTime": "2021-01-12 09:55:17", "updateBy": "admin", "updateTime": "2021-01-15 09:42:50", "deleteFlag": false, "title": "设置", "name": "mine", "icon": "icon-settings", "path": "mine", "level": 0, "frontRoute": "1", "parentId": "0", "sortOrder": 2.0, "permission": null, "children": [{"id": "1349237207378714624", "createBy": "admin", "createTime": "2021-01-13 14:09:53", "updateBy": "admin", "updateTime": "2023-03-17 20:24:50", "deleteFlag": false, "title": "用户设置", "name": "users-manage", "path": "users-manage", "level": 1, "frontRoute": "views/setting/users-manage/index", "parentId": "1348810750596767744", "sortOrder": 0.0, "permission": "/manager/passport/user*,/manager/permission*"}, {"id": "1357873097859923969", "createBy": "admin", "createTime": "2021-01-13 14:09:53", "updateBy": "admin", "updateTime": "2023-03-17 20:24:50", "deleteFlag": false, "title": "菜单管理", "name": "menu-manage", "path": "menu-manage", "level": 1, "frontRoute": "views/setting/menu-manage/index", "parentId": "1348810750596767744", "sortOrder": 0.0, "permission": "/manager/passport/user*,/manager/permission*"}, {"id": "1349255214977015808", "createBy": "admin", "createTime": "2021-01-13 14:09:53", "updateBy": "admin", "updateTime": "2023-03-17 20:24:50", "deleteFlag": false, "title": "部门管理", "name": "department-manage", "path": "department-manage", "level": 1, "frontRoute": "views/setting/department-manage/index", "parentId": "1348810750596767744", "sortOrder": 0.0, "permission": "/manager/passport/user*,/manager/permission*"}, {"id": "1349255404425338880", "createBy": "admin", "createTime": "2021-01-13 14:09:53", "updateBy": "admin", "updateTime": "2023-03-17 20:24:50", "deleteFlag": false, "title": "角色权限", "name": "role-manage", "path": "role-manage", "level": 1, "frontRoute": "views/setting/role-manage/index", "parentId": "1348810750596767744", "sortOrder": 0.0, "permission": "/manager/passport/user*,/manager/permission*"}, {"id": "1349237129847005184", "createBy": "admin", "createTime": "2021-01-13 14:09:34", "updateBy": "admin", "updateTime": "2023-03-17 20:25:34", "deleteFlag": false, "title": "系统设置", "name": "system-setup", "path": "system-setup", "level": 1, "frontRoute": "views/setting/system-setup/index", "parentId": "1348810750596767744", "sortOrder": 1.0, "permission": "/manager/setting*,/manager/common*"}, {"id": "1349246347597602816", "createBy": "admin", "createTime": "2021-01-13 14:09:53", "updateBy": "admin", "updateTime": "2023-03-17 20:24:50", "deleteFlag": false, "title": "OSS资源", "name": "oss-manage", "path": "oss-manage", "level": 1, "frontRoute": "views/setting/oss-manage/index", "parentId": "1348810750596767744", "sortOrder": 0.0, "permission": "/manager/common/file*"}, {"id": "1349246468775239680", "createBy": "admin", "createTime": "2021-01-13 14:09:53", "updateBy": "admin", "updateTime": "2023-03-17 20:24:50", "deleteFlag": false, "title": "行政地区", "name": "region", "path": "region", "level": 1, "frontRoute": "views/setting/region/index", "parentId": "1348810750596767744", "sortOrder": 0.0, "permission": "/manager/setting/region*"}, {"id": "1349246671158796288", "createBy": "admin", "createTime": "2021-01-13 14:09:53", "updateBy": "admin", "updateTime": "2023-03-17 20:24:50", "deleteFlag": false, "title": "物流公司", "name": "logistics", "path": "logistics", "level": 1, "frontRoute": "views/setting/logistics/logistics", "parentId": "1348810750596767744", "sortOrder": 0.0, "permission": "/manager/other/logistics*"}, {"id": "1384035281702748160", "createBy": "admin", "createTime": "2021-01-13 14:09:53", "updateBy": "admin", "updateTime": "2023-03-17 20:24:50", "deleteFlag": false, "title": "微信消息", "name": "wechat-message", "path": "wechat-message", "level": 1, "frontRoute": "views/setting/wechat-message/index", "parentId": "1348810750596767744", "sortOrder": 0.0, "permission": "/manager/wechat*"}, {"id": "1349246896661356544", "createBy": "admin", "createTime": "2021-01-13 14:09:53", "updateBy": "admin", "updateTime": "2023-03-17 20:24:50", "deleteFlag": false, "title": "信任登录", "name": "trusted-login", "path": "trusted-login", "level": 1, "frontRoute": "views/setting/trusted-login/index", "parentId": "1348810750596767744", "sortOrder": 0.0, "permission": "/manager/setting*"}, {"id": "1349247081504333824", "createBy": "admin", "createTime": "2021-01-13 14:09:53", "updateBy": "admin", "updateTime": "2023-03-17 20:24:50", "deleteFlag": false, "title": "支付设置", "name": "pay-message", "path": "pay-message", "level": 1, "frontRoute": "views/setting/pay-message/index", "parentId": "1348810750596767744", "sortOrder": 0.0, "permission": "/manager/setting*"}, {"id": "1374916594269945856", "createBy": "admin", "createTime": "2021-01-13 14:09:53", "updateBy": "admin", "updateTime": "2023-03-17 20:24:50", "deleteFlag": false, "title": "验证码", "name": "slider", "path": "slider", "level": 1, "frontRoute": "views/setting/slider/index", "parentId": "1348810750596767744", "sortOrder": 0.0, "permission": "/manager/other/verificationSource*"}, {"id": "1349247640584085504", "createBy": "admin", "createTime": "2021-01-13 14:09:53", "updateBy": "admin", "updateTime": "2023-03-17 20:24:50", "deleteFlag": false, "title": "敏感词", "name": "sensitive-words", "path": "sensitive-words", "level": 1, "frontRoute": "views/setting/sensitive-words/index", "parentId": "1348810750596767744", "sortOrder": 0.0, "permission": "/manager/other/sensitiveWords*"}, {"id": "1357584224760102912", "createBy": "admin", "createTime": "2021-01-13 14:09:53", "updateBy": "admin", "updateTime": "2023-03-17 20:24:50", "deleteFlag": false, "title": "APP版本", "name": "app-version", "path": "app-version", "level": 1, "frontRoute": "views/setting/app-version/index", "parentId": "1348810750596767744", "sortOrder": 0.0, "permission": "/manager/other/appVersion*"}]}, {"id": "1348810864748945408", "createBy": "admin", "createTime": "2021-01-12 09:55:45", "updateBy": "admin", "updateTime": "2021-03-15 20:57:12", "deleteFlag": false, "title": "日志", "icon": "icon-file", "name": "log", "path": "log", "level": 0, "frontRoute": "null", "parentId": "0", "sortOrder": 3.0, "permission": "/manager/setting/log*", "children": [{"id": "1349237928434098176", "createBy": "admin", "createTime": "2021-01-14 06:13:03", "updateBy": "admin", "updateTime": "2023-08-28 17:03:09", "deleteFlag": false, "title": "日志管理", "name": "log-manage", "path": "log-manage", "level": 1, "frontRoute": "views/log/log-manage/list", "parentId": "1348810864748945408", "sortOrder": 1.0, "permission": ""}]}]}}