<template>
  <a-card class="general-card" title="秒杀活动" :bordered="false">
    <a-tabs @change="(val)=>{apiParams.promotionStatus = val}" :default-active-key="seckillStatus">
      <a-tab-pane key="NEW" title="未开始"></a-tab-pane>
      <a-tab-pane key="START" title="已开始"></a-tab-pane>
      <a-tab-pane key="END" title="已结束"></a-tab-pane>
      <a-tab-pane key="CLOSE" title="已关闭"></a-tab-pane>
    </a-tabs>
    <searchTable :columns="columnsSearch" time-type="timestamp" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <a-tabs default-active-key="list" @change="clickTabPane">
      <a-tab-pane key="list" title="秒杀活动列表">
        <tablePage ref="tablePageRef" :columns="columnsTable" :methods="sortMethods" :api="getSeckillList"
          :api-params="apiParams" :bordered="true">
          <template #hours="{ record }">
            <!--{{record.split(',').length}}-->
            <a-tag v-for="(item, index) in record.split(',')" :key="index" class="item-hour">{{ item + ':00' }}</a-tag>
          </template>
          <template #btnList="{ data }">
            <a-button v-if="data.promotionStatus === 'CLOSE' || data.promotionStatus === 'NEW'" 
              type="text" status="warning" @click="edit(data)">
              编辑
            </a-button>
            &nbsp;
            <a-button type="text" status="success" v-else @click="manage(data)">
              查看
            </a-button>
            &nbsp;
            <a-button  type="text" v-if="data?.promotionStatus === 'NEW'"  status="success" @click="manage(data)">
              管理
            </a-button>
            &nbsp;
            <a-button v-if="data.promotionStatus == 'START' || data.promotionStatus == 'NEW'" 
              type="text" status="danger" @click="off(data)">
              关闭
            </a-button>
            &nbsp;
            <a-button v-if="data.promotionStatus == 'CLOSE' || data.promotionStatus == 'END'" 
              type="text" status="danger" @click="expire(data)">
              删除
            </a-button>
          </template>
        </tablePage>
      </a-tab-pane>
      <a-tab-pane key="setup" title="秒杀活动设置">
        <setupSeckill v-if="setupFlag"></setupSeckill>
      </a-tab-pane>
    </a-tabs>
  </a-card>
</template>

<script setup lang="ts">
import { delSeckill, getSeckillList, updateSeckillStatus } from '@/api/promotion';
import searchTable from '@/components/search-column/index.vue';
import tablePage from '@/components/table-pages/index.vue';
import useCurrentInstance from '@/hooks/useCurrentInstance';
import { ColumnsDataRule, MethodsRule, SearchRule } from '@/types/global';
import { promotionStatus, promotionStatusSelect } from '@/utils/tools';
import { Message } from '@arco-design/web-vue';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import setupSeckill from './seckill-setup.vue';

//  获取modal
const modal = useCurrentInstance().globalProperties?.$modal;
const setupFlag = ref<boolean>(false);
const tablePageRef = ref<any>();
const router = useRouter();
const seckillStatus = ref<string>('START');
const apiParams = ref<any>({ sort: 'startTime',promotionStatus:seckillStatus.value });
const columnsSearch: Array<SearchRule> = [
  {
    label: '活动名称',
    model: 'promotionName',
    disabled: false,
    input: true,
  },
  {
    label: '活动时间',
    model: 'selectDate',
    disabled: false,
    datePicker: {
      type: 'range',
    },
  },
];

const columnsTable: ColumnsDataRule[] = [
  {
    title: '活动名称',
    width: 300,
    dataIndex: 'promotionName',
  },
  {
    title: '活动开始时间',
    width: 300,
    dataIndex: 'startTime',
  },

  {
    title: '报名截止时间',
    width: 300,
    dataIndex: 'applyEndTime',
  },
  {
    title: '时间场次',
    dataIndex: 'hours',
    slot: true,
    width: 300,
    ellipsis: false,
    slotTemplate: 'hours',
  },
  {
    title: '状态',
    width: 300,
    dataIndex: 'promotionStatus',
    slot: true,
    slotData: {
      badge: promotionStatus,
    },
  },
];

const sortMethods: MethodsRule = {
  title: '操作',
  width: 300,
  methods: [
    {
      slot: true,
      slotTemplate: 'btnList',
    },
  ],
};
// tabs
const clickTabPane = (name: any) => {
  if (name == 'setup') {
    setupFlag.value = true
  } else {
    setupFlag.value = false
  }
}
// 编辑
const edit = (val: any) => {
  router.push({
    name: 'manager-seckill-add', query: {
      id: val.id
    }
  })
}
// 管理/查看
const manage = (val: any) => {
  router.push({
    name: 'seckill-goods', query: {
      id: val.id
    }
  })
}
// 下架
const off = (val: any) => {
  modal.confirm({
    title: '提示',
    content: '您确定要下架该活动吗？',
    alignCenter: false,
    onOk: async () => {
      const res = await updateSeckillStatus(val.id);
      if (res.data.success) {
        Message.success('删除成功！');
        tablePageRef.value.init();
      }
    },
  });
}
// 删除
const expire = (val: any) => {
  modal.confirm({
    title: '提示',
    content: '您确定要作废该活动吗？',
    alignCenter: false,
    onOk: async () => {
      const res = await delSeckill(val.id);
      if (res.data.success) {
        Message.success('作废成功');
        tablePageRef.value.init();
      }
    },
  });

}
</script>

<style lang="less" scoped>
.item-hour {
  margin: 0 2px 2px 0;
}
</style>
