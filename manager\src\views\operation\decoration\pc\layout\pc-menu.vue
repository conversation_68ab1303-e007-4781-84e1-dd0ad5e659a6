<template>
  <a-menu class="menu" :style="{ width: '80px', height: '100%' }" :selected-keys="[checkedKey]" :default-open-keys="['widget']"
    :default-selected-keys="['widget']" theme="light">
    <a-menu-item @click="handleClickMenu('widget')" key="widget" text-center>组件</a-menu-item>
    <a-menu-item @click="handleClickMenu('design')" key="design" text-center>装修</a-menu-item>
  </a-menu>
</template>

<script setup lang="ts">
import { watch, ref } from 'vue'
import { useDesign } from '@/store'
const userDesign = useDesign();
const checkedKey = ref('widget')
const emit = defineEmits(['clickMenu'])
function handleClickMenu(key: string) {
  emit('clickMenu', key)
  checkedKey.value = key
  if (key !== 'design') {
    userDesign.setCurrentPcDesign('')
  }
}
watch(() => [userDesign.currentDesignOfPc, userDesign.pc], ([designPc, indexPc]) => {
  if(designPc || Object.keys(indexPc).length > 0){
    if (designPc) userDesign.setAppActiveIndex(0.1)
    emit('clickMenu', 'design')
    checkedKey.value = 'design'
  }


}, { deep: true, immediate: true })

// watch(() => userDesign.currentDesignOfPc, (val) => {

// }, { deep: true });


// watch(() => userDesign.indexOfPc, (val) => {
//   console.log(userDesign.indexOfPc)
//   emit('clickMenu', 'design')
//   checkedKey.value = 'design'

// });
</script>

<style  scoped>
.menu{
  border-left: 1px solid #ededed;
}
</style>
