<template>
  <a-card title="用户设置" class="general-card" :bordered="false">
    <searchTable
      :columns="columnsSearch"
      @reset="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
      @search="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
    >
    </searchTable>
    <a-row style="margin-bottom: 16px">
      <a-col :span="16">
        <a-space>
          <a-button type="primary" @click="handleAdd"> 添加管理员 </a-button>
          <a-button @click="delAll"> 批量删除 </a-button>
          <a-button @click="resetPass"> 重置密码 </a-button>
        </a-space>
      </a-col>
    </a-row>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getUserListData"
      :api-params="apiParams"
      :checkbox="true"
      @selectTableChange="selectTableChange"
      :bordered="true"
    >
      <template #avatar="{ data }">
        <a-avatar>
          <img alt="avatar" :src="data.avatar" />
        </a-avatar>
      </template>
      <template #status="{ data }">
        <a-badge color="green" v-if="data.status" text="启用"></a-badge>
        <a-badge color="red" v-else text="禁用"></a-badge>
      </template>
      <template #editor="{ data }">
        <a-button type="text" status="warning" @click="handleEdit(data)">
          编辑
        </a-button>
      </template>
      <template #forbidden="{ data }">
        <a-button
          v-if="data.status"
          @click="handleForbidden(data)"
          type="text"
          status="danger"
          >禁用
        </a-button>
        <a-button
          v-else
          @click="handleEnable(data)"
          type="text"
          status="success"
        >
          启用
        </a-button>
      </template>
      <template #remove="{ data }">
        <a-button @click="handleRemove(data)" type="text" status="danger">
          删除
        </a-button>
      </template>
    </tablePage>
    <!-- 添加/编辑modal -->
    <a-modal
      v-model:visible="memberData.enableAddModal"
      :align-center="false"
      ok-text="提交"
      :mask-closable="false"
    >
      <template #title> {{ modalTitle }}</template>
      <a-form ref="formRef" :model="memberData.form">
        <a-form-item
          field="username"
          label="用户名"
          :rules="[REQUIRED, VARCHAR20]"
          :validate-trigger="['change']"
        >
          <a-input v-model="memberData.form.username" />
        </a-form-item>
        <a-form-item
          field="nickName"
          label="昵称"
          :rules="[REQUIRED, VARCHAR20]"
          :validate-trigger="['change']"
        >
          <a-input v-model="memberData.form.nickName" />
        </a-form-item>
        <a-form-item
          v-if="modalType == 0"
          field="password"
          label="密码"
          :rules="[REQUIRED]"
          :validate-trigger="['change']"
        >
          <a-input-password v-model="memberData.form.password" />
        </a-form-item>
        <a-form-item
          field="email"
          label="邮箱"
          :rules="[EMAIL, REQUIRED]"
          :validate-trigger="['change']"
        >
          <a-input v-model="memberData.form.email" />
        </a-form-item>
        <a-form-item
          field="mobile"
          label="手机号码"
          :rules="[MOBILE, REQUIRED]"
          :validate-trigger="['change']"
          
          validate-phone
        >
          <a-input :max-length="11" v-model="memberData.form.mobile">
            <template #prepend> +86 </template>
          </a-input>
        </a-form-item>
        <a-form-item
          field="avatar"
          label="头像"
          :rules="[REQUIRED]"
          :validate-trigger="['change']"
          validate-phone
        >
          <a-input v-model="memberData.form.avatar" />
          <a-tooltip>
            <a-button><icon-eye /></a-button>
            <template #content>
              <div>
                <img
                  :src="memberData.form.avatar"
                  alt="该资源不存在"
                  style="width: 100%; margin: 0 auto"
                />
                <a
                  style="margin-top: 5px; text-align: right; cursor: pointer"
                  @click="viewImage = true"
                  >查看大图</a
                >
              </div>
            </template>
          </a-tooltip>
          <a-button type="primary" @click="handlerUpload">上传图片</a-button>
        </a-form-item>
        <a-form-item field="departmentTitle" label="所属部门">
          <a-tree-select
            :field-names="{ children: 'children', title: 'title', key: 'id' }"
            :allow-search="true"
            :allow-clear="true"
            :data="dataDep"
            placeholder="选择所属部门"
            :filter-tree-node="filterTreeNode"
            v-model:model-value="memberData.form.departmentTitle"
            :label-in-value="true"
            @change="handleCheckChange"
          ></a-tree-select>
        </a-form-item>
        <a-form-item field="roles" label="选择角色">
          <a-select
            placeholder="选择角色"
            multiple
            v-model="memberData.form.roles"
            @change="handleRolesChange"
          >
            <a-option
              v-for="(item, index) in roleList"
              :key="index"
              :value="item.id"
              >{{ item.name }}</a-option
            >
          </a-select>
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button @click="memberData.enableAddModal = false">取消</a-button>
        <a-button type="primary" @click="verifyInsertUser">提交</a-button>
      </template>
    </a-modal>
    <a-modal
      v-model:visible="showOssManager"
      :width="1100"
      @ok="handleOss"
      title="oss资源管理"
      @cancel="showOssManager = false"
    >
      <ossManages @selected="changOssImage" :isSingle="true"></ossManages>
    </a-modal>

    <a-modal
      v-model:visible="viewImage"
      :width="1100"
      @ok="handleOssOk"
      @cancel="handleOssCancel"
    >
      <img
        :src="memberData.form.avatar"
        alt="该资源不存在"
        style="width: 100%; margin: 0 auto; display: block"
      />
      <template #footer>
        <div style="text-align: right">
          <a-button type="text" @click="viewImage = false">关闭</a-button>
        </div>
      </template>
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">
import {
  addUser,
  deleteUser,
  editOtherUser,
  enableUser,
  getRoleList,
  getUserListData,
  initDepartment,
  resetPassword,
} from '@/api/setting';
import ossManages from '@/components/oss-manage/index.vue';
import searchTable from '@/components/search-column/index.vue';
import tablePage from '@/components/table-pages/index.vue';
import useCurrentInstance from '@/hooks/useCurrentInstance';
import { MethodsRule, SearchRule } from '@/types/global';
import { md5 } from '@/utils/md5';
import { MOBILE, REQUIRED, VARCHAR20, EMAIL } from '@/utils/validator';
import { Message } from '@arco-design/web-vue';
import { FormInstance } from '@arco-design/web-vue/es/form';
import { onMounted, reactive, ref } from 'vue';

const tablePageRef = ref<any>('');
const viewImage = ref<boolean>(false);
const showOssManager = ref<boolean>(false); // oss弹框
const selectedSku = ref<any>(); // 选择的sku
const roleList = ref<any>([]); // 角色列表
const dataDep = ref<any>([]); // 部门列表
const modalTitle = ref<string>(''); // 弹框标题
const modalType = ref<number>(0); // 新增编辑标识
const formRef = ref<FormInstance>();
const selectList = ref([]); // 接收子组件传过来的值
const ids = ref<string>(''); // 多选行id

interface formInterface {
  enableAddModal: boolean;
  formLoading: boolean;
  fid: string | number;
  form: {
    mobile: any;
    password: number | string;
    username: string;
    email: string;
    roles: [];
    departmentId: string;
    departmentTitle: string;
    avatar: string;
    nickName: string;
    [key:string]:any
  };
}
//  获取modal
const modal = useCurrentInstance().globalProperties?.$modal;
const columnsSearch: Array<SearchRule> = [
  {
    label: '用户名',
    model: 'username',
    disabled: false,
    input: true,
  },

  {
    label: '联系方式',
    model: 'mobile',
    disabled: false,
    input: true,
  },
];

const columnsTable: any = [
  {
    title: '用户名',
    dataIndex: 'username',
  },
  {
    title: '头像',
    dataIndex: 'avatar',
    slot: true,
    slotTemplate: 'avatar',
  },

  {
    title: '手机',
    dataIndex: 'mobile',
    sortable: {
      sortDirections: ['ascend', 'descend'],
    },
  },
  {
    title: '邮箱',
    dataIndex: 'email',
  },
  {
    title: '状态',
    dataIndex: 'status',
    slot: true,
    slotTemplate: 'status',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
];

const sortMethods: MethodsRule = {
  title: '操作',
  width: 300,
  fixed: 'right',
  methods: [
    {
      title: '编辑',
      callback: 'editor',
      slot: true,
      slotTemplate: 'editor',
    },
    {
      title: '禁用',
      callback: 'forbidden',
      slot: true,
      slotTemplate: 'forbidden',
    },
    {
      title: '删除',
      callback: 'remove',
      slot: true,
      slotTemplate: 'remove',
    },
  ],
};
// 数据集
const memberData = reactive<formInterface>({
  enableAddModal: false,
  formLoading: false,
  fid: '', // 当前form的ids
  form: {
    mobile: '',
    username: '',
    password: '',
    email: '',
    roles: [],
    avatar: '',
    nickName: '',
    departmentTitle: '',
    departmentId: '',
  
  }, // 表单提交数据
});
// 选择的行
const selectTableChange = (val: any) => {
  selectList.value = val;
};
// 确认提交
const submitUser = () => {
  if (modalType.value == 0) {
    // 添加用户 避免编辑后传入id
  
    const params = JSON.parse(JSON.stringify(memberData.form));
    params.departmentId = params.departmentId || 0;
    delete params.id;
    delete params.status;
    if (params.password == '' || params.password == undefined) {
      Message.error('密码不能为空');
    }
    if (params.password.length < 6) {
      Message.error('密码长度不得少于6位');
      return false
    }
    params.password = md5(params.password);
    addUser(params).then((res) => {
      if (res.data.success) {
        Message.success('操作成功');
        tablePageRef.value.init();
        memberData.enableAddModal = false;
      }
    });
  } else {
    editOtherUser(memberData.form).then((res) => {
      if (res.data.success) {
        Message.success('操作成功');
        tablePageRef.value.init();
        memberData.enableAddModal = false;
      }
    });
  }
};

// 表单提交前验证当前表单
async function verifyInsertUser() {
  const auth = await formRef.value?.validate();
  if (!auth) {
    submitUser();
  }
}

// 点击添加
function handleAdd() {
  modalType.value = 0;
  memberData.enableAddModal = true;
  modalTitle.value = '添加管理员';
  memberData.fid = '';
  Object.keys(memberData.form).forEach((key) => {
    memberData.form[key] = '';
  });
}
// 删除用户
const handleRemove = (val: any) => {
  modal.confirm({
    title: '确认删除',
    content: '您确认要删除此用户',
    alignCenter: false,
    onOk: async () => {
      const res = await deleteUser(val.id);
      if (res.data.success) {
        Message.success('删除成功');
        tablePageRef.value.init();
      }
    },
  });
};
// 编辑回显
function handleEdit(val: any) {
  modalType.value = 1;
  if (val) {
    Object.keys(val).forEach((key) => {
      // eslint-disable-next-line no-unused-expressions,no-prototype-builtins
      memberData.form.hasOwnProperty(key)
        ? (memberData.form[key] = val[key])
        : '';
    });
    modalTitle.value = '编辑用户';
    memberData.form.id = val.id;
  }
  const selectRolesId = [] as any;
  if (memberData.form.roles) {
    memberData.form.roles.forEach((item:any) => {
      selectRolesId.push(item.id);
    });
  }
  memberData.form.roles = selectRolesId;
  memberData.enableAddModal = true;
}

// 回调禁用
function handleForbidden(data:any) {
  const params = {
    status: false,
  };
  modal.confirm({
    title: '确认禁用',
    content: '您确认要禁用此用户',
    alignCenter: false,
    onOk: async () => {
      const res = await enableUser(data.id, params);
      if (res.data.success) {
        Message.success('禁用成功');
        tablePageRef.value.init();
      }
    },
  });
}
// 回调启用
function handleEnable(data:any) {
  const params = {
    status: true,
  };
  modal.confirm({
    title: '确认启用',
    content: '您确认要启用此用户',
    alignCenter: false,
    onOk: async () => {
      const res = await enableUser(data.id, params);
      if (res.data.success) {
        Message.success('操作成功');
        tablePageRef.value.init();
      }
    },
  });
}
const apiParams = ref({
  disabled: 'OPEN',
});
// 重置密码
const resetPass = () => {
  if (selectList.value.length <= 0) {
    Message.error('请选中数据后重试');
    return;
  }
  ids.value = '';
  selectList.value.forEach((item: any) => {
    ids.value += `${item.id},`;
  });
  const joinId = ids.value.substring(0, ids.value.length - 1);
  modal.confirm({
    title: '确认重置',
    content: `您确认要重置所选的${selectList.value.length}条用户数据密码为【123456】?`,
    alignCenter: false,
    onOk: async () => {
      const res = await resetPassword(joinId);
      if (res.data.success) {
        Message.success('确认成功');
        tablePageRef.value.init();
      }
    },
  });
};
// 部门的选择事件
const handleCheckChange = (value: any) => {
  memberData.form.departmentId = value.value;
  memberData.form.departmentTitle = value.label;
};
// 批量删除
const delAll = () => {
  if (selectList.value.length <= 0) {
    Message.error('您还未选择要删除的数据');
    return;
  }
  ids.value = '';
  selectList.value.forEach((item: any) => {
    ids.value += `${item.id},`;
  });
  const joinId = ids.value.substring(0, ids.value.length - 1);
  modal.confirm({
    title: '确认删除',
    content: `您确认要删除所选的${selectList.value.length}条数据?`,
    alignCenter: false,
    onOk: async () => {
      const res = await deleteUser(joinId);
      if (res.data.success) {
        Message.success('删除成功');
        tablePageRef.value.init();
      }
    },
  });
};
// 获取角色列表
const getRoleLists = () => {
  getRoleList({ pageNumber: 1, pageSize: 10000 }).then((res) => {
    if (res.data.success) {
      roleList.value = res.data.result.records;
    }
  });
};
// 获取部门数据
const initDepartmentData = () => {
  initDepartment().then((res) => {
    if (res.data.success) {
      dataDep.value = res.data.result;
    }
  });
};

const filterTreeNode = (searchValue:any, nodeData:any) => {
  return nodeData.title.toLowerCase().indexOf(searchValue.toLowerCase()) > -1;
};

// 图片 url
// const changOssImage = (e: any) => {
//   memberData.form.avatar = e;
// };
// 关闭oss弹框
const handleOssOk = (e: any) => {
  showOssManager.value = false;
};
const handleOssCancel = (e: any) => {
  showOssManager.value = false;
};
// 上传图片
const handlerUpload = () => {
  showOssManager.value = true;
};
// oss资源确定
const handleOss = () => {
  showOssManager.value = false;
  memberData.form.avatar = selectedSku.value[selectedSku.value.length - 1].url;
};
// oss资源改变
const changOssImage = (val:any) => {
  selectedSku.value = [];
  val.forEach((item:any) => {
    selectedSku.value.push({ url: item.url });
  });
};

const handleRolesChange = (value: any) => {
  memberData.form.roles = value.filter((i:any) => i.length > 0);
};
// 初始化
onMounted(() => {
  getRoleLists();
  initDepartmentData();
});
</script>

<style lang="less" scoped>
.face {
  width: 60px;
  height: 60px;
  border-radius: 50%;
}

.edit {
  margin-left: 10px;
}
</style>
