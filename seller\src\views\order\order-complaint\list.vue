<template>
  <a-card class="general-card" title="投诉管理" :bordered="false">
    <a-tabs @change="(val)=>{apiParams.status = val}" :default-active-key="complaintStatusVar">
      <a-tab-pane key="" title="全部"></a-tab-pane>
      <a-tab-pane key="NEW" title="新投诉"></a-tab-pane>
      <a-tab-pane key="CANCEL" title="已撤销"></a-tab-pane>
      <a-tab-pane key="WAIT_APPEAL" title="待申诉"></a-tab-pane>
      <a-tab-pane key="COMMUNICATION" title="对话中"></a-tab-pane>
      <a-tab-pane key="WAIT_ARBITRATION" title="等待仲裁"></a-tab-pane>
      <a-tab-pane key="COMPLETE" title="已完成"></a-tab-pane>
    </a-tabs>
    <searchTable
      :columns="columnsSearch"
      @reset="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
      @search="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
    ></searchTable>

    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getComplainPage"
      :api-params="apiParams"
      :bordered="true"
    >
      <template #btnList="{ data }">
        <a-space>
          <a-button
            v-if="data?.complainStatus == 'COMPLETE'"
            type="text"
            status="success"
            @click="details(data)"
          >
            详情</a-button
          >
          <a-button v-else type="text" status="danger" @click="handle(data)">{{
            data.complainStatus === 'CANCEL' ? '详情' : '处理'
          }}</a-button>
        </a-space>
      </template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { getComplainPage } from '@/api/order';
  import { complaintStatus } from '@/utils/tools';
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const tablePageRef = ref('');
  const complaintStatusVar = ref<string>('');
  const apiParams = ref<any>({status:complaintStatusVar.value});

  const columnsSearch: Array<SearchRule> = [
    {
      label: '会员名称',
      model: 'memberName',
      disabled: false,
      input: true,
    },
    {
      label: '订单号',
      model: 'orderSn',
      disabled: false,
      input: true,
    },
  ];

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '会员名称',
      dataIndex: 'memberName',
    },
    {
      title: '订单编号',
      dataIndex: 'orderSn',
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
    },
    {
      title: '投诉主题',
      dataIndex: 'complainTopic',
    },
    {
      title: '投诉时间',
      dataIndex: 'createTime',
    },
    {
      title: '投诉状态',
      dataIndex: 'complainStatus',
      slot: true,
      slotData: {
        badge: complaintStatus,
      },
    },
  ];

  const sortMethods: MethodsRule = {
    title: '操作',
    width: 200,
    methods: [
      {
        slot: true,
        slotTemplate: 'btnList',
      },
    ],
  };
  // 详情
  const details = (data: any) => {
    router.push({
      name: 'order-complaint-detail',
      query: {
        id: data.id,
      },
    });
  };
  // 处理
  const handle = (data: any) => {
    router.push({
      name: 'order-complaint-detail',
      query: {
        id: data.id,
      },
    });
  };
</script>
