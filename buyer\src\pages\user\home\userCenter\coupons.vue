<template>
  <div>
    <Card _Title="优惠券列表" :_Size="16" :_Tabs="changeWay" @_Change="changeType"></Card>

    <div class="coupon-list">
      <div v-for="(item, index) in couponlist" :key="index" class="coupon-item" v-if="couponlist && couponlist.length">
        <div class="c-left">
          <div>
            <span v-if="item.couponType === 'PRICE'" class="fontsize-14 price-color">￥<span class="fontsize-22">{{ unitPrice(item.price) }}</span></span>
            <span v-if="item.couponType === 'DISCOUNT'" class="fontsize-14 price-color"><span class="fontsize-22">{{ item.discount }}</span>折</span>
            <span class="describe">满{{ item.consumeThreshold }}元可用</span>
          </div>
          <a-tooltip :content="useScope(item.scopeType, item.storeName)" position="bottom">
            <p class="ellipsis ellipsis-1 hover-pointer">使用范围：{{ useScope(item.scopeType, item.storeName) }}</p>
          </a-tooltip>
          <p class="light-text-color">有效期：{{ item.endTime }}</p>
        </div>
        <b></b>
        <div class="c-right"  :class="{ 'canot-use': apiParams.memberCouponStatus !== 'NEW' }" @click="immediateUse(item)">
          <span v-if="apiParams.memberCouponStatus==='NEW'">立即使用</span>
          <span v-if="apiParams.memberCouponStatus==='USED'">已使用</span>
          <span v-if="apiParams.memberCouponStatus==='EXPIRE'">已过期</span>
        </div>
        <i class="circle-top"></i>
        <i class="circle-bottom"></i>
      </div>
      <Empty v-else />
    </div>

    <div class="paginationBox">
      <a-pagination :total="total" :current="apiParams.pageNumber" :page-size="apiParams.pageSize" show-page-size
                    @change="(number) => {apiParams.pageNumber = number;}" @page-size-change="(number) => {apiParams.pageSize = number; apiParams.pageNumber = 1;}" >
      </a-pagination>
    </div>

  </div>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router';
  import { ref, onMounted, watch } from 'vue';
  import { unitPrice } from '@/utils/filters';
  import { memberCouponList } from "@/api/member";

  const router = useRouter();
  const loading = ref(false);
  // 优惠券状态
  const changeWay = ref(['未使用', '已使用', '已过期']);
  const statusList = ref(["NEW", "USED", "EXPIRE"]);
  // 请求参数
  const apiParams = ref({
    pageNumber: 1,
    pageSize: 10,
    memberCouponStatus: "NEW",
  });
  const total = ref(0); // 优惠券总数
  const couponlist = ref<Array<any>>([]); // 优惠券列表

  // 获取优惠券列表
  const getList = () => {
    // 获取优惠券列表
    loading.value = true;
    memberCouponList(apiParams.value).then((res) => {
      loading.value = false;
      if (res.data.success) {
        couponlist.value = res.data.result.records;
        total.value = res.data.result.total;
      }
    });
  };

  const useScope = (type: any, storeName: any) => {
    // 根据字段返回 优惠券适用范围
    let shop = "平台";
    let goods = "全部商品";
    if (storeName !== "platform") shop = storeName;
    switch (type) {
      case "ALL":
        goods = "全部商品";
        break;
      case "PORTION_GOODS":
        goods = "部分商品";
        break;
      case "PORTION_GOODS_CATEGORY":
        goods = "部分分类商品";
        break;
    }
    return `${shop}${goods}可用`;
  };
  // 切换订单状态
  const changeType = (index: any) => {
    apiParams.value.memberCouponStatus = statusList.value[index];
    apiParams.value.pageNumber = 1;
  };
  // 立即使用
  const immediateUse = (item: any) => {
    // 根据使用条件跳转商品列表页面
    if (apiParams.value.memberCouponStatus !== "NEW") return;
    router.push({path: "/goodsList", query: { promotionsId: item.couponId, promotionType: "COUPON" },});
  };

  onMounted(() => {
    getList();
  });

  watch(() => [apiParams.value],
    (val) => {
      getList();
    }, { deep: true }
  );
</script>

<style scoped lang="less">
  .coupon-list {
    display: flex;
    flex-wrap: wrap;
    .coupon-item {
      display: flex;
      width: 400px;
      height: 120px;
      margin: 10px 20px;
      position: relative;
      .c-left {
        width: 360px;
        height: 120px;
        border: 1px solid #eeeeee;
        padding: 20px 15px;
        box-sizing: border-box;
        .describe {
          background-color: #fff4ec;
          color: @theme_color;
          padding: 0 5px;
          margin-left: 10px;
          font-size: 13px;
        }
      }
      b {
        position: absolute;
        z-index: 2;
        top: 0;
        display: block;
        width: 3px;
        height: 100%;
        background: url("../../../../assets/images/small-circle.png") top left repeat-y;
        right: 38px;
      }
      .c-right {
        width: 40px;
        height: 120px;
        writing-mode: vertical-rl; /* 从右到左 */
        text-orientation: upright; /* 保持正常显示 */
        background-color: @theme_color;
        color: @light_white_background_color;
        text-align: center;
        line-height: 40px;
        letter-spacing: 5px;
        cursor: pointer;
      }
      .canot-use {
        color: #999;
        background-color: #eee;
        cursor: inherit;
      }
      i {
        position: absolute;
        width: 20px;
        height: 20px;
        right: 30px;
        border: 1px solid #eee;
        background-color: #fff;
        border-radius: 20px;
        &:after {
          content: "";
          position: absolute;
          width: 25px;
          height: 20px;
          left: -2px;
          background-color: #fff;
        }
      }
      i.circle-top {
        top: -10px;
        &::after {
          top: -11px;
        }
      }
      i.circle-bottom {
        bottom: -10px;
        &::after {
          bottom: -11px;
        }
      }
    }
  }
</style>
