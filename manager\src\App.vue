<template>
  <div ref="el">
    <a-config-provider>
      <DndProvider :backend="HTML5Backend">
        <router-view v-if="isRouterActive" />
        <global-setting />
      </DndProvider>
    </a-config-provider>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';
import { watch, ref, provide, nextTick, onMounted } from 'vue';
import GlobalSetting from '@/components/global-setting/index.vue';
import { useAppStore } from '@/store/index';
import { DndProvider } from 'vue3-dnd'
import { useResizeObserver } from '@vueuse/core'
import { HTML5Backend } from 'react-dnd-html5-backend'
const appStore = useAppStore();
const el = ref(null);
// const routeList=store.$state.r

const router = useRouter();
watch(
  () => router.currentRoute.value.path,
  (newValue) => {

  },
  { immediate: true }
);

// 获取当前窗口的宽高
// @ts-ignore
useResizeObserver(el, (entries) => {
  const entry = entries[0]
  const { width, height } = entry.contentRect
  appStore.updateWindowSizeObserver({ width, height })
})

// 刷新页面
const isRouterActive = ref(true);
provide('reload', () => {
  isRouterActive.value = false;
  nextTick(() => {
    isRouterActive.value = true
  })
})


onMounted(() => {

});

</script>
<style scoped>
li {
  list-style: none;
}
</style>
