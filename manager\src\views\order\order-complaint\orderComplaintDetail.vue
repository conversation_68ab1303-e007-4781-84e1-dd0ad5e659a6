<template>
  <div class="search">
    <a-card
      class="general-card"
      :style="{ paddingTop: '5px' }"
      :bordered="false"
    >
      <div class="main-content">
        <div class="div-flow-left">
          <div class="div-form-default">
            <h4>投诉信息</h4>
            <dl>
              <dt>投诉商品</dt>
              <dd>{{ complaintInfo.goodsName }}</dd>
            </dl>
            <dl>
              <dt>投诉状态</dt>
              <dd v-if="complaintInfo.complainStatus == 'NEW'">新投诉</dd>
              <dd v-if="complaintInfo.complainStatus == 'CANCEL'">已撤销</dd>
              <dd v-if="complaintInfo.complainStatus == 'WAIT_APPEAL'"
                >待申诉</dd
              >
              <dd v-if="complaintInfo.complainStatus == 'COMMUNICATION'"
                >对话中</dd
              >
              <dd v-if="complaintInfo.complainStatus == 'WAIT_ARBITRATION'"
                >等待仲裁</dd
              >
              <dd v-if="complaintInfo.complainStatus == 'COMPLETE'">已完成</dd>
            </dl>
            <dl>
              <dt>投诉时间</dt>
              <dd>{{ complaintInfo.createTime }}</dd>
            </dl>
            <dl>
              <dt>投诉时间</dt>
              <dd>{{ complaintInfo.createTime }}</dd>
            </dl>
            <dl>
              <dt>投诉主题</dt>
              <dd>{{ complaintInfo.complainTopic }}</dd>
            </dl>
            <dl>
              <dt>投诉内容</dt>
              <dd>{{ complaintInfo.content }}</dd>
            </dl>
            <dl>
              <dt>投诉凭证</dt>
              <dd v-if="images &&images.length">
                <div
                  v-for="(item, index) in images"
                  :key="index"
                  class="div-img"
                >
                  <img class="complain-img" :src="item" />
                </div>
              </dd>
              <dd v-else> 暂无投诉凭证 </dd>
            </dl>
          </div>
         
          <div v-if="complaintInfo.appealContent" class="div-form-default">
            <h4>商家申诉信息</h4>
            <dl>
              <dt>申诉时间</dt>
              <dd>{{ complaintInfo.appealTime }}</dd>
            </dl>
            <dl>
              <dt>申诉内容</dt>
              <dd>{{ complaintInfo.appealContent }}</dd>
            </dl>
            <dl>
              <dt>申诉凭证</dt>
             
              <dd v-if="complaintInfo.appealImagesList && complaintInfo?.appealImagesList.length == 0">
                暂无申诉凭证
              </dd>
              <dd v-else>
                <div
                  v-for="(item, index) in complaintInfo.appealImagesList"
                  :key="index"
                  class="div-img"
                >
                  <img class="complain-img" :src="item" />
                </div>
              </dd>
              <dd v-else>暂无申诉凭证</dd>
            </dl>
          </div>
          <div class="div-form-default">
            <h4>对话详情</h4>
            <dl>
              <dt>对话记录</dt>
              <dd>
                <div class="div-content">
                  <p
                    v-for="(
                      item, index
                    ) in complaintInfo.orderComplaintCommunications"
                    :key="index"
                  >
                    <span v-if="item.owner == 'STORE'"
                      >商家[{{ item.createTime }}]</span
                    >
                    <span v-if="item.owner == 'BUYER'"
                      >买家[{{ item.createTime }}]</span
                    >
                    <span v-if="item.owner == 'PLATFORM'"
                      >平台[{{ item.createTime }}]</span
                    >
                    {{ item.content }}
                  </p>
                </div>
              </dd>
            </dl>
            <dl v-if="complaintInfo.complainStatus != 'COMPLETE'">
              <dt>发送对话</dt>
              <dd>
                <div style="display: flex; align-items: center;">
                  <a-textarea v-model="params.content" allow-clear :max-length="200"/>
                  <a-button type="primary" :loading="submitLoading" :disabled="complaintInfo.complainStatus == 'CANCEL'" style="margin-left: 5px" @click="handleSubmit">回复</a-button>
                </div>
              </dd>
            </dl>
          </div>
          <div
            v-if="complaintInfo.complainStatus == 'COMPLETE'"
            class="div-form-default"
          >
            <h4>仲裁结果</h4>
            <dl>
              <dt>仲裁意见</dt>
              <dd>
                {{ complaintInfo.arbitrationResult }}
              </dd>
            </dl>
          </div>
          <div
            v-if="complaintInfo.complainStatus != 'COMPLETE'"
            class="div-form-default"
          >
            <h4>平台仲裁</h4>
            <dl v-if="arbitrationResultShow == true">
              <dt>仲裁</dt>
              <dd>
                <a-textarea
                  v-model="arbitrationParams.arbitrationResult"
                  allow-clear
                  :max-length="200"
                />
              </dd>
            </dl>
            <dl>
              <dd
                style="
                  text-align: right;
                  display: flex;
                  justify-content: space-between;
                "
              >
                <a-button
                  v-if="arbitrationResultShow == false"
                  type="outline"
                  status="danger"
                  :disabled="complaintInfo.complainStatus == 'CANCEL'"
                  :loading="submitLoading"
                  @click="arbitrationHandle"
                >
                  直接仲裁结束投诉流程
                </a-button>
                <a-button
                  v-if="complaintInfo.complainStatus == 'NEW'"
                  :loading="submitLoading"
                  :disabled="complaintInfo.complainStatus == 'CANCEL'"
                  type="outline"
                  status="danger"
                  @click="handleStoreComplaint"
                >
                  交由商家申诉
                </a-button>
                <a-button
                  v-if="arbitrationResultShow == true"
                  type="outline"
                  status="danger"
                  :disabled="complaintInfo.complainStatus == 'CANCEL'"
                  :loading="submitLoading"
                  @click="arbitrationHandleSubmit"
                >
                  提交仲裁
                </a-button>
              </dd>
            </dl>
          </div>
          <a-button :loading="submitLoading" style="margin-left: 5px; margin-top: 20px;" @click="returnDataList">返回列表</a-button>
        </div>
        <div class="div-flow-center"> </div>
        <div class="div-flow-right">
          <div class="div-form-default">
            <h4>相关商品交易信息</h4>
            <dl>
              <dt>
                <img :src="complaintInfo.goodsImage" width="60" height="60" />
              </dt>
              <dd>
                <a
                  class="Hyperlink"
                  @click="
                    store.viewGoodsDetail(complaintInfo.goodsId, complaintInfo.skuId)
                  "
                  >{{ complaintInfo.goodsName }}</a
                ><br />
                <span style="font-size: 12px;">￥{{ complaintInfo.goodsPrice }} * {{ complaintInfo.num }}(数量)</span>
              </dd>
            </dl>
          </div>
          <div class="div-form-default">
            <h4>订单相关信息</h4>
            <dl>
              <dt> 订单编号 </dt>
              <dd>
                {{ complaintInfo.orderSn }}
              </dd>
            </dl>
            <dl>
              <dt> 下单时间 </dt>
              <dd>
                {{ complaintInfo.createTime }}
              </dd>
            </dl>
            <dl>
              <dt> 订单金额 </dt>
              <dd>
                {{ complaintInfo.orderPrice }}
              </dd>
            </dl>
          </div>
          <div class="div-form-default">
            <h4>收件人信息</h4>
            <dl>
              <dt> 收货人 </dt>
              <dd>
                {{ complaintInfo.consigneeName }}
              </dd>
            </dl>
            <dl>
              <dt> 收货地址 </dt>
              <dd>
                {{ complaintInfo.consigneeAddressPath }}
              </dd>
            </dl>
            <dl>
              <dt> 收货人手机 </dt>
              <dd>
                {{ complaintInfo.consigneeMobile }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import {
addOrderCommunication,
getOrderComplainDetail,
orderComplete,
storeComplain,
} from '@/api/order';
import { usePathJumpStore } from '@/store/index';
import { Message } from '@arco-design/web-vue';
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

  const store = usePathJumpStore();

  const route = useRoute();
  const router = useRouter();
  const complaintInfo = ref({
    goodsName: '',
    complainStatus: '',
    createTime: '',
    complainTopic: '',
    content: '',
    appealContent: '',
    appealTime: '',
    appealImagesList: [],
    orderComplaintCommunications: [],
    id: '',
  }) as any; // 投诉信息
  const params = ref({
    // 管理端回复内容
    content: '',
    complainId: '',
  }) as any;
  const images = ref([]); // 会员申诉图片
  const appealImages = ref([]); // 商家申诉的图片
  const submitLoading = ref<boolean>(false); // 添加或编辑提交状态
  const arbitrationResultShow = ref<boolean>(false); // 是否显示仲裁框
  const arbitrationParams = ref({
    // 仲裁结果
    arbitrationResult: '',
  });
  // 初始化
  const init = async () => {
    const res = await getOrderComplainDetail(route.query.id);
    if (res.data.code == 200) {
      complaintInfo.value = res.data.result;
      images.value = (res.data.result.images || '').split(',');
      appealImages.value = (res.data.result.appealImages || '').split(',');
    }
  };
  onMounted(async () => {
    init();
  });
  // 回复
  const handleSubmit = () => {
    if (params.value.content == '') {
      Message.error('请填写对话内容');
      return;
    }
    params.value.complainId = route.query.id;
    addOrderCommunication(params.value).then((res) => {
      submitLoading.value = false;
      if (res.data.code == 200) {
        Message.success('对话成功');
        params.value.content = '';
        init();
      }
    });
  };
  // 仲裁
  const arbitrationHandle = () => {
    arbitrationResultShow.value = true;
  };
  // 仲裁结果提交
  const arbitrationHandleSubmit = () => {
    if (arbitrationParams.value.arbitrationResult == '') {
      Message.error('请填写仲裁内容');
      return;
    }
    orderComplete(route.query.id, arbitrationParams.value).then((res: any) => {
      submitLoading.value = false;
      if (res.data.code == 200) {
        Message.success('仲裁成功');
        arbitrationParams.value.arbitrationResult = '';
        init();
      }
    });
    arbitrationResultShow.value = false;
  };
  // 交给商家申诉
  const handleStoreComplaint = () => {
    const params = {
      complainStatus: 'WAIT_APPEAL',
      complainId: complaintInfo.value.id,
    };
    storeComplain(params).then((res: any) => {
      if (res.data.code == 200) {
        Message.success('操作成功');
        init();
      }
    });
  };
  // 返回列表
  const returnDataList = () => {
    router.push({name: 'order-complaint'});
  };
</script>

<style lang="less" scoped>
  .Hyperlink {
    color: #2d8cf0;
    font-size: 14px;
  }

  .main-content {
    min-height: 600px;
    padding: 10px;
  }

  .div-flow-left {
    width: 49%;
    letter-spacing: normal;
    display: inline-block;
    border-right: solid #f5f5f5 1px;

    .div-form-default {
      width: 97%;

      h4 {
        font-weight: 600;
        line-height: 22px;
        background-color: #f5f5f5;
        padding: 6px 0 6px 12px;
        border-bottom: solid 1px #e7e7e7;
      }

      dl {
        font-size: 0;
        line-height: 30px;
        clear: both;
        padding: 0;
        margin: 0;
        border-bottom: dotted 1px #e6e6e6;
        overflow: hidden;

        dt {
          display: inline-block;
          width: 13%;
          vertical-align: top;
          text-align: right;
          padding: 15px 1% 15px 0;
          margin: 0;
          font-size: 14px;
        }

        dd {
          display: inline-block;
          width: 84%;
          padding: 15px 0 15px 1%;
          margin: 0;
          border-left: 1px solid #f0f0f0;
          font-size: 14px;
        }
      }
    }
  }

  .div-img {
    width: 130px;
    height: 130px;
    text-align: center;
    float: left;
  }

  .div-flow-center {
    width: 2%;
    display: inline-block;
  }

  .div-flow-right {
    width: 49%;
    vertical-align: top;
    word-spacing: normal;
    display: inline-block;

    .div-form-default {
      width: 97%;

      h4 {
        font-weight: 600;
        line-height: 22px;
        background-color: #f5f5f5;
        padding: 6px 0 6px 12px;
        border-bottom: solid 1px #e7e7e7;
      }

      dl {
        font-size: 0;
        line-height: 30px;
        clear: both;
        padding: 0;
        margin: 0;
        border-bottom: dotted 1px #e6e6e6;
        overflow: hidden;

        dt {
          display: inline-block;
          width: 13%;
          vertical-align: top;
          text-align: right;
          padding: 15px 1% 15px 0;
          margin: 0;
          font-size: 14px;
        }

        dd {
          display: inline-block;
          width: 84%;
          padding: 15px 0 15px 1%;
          margin: 0;
          border-left: 1px solid #f0f0f0;
          font-size: 14px;
        }
      }
    }
  }

  .complain-img {
    width: 120px;
    height: 120px;
    text-align: center;
  }

  .div-content {
    overflow-y: auto;
    overflow-x: auto;
    height: 150px;
  }
</style>
