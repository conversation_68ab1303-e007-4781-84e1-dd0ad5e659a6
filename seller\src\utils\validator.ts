/* eslint-disable no-useless-escape */
/* eslint-disable no-lonely-if */

/**
 * 对于arco form的rules验证
 * ----------------------------------------------------------------
 */

// 手机号(mobile phone)中国(最宽松), 只要是1开头即可, 如果你的手机号是用来接收短信, 优先建议选择这一条"
export const MOBILE = {
  match: /^(?:(?:\+|00)86)?1\d{10}$/,
  message: '请输入正确电话号码',
};

// 正整数
export const INTEGER = {
  match: /^[0-9]\d{0,10}|0$/,
  message: '请输入正整数',
};
// 正整数
export const NUMBER = {
  match: /^(\-|\+)?\d{0,10}$/,
  message: '请输入数字',
};
export const VARCHAR5 = {
  match: /^.{1,5}$/,
  message: '长度应该限制在1-5个字符',
};

export const VARCHAR20 = {
  match: /^.{1,20}$/,
  message: '长度应该限制在1-20个字符',
};

export const VARCHAR255 = {
  match: /^.{1,255}$/,
  message: '超出最大长度限制',
};

export const URL200 = {
  match: /[a-zA-z]+\:\/\/[^\s]{1,190}/,
  message: '请输入长度不超过200的URL地址',
};
export const REQUIRED = {
  required: true,
  message: '请填写参数',
};
export const VARCHARDECIMAL10 = {
  match: /^[0-9]{1}(\.[0-9])?$/,
  message: '请输入0-10之间的数字，可有一位小数',
};

export const MONEY = {
  match: /^[0-9]\d*(((,\d{3}){1})?(\.\d{1,2})?)$/,
  message: '请输入大于0的合法金额'
};

/** ---------------------------------------------------------------- */
