<template>
  <a-card class="general-card" title="收款记录" :bordered="false">
    <a-tabs @change="(val)=>{apiParams.payStatus = val}" :default-active-key="payStatusVar">
      <a-tab-pane key="PAID" title="已付款"></a-tab-pane>
      <a-tab-pane key="UNPAID" title="未付款"></a-tab-pane>
    </a-tabs>
    <!-- 搜索 -->
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <a-button style="margin-bottom: 15px" type="primary" @click="entryReconciliation">
      对账
    </a-button>
    <!-- 表格 -->
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :api="paymentLog"
      :api-params="apiParams"
      :bordered="true"
    />
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, ColumnsDataRule } from '@/types/global';
  import { paymentLog, paymentReconciliation } from '@/api/order';
  import useCurrentInstance from '@/hooks/useCurrentInstance';
  import {
    paymentStatus,
    rechargeWay,
    rechargeWayFilter,
    orderClientType,
  } from '@/utils/tools';
  import { Message } from '@arco-design/web-vue';
  import { ref } from 'vue';

  const payStatusVar = ref<string>('PAID');
  const apiParams = ref<any>({paymentStatus:payStatusVar.value});
  // 获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;
  const tablePageRef = ref<any>();
  const columnsSearch: Array<SearchRule> = [
    {
      label: '订单编号',
      model: 'sn',
      disabled: false,
      input: true,
    },

    {
      label: '支付方式',
      model: 'paymentMethod',
      disabled: false,
      select: {
        options: rechargeWayFilter,
      },
    },
    {
      label: '订单创建时间',
      disabled: false,
      datePicker: {
        type: 'range',
      },
    },
  ];

  // 表格搜索列表
  const columnsTable: ColumnsDataRule[] = [
    {
      title: '订单/交易编号',
      dataIndex: 'sn',
    },

    {
      title: '店铺名称',
      dataIndex: 'storeName',
    },

    {
      title: '第三方流水',
      dataIndex: 'receivableNo',
    },
    {
      title: '客户端',
      dataIndex: 'clientType',
      slot: true,
      slotData: {
        badge: orderClientType,
      },
    },
    {
      title: '订单金额',
      dataIndex: 'flowPrice',
      currency: true,
    },
    {
      title: '支付方式',
      dataIndex: 'paymentMethod',
      slot: true,
      slotData: {
        badge: rechargeWay,
      },
    },

    {
      title: '支付时间',
      width: 200,
      dataIndex: 'paymentTime',
    },
    {
      title: '支付状态',
      dataIndex: 'payStatus',
      slot: true,
      slotData: {
        badge: paymentStatus,
      },
    },
  ];

  // 对账
  function entryReconciliation() {
    modal.confirm({
      title: '提示',
      content: '您确定要收款对账？此操作需异步进行，等待约一分钟刷新列表查看',
      alignCenter: false,
      onOk: async () => {
        const res = await paymentReconciliation();
        if (res.data.success) {
          Message.success('对账成功!');
          tablePageRef.value.init();
        }
      },
    });
  }
</script>

<style scoped></style>
