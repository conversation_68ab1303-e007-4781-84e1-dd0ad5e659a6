<script setup lang="ts">
import { useRouter } from 'vue-router'
import { getCategory } from '@/api/goods'
import storage from '@/utils/storage'

const router = useRouter()
const panel = ref<boolean>(false)
const panelData = ref<any>('')
const cateList = ref([])
function fetchCategory() {
  getCategory(0).then((res) => {
    if (res.data.success) {
      cateList.value = res.data.result
      // this.$store.commit("SET_CATEGORY", res.result);
      // 过期时间
      const expirationTime = new Date().setHours(new Date().getHours() + 1)
      // 存放过期时间
      storage.setCategoryExpirationTime(expirationTime)
      // 存放分类信息
      storage.setCategory(JSON.stringify(res.data.result))
    }
  })
}

function fetchGategoryDetail(id, secondId, firstId) {
  // 分类共有三级，传全部分类过去
  const arr = [firstId, secondId, id]
  if (!arr[1])
    arr.splice(0, 2)

  if (!arr[0])
    arr.shift()

  const routerUrl = router.resolve({
    path: '/goodsList',
    query: { categoryId: arr.toString() },
  })
  window.open(routerUrl.href, '_blank')
}

function showDetail(index) {
  // 展示全部分类
  panel.value = true
  panelData.value = cateList.value[index].children
}

onMounted(() => {
  if (storage.getCategory() && storage.getCategoryExpirationTime()) {
    // 如果缓存过期，则获取最新的信息
    if (Number(new Date()) > Number(localStorage.getItem('category_expiration_time'))) {
      fetchCategory()
      return
    }
    cateList.value = JSON.parse(JSON.stringify(localStorage.getItem('category')))
  }
})
</script>

<template>
  <div class="category" flex flex-col flex-a-c>
    <div flex-start h35px w-full flex line-height-20px>
      <div pl-37px>
        商品分类
      </div>
    </div>

    <ul>
      <li v-for="(item, index) in cateList" :key="index" class="nav-item" @mouseenter="showDetail(index)">
        <span class="nav-side-item" @click="fetchGategoryDetail(item.id)">{{
          item.name
          }}</span>
        <span v-for="(second, secIndex) in item.children" :key="secIndex">
          <span v-if="secIndex < 2"> / </span>
          <span v-if="secIndex < 2" class="nav-side-item" @click="fetchGategoryDetail(second.id, second.parentId)">{{
            second.name }}</span>
        </span>
      </li>
    </ul>

    <div v-show="panel" class="detail-item-panel" :style="{ minHeight: large ? '470px' : '340px' }"
      @mouseenter="panel = true" @mouseleave="panel = false">
      <div class="nav-detail-item">
        <template v-for="(item, index) in panelData">
          <span v-if="index < 8" :key="index" @click="fetchGategoryDetail(item.id, item.parentId)">{{ item.name }}
            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24">
              <path fill="currentColor" d="M6.23 20.23L8 22l10-10L8 2L6.23 3.77L14.46 12z" />
            </svg>
          </span>
        </template>
      </div>
      <ul>
        <li v-for="(items, index) in panelData" :key="index" class="detail-item-row">
          <span class="detail-item-title" @click="fetchGategoryDetail(items.id, items.parentId)">
            {{ items.name }}
            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24">
              <path fill="currentColor" d="M6.23 20.23L8 22l10-10L8 2L6.23 3.77L14.46 12z" />
            </svg>
            <span class="glyphicon glyphicon-menu-right" />
          </span>
          <div>
            <span v-for="(item, subIndex) in items.children" :key="subIndex" class="detail-item"
              @click="fetchGategoryDetail(item.id, items.id, items.parentId)">{{ item.name }}</span>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<style scoped lang="less">
ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.category {
  position: relative;
}

li {
  list-style: none;
  padding-bottom: 16px;
  line-height: 18px;
}

.nav-side-item {
  font-size: 13px;
}

.nav-item {
  color: #666;
}

.nav-side-item:hover {
  cursor: pointer;

  color: @theme_color;
}

/*显示商品详细信息*/
.detail-item-panel {
  width: 1000px;
  min-height: 340px;
  background-color: #fff;
  box-shadow: 0px 0px 15px #ccc;
  position: absolute;
  top: 0;
  left: 200px;
  z-index: 1000;
  padding: 15px;
}

.detail-item-panel {
  line-height: 30px;
  font-size: 13px;
}

/*显示商品详细信息*/
.detail-item-panel {
  width: 1000px;
  min-height: 340px;
  background-color: #fff;
  box-shadow: 0px 0px 15px #ccc;
  position: absolute;
  top: 0;
  left: 200px;
  z-index: 1000;
  padding: 15px;
}

.nav-detail-item {
  margin-top: 5px;
  margin-bottom: 11px;
  cursor: pointer;
  color: #eee;
}

.nav-detail-item span {
  width: 81px;
  margin-right: 15px;
  font-size: 12px;
  color: #333;
  justify-content: center;
  display: inline-flex;
  align-items: center;
}

.nav-detail-item span:hover {
  background-color: @theme_color;
  color: #fff;
}

.detail-item-panel li {
  line-height: 30px;
}

.detail-item-title {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 12px;
  cursor: pointer;
  color: #555555;
  padding-right: 10px;
  width: 81px;
  justify-content: flex-end;
}

.detail-item-title:hover {
  color: @theme_color;
}

.detail-item-row {
  display: flex;
  padding-bottom: 0;

  >div {
    flex: 1;
  }
}

.detail-item {
  font-size: 12px;
  padding-left: 8px;
  padding-right: 8px;
  cursor: pointer;
  border-left: 1px solid #ccc;

  &:first-child {
    border: none;
    padding-left: 0;
  }
}

.detail-item:hover {
  color: @theme_color;
}
</style>
