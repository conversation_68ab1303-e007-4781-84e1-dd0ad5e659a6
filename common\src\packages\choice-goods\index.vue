<template>
  <a-modal v-model:visible="modalData.visible" :align-center="false" title-align="start" :width="800" draggable
    @ok="confirm" @cancel="close">
    <template #title> 商品选择器 </template>
    <div class="modal-wrapper">
      <searchTable :row-span="12" :columns="columnsSearch" @reset="reset"
        @search="(val) => { modalData.apiParams = { ...modalData.apiParams, ...val } }" @category-list="changeCategory">
      </searchTable>
      <a-radio-group v-model:model-value="modalData.queryType" style="margin-bottom: 10px" type="button">
        <a-radio value="goodsList">商品库</a-radio>
      </a-radio-group>
      <span style="margin-left: 20px;">已选择({{ modalData.goodsCount }})</span>
      <tablePage v-if="modalData.visible" ref="tablePageRef" :checkbox="true"
        :defaultSelectedKeys="modalData.goodsSelectedList" :columns="columnsTable" :api-params="modalData.apiParams"
        :api="api" @select-table-change="selectTableChange" />

    </div>
  </a-modal>
</template>

<script setup lang="ts">
import searchTable from '../search-column/index.vue';
import tablePage from '../table-pages/index.vue';
import { reactive, ref, watch, onMounted } from 'vue';
import { ColumnsDataRule, SearchRule } from '../types';

interface ModalRule {
  visible: boolean;
  categoryPath: string | number;
  queryType: string;
  goodsCount: number
  goodsSelectedList: any[];
  apiParams: {
    pageSize: number;
    categoryPath: string | number;
    [key: string]: unknown;
  };
  selectedKeys: any[];
}
const tablePageRef = ref();
const props = defineProps<{
  apiParams: any,
  api(): any
  defaultGoodsSelectedList: any;
}>();

const columnsSearch: Array<SearchRule> = [
  {
    label: '商品名称',
    model: 'goodsName',
    disabled: false,
    input: true,
  },
  {
    label: '商品分类',
    model: 'goodsName',
    disabled: false,
    category: true,
  },
];

const columnsTable: ColumnsDataRule[] = [
  {
    title: '商品',
    dataIndex: 'goods',
    width: 300,
    slot: true,
    slotData: {
      goods: {
        goodsImage: 'thumbnail',
        goodsName: 'goodsName',
      },
    },
  },
  {
    title: '价格',
    dataIndex: 'price',
    currency: true,
  },

  {
    title: '销售模式',
    dataIndex: 'salesModel',
    slot: true,
    slotData: {
      tag: [
        {
          value: 'RETAIL',
          label: '零售',
          color: 'orange',
        },
        {
          value: 'WHOLESALE',
          label: '批发',
          color: 'magenta',
        },

      ],
    },
  },
  {
    title: '商品类型',
    dataIndex: 'goodsType',
    slot: true,
    slotData: {
      tag: [
        {
          value: 'PHYSICAL_GOODS',
          label: '实物商品',
          color: 'blue',
        },
        {
          value: 'VIRTUAL_GOODS',
          label: '虚拟商品',
          color: 'purple',
        },
      ],
    },
  },

  {
    title: '库存',
    dataIndex: 'quantity',
  },
];

const modalData: ModalRule = reactive({
  visible: false, // 是否可见
  categoryPath: '',
  queryType: 'goodsList',
  goodsSelectedList: [], // 选择的商品列表
  apiParams: { pageSize: 6, categoryPath: '', ...props.apiParams },
  goodsCount: 0,
  selectedKeys: []
});
const emit = defineEmits<{
  (e: 'change', val: any): void;
}>();
const selectTableChange = (goodsList: any) => {
  modalData.goodsSelectedList = goodsList;
  modalData.goodsCount = modalData.goodsSelectedList.length;
};

// 点击分类回调内容
const changeCategory: any = (id: string | number) => {
  modalData.apiParams.categoryPath = id;
};

// 重制搜索条件
const reset = (val: any) => {
  changeCategory('');
  // tablePageRef.value.init(val);
  modalData.apiParams = { ...modalData.apiParams, ...val };
};

// 初始化内容
const init = () => {
  modalData.visible = true;
};

// 确认
const confirm = () => {
  emit('change', modalData.goodsSelectedList);
};

// 取消
const close = () => {
  modalData.visible = false;
};

watch(() => props.defaultGoodsSelectedList, (newValue: any, oldValue) => {
  modalData.goodsSelectedList = newValue;
  modalData.goodsCount = newValue.length;
  modalData.selectedKeys = newValue.map((item: any) => {
    return item.skuId;
  });
}, { deep: true, },);

onMounted(() => {
  modalData.goodsSelectedList = props.defaultGoodsSelectedList;
  modalData.goodsCount = props.defaultGoodsSelectedList.length;
  modalData.selectedKeys = props.defaultGoodsSelectedList.map((item: any) => {
    return item.skuId;
  });
});

defineExpose({
  init,
  close,
  modalData
});
</script>

<style lang="less" scoped>
.modal-wrapper {
  height: 650px;
}
</style>
