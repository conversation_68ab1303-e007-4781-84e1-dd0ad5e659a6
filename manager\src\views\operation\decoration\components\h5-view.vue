<template>
    
      <a-form-item field="url" label="链接" >
        <a-input v-model="url" @change="urlChange"  size="large"  />
      </a-form-item>
</template>

<script setup lang="ts">
import { ref } from "vue"
const url = ref("");
const active = ref<String | number>('')
const emit = defineEmits(['selectTableChange'])
function urlChange() {
  console.log("改变");
  url.value = url.value.trim();
  let val = {
    label: url.value,
    icon: "no",
    value: url.value,
  };
  emit('selectTableChange', {...val,___type:'url'})

}

</script>

<style scoped>
.active{
  background: #d1d5db;
}
</style>
