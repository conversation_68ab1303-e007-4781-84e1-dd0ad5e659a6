/**
 * 工具
 */

export const afterSaleStatusList = [
  // 售后状态列表
  {
    value: 'APPLY',
    label: '待处理',
    color: 'blue',
  },
  {
    value: 'PASS',
    label: '待买家退货',
    color: 'cyan',
  },
  {
    value: 'REFUSE',
    label: '商家拒绝售后',
    color: 'red',
  },
  {
    value: 'BUYER_RETURN',
    label: '退货待卖家收货',
    color: 'orange',
  },
  {
    value: 'SELLER_CONFIRM',
    label: '退货卖家确认收货',
    color: 'pinkpurple',
  },
  {
    value: 'SELLER_TERMINATION',
    label: '卖家拒绝收货',
    color: 'orangered',
  },
  {
    value: 'BUYER_CONFIRM',
    label: '买家确认收货',
    color: 'lime',
  },
  {
    value: 'BUYER_CANCEL',
    label: '买家取消售后',
    color: 'purple',
  },
  {
    value: 'COMPLETE',
    label: '售后关闭',
    color: 'green',
  },
  {
    value: 'WAIT_REFUND',
    label: '待平台退款',
    color: 'blue',
  },
];

// 订单状态列表
export const orderStatusList = [
  {
    label: '全部',
    value: '',
    color: '',
  },
  {
    label: '未付款',
    value: 'UNPAID',
    color: 'magenta',
  },
  {
    label: '已付款',
    value: 'PAID',
    color: 'blue',
  },
  {
    label: '待发货',
    value: 'UNDELIVERED',
    color: 'orange',
  },
  {
    label: '已发货',
    value: 'DELIVERED',
    color: 'cyan',
  },
  {
    label: '已完成',
    value: 'COMPLETED',
    color: 'green',
  },
  {
    label: '待通知第三方',
    value: 'TAKE',
    color: 'Orange Red',
  },

  {
    label: '待充值',
    value: 'CREATE_ZZ_SUCCESS',
    color: 'green',
  },
  {
    label: '下单失败',
    value: 'CREATE_ZZ_FAIL',
    color: 'red',
  },
  {
    label: '充值成功',
    value: 'RECHARGE_SUCCESS',
    color: 'green',
  },
  {
    label: '充值失败',
    value: 'RECHARGE_FAIL',
    color: 'red',
  },
  {
    label: '待使用权益',
    value: 'TO_BE_ALLOCATED',
    color: 'Orange Red',
  },
  {
    label: '已取消',
    value: 'CANCELLED',
    color: 'red',
  },
  {
    label: '待自提',
    value: 'STAY_PICKED_UP',
    color: 'purple',
  },
];
// 会员列表详情，订单类型
export const orderType = [
  {
    value: 'NORMAL',
    label: '普通订单',
    color: 'purple',
  },
  {
    value: 'VIRTUAL',
    label: '虚拟订单',
    color: 'green',
  },
  {
    value: 'GIFT',
    label: '赠品订单',
    color: 'blue',
  },
  {
    value: 'PINTUAN',
    label: '拼团订单',
    color: 'orange',
  },
  {
    value: 'POINTS',
    label: '积分订单',
    color: 'purple',
  },
  {
    value: 'KANJIA',
    label: '砍价订单',
    color: 'blue',
  },
];
// 评论类型
export const gradeList = [
  {
    label: '好评',
    value: 'GOOD',
    color: 'green',
  },
  {
    label: '中评',
    value: 'MODERATE',
    color: 'orange',
  },
  {
    label: '差评',
    value: 'WORSE',
    color: 'red',
  },
];

// 回复状态
export const replyStatus = [
  {
    value: true,
    label: '已回复',
    color: 'green',
  },
  {
    value: false,
    label: '未回复',
    color: 'blue',
  },
];

// 订单来源
export const orderClientType = [
  {
    value: 'H5',
    label: '移动端',
    color: 'purple',
  },
  {
    value: 'PC',
    label: 'PC端',
    color: 'blue',
  },
  {
    value: 'WECHAT_MP',
    label: '小程序端',
    color: 'green',
  },
  {
    value: 'APP',
    label: '移动应用端',
    color: 'magenta',
  },
  {
    value: 'API',
    label: 'API推送',
    color: 'magenta',
  },
];

// 订单状态
export const orderClientStatus = [
  {
    value: 'UNDELIVERED',
    label: '待发货',
    color: 'purple',
  },
  {
    value: 'UNPAID',
    label: '未付款',
    color: 'orange',
  },
  {
    value: 'PAID',
    label: '已付款',
    color: 'purple',
  },
  {
    value: 'DELIVERED',
    label: '已发货',
    color: 'arcoblue',
  },
  {
    value: 'CANCELLED',
    label: '已取消',
    color: 'red',
  },
  {
    value: 'COMPLETED',
    label: '已完成',
    color: 'green',
  },
  {
    value: 'TAKE',
    label: '待通知第三方',
    color: 'yellow',
  },
  {
    value: 'TO_BE_ALLOCATED',
    label: '待使用权益',
    color: 'yellow',
  },
  {
    label: '待充值',
    value: 'CREATE_ZZ_SUCCESS',
    color: 'green',
  },
  {
    label: '下单失败',
    value: 'CREATE_ZZ_FAIL',
    color: 'red',
  },
  {
    label: '充值成功',
    value: 'RECHARGE_SUCCESS',
    color: 'green',
  },
  {
    label: '充值失败',
    value: 'RECHARGE_FAIL',
    color: 'red',
  },
  {
    label: '待自提',
    value: 'STAY_PICKED_UP',
    color: 'purple',
  },
];

// 评论状态
export const commentStatus = [
  {
    value: 'CLOSE',
    label: '隐藏',
    color: 'red',
  },
  {
    value: 'OPEN',
    label: '展示',
    color: 'green',
  },
];

// 服务状态
export const serviceStatus = [
  {
    value: 'APPLY',
    label: '申请中',
    color: 'blue',
  },
  {
    value: 'PASS',
    label: '通过售后',
    color: 'cyan',
  },
  {
    value: 'REFUSE',
    label: '拒绝售后',
    color: 'red',
  },
  {
    value: 'BUYER_RETURN',
    label: '买家退货，待卖家收货',
    color: 'orange',
  },
  {
    value: 'SELLER_RE_DELIVERY',
    label: '商家换货/补发',
    color: 'magenta',
  },
  {
    value: 'SELLER_CONFIRM',
    label: '卖家确认收货',
    color: 'pinkpurple',
  },
  {
    value: 'SELLER_TERMINATION',
    label: '卖家终止售后',
    color: 'orangered',
  },
  {
    value: 'BUYER_CONFIRM',
    label: '买家确认收货',
    color: 'lime',
  },
  {
    value: 'BUYER_CANCEL',
    label: '买家取消售后',
    color: 'purple',
  },
  {
    value: 'COMPLETE',
    label: '完成售后',
    color: 'green',
  },
  {
    value: 'WAIT_REFUND',
    label: '待平台退款',
    color: 'blue',
  },
];

// 商品状态
export const marketEnable = [
  {
    value: 'DOWN',
    label: '下架',
    color: 'red',
  },
  {
    value: 'UPPER',
    label: '上架',
    color: 'green',
  },
];

// 审核状态
export const authFlag = [
  {
    value: 'PASS',
    label: '通过',
    color: 'green',
  },
  {
    value: 'TOBEAUDITED',
    label: '待审核',
    color: 'blue',
  },
  {
    value: 'REFUSE',
    label: '审核拒绝',
    color: 'red',
  },
];

// 商品类型
export const goodsType = [
  {
    value: 'PHYSICAL_GOODS',
    label: '实物商品',
    color: 'blue',
  },
  {
    value: 'VIRTUAL_GOODS',
    label: '虚拟商品',
    color: 'purple',
  },
  {
    value: 'EQUITY',
    label: '权益商品',
    color: 'red',
  },
  // {
  //   value: '',
  //   label: '电子卡券',
  //   color: 'cyan',
  // },
];

// 销售状态
export const salesModel = [
  {
    value: 'RETAIL',
    label: '零售',
    color: 'orange',
  },
  {
    value: 'WHOLESALE',
    label: '批发',
    color: 'magenta',
  },
  // {
  //   value: '',
  //   label: '其他类型',
  //   color: 'Orange Red',
  // },
];

// 投诉状态
export const complaintStatus = [
  {
    value: 'NEW',
    label: '新投诉',
    color: 'purple',
  },
  {
    value: 'CANCEL',
    label: '已撤销',
    color: 'cyan',
  },
  {
    value: 'WAIT_APPEAL',
    label: '待申诉',
    color: 'red',
  },
  {
    value: 'COMMUNICATION',
    label: '对话中',
    color: 'orange',
  },
  {
    value: 'WAIT_ARBITRATION',
    label: '等待仲裁',
    color: 'blue',
  },
  {
    value: 'COMPLETE',
    label: '已完成',
    color: 'green',
  },
];
export const billStatus = [
  {
    value: 'OUT',
    label: '待对账',
    color: 'blue',
  },
  {
    value: 'CHECK',
    label: '待结算',
    color: 'purple',
  },
  {
    value: 'COMPLETE',
    label: '已完成',
    color: 'green',
  },
];

export const billStatusSelect = [
  {
    value: 'OUT',
    label: '待对账',
    color: 'blue',
  },
  {
    value: 'CHECK',
    label: '待结算',
    color: 'purple',
  },
  {
    value: 'COMPLETE',
    label: '已完成',
    color: 'green',
  },
];

export const receiptStatus = [
  {
    value: 0,
    label: '未开票',
    color: 'red',
  },
  {
    value: 1,
    label: '已开票',
    color: 'green',
  },
];

export const promotionsStatusRender = [
  {
    value: 'NEW',
    label: '未开始',
    color: 'orange',
  },
  {
    value: 'START',
    label: '已开始',
    color: 'green',
  },
  {
    value: 'CLOSE',
    label: '已关闭',
    color: 'red',
  },
  {
    value: 'END',
    label: '已结束',
    color: 'purple',
  },
];

export const promotionsScopeTypeRender = [
  {
    value: 'ALL',
    label: '全品类',
    color: 'green',
  },
  {
    value: 'PORTION_GOODS_CATEGORY',
    label: '商品分类',
    color: 'orange',
  },
  {
    value: 'PORTION_SHOP_CATEGORY',
    label: '店铺分类',
    color: 'pink',
  },
  {
    value: 'PORTION_GOODS',
    label: '指定商品',
    color: 'magenta',
  },
];

export const promotionStatusSelect = [
  {
    value: 'NEW',
    label: '未开始',
    color: 'orange',
  },
  {
    value: 'START',
    label: '已开始/上架',
    color: 'green',
  },
  {
    value: 'END',
    label: '已结束/下架',
    color: 'blue',
  },
  {
    value: 'CLOSE',
    label: '紧急关闭/作废',
    color: 'red',
  },
];

export const promotionStatus = [
  {
    value: 'NEW',
    label: '未开始',
    color: 'orange',
  },
  {
    value: 'START',
    label: '已开始',
    color: 'green',
  },
  {
    value: 'END',
    label: '已结束',
    color: 'blue',
  },
  {
    value: 'CLOSE',
    label: '已关闭',
    color: 'red',
  },
];

export const liveStatus = [
  {
    value: 'NEW',
    label: '未开始',
    color: 'orange',
  },
  {
    value: 'START',
    label: '直播中',
    color: 'green',
  },
  {
    value: 'END',
    label: '已结束',
    color: 'red',
  },
];

export const couponType = [
  {
    value: 'DISCOUNT',
    label: '折扣',
    color: 'blue',
  },
  {
    value: 'PRICE',
    label: '减免现金',
    color: 'green',
  },
];

export const logisticsStatus = [
  {
    value: '1533736232324067329',
    label: '开启',
    color: 'green',
  },
  {
    value: null,
    label: '关闭',
    color: 'red',
  },
];

export const distributionOrderStatus = [
  {
    value: 'COMPLETE_CASH',
    label: '提现完成',
    color: 'green',
  },
  {
    value: 'WAIT_BILL',
    label: '待结算',
    color: 'orange',
  },
  {
    value: 'WAIT_CASH',
    label: '待提现',
    color: 'blue',
  },
  {
    value: 'CANCEL',
    label: '取消',
    color: 'red',
  },
];

export const fullMinusStatus = [
  {
    value: true,
    label: '满减',
    color: 'green',
  },
  {
    value: false,
    label: '满折',
    color: 'blue',
  },
];
// 获取方式
export const claimStatus = [
  {
    value: 'FREE',
    label: '免费获取',
    color: 'red',
  },
  {
    value: 'ACTIVITY',
    label: '活动获取',
    color: 'green',
  },
  {
    value: 'INSIDE',
    label: '内购',
    color: 'pink',
  },
];
// 会员优惠券状态
export const memberCouponStatus = [
  {
    value: 'NEW',
    label: '已领取',
    color: 'purple',
  },
  {
    value: 'USED',
    label: '已使用',
    color: 'green',
  },
  {
    value: 'EXPIRE',
    label: '已过期',
    color: 'red',
  },
  {
    value: 'CLOSED',
    label: '已作废',
    color: 'blue',
  },
];

// 配送方式
export const deliveryMethod = [
  {
    value: 'SELF_PICK_UP',
    label: '自提',
    color: 'red',
  },
  {
    value: 'LOGISTICS',
    label: '物流',
    color: 'green',
  },
  // {
  //   value: 'LOCAL_TOWN_DELIVERY',
  //   label: '同城配送',
  //   color: 'yellow',
  // }
];
