<template>
  <div>
    <div w-full>
      <div w-full :style="{ color: props.res.data.textColor, textAlign: props.res.data.align }" block text-31px font-400
        line-height-37px mb-25px>
        {{ props.res.data.text }}
      </div>
      <!-- 商品列表 -->
      <div flex gap-10px flex-wrap>
        <div v-for="(item, index) in props.res.data.list" :key="index" v-if="props.res.data.list.length">
          <div w-287px h-343px mb-14.3px rounded-9.8px cursor-pointer class="goods-item">
            <div overflow-hidden flex flex-a-c flex-j-c>
            
              <img mt-16px max-h-183px :src="item.img || item.small" alt="">
            </div>
            <div class="goods-name">{{ item.title || item.goodsName}}</div>
            <div class="goods-price">￥{{ unitPrice(item.price) }}</div>
          </div>
        </div>
        <div v-else w-full flex flex-a-c h-100px flex-j-c color-gray-300>
          当前无展示的商品，此组件在买家页面中不可见
        </div>
      </div>
      <!-- <div cursor-pointer text-21px font-400 line-height-25px flex flex-a-c>
        <div @click="handleChangeCategory(item, index)" :class="{ 'active': index === active }" ml-28px
          v-for="(item, index) in props.res.data.list" :key="index">
          {{ item.category }}
        </div>
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { unitPrice } from '@/utils/filters';
import { ref } from 'vue';

// const active = ref<number>(0);
const props = defineProps<{
  res: any
}>()

// 切换分类
// function handleChangeCategory(current: any, index: number) {
//   active.value = index;
// }
</script>

<style scoped>
.active {
  color: #f31947;
}

.goods-item {
  background: #fff;
  box-shadow: 0 1px 13px 0 #e5e5e5;
}

.goods-name {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 2;
  font-weight: 400;
  text-align: center;
  letter-spacing: 0;
  -webkit-text-stroke: #979797 0.7px;
  font-size: 18px;
  line-height: 22px;
  width: 200px;
  margin: 12.4px auto 18px auto;
  color: #333;
}

.goods-price {
  font-size: 27px;
  font-weight: 400;
  line-height: 30px;
  text-align: center;
  letter-spacing: 0;
  color: #f31947;
  -webkit-text-stroke: #979797 0.7px;
}
</style>
