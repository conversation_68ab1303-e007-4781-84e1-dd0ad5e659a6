<template>
  <div>
    <div class="wrapper" :style="{right:handleDrawer ? '300px' : '0px'}">
      <div class="barItem" @mouseenter="showCartNum(item)" @click="clickBar(item)" v-for="(item,index) in resetConfig.menuList" :key="index">
        <a-tooltip :content="item.title" position="left">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="#FFFFFF" :d="item.d"></path></svg>
          <p class="barTitle" v-if="item.titleShow"> {{item.title}}</p>
          <div class="circle" v-if="item.title === '购物车'">
            {{cartNum < 100 ? cartNum : 99}}
          </div>
        </a-tooltip>
      </div>
    </div>
    <a-drawer :width="300" :visible="handleDrawer" :mask="resetConfig.mask" :closable="resetConfig.closable" unmountOnClose :footer="false"
              @ok="handleDrawer = false" @cancel="handleDrawer = false">
      <template #title>{{drawerData.title}}</template>
      <DrawerPage :title="drawerData.title"></DrawerPage>
    </a-drawer>


  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { Modal } from '@arco-design/web-vue';
  import storage from '@/utils/storage';
  import { useRouter } from 'vue-router';
  import Configuration from './config';
  import { cartCount } from '@/api/cart';
  import Cookies from 'js-cookie';

  const router = useRouter();
  // 用户信息
  const userInfo = computed(() => {
    return storage.getUserInfo();
  });
  // 购物车商品数量
  const cartNum = computed(() => {
    return Number(storage.getCartNum()) || 0;
  });
  const resetConfig = Configuration; // 菜单项
  const handleDrawer = ref(false); // 是否可展开
  const drawerData = ref<any>(''); // 菜单基础数据

  // 获取购物车数量
  const showCartNum = (item: any) => {
    if (userInfo.value && item.title === '购物车') {
      getCartList();
    }
  };
  // 获取购物车列表
  const getCartList = () => {
    cartCount().then(res => {
      storage.setCartNum(res.data.result);
      Cookies.set("cartNum",res.data.result);
    })
  };
  // tabbar点击操作
  const clickBar = (val: any) => {
    if (!userInfo.value) {
      Modal.confirm({
        title: '请登录',
        content: `请登录后执行此操作`,
        okButtonProps: {type: "primary", status: "danger"
        },
        okText: '立即登录',
        cancelText: '继续浏览',
        onOk: () => {
          router.push({path: '/Login', query: {rePath: router.currentRoute.value.path, query: JSON.stringify(router.currentRoute.value.query)}});
        }
      })
    } else {
      if (val.display) {
        handleDrawer.value = true;
        drawerData.value = val;
      } else {
        handleDrawer.value = false;
        switch (val.title) {
          case '会员中心':
            openBlank('/user/home');
            break;
          case '我的资产':
            openBlank('/user/home/<USER>/moneyManagement');
            break;
        }
      }
    }
  };
  // 新页面打开地址
  const openBlank = (path: any) => {
    let routerUrl = router.resolve({path: path});
    window.open(routerUrl.href, '_blank');
  };
</script>

<style scoped lang="less">
  .wrapper {
    width: 40px;
    position: fixed;
    transition: .35s;
    height: 100%;
    z-index: 9999;
    background: @dark_background_color;
    top: 0;
    display: flex;
    justify-content: center;
    flex-direction: column;
    .barItem {
      text-align: center;
      padding: 13px 0;
      cursor: pointer;
      color: #fff;
      display: flex;
      flex-direction: column;
      align-items: center;
      user-select: none;
      &:hover{
        background-color: @theme_color;
        .circle{
          color: @theme_color;
          background-color: #fff;
        }
      }
    }
    .barTitle {
      writing-mode: vertical-lr;
      letter-spacing: 2px;
      padding: 4px 0;
    }
    .circle {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      color: #fff;
      background: @theme_color;
      line-height: 20px;
      font-size: 13px;
    }
  }


</style>
