<template>
  <div>
    <Card _Title="申请售后" :_Size="16"></Card>
    <a-table :columns="columns" :data="goodsData" :pagination="false">
      <template #goodsName="{ record }">
        <a-image width="60" height="60" :src="record.image"/>
        <span class="ml_10">{{record.goodsName}}</span>
      </template>
      <template #goodsPrice="{ record }">
        <div>{{ unitPrice(record.applyRefundPrice, '￥') }}</div>
      </template>
    </a-table>

    <a-form ref="afterSaleRef" :model="afterSaleForm" @submit-success="handleSubmit"
            size="large" layout="horizontal" auto-label-width :style="{ width: '400px'}" class="mt_20">
      <a-form-item field="serviceType" label="售后类别" :rules="[REQUIRED]">
        <a-radio-group v-model="afterSaleForm.serviceType" type="button" class="mb_10">
          <a-radio v-if="info.returnGoods" value="RETURN_GOODS">退货</a-radio>
          <a-radio v-if="info.returnMoney" value="RETURN_MONEY">退款</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item field="num" label="提交数量" :rules="[REQUIRED]">
        <a-input-number v-model="afterSaleForm.num" :style="{width:'320px'}" placeholder="" :min="1" :max="info.num"/>
      </a-form-item>
      <a-form-item field="reason" label="提交原因" :rules="[REQUIRED]">
        <a-select :style="{width:'320px'}" placeholder="" v-model="afterSaleForm.reason">
          <a-option v-for="item in reasonList" :value="item.id" :key="item.id">{{item.reason}}</a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="problemDesc" label="问题描述" :rules="[REQUIRED]">
        <a-textarea placeholder="请输入问题描述" v-model="afterSaleForm.problemDesc" :max-length="500" allow-clear show-word-limit :auto-size="{minRows:4,maxRows:6}" ></a-textarea>
      </a-form-item>
      <a-form-item field="" label="图片信息">
        <a-upload list-type="picture-card" :action="uploadFile" :headers="{ accessToken: accessToken }" image-preview :limit="5"
                  :file-list="fileList" v-model="fileList" :onSuccess="uploadSuccess" @change="handleChange" @before-upload="beforeUpload"></a-upload>
      </a-form-item>
      <a-form-item label="退款方式">
        <div>{{info.refundWay == 'ORIGINAL' ? '原路退回' : '账号退款'}}</div>
      </a-form-item>

      <a-form-item field="bankDepositName" label="开户行" :rules="[REQUIRED]" v-if="info.accountType === 'BANK_TRANSFER' && info.applyRefundPrice != 0">
        <a-input v-model="afterSaleForm.bankDepositName" allow-clear placeholder="请输入银行开户行" :style="{width:'320px'}"></a-input>
      </a-form-item>
      <a-form-item field="bankAccountName" label="开户名" :rules="[REQUIRED]" v-if="info.accountType === 'BANK_TRANSFER' && info.applyRefundPrice != 0">
        <a-input v-model="afterSaleForm.bankAccountName" allow-clear placeholder="请输入银行开户名" :style="{width:'320px'}"></a-input>
      </a-form-item>
      <a-form-item field="bankAccountNumber" label="银行账号" :rules="[REQUIRED]" v-if="info.accountType === 'BANK_TRANSFER' && info.applyRefundPrice != 0">
        <a-input v-model="afterSaleForm.bankAccountNumber" allow-clear placeholder="请输入银行账号" :style="{width:'320px'}"></a-input>
      </a-form-item>

      <a-form-item label="返回方式" v-if="afterSaleForm.serviceType === 'RETURN_GOODS'">
        <div>快递至第三方卖家</div>
      </a-form-item>
      <a-form-item>
        <a-button html-type="submit"  size="small" status="danger" type="primary">提交申请</a-button>
      </a-form-item>
    </a-form>



  </div>
</template>

<script setup type="ts">
  import { useRouter, useRoute } from 'vue-router';
  import { ref, onMounted } from 'vue';
  import { orderStatusList, orderPromotionList } from '../enumeration.js';
  import { unitPrice } from '@/utils/filters';
  import { secrecyMobile, unitAddress } from '@/utils/filters';
  import { Message, Modal } from '@arco-design/web-vue';
  import { afterSaleReason, afterSaleInfo, applyAfterSale } from '@/api/member';
  import { VARCHAR255, REQUIRED, VARCHAR20, MOBILE } from '@/utils/validator';
  import uploadFile from '@/api/common';
  import { handleSuccess, handleError, beforeUpload, accessToken } from '@/utils/upload';

  const route = useRoute();
  const router = useRouter();
  const info = ref({}); // 售后信息
  const reasonList = ref([]); // 售后原因列表
  const goodsData = ref([]);  // 商品数据
  const columns = [
    {title: '商品名称', slotName: 'goodsName'},
    {title: '价格', slotName: 'goodsPrice'},
    {title: '购买数量', dataIndex: 'num'}
  ];
  const afterSaleRef = ref();
  // 售后数据表单
  const afterSaleForm = ref({
    serviceType: 'RETURN_GOODS',
    num: 1
  });
  // 上传图片列表
  const fileList = ref([]);

  // 获取售后信息
  const getInfo = () => {
    afterSaleInfo(route.query.sn).then(res => {
      if (res.data.success) {
        info.value = res.data.result;
        goodsData.value.push(res.data.result);
        if (!info.value.returnGoods && info.value.returnMoney) {
          afterSaleForm.value.serviceType = 'RETURN_MONEY'
        }
        getReason(afterSaleForm.value.serviceType);
      }
    })
  };
  // 获取售后原因
  const getReason = (type) => {
    afterSaleReason(type).then(res => {
      if (res.data.success) reasonList.value = res.data.result;
    })
  };
  // 上传成功回调
  const uploadSuccess = (val) => {
    // console.log('上传成功回调', val);
  };
  const handleChange = (list, files) => {
    if (list && list.length) {
      fileList.value = list;
    } else {
      fileList.value = [];
    }
  };

  // 提交申请
  const handleSubmit = async () => {
    const auth = await afterSaleRef.value?.validate();
    if (!auth) {
      let params = Object.assign(info.value, afterSaleForm.value);
      params.images = fileList.value.map(item => item.response.result).toString(),
      params.orderItemSn = route.query.sn;
      params.reason = reasonList.value.find(item => item.id == params.reason).reason;
      applyAfterSale(params).then(res => {
        if (res.data.success) {
          Message.success('售后申请提交成功，请到售后订单查看！');
          router.push({path: `/user/home/<USER>/afterSale`});
        }
      })
    }
  };

  onMounted(() => {
    getInfo();
  })
</script>

<style scoped lang="less">



</style>
