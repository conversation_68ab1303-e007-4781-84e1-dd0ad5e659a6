// eslint-disable-next-line import/no-unresolved
import Cookies from 'js-cookie';
import request, { Method, commonUrl } from '@/utils/axios';

/**
 * 获取拼图验证
 */
export function getVerifyImg(verificationEnums) {
  return request({
    url: `${commonUrl}/common/common/slider/${verificationEnums}`,
    method: Method.GET,
    needToken: false,
    headers: { uuid: Cookies.get('uuid') },
  });
}

/**
 * 验证码校验
 */
export function postVerifyImg(params) {
  return request({
    url: `${commonUrl}/common/common/slider/${params.verificationEnums}`,
    method: Method.POST,
    needToken: false,
    params,
    headers: { uuid: Cookies.get('uuid') },
  });
}
