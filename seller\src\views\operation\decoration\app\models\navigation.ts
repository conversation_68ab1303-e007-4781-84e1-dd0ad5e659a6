import { DragRule } from './types'

const navigationModel: Array<DragRule> = [

    {
        type: "bottomNav",
        name: "底部导航",
        roles: ['iconSize'],
        border: 'normal',
        models: [{
            label: '菜单',
            model: 'mix-model'
        }],
        data: {
            iconSize: 24, // 默认图标大小
            list: [
                {
                    title: '主页',
                    img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/166519837827005655.jpg",
                    url: {
                        ___value: 'tabbar',
                        title: '首页',
                        path: '/pages/tabbar/home/<USER>',
                        ___key: '首页',
                        ___type: 'Tab页面'
                    },
                    size: "1:1"
                },
                {
                    title: '分类',
                    img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/166519830495916803.jpg",
                    url: {
                        ___value: 'tabbar',
                        title: '分类',
                        path: '/pages/tabbar/category/category',
                        ___key: '分类',
                        ___type: 'Tab页面'
                    },
                    size: "1:1"
                },
                {
                    title: '充值中心',
                    img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/9e3b1278663d48bbb60a64b13cb29e7b.png",
                    url: {
                        ___value: 'tabbar',
                        title: '充值中心',
                        path: '/pages/tabbar/voucher/voucher',
                        ___key: '充值中心',
                        ___type: 'Tab页面'
                    },
                    size: "1:1"
                },
                {
                    title: '优惠购',
                    img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/4b13e9f1508c44bf9a35eb9b0cbd5b2c.png",
                    url: {
                        ___value: 'tabbar',
                        title: '优惠购',
                        path: '/pages/tabbar/special/tabbar',
                        ___key: '优惠购',
                        ___type: 'Tab页面'
                    },
                    size: "1:1"
                },
                {
                    title: '我的',
                    url: {
                        ___value: 'tabbar',
                        title: '我的',
                        path: '/pages/tabbar/user/my',
                        ___key: '我的',
                        ___type: 'Tab页面'
                    },
                    img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/767169e6f7bd4f239acad82db204158b.jpg",
                    size: "1:1"
                },

            ],

        }
    },
]

export default navigationModel
