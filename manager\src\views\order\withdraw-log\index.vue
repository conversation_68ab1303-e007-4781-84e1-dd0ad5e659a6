<template>
    <a-card class="general-card" title="提现记录" :bordered="false">
      <a-tabs @change="(val)=>{apiParams.status = val}" :default-active-key="withdrawStatus">
        <a-tab-pane key="CREATE_SUCCESS" title="受理成功"></a-tab-pane>
        <a-tab-pane key="SUCCESS" title="提现成功"></a-tab-pane>
        <a-tab-pane key="FAIL" title="提现失败"></a-tab-pane>
        <a-tab-pane key="REFUND" title="提现退票"></a-tab-pane>
        <a-tab-pane key="CLOSE" title="关单"></a-tab-pane>
        <a-tab-pane key="INIT" title="业务单创建"></a-tab-pane>
      </a-tabs>
      <!-- 搜索 -->
      <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
      </searchTable>
      <!-- 表格 -->
      <tablePage
        ref="tablePageRef"
        :columns="columnsTable"
        :api="withdrawLog"
        :api-params="apiParams"
        :bordered="true"
      />
    </a-card>
  </template>
  
  <script setup lang="ts">
    import searchTable from '@/components/search-column/index.vue';
    import tablePage from '@/components/table-pages/index.vue';
    import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
    import { withdrawLog } from '@/api/order';
    import { withdrawSelect } from '@/utils/tools';
    import { ref } from 'vue';

    const withdrawStatus = ref<string>('SUCCESS');
    const apiParams = ref<any>({status:withdrawStatus.value});
    const tablePageRef = ref<any>();
    const columnsSearch: Array<SearchRule> = [
      {
        label: '二级商户号',
        model: 'subMchid',
        disabled: false,
        input: true,
      },
      {
        label: '创建时间',
        disabled: false,
        datePicker: {
          type: 'range',
        },
      },
    ];
  
    // 表格搜索列表
    const columnsTable: ColumnsDataRule[] = [
      {
        title: '商户预约提现单号',
        dataIndex: 'outRequestNo',
      },
      
      {
        title: '提现金额',
        dataIndex: 'amount',
        currency: true,
      },
     
      {
        title: '账单状态',
        dataIndex: 'status',
        slot: true,
        slotData: {
          badge: withdrawSelect,
        },
      },
      {
        title: '二级商户号',
        dataIndex: 'subMchid',
      },
      {
        title: '微信支付预约提现单号',
        dataIndex: 'withdrawId',
        width: 400,
      },
      
      {
        title: '创建时间',
        width: 200,
        dataIndex: 'createTime',
      },
    ];
  
  </script>
  
  <style scoped></style>
  