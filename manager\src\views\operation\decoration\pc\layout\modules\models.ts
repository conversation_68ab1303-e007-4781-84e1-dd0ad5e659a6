import text from '@/views/operation/decoration/components/text.vue'
import mixModel from '@/views/operation/decoration/components/mix-model.vue'
// import goodsStyle from './goods-style.vue'
import goodsChoice from '@/views/operation/decoration/components/goods-choice.vue'
// import swiperModel from './swiper-model.vue'
import background from '@/views/operation/decoration/components/background.vue'
export default {
  text,
  'mix-model': mixModel,
  // 'goods-style': goodsStyle,
  'goods-choice': goodsChoice,
  // 'swiper-model': swiperModel,
  'background': background
}
