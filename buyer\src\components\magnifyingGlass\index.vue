<template>
  <div class="magnifying-glass">
    <div class="goods-image">
      <!--<div class="large" v-if="isShow" :style="[{ backgroundImage: `url(${images[currIndex]})` }, bgPosition]"></div>-->
      <div class="large large2" v-if="isShow">
        <img :src="currImages[currIndex]" alt="" :style="[bgPosition2]">
      </div>
      <div class="middle" ref="target">
        <img :src="currImages[currIndex]" alt="" />
        <div class="layer" v-if="isShow" :style="[position]"></div>
      </div>
      <div class="small">
        <div v-for="(img, i) in currImages" :key="img" :class="{ active: i === currIndex }"><img @mouseenter="currIndex = i" :src="img" alt="" /></div>
      </div>
    </div>
    <div class="video-box"  v-if="isShowVideo">
      <div class="video-play" @click="videoClick(false)">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
          <path fill="#ffffff" d="M8 18.392V5.608L18.226 12zM6 3.804v16.392a1 1 0 0 0 1.53.848l13.113-8.196a1 1 0 0 0 0-1.696L7.53 2.956A1 1 0 0 0 6 3.804"/>
        </svg>
      </div>
    </div>
    <div class="video-box" v-if="!isShowVideo && video">
      <video controls autoplay class="video" ref="videoRef" :src="video"></video>
      <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 24 24" @click="videoClick(true)" class="close">
        <path fill="#666666" d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10m0-11.414L9.172 7.757L7.757 9.172L10.586 12l-2.829 2.828l1.415 1.415L12 13.414l2.828 2.829l1.415-1.415L13.414 12l2.829-2.828l-1.415-1.415z"/>
      </svg>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { ref, watch, reactive, onMounted } from 'vue'
  import { useMouseInElement, useEventListener } from '@vueuse/core';

  const props = defineProps({
    // 图片列表
    images: {
      type: Array,
      default: () => []
    },
    // 视频
    video: {
      type: String,
      default: ''
    }
  });

  const currImages = ref<any>([]);
  const currIndex = ref<number>(0); // 当前图片索引
  const target = ref(null); // 主图ref
  const isShow = ref(false);  // 是否显示放大镜
  const position = reactive<any>({left: 0, top: 0});
  const bgPosition = reactive<any>({backgroundPositionX: 0, backgroundPositionY: 0});
  const bgPosition2 = reactive<any>({left: 0, top: 0});
  const { elementX, elementY, isOutside } = useMouseInElement(target);  // 监听鼠标在某元素中移动
  const videoRef = ref(); // 视频ref
  const isVideo = ref(false); // 是否展示视频
  const isShowVideo = ref(false); // 是否展示视频播放按钮

  // 点击播放视频按钮
  const videoClick = (type: any) => {
    isShowVideo.value = type;
    isVideo.value = false;
  };
  // 监听鼠标移入视频
  useEventListener(videoRef, "mouseenter", () => {
    isVideo.value = true;
  });
  // 监听鼠标移出视频
  useEventListener(videoRef, "mouseleave", () => {
    isVideo.value = false;
  });
  // 监听视频播放结束
  useEventListener(videoRef, "ended", () => {
    videoClick(true);
  });

  onMounted(() => {
    isShowVideo.value = props.video?true:false;
    currImages.value = props.images;
  });
  watch([elementX, elementY, isOutside], () => {
    isShow.value = !isOutside.value && !isVideo.value;  // 判断放大镜是否展示
    if (isOutside.value) return;
    if (elementX.value <= 100) {
      position.left = 0
    } else if (elementX.value >= 275) {
      position.left = 175
    } else {
      position.left = elementX.value - 100
    }
    if (elementY.value <= 100) {
      position.top = 0
    } else if (elementY.value >= 275) {
      position.top = 175
    } else {
      position.top = elementY.value - 100
    }
    bgPosition.backgroundPositionX = -position.left * 2 + 'px';
    bgPosition.backgroundPositionY = -position.top * 2 + 'px';
    bgPosition2.left = -position.left * 2 + 'px';
    bgPosition2.top = -position.top * 2 + 'px';
    position.left += 'px';
    position.top += 'px';
  });
  watch(() => isVideo.value, (value) => {
    if (value) {
      isShow.value = false;
    }
  })
</script>
<style scoped lang="less">
  .magnifying-glass {
    position: relative;
    /*视频组件*/
    .video-box {
      z-index: 1000;
      position: absolute;
      top: 0;
      left: 0;
      width: 350px;
      height: 350px;
      display: flex;
      align-items: center;
      justify-content: center;
      .video-play {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: rgba( 0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 145px;
        z-index: 100;
        margin-top: 110px;
      }
      video {
        min-width: 350px;
        max-width: 350px;
        min-height: 350px;
        max-height: 350px;
      }
      .close {
        position: absolute;
        top: 10px;
        right: 10px;
      }
    }
  }
  .goods-image {
    width: 350px;
    /*height: 350px;*/
    position: relative;
    z-index: 500;
    .large {
      position: absolute;
      top: 0;
      left: 360px;
      width: 350px;
      height: 350px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      background-repeat: no-repeat;
      background-size: 700px 700px;
      background-color: #f8f8f8;
    }
    .large2 {
      overflow: hidden;
      img {
        min-width: 700px;
        max-width: 700px;
        min-height: 700px;
        max-height: 700px;
        object-fit: contain;
        position: absolute;
      }
    }
    .middle {
      width: 350px;
      height: 350px;
      background: #ffffff;
      position: relative;
      cursor: move;
      box-shadow: 0px 0px 8px #dddee1;
      img {
        min-width: 100%;
        max-width: 100%;
        min-height: 100%;
        max-height: 100%;
        object-fit: contain;
      }
      .layer {
        width: 175px;
        height: 175px;
        background: rgba(0,0,0,.2);
        left: 0;
        top: 0;
        position: absolute;
      }
    }
    .small {
      margin-top: 10px;
      display: flex;
      width: 350px;
      height: 72px;
      overflow: auto;
      overflow-y: hidden;
      background-color: #ffffff;
      > div {
        width: 68px;
        height: 68px;
        box-shadow: 0px 0px 3px #dddddd;
        cursor: pointer;
        margin: 2px 5px;
        flex-shrink: 0;
      }
      > div img {
        min-width: 100%;
        max-width: 100%;
        min-height: 100%;
        max-height: 100%;
        object-fit: contain;
      }
    }
    .small::-webkit-scrollbar {
      width: 1px;
      height: 4px;
    }
  }
</style>
