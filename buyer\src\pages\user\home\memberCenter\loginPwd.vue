<template>
  <div>
    <Card _Title="修改密码" :_Size="16"></Card>
    <a-form ref="loginPwdFormRef" size="large" layout="horizontal" :style="{ width: '400px'}" auto-label-width @submit-success="handleSubmit" :model="loginPwdForm">
      <a-form-item field="password" label="旧密码" :rules="[REQUIRED, VARCHAR20, LEAST6]">
        <a-input-password v-model="loginPwdForm.password" value="large" allow-clear placeholder="请输入旧密码">
          <template #prefix>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24">
              <path fill="currentColor" d="M19 10h1a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V11a1 1 0 0 1 1-1h1V9a7 7 0 0 1 14 0zm-2 0V9A5 5 0 0 0 7 9v1zm-6 4v4h2v-4z"/>
            </svg>
          </template>
        </a-input-password>
      </a-form-item>
      <a-form-item field="newPassword" label="新密码" :rules="[REQUIRED, VARCHAR20, LEAST6]">
        <a-input-password v-model="loginPwdForm.newPassword" value="large" allow-clear placeholder="请输入新密码">
          <template #prefix>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24">
              <path fill="currentColor" d="M19 10h1a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V11a1 1 0 0 1 1-1h1V9a7 7 0 0 1 14 0zm-2 0V9A5 5 0 0 0 7 9v1zm-6 4v4h2v-4z"/>
            </svg>
          </template>
        </a-input-password>
      </a-form-item>
      <a-form-item field="againPassword" label="确认密码" :rules="[REQUIRED, VARCHAR20, LEAST6]">
        <a-input-password v-model="loginPwdForm.againPassword" value="large" allow-clear placeholder="请输入确认密码">
          <template #prefix>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24">
              <path fill="currentColor" d="M19 10h1a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V11a1 1 0 0 1 1-1h1V9a7 7 0 0 1 14 0zm-2 0V9A5 5 0 0 0 7 9v1zm-6 4v4h2v-4z"/>
            </svg>
          </template>
        </a-input-password>
      </a-form-item>
      <a-form-item>
        <a-button html-type="submit" type="primary" status="danger" class="mr_10" :loading="loading">修改</a-button>
        <a-button @click="router.back()">返回</a-button>
      </a-form-item>
    </a-form>


  </div>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router';
  import { ref, onMounted } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { REQUIRED, VARCHAR20, LEAST6 } from '@/utils/validator';
  import { md5 } from '@/utils/md5';
  import { editPwd } from '@/api/account';

  const router = useRouter();
  const loading = ref(false);
  const loginPwdFormRef = ref();

  // 数据集
  const loginPwdForm = ref({
    password: '',
    newPassword: '',
    againPassword: '',
  });

  const handleSubmit = async () => {
    const auth = await loginPwdFormRef.value?.validate();
    if (!auth) {
      const {newPassword, againPassword, password} = loginPwdForm.value;
      if (newPassword !== againPassword) {
        Message.error({content: '新旧密码不一致'});
        return;
      }
      const params = {newPassword, password};
      params.newPassword = md5(newPassword);
      params.password = md5(password);
      editPwd(params).then(res => {
        if (res.data.message === 'success' && res.data.result) {
          Message.success('修改密码成功');
          router.push('/user/home/<USER>/accountSafe');
        }
      });
    }
  };



  onMounted(() => {

  })
</script>

<style scoped lang="less">

</style>
