<!-- eslint-disable vue/no-use-v-if-with-v-for,vue/no-confusing-v-for-v-if -->
<template>
  <a-card
    class="general-card"
    :header-style="{ paddingBottom: { value: 0, label: '' } }"
    :body-style="{
      paddingTop: '20px',
    }"
    title="统计数据"
  >
    <div class="count-list flex">
      <div class="count-item" @click="$router.push({ name: 'goods-list' })">
        <div>
          <icon-copy size="31" />
        </div>
        <div class="counts">
          <div>{{ props.res.goodsNum || 0 }}</div>
          <div>上架商品数量</div>
        </div>
      </div>
      <div class="count-item" @click="$router.push({ name: 'order' })">
        <div>
          <icon-email size="28" />
        </div>
        <div class="counts">
          <div>{{ props.res.orderPrice || 0 }}</div>
          <div>今日订单总额</div>
        </div>
      </div>
      <div class="count-item" @click="$router.push({ name: 'order-list' })">
        <div>
          <icon-align-left size="28" />
        </div>
        <div class="counts">
          <div>{{ props.res.orderNum || 0 }}</div>
          <div>今日订单数量</div>
        </div>
      </div>
      <div class="count-item" @click="$router.push({ name: 'preview' })">
        <div>
          <icon-user-group size="28" />
        </div>
        <div class="counts">
          <div>{{ props.res.storeUV || 0 }}</div>
          <div>今日访客数量</div>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script lang="ts" setup>
  import { reactive,  watch } from 'vue';

  const props = defineProps({
    res: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });

  const todoList: any = reactive({
    list: {},
  });
  // watch(
  //   () => props.res,
  //   (res) => {
  //     if (res) {
  //       // 循环判断和后端返回的字段对应后进行赋值操作
  //       Object.keys(res).forEach((key: string) => {
  //         Object.keys(todoList.list).forEach((item: any) => {
  //           if (todoList.list[item][key]?.label && item != '_value') {
  //             todoList.list[item][key].value = res[key];
  //           }
  //         });
  //       });
  //     }
  //   },
  //   { deep: true, immediate: true }
  // );
</script>

<style lang="less" scoped>
  .count-item {
    color: #fff;
    height: 84px;
    display: flex;
    cursor: pointer;
    justify-content: center;
    align-items: center;
    border-radius: 0.4em;
    flex: 1;
    margin-right: 20px;
    box-shadow: 1px 3px 12px;
    font-size: 14px;
  }
  .counts {
    margin-left: 10px;
  }
  .count-item:nth-of-type(1) {
    background-image: linear-gradient(
      109.6deg,
      rgba(255, 154, 118, 0.6) 11.2%,
      #ff9a76 100.2%
    );
    //   background:#ff9a76 ;
  }
  .count-item:nth-of-type(2) {
    background-image: linear-gradient(
      109.6deg,
      rgba(78, 137, 174, 0.6) 11.2%,
      #4e89ae 100.2%
    );

    //   box-shadow: 1px 3px 12px rgba($color: #4e89ae, $alpha: 0.3);
  }
  .count-item:nth-of-type(3) {
    background-image: linear-gradient(
      109.6deg,
      rgba(103, 155, 155, 0.6) 11.2%,
      #679b9b 100.2%
    );

    //   box-shadow: 1px 3px 12px rgba($color: #679b9b, $alpha: 0.3);
  }
  .count-item:nth-of-type(4) {
    background-image: linear-gradient(
      109.6deg,
      rgba(99, 115, 115, 0.6) 11.2%,
      #637373 100.2%
    );

    //   box-shadow: 1px 3px 12px rgba($color: #637373, $alpha: 0.3);
  }
</style>
