## 商品选择器



#### OBJECT 传参数说明
属性|说明|类型|必填
---|---|---|---
`api-params`| 附加请求参数 |function|否
`change`| 选择回调内容 |function|是
``` js
<button @click='click'>提交</button>
<goodsSkuTemplate ref="goodsSkuRef" @change='change' />

const click = () => {
  const goodsSkuRef = ref('');
  goodsSkuRef.value.init();
}

const change = (val) => {
  console.log(val)
}
  
```

``` js
  import goodsSkuTemplate from '@/components/goods-sku-selector/index.vue';
```
