<template>
  <div min-h-100px>
    <div v-if="props.res.data.list?.length">
      <div v-if="props.res.data.goodsType === 'one'" class="goods-item flex goods-item-one-row" :class="{

      }" :key="item_index" v-for="(item, item_index) in props.res.data.list">
        <div class="goods-item-img-row">
   
          <img :src="item.img || item.thumbnail" />
        </div>
        <div class="goods-item-desc-row">
          <div class="goods-title">
            {{ item.title }}
          </div>
          <div>
            <icon-close-circle-fill size="20" color="#e1251b" class="goods-icon" @click="closeGoods(item_index)" />
          </div>
          <div class="goods-bottom">
            <div class="goods-price">￥{{ item.price }}</div>
          </div>
        </div>
      </div>

      <div class="goods-list" :class="{
        'scroll-list': props.res.data.goodsType === 'scroll',
      }">
        <div v-if="props.res.data.goodsType !== 'one'" class="goods-item"
          v-for="(item, item_index) in props.res.data.list" :key="item_index" :class="{

            two: props.res.data.goodsType === 'two',
            three: props.res.data.goodsType === 'three',
            big:
              props.res.data.goodsType === 'big' || (props.res.data.goodsType === 'flag' && item.big),
            scroll: props.res.data.goodsType === 'scroll',
            flag: props.res.data.goodsType === 'flag',
          }">
          <div class="goods-img">
            <icon-close-circle-fill size="20" color="#e1251b" class="goods-icon" @click="closeGoods(item_index)" />
            <img :src="item.img || item.thumbnail" />
          </div>
          <div class="goods-desc">
            <div class="goods-title">
              {{ item.title }}
            </div>
            <div class="goods-bottom">
              <div class="goods-price">￥{{ item.price }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else flex flex-a-c h-100px flex-j-c color-gray-300>
      当前无展示的商品，此组件在买家页面中不可见
    </div>

  </div>
</template>

<script setup lang="ts">

import { DragRule } from '@/views/operation/decoration/app/models/types';
const props = defineProps<{
  res: DragRule,
}>()




function closeGoods(index: number) {
  props.res.data.list?.splice(index, 1)
}
</script>

<style scoped>
.noneBorder {
  border: none;
}

.shadow {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.border {
  border: 1px solid #dcdcdc;
}

.layout {
  padding: 8px 0;
  background: #e8e8e8;
}

.goods-cell-title {
  padding: 10px;
  transition: 0.35s;
  display: flex;
}

.goods-cell-title>.goods-item-title {
  flex: 1;
  text-align: center;
}

.goods-cell-title>.goods-item-title h4 {
  font-size: 16px;
  color: #666;
}

.goods-cell-title>.goods-item-title div {
  color: #999;
  font-size: 12px;
}

.goods-list {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.goods-item {
  margin-bottom: 10px;
  border-radius: 0.4em;
  overflow: hidden;
}

.two,
.flag {
  width: 50%;
}

.scroll {
  overflow-x: auto;
  width: 33.33%;
}

.scroll .goods-img {
  width: 108px;
  height: 108px;
}

.scroll .goods-desc {
  width: 108px;
}

.scroll .goods-item {
  margin-bottom: 10px;
}

.scroll-list {
  flex-wrap: nowrap;
  display: -webkit-box;
  overflow-x: auto !important;
}

.big {
  width: 100%;
}

.big .goods-img {
  width: 330px;
  height: 330px;
}

.big .goods-img img {
  width: 330px !important;
}

.big .goods-desc {
  width: 330px;
}

.three {
  width: 33.33%;
}

.three .goods-img {
  width: 108px;
  height: 108px;
}

.three .goods-desc {
  width: 108px;
}

.three .goods-item {
  margin-bottom: 10px;
}

.goods-img {
  position: relative;
  margin: 0 auto;
  width: 170px;
  height: 170px;
  border-top-left-radius: 0.4em;
  border-top-right-radius: 0.4em;
  overflow: hidden;
}

.goods-img img {
  width: 100%;
  height: 100%;
}

.goods-desc {
  border-bottom-left-radius: 0.4em;
  border-bottom-right-radius: 0.4em;
  width: 170px;
  background: #fff;
  padding: 4px;
  margin: 0 auto;
}

.goods-desc .goods-title {
  font-size: 12px;
  height: 28px;
  display: -webkit-box;
  font-weight: 500;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.goods-desc .goods-bottom {
  display: flex;
}

.goods-desc .goods-bottom .goods-price {
  line-height: 2;
  color: #e1251b;
}

.goods-icon {
  right: 5px;
  top: 5px;
  position: absolute
}

.goods-item-img-row {
  flex: 1;
  width: 170px;
  height: 170px;
}

.goods-item-img-row img {
  width: 100%;
  height: 100%;
}

.goods-item-one-row {
  width: 100%;
  background: #fff;
  margin-bottom: 0 !important;
}

.goods-item-desc-row .goods-title {
  font-size: 12px;
  height: 38px;
  display: -webkit-box;
  font-weight: 500;
  -webkit-box-orient: vertical;

  -webkit-line-clamp: 2;

  overflow: hidden;
}

.goods-item-desc-row .goods-bottom {
  display: flex;
}

.goods-item-desc-row .goods-bottom .goods-price {
  line-height: 2;
  color: red;
}

.goods-item-desc-row {
  padding: 10px;
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
</style>
