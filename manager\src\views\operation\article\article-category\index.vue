<template>
  <a-card class="general-card" title="文章分类" :bordered="false">
    <a-row style="margin-bottom: 16px">
      <a-col :span="16">
        <a-space>
          <a-button type="primary" @click="addParent">添加一级分类</a-button>
        </a-space>
      </a-col>
    </a-row>
    <tablePage :dataList="labelData" ref="articleClassRef" :columns="articleClassColumns" :methods="articleClassMethods" :enablePagination="false" :bordered="true">
      <template #children="{ data }">{{ data.articleCategoryName }}</template>
      <template #operation="{ data }">
        <a-button style="margin-right: 10px;" v-if="data.level != 1" @click="detailChildCategory(data)" type="text">添加子分类</a-button>
        <a-button style="margin-right: 10px;" @click="TbaleDataEdit(data)" type="text" status="warning">编辑</a-button>
        <a-button @click="handleDelete(data)" type="text" status="danger">删除</a-button>
      </template>
    </tablePage>
    <!--添加文章分类弹窗-->
    <a-modal v-model:visible="articleClassFormData.articleClassVisible" :align-center="false" :footer="false">
      <template #title>{{articleClassFormData.modalTitle}}</template>
      <a-form ref="formAddRef" :model="articleClassFormData.formAdd" :style="{ width: '450px' }">
        <template v-if="articleClassFormData.showParent">
          <a-form-item field="parentId" label="上级分类">
            {{articleClassFormData.parentTitle}}
            <a-input v-model="articleClassFormData.formAdd.parentId" style="display: none"></a-input>
          </a-form-item>
        </template>
        <a-form-item field="articleCategoryName" label="分类名称" :rules="[REQUIRED]">
          <a-input v-model="articleClassFormData.formAdd.articleCategoryName" :style="{ width: '320px' }" />
        </a-form-item>
        <a-form-item field="sort" label="排序值" :rules="[REQUIRED]">
          <a-input-number v-model="articleClassFormData.formAdd.sort" :style="{ width: '320px' }"/>
        </a-form-item>
        <a-form-item label="操作">
          <a-button @click="articleClassFormSubmit" type="primary">保存</a-button>
        </a-form-item>
      </a-form>
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">
  import{ getNoticeMessageData } from '@/api/operation';
  import tablePage from '@/components/table-pages/index.vue';
  import { MethodsRule, ColumnsDataRule } from '@/types/global';
  import { getArticleCategory,saveArticleCategory, delArticleCategory, updateArticleCategory, } from '@/api/operation';
  import useCurrentInstance from '@/hooks/useCurrentInstance';
  import { Message } from '@arco-design/web-vue';
  import { ref, onMounted, reactive } from 'vue';
  import { REQUIRED } from '@/utils/validator';

  // 获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;
  const tablePageRef = ref<any>();
  const formAddRef = ref<any>();
  const articleClassRef = ref('');
  const labelData = ref([]);
  const articleClassColumns : ColumnsDataRule[] = [
    {title: '分类名称', dataIndex: 'articleCategoryName', width: 150},
    {title: '排序', dataIndex: 'sort', width: 150},
  ];
  const articleClassMethods :MethodsRule = {
    title: '操作',
    width: 250,
    fixed: 'right',
    methods: [
      {title: '操作', slot: true, slotTemplate: 'operation'}
    ]
  };
  interface formInterface {
    articleClassVisible: boolean,
    parentTitle: string,
    formAdd: any,
    modalType: any,
    modalTitle: string,
    showParent: boolean,
  }
  const articleClassFormData :formInterface = reactive({
    articleClassVisible: false, // 添加文章分类弹窗
    parentTitle: '',  // 父级菜单名称
    modalType: 0, // 添加或编辑标识 0:添加 1:编辑
    modalTitle: '添加一级分类', // 添加或编辑标题
    showParent: false,  // 是否展示上级菜单
    // 文章分类表单对象初始化数据
    formAdd: {
      parentId: "",
      sort: 1,
      level: 0,
      articleCategoryName: "",
    }
  })

  // 获取分类数据
  const getData = () => {
    getArticleCategory({}).then(res => {
      if(res.data.success) {
        labelData.value = res.data.result.map((item: any) => {
          if (item.children && item.children.length) {
            item.children.map((childItem: any) => {
              childItem.children = null;
              return childItem;
            });
          } else {
            item.children = null
          }
          return item;
        });
      }
    })
  };

  // 添加一级分类
  const addParent = () => {
    articleClassFormData.modalType = 0;
    articleClassFormData.parentTitle = '顶级分类';
    articleClassFormData.showParent = true;
    articleClassFormData.modalTitle = '添加一级分类';
    articleClassFormData.articleClassVisible = true;
    formAddRef.value.resetFields();
    delete  articleClassFormData.formAdd.id;
    articleClassFormData.formAdd.parentId = '0';
    articleClassFormData.formAdd.level = 0;
  }
  // 添加子分类
  const detailChildCategory = (data: any) => {
    articleClassFormData.modalType = 0;
    articleClassFormData.parentTitle = data.articleCategoryName;
    articleClassFormData.showParent = true;
    articleClassFormData.modalTitle = '添加子分类';
    articleClassFormData.articleClassVisible = true;
    articleClassFormData.formAdd.parentId = data.id;
    articleClassFormData.formAdd.level = data.level + 1;
    articleClassFormData.formAdd.articleCategoryName = "";
    delete  articleClassFormData.formAdd.id;
  }
  // 编辑分类
  const TbaleDataEdit = (data: any) => {
    articleClassFormData.modalType = 1;
    articleClassFormData.parentTitle = data.articleCategoryName;
    articleClassFormData.showParent = false;
    articleClassFormData.modalTitle = '编辑';
    articleClassFormData.articleClassVisible = true;
    articleClassFormData.formAdd.parentId = data.parentId;
    articleClassFormData.formAdd.level = data.level;
    articleClassFormData.formAdd.articleCategoryName = data.articleCategoryName;
    articleClassFormData.formAdd.id = data.id;
  }
  const articleClassFormSubmit = async () => {
    const auth = await formAddRef.value?.validate();
    if (!auth) {
      if (articleClassFormData.modalType === 0) {
        // 添加 避免编辑后传入id等数据 记得删除
        delete articleClassFormData.formAdd.id;
        saveArticleCategory(articleClassFormData.formAdd).then(res => {
          if (res.data.success) {
            Message.success('添加成功');
            articleClassFormData.articleClassVisible = false;
            // articleClassRef.value.init(); // 更新表格数据
            getData();
            // 文章分类表单对象初始化数据
            articleClassFormData.formAdd = {
              parentId: "",
              sort: 1,
              level: 0,
              articleCategoryName: "",
            }
          }
        })
      } else if (articleClassFormData.modalType === 1) {
        // 编辑
        updateArticleCategory(articleClassFormData.formAdd.id, articleClassFormData.formAdd).then(res => {
          if (res.data.success) {
            Message.success('修改成功');
            articleClassFormData.articleClassVisible = false;
            // articleClassRef.value.init(); // 更新表格数据
            getData();
            formAddRef.value.resetFields(); // 清空表单
          }
        })
      }
    }
  };
  // 回调删除
  function handleDelete(data: any) {
    modal.confirm({
      title: '确认删除',
      content: `您确认要删除${data.articleCategoryName}?`,
      alignCenter: false,
      onOk: async () => {
        const res = await delArticleCategory(data.id);
        if (res.data.success) {
          Message.success('删除成功！');
          // articleClassRef.value.init();
          getData();
        }
      },
    });
  }
  // 初始化
  onMounted(() => {
    getData();
  });
</script>

