.home {
  min-height: 100vh;
  background-color: @light_background_color;
  overflow: hidden;

}
.home-container {
  width: 1200px;
  min-height: 90vh;
  margin: 20px auto;
  display: flex;
  justify-content: space-between;
  align-items: stretch;
}
// 导航栏
.side-bar {
  width: 200px;
  background-color: @light_white_background_color;
  .user-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: start;
    padding: 30px 0 40px;
    > div:nth-last-of-type(1) {
      margin-top: 10px;
      color: @text_color;
    }
  }
}
// 主体
.main-body {
  width: 990px;
  background-color: @light_white_background_color;
  box-sizing: border-box;
  padding: 20px 40px;
}
