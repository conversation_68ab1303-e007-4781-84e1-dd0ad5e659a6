<template>
  <div :ref="drag" role="Box" :style:any="{ ...style, opacity }" :data-id="`box-${result.type}`">
    {{ result.name }}
  </div>
</template>

<script lang="ts" setup>
import { useDrag } from 'vue3-dnd'

import { computed, unref } from 'vue'
import { toRefs } from '@vueuse/core'
import { DragRule } from '../models/types'


const props = defineProps<{ result: DragRule, index: number }>()

const style = {
  border: '1px dashed gray',
  backgroundColor: 'white',
  padding: '0.5rem 1rem',
  marginRight: '1.5rem',
  marginBottom: '1.5rem',
  cursor: 'move',
  float: 'left',
}

const [collect, drag] = useDrag(() => ({
  type: 'box',
  item: JSON.parse(JSON.stringify({ ...props.result, })),
  options: {
    dropEffect: 'move',
  },
  end: (item, monitor) => {
    const dropResult = monitor.getDropResult()
    if (item && dropResult) {
      //说明把值赋值上去了
    }
  },
  collect: monitor => ({
    isDragging: monitor.isDragging(),
    handlerId: monitor.getHandlerId(),
  }),
}))

const { isDragging } = toRefs(collect)

const opacity = computed(() => (unref(isDragging) ? 0.4 : 1))
</script>

<style lang="less" scoped>
.box {
  float: left;
  margin-right: 1.5rem;
  margin-bottom: 1.5rem;
  padding: 0.5rem 1rem;
  background-color: white;
  border: 1px solid gray;
  cursor: move;

  &.dragging {
    opacity: 0.4;
  }
}
</style>
