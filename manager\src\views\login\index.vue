<template>
  <div class="wrapper" :style="background" @click="closeVerify">
    <div class="login-box" v-auto-animate>
      <div class="logo" v-if="domain_logo" ><img :src="domain_logo" /></div>
      <a-form size="large" layout="vertical" :model="form" :style="{ width: '400px' }" @submit-success="handleSubmit">
        <a-form-item :hide-asterisk="true" field="username" :rules="[REQUIRED, VARCHAR20]">
          <a-input v-model="form.username" size="large" allow-clear placeholder="请输入用户名">
            <template #prefix>
              <icon-user />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item :hide-asterisk="true" field="password" :rules="[REQUIRED, VARCHAR20]">
          <a-input-password v-model="form.password" value="large" allow-clear placeholder="请输入密码">
            <template #prefix>
              <icon-lock />
            </template>
          </a-input-password>
        </a-form-item>

        <a-button html-type="submit" class="login" type="primary" :loading="loading">登录</a-button>
      </a-form>
    </div>
    <!-- 拼图验证码 -->
    <verify ref="verifyDom" class="verify" verify-type="LOGIN" @on-change="verifyChange"></verify>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import { useRouter } from 'vue-router';
/** 引入 api components  */
import { useUserStore, useAppStore } from '@/store';
import storage from '@/utils/storage';
import { login, getUserInfo } from '@/api/login';
import verify from '@/components/verify/index.vue';
import { md5 } from '@/utils/md5';
import getAssetsImages from '@/utils/assetsImages';
import { VARCHAR20, REQUIRED } from '@/utils/validator';
import { initSiteInfo } from '@/hooks/fetchBaseSite';

import { initRouterNode } from '@/hooks/roleRouter';
const form = reactive({
  username: 'admin',
  password: '123456',
});
const loading = ref(false);
const verifyDom = ref<any>({
  verifyShow: false,
});
const router = useRouter();
const background = {
  background: `url(${getAssetsImages('wave.svg')}) no-repeat bottom`,
};
const domain_logo = ref<string>('');
// 点击登录
function handleSubmit() {
  verifyDom.value.init();
}

// 验证成功之后执行登录方法
async function executeLogin(res: any) {
  loading.value = true;
  const { accessToken, refreshToken } = res.data.result;
  storage.setAccessToken(accessToken);
  storage.setRefreshToken(refreshToken);
  const store = useUserStore();
  const storeInfo = await getUserInfo();
  store.userInfo = storeInfo.data.result;
  const { redirect, ...othersQuery } = router.currentRoute.value.query;
  const appStore: any = useAppStore();
  if(appStore.route.length){
    appStore.updateRoute([])
  }

  await initRouterNode(true);



  await router.push({
    name: (redirect as string) || 'Workplace',
    query: {
      ...othersQuery,
    },
  });
}
// 验证是否正确
async function verifyChange(callback: any) {
  if (!callback.status) return;
  try {
    const res = await login({
      username: form.username,
      password: md5(form.password),
    });
    if (res.data.success) {
      Message.loading('验证成功!正在登录中...');
      await executeLogin(res);
    }
  } catch (error) {
    loading.value = false;
  }
  verifyDom.value && (verifyDom.value.verifyShow = false);
}

function closeVerify() {
  verifyDom.value && (verifyDom.value.verifyShow = false);
}


const init = () => {
  initSiteInfo()
  domain_logo.value = localStorage.getItem("manager_logo") || getAssetsImages('logo.png')
};

onMounted(() => {
  init();
})
</script>

<style lang="less" scoped>
.wrapper {
  display: flex;
  width: 100%;
  height: 100vh;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.login-box {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.arco-form-item {
  padding: 2px 0;
}

.login {
  margin-top: 20px;
}

.arco-input-wrapper,
.arco-btn {
  height: 40px;
}

.verify {
  position: absolute;

  z-index: 10;
}

.logo {
  >img {
    min-width: 400px;
    max-width: 400px;
  }
}
</style>
