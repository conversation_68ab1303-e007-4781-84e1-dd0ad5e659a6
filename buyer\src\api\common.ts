import qs from 'query-string';
// eslint-disable-next-line import/no-cycle
import request, { Method, commonUrl } from '@/utils/axios';
import { ParamsRule } from '@/types/global';

const uploadFile = `${commonUrl}/common/common/upload/file`;
export default uploadFile;

/**
 * 发送短信验证码
 */
export function sendSms(params: any) {
  return request({
    url: `${commonUrl}/common/common/sms/${params.verificationEnums}/${params.mobile}`,
    method: Method.GET,
    needToken: false,
    params
  });
}

// 地区数据，用于三级联动
export function getRegion (id: number | string) {
  return request({
    url: `${commonUrl}/common/common/region/item/${id}`,
    needToken: true,
    method: Method.GET
  });
}











/**
 * 获取拼图验证
 */
export function getVerifyImg (verificationEnums: number | string) {
  return request({
    url: `${commonUrl}/common/common/slider/${verificationEnums}`,
    method: Method.GET,
    needToken: false
  });
}
/**
 * 验证码校验
 */
export function postVerifyImg (params: ParamsRule) {
  return request({
    url: `${commonUrl}/common/common/slider/${params.verificationEnums}`,
    method: Method.POST,
    needToken: false,
    params
  });
}

/**
 * 分页获取文章列表
 * @param cateId 文章分类id
 */
export function articleList (params: ParamsRule) {
  return request({
    url: `/other/article`,
    method: Method.GET,
    params
  });
}

/**
 * 获取帮助中心文章分类列表
 * @param cateId 文章分类id
 */
export function articleCateList () {
  return request({
    url: `/other/article/articleCategory/list`,
    method: Method.GET
  });
}

// 通过id获取文章
export function articleDetail (id: number | string) {
  return request({
    url: `/other/article/get/${id}`,
    method: Method.GET
  });
}

// 获取IM接口前缀
export function getIMDetail () {
  return request({
    url: `${commonUrl}/common/common/IM`,
    method: Method.GET
  });
}

//获取图片logo
export function getBaseSite(){
  return request ({
    url:`${commonUrl}/common/common/site`,
    method: Method.GET,
    needToken: false
  })
}
