.search {
  .oss-operation {
    margin-bottom: 2vh;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    button {
      margin-right: 5px;
    }
  }
}

.none {
  display: none;
}

.oss-wrapper {
  display: flex;
  flex-wrap: wrap;
  position: relative;
}

.oss-card {
  margin: 10px 20px 10px 0;
  width: 290px;

  :hover {
    .content .other .name {
      color: #1890ff;
      transition: color .3s;
    }
  }

  cursor: pointer;

  .content {
    display: flex;
    flex-direction: column;

    :hover {
      .play {
        transition: opacity .3s;
        opacity: 1 !important;
      }
    }

    .img {
      height: 135px;
      object-fit: cover;
    }

    .video {
      height: 135px;
      position: relative;

      .cover {
        height: 100%;
        width: 100%;
        object-fit: fill;
      }

      .play {
        position: absolute;
        top: 43px;
        left: 117px;
        height: 50px;
        width: 50px;
        opacity: 0.8;
      }
    }

    .other {
      padding: 16px;
      height: 135px;

      .name {
        font-size: 16px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        color: rgba(0, 0, 0, .85);
        font-weight: 500;
        margin-bottom: 4px;
      }

      .key {
        overflow: hidden;
        text-overflow: ellipsis;
        height: 45px;
        word-break: break-all;
        color: rgba(0, 0, 0, .45);
      }

      .info {
        font-size: 12px;
        color: rgba(0, 0, 0, .45);
        overflow: hidden;
        text-overflow: ellipsis;
        height: 36px;
        word-break: break-all;
      }
    }

    .actions {
      display: flex;
      align-items: center;
      height: 50px;
      background: #f7f9fa;
      border-top: 1px solid #e8e8e8;

      i:hover {
        color: #1890ff;
      }

      .btn {
        display: flex;
        justify-content: center;
        width: 33.33%;
        border-right: 1px solid #e8e8e8;
      }

      .btn-no {
        display: flex;
        justify-content: center;
        width: 33.33%;
      }
    }
  }
}
.select-clear{
  color: #2D8cF0;
  cursor: pointer;
}
.aaa{
  color: red;
}

.oss-btn {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}


// OSS资源
.oss-manage {
  /*height: 632px;*/
  width: 100%;
  display: flex;
  flex-wrap: nowrap;
  align-content: stretch;
  > div:nth-of-type(1) {
    width: 200px;
    flex-shrink: 0;
    align-self: stretch;
  }
  > div:nth-of-type(2) {
    width: 100%;
  }
  // 文件列表
  .file-list {
    height: 100%;
    box-sizing: border-box;
    border-right: 1px solid #e5e6eb;
    padding: 0 0 24px;
  }
  // 图片列表
  .pic-list {
    height: 100%;
    box-sizing: border-box;
    padding: 0 0 24px 0;
  }
  .search-box {
    display: flex;
    /*flex-direction: row-reverse;*/
    /*justify-content: space-between;*/
    justify-content: flex-end;
  }
  .img-box {
    width: 100%;
    /*height: 500px;*/
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: flex-start;
    align-content: flex-start;
    margin-top: 20px;
    .img-item {
      width: 120px;
      height: 158px;
      box-sizing: border-box;
      /*margin: 0 26px 8px 0;*/
      margin: 0 13px 8px;
      .card {
        width: 120px;
        height: 120px;
        border-radius: 8px;
        border: 2px solid transparent;
        overflow: hidden;
        box-sizing: border-box;
        position: relative;
        img {
          width: 100%;
          height: 100%;
        }
        .checkbox {
          position: absolute;
          top: 10px;
          right: 10px;
          z-index: 1000;
        }
        .preview {
          width: 100%;
          height: 26px;
          background-color: #ffffff;
          text-align: center;
          line-height: 30px;
          color: #666666;
          position: absolute;
          left: 0;
          bottom: 0;
          display: flex;
          flex-wrap: nowrap;
          > div {
            width: 100%;
            flex: 1;
          }
        }
      }
      .card:hover,
      .custom-checkbox-card-checked {
        border: 2px solid #1966ff;
      }
      .text {
        width: 120px;
        height: 36px;
        align-items: center;
        display: flex;
        justify-content: center;
        cursor: pointer;
        div {
          color: #252931;
          font-size: 14px;
          line-height: 36px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
  .pagination-box {
    display: flex;
    flex-direction: row-reverse;
  }

  .custom-checkbox-card {
    border: 1px solid var(--color-border-2);
    border-radius: 4px;
    width: 40px;
    height: 40px;
    box-sizing: border-box;
    position: relative;
  }

  .custom-checkbox-card-mask {
    height: 14px;
    width: 14px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    border: 1px solid var(--color-border-2);
    box-sizing: border-box;
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #ffffff;
  }

  .custom-checkbox-card-mask-dot {
    width: 8px;
    height: 8px;
    border-radius: 2px;
  }

  .custom-checkbox-card-title {
    color: var(--color-text-1);
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 8px;
  }

  .custom-checkbox-card:hover,
  .custom-checkbox-card-checked,
  .custom-checkbox-card:hover .custom-checkbox-card-mask,
  .custom-checkbox-card-checked .custom-checkbox-card-mask {
    border-color: rgb(var(--primary-6));
  }

  .custom-checkbox-card-checked {
    background-color: var(--color-primary-light-1);
  }

  .custom-checkbox-card:hover .custom-checkbox-card-title,
  .custom-checkbox-card-checked .custom-checkbox-card-title {
    color: rgb(var(--primary-6));
  }

  .custom-checkbox-card-checked .custom-checkbox-card-mask-dot {
    background-color: rgb(var(--primary-6));
  }
  :deep(.arco-tree-node-selected) {
    background-color: #f2f3f5;
  }
  :deep(.arco-tree-node) {
    height: 36px;
    color: #19191a;
    font-size: 14px;
  }
  :deep(.arco-tree-node:hover) {
    background-color: #f2f3f5;
  }
  .btnBox {
    margin: 10px 15px 20px 0;
  }
}
