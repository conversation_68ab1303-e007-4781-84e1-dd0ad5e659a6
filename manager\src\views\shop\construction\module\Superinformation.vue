<template>
  <div>
    <div align="right">
      <a-button type="primary" status="warning" @click="nextStep('2')" size="small">上一步</a-button>
      <a-button type="primary" style="margin-left: 20px;" @click="nextStep('4')" size="small">下一步</a-button>
    </div>
    <a-form ref="contactInfoFromRef" :model="mainForm">
        <a-form-item field="contact_info.contact_type" label="超级管理员类型" :rules="[REQUIRED]">
          <a-select :style="{ width: '280px' }" v-model="mainForm.contact_info.contact_type" placeholder="请选择超级管理员类型">
            <a-option value="66">经办人</a-option>
            <a-option value="65">经营者/法人</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="contact_info.contact_name" label="超级管理员姓名" :rules="[REQUIRED]">
          <a-input style="width: 400px;" v-model="mainForm.contact_info.contact_name" placeholder="请输入超级管理员姓名"/>
        </a-form-item>
        <a-form-item field="contact_info.contact_id_doc_type" label="超级管理员证件类型" :rules="[REQUIRED]" v-if="mainForm.contact_info.contact_type=='66'">
          <a-select :style="{ width: '280px' }" v-model="mainForm.contact_info.contact_id_doc_type" placeholder="请选择超级管理员证件类型">
            <a-option v-for="item in documentType" :key="item.value" :value="item.value">{{ item.title }}</a-option>
          </a-select>
        </a-form-item>
        <div v-if="mainForm.contact_info.contact_type === '66'">
          <a-form-item field="contact_info.contact_id_card_number" label="超级管理员身份证件号码" :rules="[REQUIRED]">
            <a-input style="width: 400px;" v-model="mainForm.contact_info.contact_id_card_number" placeholder="超级管理员身份证件号码"/>
            <template #extra>
              <div><p>1.可传身份证、来往内地通行证、来往大陆通行证、护照等证件号码。</p></div>
            </template>
          </a-form-item>
          <a-form-item field="contact_info.contact_id_doc_copy" label="超级管理员证件正面照片" :rules="[REQUIRED]">
            <div>
              <a-avatar :size="100" shape="square" @click="handleClickImg('contact_id_doc_copy')" style="margin-right: 20px;">
                <icon-plus v-if="!mainForm.contact_info.contact_id_doc_copy" />
                <img alt="avatar" v-if="mainForm.contact_info && mainForm.contact_info.contact_id_doc_copy" :src="mainForm.contact_info.contact_id_doc_copy" />
              </a-avatar>
            </div>
            <template #extra>
              <div>
                <p>1.可上传1张图片，请填写通过图片上传API预先上传图片生成好的MediaID。</p>
                <p>2.、请上传彩色照片or彩色扫描件or复印件（需加盖公章鲜章），可添加“微信支付”相关水印（如微信支付认证）</p>
              </div>
            </template>
          </a-form-item>
          <a-form-item field="contact_info.contact_id_doc_copy_back" label="超级管理员证件反面照片" :rules="[REQUIRED]">
            <div>
              <a-avatar :size="100" shape="square" @click="handleClickImg('contact_id_doc_copy_back')" style="margin-right: 20px;">
                <icon-plus v-if="!mainForm.contact_info.contact_id_doc_copy_back" />
                <img alt="avatar" v-if="mainForm.contact_info && mainForm.contact_info.contact_id_doc_copy_back" :src="mainForm.contact_info.contact_id_doc_copy_back" />
              </a-avatar>
            </div>
            <template #extra>
              <div>
                <p>1.可上传1张图片，请填写通过图片上传API预先上传图片生成好的MediaID。</p>
                <p>2.、请上传彩色照片or彩色扫描件or复印件（需加盖公章鲜章），可添加“微信支付”相关水印（如微信支付认证）</p>
              </div>
            </template>
          </a-form-item>
          <a-form-item field="contact_info.contact_id_doc_period_begin" label="超级管理员证件有效期开始时间" :rules="[REQUIRED]">
            <a-date-picker
                v-model="mainForm.contact_info.contact_id_doc_period_begin"
                style="width: 200px"
                placeholder="选择超级管理员证件有效期开始时间"
                @change="(event) => { mainForm.contact_info.contact_id_doc_period_begin = event }"
            ></a-date-picker>
          </a-form-item>
          <a-form-item field="contact_info.contact_id_doc_period_end" label="超级管理员证件有效期结束时间" :rules="[REQUIRED]">
            <a-date-picker
                v-model="mainForm.contact_info.contact_id_doc_period_end2"
                style="width: 200px"
                placeholder="选择超级管理员证件有效期结束时间"
                @change="(event) => { mainForm.contact_info.contact_id_doc_period_end2 = event; mainForm.contact_info.contact_id_doc_period_end = '' }"
            ></a-date-picker>
            <a-button @click="longTimeEffect('contact_id_doc_period_end')" :status="mainForm.contact_info.contact_id_doc_period_end === '长期'?'danger':'primary'"
                      style="margin-left: 10px;">长期有效</a-button>
          </a-form-item>
          <a-form-item field="contact_info.business_authorization_letter" label="业务办理授权函" :rules="[REQUIRED]">
            <div>
              <a-avatar :size="100" shape="square" @click="handleClickImg('business_authorization_letter')" style="margin-right: 20px;">
                <icon-plus v-if="!mainForm.contact_info.business_authorization_letter" />
                <img alt="avatar" v-if="mainForm.contact_info && mainForm.contact_info.business_authorization_letter" :src="mainForm.contact_info.business_authorization_letter" />
              </a-avatar>
            </div>
            <template #extra>
              <div>
                <p>1、请参照<a-link @click="Examplediagram()" :hoverable="false" style="font-size: 12px;">示例图</a-link>
                  打印业务办理授权函，全部信息需打印，不支持手写商户信息，并加盖公章。</p>
                <p>2、可上传1张图片,请填写通过图片上传API预先上传图片生成好的MediaID。</p>
              </div>
            </template>
          </a-form-item>
        </div>
        <a-form-item field="contact_info.mobile_phone" label="联系手机">
          <a-input style="width: 400px;" v-model="mainForm.contact_info.mobile_phone" placeholder="请输入联系手机"/>
          <template #extra><div>用于接收微信支付的重要管理信息及日常操作验证码</div></template>
        </a-form-item>
        <a-form-item field="contact_info.contact_email" label="联系邮箱">
          <a-input style="width: 400px;" v-model="mainForm.contact_info.contact_email" placeholder="请输入联系邮箱"/>
          <template #extra>
            <div>
              <p>1、小微选填，个体工商户/企业/党政、机关及事业单位/其他组织必填；</p>
              <p>2、用于接收微信支付的开户邮件及日常业务通知；</p>
            </div>
          </template>
        </a-form-item>
      </a-form>


    <a-modal v-model:visible="showOssManager" :width="1100"  @ok="handleOss" title="oss资源管理" @cancel="showOssManager = false">
      <ossManages :initialize="showOssManager" @selected="changOssImage" :isSingle="true"></ossManages>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, watch, computed, ref } from 'vue';
  import { REQUIRED, VARCHAR20 } from '@/utils/validator';
  import ossManages from '@/components/oss-manage/index.vue';
  import { useAppStore } from '@/store';

  const appStore = useAppStore();
  const emit = defineEmits(['callbackTab']);
  const props = defineProps({
    submitFrom: {
      type: Object,
      default: () => {},
    }
  });

  const contactInfoFromRef = ref<any>('');
  const mainForm = ref<any>({
    contact_info: {}
  });  // 表单主体内容
  const showOssManager = ref<boolean>(false); // oss弹框
  const selectedFormBtnName = ref<string>(''); // 点击图片绑定form
  const picIndex = ref<any>(0); // 存储身份证图片下标，方便赋值
  const selectedSku = ref(); // 选择的sku

  const documentType = computed(() => {
    return appStore.weChatApplyDocTypeList;
  });

  // 设置为长期有效
  const longTimeEffect = (type?: any) => {
    if (type === 'contact_id_doc_period_end') {
      mainForm.value.contact_info.contact_id_doc_period_end = '长期';
      mainForm.value.contact_info.contact_id_doc_period_end2 = '';
    }
  };
  const Examplediagram = () => {
    window.open("https://kf.qq.com/faq/220509Y3Yvym220509fQvYR7.html");
  };
  // 选择图片
  const handleClickImg = (val: string, index?: number) => {
    selectedFormBtnName.value = val;
    picIndex.value = index;
    showOssManager.value = true;
  };
  // oss资源改变
  const changOssImage = (val:any) => {
    selectedSku.value = [];
    val.forEach((item:any)=>{
      selectedSku.value.push({url:item.url})
    })
  };
  // oss资源确定
  const handleOss = () => {
    showOssManager.value = false;
    let currentUrl = selectedSku.value[selectedSku.value.length -1].url;  // 当前选择的图片
    mainForm.value.contact_info[selectedFormBtnName.value] = currentUrl;
  };

  // 上一步/下一步
  const nextStep = (name) => {
    emit("callbackTab", name);
  };
  // 表单校验
  const checkoutForm = async () => {
    const auth = await contactInfoFromRef.value?.validate();
    if (!auth) {
      return true;
    } else {
      return false;
    }
  };
  // 组件暴露自己的属性
  defineExpose({
    checkoutForm
  });

  watch(() => props.submitFrom, (val) => {
    mainForm.value = val;
    // 超级管理员证件有效结束时间
    if (val.contact_info.contact_id_doc_period_end && val.contact_info.contact_id_doc_period_end !== '长期') {
      mainForm.value.contact_info.contact_id_doc_period_end2 = val.contact_info.contact_id_doc_period_end;
    }
  }, { immediate: true, deep: true });
</script>

<style scoped lang="less">

</style>