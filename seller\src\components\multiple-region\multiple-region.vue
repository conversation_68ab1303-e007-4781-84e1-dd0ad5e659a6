<template>
  <a-modal v-model:visible="initData.visible" :align-center="false" :body-style="{ height: '480px' }" @ok="handleOk">
    <template #title>选择地址</template>
    <div class="city-box">
      <a-tree ref="treeRef" v-model:selected-keys="initData.selectedKeys" v-model:checked-keys="initData.selectedKeys"
        :multiple="true" :checkable="true" :data="initData.cityData" :check-strictly="false" :only-check-leaf="true"
      >
      </a-tree>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>

import { getAllCity } from '@/api/statisitics';
import {
reactive,
ref,
watch,
} from 'vue';
const chosenAreaIds = ref<any>([]);
interface initDataRule {
  visible: boolean;
  cityData: any;
  selectedKeys: any;
}
const initData = reactive<initDataRule>({
  visible: false,
  cityData: [], // 初始化地区数据
  selectedKeys: [], // 默认选择的地区
});
interface PropsRule {
  isShow: boolean;
  areaIds:any;
  disabledIds: any;
}

const props = withDefaults(defineProps<PropsRule>(), {
  isShow: false,
  areaIds: [],
  disabledIds: []
})
const treeRef = ref();
const emit = defineEmits(['visibleChange','selected']);


// 初始化地区数据
const init = () => {
   
    getAllCity().then((res) => {
      res.data.result.forEach((item: any) => {
        item.children.forEach((child: any) => {
          child.title = child.name;
          child.key = child.id;
          child.disableCheckbox = props.disabledIds.includes(child.id)
        });
        const data = {
          title: item.name,
          key: item.id,
          ...item,
          disabled: item.children.every((child: any) => child.disableCheckbox === true),
        };
        initData.cityData.push(data);
      });
      console.log('初始化地区数据', initData.cityData);
    });
};

const handleOk = () => {
  // 筛选出省市
  const selectedNodes = treeRef.value
    .getCheckedNodes()
    .concat(treeRef.value.getHalfCheckedNodes());
  const provinceList = selectedNodes.filter((item: any) => {
    return item.level === 'province';
  });
  const cityList = selectedNodes.filter((item: any) => {
    return item.level === 'city';
  });
  const sort = provinceList.map((item: any) => {
    item.selectedList = [];
    item.selectedAll = false;
    cityList.forEach((cityItem: any) => {
      cityItem.selectedAll = false;
      if (item.id === cityItem.parentId) {
        item.selectedList.push(cityItem);
      }
    });
    return item;
  });
  // 判断如果当前省是否全选
  initData.cityData.forEach((whether: any) => {
    sort.forEach((item: any) => {
      // 如果当前省匹配
      if (
        item.id == whether.id &&
        item.selectedList.length == whether.children.length
      ) {
        // 给一个全选子级的标识符
        item.selectedList.forEach((child: any) => {
          child.selectedAll = true;
        });
        item.selectedAll = true;
      }
    });
  });
  emit('selected', sort);
};

watch(
  () => [initData.visible, props.isShow],
  (newValue, oldValue) => {
    if (!newValue[0]) {
      emit('visibleChange', false);
    }
    if (newValue[1] && newValue[0] !== newValue[1]) {
      initData.visible = true;
      initData.cityData = [];
      chosenAreaIds.value = props.areaIds.split(',')

      console.log('props.areaIds', props.areaIds);
      initData.selectedKeys = props.areaIds.split(',');
      init();
    }
  },
  { deep: true }
);
</script>

<style scoped lang="less"></style>
