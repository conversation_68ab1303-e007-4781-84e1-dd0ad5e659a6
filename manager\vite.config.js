// vite.config.js
import { defineConfig } from 'vite';
import unocss from 'unocss/vite';
import path from 'node:path'
import vue from '@vitejs/plugin-vue'
import Components from 'unplugin-vue-components/vite';
export default defineConfig({
  plugins: [
    vue(),
    Components(),
    unocss(),
  ],
  css: {
    modules: true,
  },
  resolve: {
    alias: {
      '~': path.resolve(__dirname, 'src'),
      '@': path.resolve(__dirname, 'src')
      // '~@': `${path.resolve(__dirname, 'src')}/`,s
    },
  },
});
