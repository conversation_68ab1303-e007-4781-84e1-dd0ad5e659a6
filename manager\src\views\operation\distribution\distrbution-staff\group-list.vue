<template>
    <div class="search">
      <a-card :style="{ width: '100%' }">
        <a-button style="margin-bottom: 10px" @click="back()">返回</a-button>
       <tablePage ref="tablePageRef" :columns="columns" :api="getDistributionGroup" :api-params="apiParams"  :bordered="true" />
      </a-card>
    </div>
  </template>
  
  <script setup lang='ts'>
  import { ref,  onMounted } from "vue"
  import { useRouter, useRoute } from 'vue-router';
  import tablePage from '@/components/table-pages/index.vue';
  import {   ColumnsDataRule } from '@/types/global';
  import { getDistributionGroup } from '@/api/operation';
  
  const tablePageRef = ref('');
  const route = useRoute();
  const apiParams = ref({
    id: route.query.id ? route.query.id : null
  });

  const columns: ColumnsDataRule[] = [
    {
      title: '昵称',
      dataIndex: 'memberName',
    },
    {
      title: '销售额',
      dataIndex: 'distributionOrderPrice',
      currency:true,
    },
    {
      title: '订单量',
      dataIndex: 'distributionOrderCount',
    },
  ]
  const router = useRouter()
  // 返回
  const back = () => {
    router.push({ name: 'distrbution-staff' })
  }
  onMounted(() => {
  })
  </script>
  
  <style lang="less" scoped>
  
  </style>
  