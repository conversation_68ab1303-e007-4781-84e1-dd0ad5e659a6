<script setup lang="ts">
import goodsModal from './mix/goodsModal.vue'
import brandModal from './mix/brandModal.vue'

const props = defineProps<{
  res: any
}>()
</script>

<template>
  <div flex flex-j-sb flex-a-c>
    <div h-343px w-584px>
      <goodsModal :res="props.res.data.leftData" />
    </div>
    <div h-343px w-584px rounded-10px class="box-shadow">
      <brandModal :res="props.res.data.rightData" />
    </div>
  </div>
</template>

<style lang="less" scoped>
.box-shadow {
  box-shadow: 0 1px 13px 0 #e5e5e5;
}
</style>
