<template>
  <a-card class="general-card" title="直播商品" :bordered="false">
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <a-row style="margin-bottom: 16px">
      <a-col :span="16">
        <a-space>
          <a-button type="outline" @click="selectGoods">
            选择商品
          </a-button>
          <a-button > 更新状态 </a-button>
        </a-space>
      </a-col>
    </a-row>
    <a-alert v-if="true" style="margin-bottom: 16px">
      由于直播商品需经过小程序直播平台的审核，你需要在此先提审商品，为了不影响直播间选取商品，请提前1天提审商品；
    </a-alert>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getLiveGoods"
      :api-params="apiParams"
      :bordered="true"
    />

    <goodsSkuTemplate ref="goodsSkuRef" />
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import goodsSkuTemplate from '@/components/goods-sku-selector/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { getLiveGoods } from '@/api/promotion';
  import { ref } from 'vue';

  const apiParams = ref({});
  const tablePageRef = ref('');
  const goodsSkuRef = ref<any>();
  const columnsSearch: Array<SearchRule> = [
    {
      label: '商品名称',
      model: 'goodsName',
      disabled: false,
      input: true,
    },
  ];

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '商品',
      dataIndex: 'goods',
      slot: true,
      slotData: {
        goods: {
          goodsImage: 'goodsImage',
          goodsName: 'goodsName',
        },
      },
    },
    {
      title: '价格',
      dataIndex: 'price',
    },

    {
      title: '库存',
      dataIndex: 'quantity',
    },
  ];

  const sortMethods: MethodsRule = {
    title: '操作',
    width: 100,
    methods: [
      {
        title: '查看/添加商品',
        callback: 'detail',
        type:'text'
      },
    ],
  };

  // 选择商品
  const selectGoods = () => {
    // 初始化商品
    goodsSkuRef.value.init();
  };
</script>
