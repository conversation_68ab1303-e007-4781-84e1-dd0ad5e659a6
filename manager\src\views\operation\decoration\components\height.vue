<template>
  <div py-16px border-b-1>
    <a-space>

      <div w-50px>高度</div>
      <div>
        <a-input-number allow-clear @clear="props.res.data.height = 0" hide-button @change="(val)=>props.res.data.height" v-model="props.res.data.height"
          :style="{ width: '150px' }" placeholder="请输入高度" :min="1" :max="1000">
          <template #suffix>
            px
          </template>
        </a-input-number>
        <a-slider mt-10px :max="1000" :min="1" v-model="props.res.data.height" :style="{ width: '200px' }" />
      </div>
    </a-space>
  </div>
</template>

<script setup lang="ts">
import { DragRule } from '@/views/operation/decoration/app/models/types';
const props = defineProps<{
  res: DragRule
}>()

</script>

<style scoped>
</style>
