import {
  defineConfig,
  presetAttributify,
  presetIcons,
  presetTypography,
  presetUno,
  presetWebFonts,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss'

import configs from './src/config'

export default defineConfig({
  shortcuts: [
    ['flex-a-c', 'flex-items-center'],
    ['flex-j-sb', 'flex-justify-between'],
    ['theme-color', `text-[${configs.themeColor}]`],
    ['bg-theme-color', `bg-[${configs.themeColor}]`],
  ],
  presets: [
    presetUno(),
    presetAttributify(),
    presetIcons({
      scale: 1.2,
    }),
    presetTypography(),
    presetWebFonts({
      fonts: {
        sans: 'DM Sans',
        serif: 'DM Serif Display',
        mono: 'DM Mono',
      },
    }),
  ],
  transformers: [
    transformerDirectives(),
    transformerVariantGroup(),
  ],
  safelist: 'prose m-auto text-left'.split(' '),
})
