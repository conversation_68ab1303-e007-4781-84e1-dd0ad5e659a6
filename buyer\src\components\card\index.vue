<template>
  <div>
    <a-card :title="props._Title" :bordered="false" :body-style="bodyStyle">
      <template #title>
        <div class="card-title">
          <div class="title">{{props._Title}}</div>
          <div>
            <div v-if="_Tabs" class="cardTabs">
              <div @click="tabsChange(index)" :class="{active:(isActive==index)}" class="cardTabsItem" :style="{fontSize:`${_Size-2}px`}"
                   v-for="(item,index) in _Tabs" :key="index">
                {{item}}
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #extra>
        <span class="hover-pointer global-color" v-if="props._More" @click="callBack()">{{props._More}}</span>
      </template>
    </a-card>


  </div>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router';
  import { ref, watch, onMounted } from 'vue';


  /**
   * 接收父组件传值
   */
  const props = defineProps({
    // 可点击的tab栏
    _Tabs: {
      type: null,
      default: ''
    },
    // 当前选中的tab栏
    _Active: {
      type: Number,
      default: 0
    },
    // 头部标题
    _Title: {
      type: null,
      default: '卡片头部'
    },
    // 右侧更多
    _More: {
      type: null,
      default: false
    },
    // 文字大小
    _Size: {
      type: Number,
      default: 16
    },
    // 点击更多触发跳转
    _Src: {
      type: null,
      default: false
    }
  });
  const router = useRouter();
  // 已激活tab栏下标
  const isActive = ref(0);
  const bodyStyle = ref({border: 'none'});
  const emits = defineEmits<{ (e: '_Change', obj: any): void }>();
  const more = ref(null);


  // 点击tab的回调
  const tabsChange = (index: any) => {
    // 处理并返回index
    isActive.value = index;
    emits('_Change', index);
  };
  // 点击右侧的回调
  const callBack = () => {
    if (props._Src && props._Src !== '') {
      router.push(props._Src);
    }
  };

  watch(() => [props._More],
    (val: any) => {
      more.value = val;
    }, { deep: true }
  );
  onMounted(() => {
    if (props._Active) {
      isActive.value = props._Active;
    }
  })
</script>

<style scoped lang="less">
.card-title {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  align-content: stretch;
  .title {
    margin-right: 15px;
  }
  .cardTabs {
    display: flex;
    padding: 0 12px;
    height: 50px;
    line-height: 50px;

    > .cardTabsItem {
      padding: 0 12px;
      cursor: pointer;
    }
    > .cardTabsItem:hover {
      color: @theme_color;
    }
  }
  .active{
    color: @theme_color;
    position: relative;
    &::before{
      content: '';
      position: absolute;
      width: 100%;
      height: 3px;
      bottom: 0;
      left: 0;
      background: @theme_color;
    }
  }
}


:deep(.arco-card-header) {
  position: relative;
  padding: 0 14px;
  height: 50px;
  line-height: 50px;
  &::before {
    content: '';
    width: 3px;
    height: 50%;
    top: 25%;
    background: @theme_color;
    position: absolute;
    left: 0;
  }
}

:deep(.arco-card-body) {
    padding: 0 0 20px;
  }
</style>
