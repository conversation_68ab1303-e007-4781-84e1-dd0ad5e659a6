<template>
  <li class="hz-m-item" v-drag-item :style="{
    top: zoneTop,
    left: zoneLeft,
    width: zoneWidth,
    height: zoneHeight,
  }">
    <ul v-change-size class="hz-m-box" :class="{
      'hz-z-hidden': tooSmall,
      'hz-m-hoverbox': !hideZone,
    }">
      <li class="hz-u-index" :title="`热区${index + 1}`">{{ index + 1 }}</li>

      <li class="hz-u-square hz-u-square-tl" data-pointer="dealTL"></li>
      <li class="hz-u-square hz-u-square-tc" data-pointer="dealTC"></li>
      <li class="hz-u-square hz-u-square-tr" data-pointer="dealTR"></li>
      <li class="hz-u-square hz-u-square-cl" data-pointer="dealCL"></li>
      <li class="hz-u-square hz-u-square-cr" data-pointer="dealCR"></li>
      <li class="hz-u-square hz-u-square-bl" data-pointer="dealBL"></li>
      <li class="hz-u-square hz-u-square-bc" data-pointer="dealBC"></li>
      <li class="hz-u-square hz-u-square-br" data-pointer="dealBR"></li>
    </ul>
  </li>
</template>

<script setup >
import { ref, watch, onMounted, inject } from 'vue';
import _ from './zone/index.js'


const emit = defineEmits(['changeInfo', 'reloadOn'])

const vChangeSize = {
  beforeMount: function (el, binding, vnode) {
    el.addEventListener('mousedown', handleMouseDown, { passive: false })

    function handleMouseDown(e) {
      let pointer = e.target.dataset.pointer //元素上绑定的方法名

      if (!pointer) {
        return
      }

      e && e.stopPropagation()

      let zone = el.parentNode
      let setting = props.setting
      let currentIndex = props.index
      let container = _.getOffset(zone.parentNode)


      let itemInfo = {
        width: _.getOffset(zone).width || 0,
        height: _.getOffset(zone).height || 0,
        top: setting.topPer * container.height || 0,
        left: setting.leftPer * container.width || 0
      }
      let preX = _.getPageX(e)
      let preY = _.getPageY(e)
      let flag

      // Hide the info displayed by hover
      handlehideZone(true)

      window.addEventListener('mousemove', handleChange, { passive: false })
      window.addEventListener('mouseup', handleMouseUp, { passive: false })

      function handleChange(e) {
        e && e.preventDefault()
        flag = true

        let moveX = _.getPageX(e) - preX
        let moveY = _.getPageY(e) - preY

        preX = _.getPageX(e)
        preY = _.getPageY(e)

        // Handling the situation when different dragging points are selected
        let styleInfo = _[pointer](itemInfo, moveX, moveY)//调用对应的方法
        // Boundary value processing
        itemInfo = _.dealEdgeValue(itemInfo, styleInfo, container, props.res, currentIndex)

        Object.assign(zone.style, {
          top: `${itemInfo.top}px`,
          left: `${itemInfo.left}px`,
          width: `${itemInfo.width}px`,
          height: `${itemInfo.height}px`
        })
      }

      function handleMouseUp() {
        if (flag) {
          flag = false
          let perInfo = {
            topPer: _.decimalPoint(itemInfo.top / container.height),
            leftPer: _.decimalPoint(itemInfo.left / container.width),
            widthPer: _.decimalPoint(itemInfo.width / container.width),
            heightPer: _.decimalPoint(itemInfo.height / container.height)
          }
          const reload = inject('area-reload')
          if (!reload) emit('reloadOn')
          changeInfo(perInfo)
          // 兼容数据无变更情况下导致 computed 不更新，数据仍为 px 时 resize 出现的问题
          Object.assign(zone.style, {
            top: `${itemInfo.top}px`,
            left: `${itemInfo.left}px`,
            width: `${itemInfo.width}px`,
            height: `${itemInfo.height}px`
          })
        }
        // Show the info
        handlehideZone(false)
        window.removeEventListener('mousemove', handleChange)
        window.removeEventListener('mouseup', handleMouseUp)
      }
    }

    el.$destroy = () => el.removeEventListener('mousedown', handleMouseDown)
  },
  unmounted: function (el) {
    el.$destroy()
  }
}
const vDragItem = {
  beforeMount: function (el, binding, vnode) {
    el.addEventListener('mousedown', handleMouseDown)
    let collision
    function handleMouseDown(e) {
      e && e.stopPropagation()
      let container = _.getOffset(el.parentNode)
      let preX = _.getPageX(e)
      let preY = _.getPageY(e)
      let topPer
      let leftPer
      let flag


      window.addEventListener('mousemove', handleChange, { passive: false })
      window.addEventListener('mouseup', handleMouseUp, { passive: false })

      function handleChange(e) {

        e && e.preventDefault()
        flag = true
        collision = false
        // Hide the info displayed by hover
        handlehideZone(true)

        let setting = props.setting
        let currentIndex = props.index
        let moveX = _.getPageX(e) - preX
        let moveY = _.getPageY(e) - preY

        setting.topPer = setting.topPer || 0
        setting.leftPer = setting.leftPer || 0
        topPer = _.decimalPoint(moveY / container.height + setting.topPer)
        leftPer = _.decimalPoint(moveX / container.width + setting.leftPer)

        // Hotzone moving boundary processing
        if (topPer < 0) {
          topPer = 0
          moveY = -container.height * setting.topPer
        }

        if (leftPer < 0) {
          leftPer = 0
          moveX = -container.width * setting.leftPer
        }

        if (topPer + setting.heightPer > 1) {
          topPer = 1 - setting.heightPer
          moveY = container.height * (topPer - setting.topPer)
        }

        if (leftPer + setting.widthPer > 1) {
          leftPer = 1 - setting.widthPer
          moveX = container.width * (leftPer - setting.leftPer)
        }
        // 拖拽碰撞检测
        if (props.res.length > 1) {
          let currentzones = JSON.parse(JSON.stringify(props.res)).map((zone) => {
            return {
              left: (zone.leftPer || 0) * container.width,
              top: (zone.topPer || 0) * container.height,
              width: (zone.widthPer || 0) * container.width,
              height: (zone.heightPer || 0) * container.height
            }
          })

          // 矫正
          let changeSetting = {}
          changeSetting.left = setting.leftPer * container.width + moveX
          changeSetting.top = setting.topPer * container.height + moveY
          changeSetting.width = setting.widthPer * container.width
          changeSetting.height = setting.heightPer * container.height
          // 碰撞检测
          for (let i = 0, len = currentzones.length; i < len; i++) {
            if (currentIndex !== i && _.handleEgdeCollisions(currentzones[i], changeSetting)) {
              collision = true
              break
            }
          }
        }
        el.style.transform = `translate(${moveX}px, ${moveY}px)`


      }

      function handleMouseUp() {

        if (flag) {

          flag = false
          el.style.transform = 'translate(0, 0)'
          console.log(el.style.transform)
          if (!collision) {
            const reload = inject('area-reload')
            if (!reload) emit('reloadOn')
            changeInfo({
              topPer,
              leftPer
            })
          }
        }

        // Show the info
        handlehideZone(false)

        window.removeEventListener('mousemove', handleChange)
        window.removeEventListener('mouseup', handleMouseUp)
      }
    }

    el.$destroy = () => el.removeEventListener('mousedown', handleMouseDown)
  },
  unmounted: function (el) {
    el.$destroy()
  }
}
const props = defineProps(['index', 'setting', 'res'])
const hideZone = ref(false)
const tooSmall = ref(false)
const zoneTop = ref(0)
const zoneLeft = ref(0)
const zoneWidth = ref(0)
const zoneHeight = ref(0)

function getZoneStyle(val) {
  return `${(val || 0) * 100}%`;
}

function setZoneInfo(val) {

  zoneTop.value = getZoneStyle(val.topPer);
  zoneLeft.value = getZoneStyle(val.leftPer);
  zoneWidth.value = getZoneStyle(val.widthPer);
  zoneHeight.value = getZoneStyle(val.heightPer);
  tooSmall.value = val.widthPer < 0.01 && val.heightPer < 0.01;


}

watch(() => props.setting, (val) => {
  setZoneInfo(val)
})

onMounted(() => {
  setZoneInfo(props.setting)
})


function changeInfo(info = {}) {
  emit("changeInfo", {
    info,
    'index': props.index,
  });
}

function handlehideZone(isHide = true) {
  if (hideZone.value === isHide) {
    return;
  }
  hideZone.value = isHide;
}
</script>

<style scoped lang="less">
@import './zone/zone.less';
</style>
