<template>
  <div>
    <a-drawer :width="966" :visible="show" @ok="handleOk" @cancel="handleCancel" unmountOnClose>
      <template #title>
        选择图片
      </template>
      <div>
        <!--<oss @changOssImage="callbackImage" templateModel="template" />-->
        <ossManages @selected="callbackImage" :isSingle="true" templateModel="template"></ossManages>

      </div>
    </a-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import oss from '@/views/setting/oss-manage/index.vue'
import ossManages from '@/components/oss-manage/index.vue';

const emit = defineEmits(['callback'])
const show = ref(false);

function callbackImage(imgUrl: string) {
  emit('callback', imgUrl[imgUrl.length -1].url);
}
const open = () => {
  show.value = true
}
const handleOk = () => {
  show.value = false;
};
const handleCancel = () => {
  show.value = false;
}
defineExpose({ open })
</script>

<style scoped>
</style>
