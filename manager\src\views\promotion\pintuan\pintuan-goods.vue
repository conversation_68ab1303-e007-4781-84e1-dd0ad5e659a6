<template>
  <div class="pintuan-goods">
    <a-card :style="{ width: '100%' }">
      <h4>活动详情</h4>
      <tablePage :columns="columns" time-type="timestamp" :dataList="data" :enablePagination="false" :bordered="true"></tablePage>
      <h4>商品信息</h4>
      <tablePage :columns="goodsColumns" :api="getPintuanGoodsList" :apiParams="{pintuanId: route.query.id}" :bordered="true">
        <template #goodsName="{ data }">
          <a href="javascript:;" style="text-decoration:none" @click="store.viewGoodsDetail(data.goodsId, data.skuId)"> {{
            data.goodsName
          }}</a>
         
        </template>
      </tablePage>
    </a-card>
  </div>
</template>

<script setup lang='ts'>
import { getPintuanDetail, getPintuanGoodsList } from '@/api/promotion';
import tablePage from '@/components/table-pages/index.vue';
import { usePathJumpStore } from '@/store/index';
import getAssetsImages from '@/utils/assetsImages';
import { promotionsStatusRender } from '@/utils/tools';
import VueQrcode from '@chenfengyuan/vue-qrcode';
import { onMounted, ref } from "vue";
import { useRoute } from 'vue-router';


const qrcode = getAssetsImages('qrcode.svg');
const store = usePathJumpStore()
const route = useRoute()
const data = ref<any>([])// 表单数据
const goodsData = ref([])
const searchForm = ref({
  // 搜索框初始化对象
  pageNumber: 1, // 当前页数
  pageSize: 10, // 页面大小,
  pintuanId: route.query.id
})
const columns: any = [
  {
    title: '活动名称',
    width: 120,
    dataIndex: 'promotionName',
  },
  {
    title: '活动开始时间',
    width: 120,
    dataIndex: 'startTime',
  },
  {
    title: '活动结束时间',
    width: 120,
    dataIndex: 'endTime',
  },
  {
    title: '成团人数',
    width: 120,
    dataIndex: 'requiredNum',
  },
  {
    title: '限购数量',
    width: 120,
    dataIndex: 'limitNum',
  },
  {
    title: '状态',
    width: 120,
    dataIndex: 'promotionStatus',
    slot: true,
    slotData: {
      badge: promotionsStatusRender,
    },
  },
]
const goodsColumns: any = [
  {
    title: '商品名称',
    width: 120,
    dataIndex: 'goodsName',
    slot: true,
    slotTemplate: 'goodsName',
  },
  {
    title: '库存',
    width: 120,
    dataIndex: 'quantity',
  },
  {
    title: '拼团价格',
    width: 120,
    dataIndex: 'price',
    currency: true,
  },
]
//  获取拼团详情
const getPintuanMsg = () => {
  getPintuanDetail(route.query.id).then((res: any) => {
    if (res.data.success) data.value.push(res.data.result)
  })
}
const init = () => {
  getPintuanMsg();
}
onMounted(() => {
  init()
})
</script>

<style lang="less" scoped>
h4 {
  margin: 20px 0;
  padding: 0 10px;
  font-weight: bold;
  color: #333;
  font-size: 14px;
  text-align: left;
  border-left: 3px solid #165dff;
}

.qrcode {
  width: 15px;
}
</style>
