/**
 * 存储localStorage
 */
const setStorage = (label: any, value: any) => {
  if (label) {
    if (typeof value !== 'string') {
      value = JSON.stringify(value);
    }
    window.localStorage.setItem(label, value);
  }
};

/**
 * 获取localStorage
 */
const getStorage = (label: any) => {
  return label ? window.localStorage.getItem(label) : '';
};

export default {
  // 写入用户信息
  setUserInfo(val: any) {
    setStorage('userInfo', val);
  },
  // 获取用户信息
  getUserInfo() {
    return getStorage('userInfo');
  },
  // 写入uuid
  setUuid(val: any) {
    setStorage('uuid', val);
  },
  // 获取uuid
  getUuid() {
    return getStorage('uuid');
  },
  // 写入accessToken
  setAccessToken(val: any) {
    setStorage('accessToken', val);
  },
  // 获取accessToken
  getAccessToken() {
    return getStorage('accessToken');
  },
  // 写入刷新token
  setRefreshToken(val: any) {
    setStorage('refreshToken', val);
  },
  // 获取刷新token
  getRefreshToken() {
    return getStorage('refreshToken');
  },
  // 清除token
  clearToken() {
    setStorage('accessToken', '');
    setStorage('refreshToken', '');
  },
};
