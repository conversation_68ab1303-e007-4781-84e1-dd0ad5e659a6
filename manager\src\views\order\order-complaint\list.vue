<template>
  <a-card class="general-card" title="交易投诉" :bordered="false">
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <tablePage ref="tablePageRef" :columns="columnsTable" :methods="sortMethods" :api="getComplainPage" :api-params="apiParams" :bordered="true">
      <template #btnList="{ data }">
        <a-space>
          <a-button v-if="data?.complainStatus == 'COMPLETE'" type="text" status="success" @click="details(data)">
            详情</a-button>
          <a-button v-else type="text" status="warning" @click="handle(data)">处理</a-button>
        </a-space>
      </template>
      <template #goodsName="{ data }">
        <a href="javascript:;" style="text-decoration:none" @click="store.viewGoodsDetail(data.goodsId, data.skuId)"> {{
          data.goodsName
        }}</a>
      </template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
import { usePathJumpStore } from '@/store/index';
import searchTable from '@/components/search-column/index.vue';
import tablePage from '@/components/table-pages/index.vue';
import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
import { getComplainPage } from '@/api/order';
import { complaintStatus } from '@/utils/tools';
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const apiParams = ref({});
const store = usePathJumpStore()
const tablePageRef = ref('');
const router = useRouter()
const columnsSearch: Array<SearchRule> = [
  {
    label: '订单编号',
    model: 'orderSn',
    disabled: false,
    input: true,
  },
  {
    label: '会员名称',
    model: 'memberName',
    disabled: false,
    input: true,
  },
  {
    label: '状态',
    model: 'status',
    disabled: false,
    select: {
      options: complaintStatus,
    },
  },
];

const columnsTable: ColumnsDataRule[] = [
  {
    title: '会员名称',
    dataIndex: 'memberName',
  },
  {
    title: '订单编号',
    dataIndex: 'orderSn',
  },
  {
    title: '商品名称',
    dataIndex: 'goodsName',
    slot: true,
    slotTemplate: 'goodsName',
  },
  {
    title: '投诉主题',
    dataIndex: 'complainTopic',
  },
  {
    title: '投诉时间',
    dataIndex: 'createTime',
  },
  {
    title: '投诉状态',
    dataIndex: 'complainStatus',
    slot: true,
    slotData: {
      badge: complaintStatus,
    },
  },
];

const sortMethods: MethodsRule = {
  title: '操作',
  width: 200,
  methods: [
    {
      slot: true,
      slotTemplate: 'btnList',
    },
  ],
};
// 详情
const details = (data: any) => {
  router.push({
    name: 'order-complaint-detail',
    query: {
      id: data.id
    }
  })
}
// 处理
const handle = (data: any) => {
  router.push({
    name: 'order-complaint-detail',
    query: {
      id: data.id
    }
  })
}
</script>
