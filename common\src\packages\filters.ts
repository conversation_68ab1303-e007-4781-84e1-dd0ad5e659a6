/**
 * 货币格式化
 * @param price
 */
function formatPrice(price: number) {
  return String(price.toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

/**
 * 返回格式化金额
 * @param val 金额数值
 * @param unit 单位，默认为空字符串
 * @param location 单位位置，可选值为 'before'、'after' 或其他（默认为其他）
 */
export function unitPrice(val: number, unit?: string, location?: 'before' | 'after') {
  if (!val) val = 0;
  const price = formatPrice(val);
  if (location === 'before') {
    return price.slice(0, price.length - 3);
  }
  if (location === 'after') {
    return price.slice(-2);
  }
  return (unit || '') + price;
}
