<template>
  <a-card class="general-card" title="店铺审核" :bordered="false">
    <!-- 搜索 -->
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = { ...val,...apiParams}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <!-- 表格 -->
    <tablePage ref="tablePageRef" :columns="columnsTable" :methods="sortMethods" :api="getShopListData"
      :api-params="apiParams" @detail="seeStore" :bordered="true"/>
  </a-card>
</template>

<script setup lang="ts">
import { getShopListData } from '@/api/shops';
import searchTable from '@/components/search-column/index.vue';
import tablePage from '@/components/table-pages/index.vue';
import { ColumnsDataRule, MethodsRule, SearchRule } from '@/types/global';
import {
selfOperated,
storeDisable,
} from '@/utils/tools';
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter()
const tablePageRef = ref('');
// 查询海选列表
const columnsSearch: Array<SearchRule> = [
  {
    label: '会员名称',
    model: 'memberName',
    disabled: false,
    input: true,
  },
  {
    label: '店铺名称',
    model: 'storeName',
    disabled: false,
    input: true,
  },
  // {
  //   label: '店铺状态',
  //   model: 'storeDisable',
  //   disabled: false,
  //   select: {
  //     options: storeDisable,
  //   },
  // },
  {
    label: '创建时间',
    disabled: false,
    datePicker: {
      type: 'range',
    },
  },
];

// 表格搜索列表
const columnsTable: ColumnsDataRule[] = [
  {
    title: '店铺名称',
    dataIndex: 'storeName',
  },
  {
    title: '会员名称',
    dataIndex: 'memberName',
  },
  {
    title: '店铺地址',
    dataIndex: 'storeAddressPath',
  },
  {
    title: '是否自营',
    dataIndex: 'selfOperated',
    slot: true,
    slotData: {
      badge: selfOperated,
    },
  },
  {
    title: '店铺状态',
    dataIndex: 'storeDisable',
    slot: true,
    slotData: {
      badge: storeDisable,
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
];
// 操作列表
const sortMethods: MethodsRule = {
  title: '操作',
  methods: [
    {
      title: '查看',
      callback: 'detail',
      type:'text',
      status:"success"
    },
  ],
};

const apiParams = ref({
  pageNumber: 1,
  pageSize: 10,
  storeDisable: 'APPLYING',
});
// 查看
const seeStore = (v: any) => {
  router.push({ name: 'shop-operation', query: { id: v.record.id } })
}
</script>

<style scoped></style>
