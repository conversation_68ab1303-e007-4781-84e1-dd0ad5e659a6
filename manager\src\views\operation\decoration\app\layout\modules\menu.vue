<template>
  <div py-16px border-b-1>
    <div w-80px>选择模板</div>
    <div mt-20px>
      <a-radio-group v-model="props.res.data.model">
        <a-radio v-for="(item, index) in models"  :key="index" :value="item.value">{{ item.label }}</a-radio>
      </a-radio-group>
    </div>

  </div>
</template>

<script setup lang="ts">
import { DragRule } from '@/views/operation/decoration/app/models/types';
const props = defineProps<{
  res: DragRule
}>()

const models = [
  {
    label: '图文导航',
    value: 'default',
  },
  {
    label: '文字导航',
    value: 'text',
  },
]
</script>

<style scoped>
</style>
