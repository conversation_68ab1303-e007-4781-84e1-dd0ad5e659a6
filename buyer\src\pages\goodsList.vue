<template>
  <div class="home-bgcolor">
    <Search @search="handleSearch"></Search>

    <!--商品主体内容-->
    <div class="goods-container">
      <!--商品筛选栏-->
      <GoodsClassNav @getParams="getParams"></GoodsClassNav>
      <!--商品展示容器-->
      <div class="goods-list-box">
        <!--排序-->
        <div class="goods-list-tool">
          <div v-for="(item, index) in goodsTool" :key="index" @click="orderBy(item.en, index)" :class="{ 'goods-list-tool-active': index === sortIndex }">
                <span>
                  {{ item.title }}&nbsp;
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" v-if="index === sortIndex">
                    <path fill="#ffffff" d="m13 16.172l5.364-5.364l1.414 1.414L12 20l-7.778-7.778l1.414-1.414L11 16.172V4h2z"/>
                  </svg>
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" v-else>
                    <path fill="#999999" d="m13 16.172l5.364-5.364l1.414 1.414L12 20l-7.778-7.778l1.414-1.414L11 16.172V4h2z"/>
                  </svg>
                </span>
          </div>
          <div @click="orderBy('price', 5)" class="price-sort" :class="{ 'goods-list-tool-active': 5 === sortIndex }">
                <span>
                  价格&nbsp;
                  <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 24 24" v-if="5 === sortIndex">
                    <path fill="#ffffff" d="m11.95 7.95l-1.414 1.414L8 6.828V20H6V6.828L3.466 9.364L2.05 7.95L7 3zm10 8.1L17 21l-4.95-4.95l1.414-1.414l2.537 2.536L16 4h2v13.172l2.536-2.536z"/>
                  </svg>
                  <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 24 24" v-else>
                    <path fill="#999999" d="m11.95 7.95l-1.414 1.414L8 6.828V20H6V6.828L3.466 9.364L2.05 7.95L7 3zm10 8.1L17 21l-4.95-4.95l1.414-1.414l2.537 2.536L16 4h2v13.172l2.536-2.536z"/>
                  </svg>
                  <!--<div><Icon type="md-arrow-dropup" :class="{ 'price-color': sortPriceIndex == 'desc' }"/><Icon type="md-arrow-dropdown" :class="{ 'price-color': sortPriceIndex == 'asc' }"/></div>-->
                </span>
          </div>
        </div>
        <!--列表-->
        <div class="goods-list" v-if="goodsData && goodsData.length">
          <div class="goods-show-info" v-for="(item, index) in goodsData" :key="index" @click="goGoodsDetail(item.id, item.goodsId)">
            <div class="goods-show-img"><img width="210" height="210" :src="item.thumbnail" /></div>
            <div class="goods-show-price price-color"><span class="seckill-price text-danger">{{unitPrice(item.price, "￥")}}</span></div>
            <div class="goods-show-detail ellipsis ellipsis-2 light-text-color">
              <a-tag class="goods-show-tag" color="purple" bordered v-if="item.salesModel === 'WHOLESALE'">批发</a-tag>
              <span>{{ item.goodsName }}</span>
            </div>
            <div class="goods-show-num">已有<span>{{ item.commentNum || 0 }}</span>人评价</div>
            <div class="goods-show-seller"><span class="text-bottom global-color">{{item.storeName}}</span></div>
            <div class="goods-show-right">
              <a-tag class="goods-show-tag" color="red" bordered v-if="item.selfOperated">自营</a-tag>
              <a-tag class="goods-show-tag" color="blue" bordered v-if="item.goodsType === 'VIRTUAL_GOODS'">虚拟</a-tag>
              <a-tag class="goods-show-tag" color="blue" bordered v-else-if="item.goodsType === 'PHYSICAL_GOODS'">实物</a-tag>
            </div>
          </div>
        </div>
        <Empty v-else />
      </div>
      <!--分页-->
      <div class="paginationBox mt_10 pb_20">
        <a-pagination :total="total" :current="apiParams.pageNumber" :page-size="apiParams.pageSize" show-page-size
                      @change="(number) => {apiParams.pageNumber = number;}" @page-size-change="(number) => {apiParams.pageSize = number; apiParams.pageNumber = 1;}" >
        </a-pagination>
      </div>


    </div>

  </div>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted } from 'vue';
  import { unitPrice } from '@/utils/filters';
  import { useRouter, useRoute } from 'vue-router';
  import { goodsList } from "@/api/goods";

  const route = useRoute();
  const router = useRouter();
  const loading = ref(false);
  // 排序类型
  const goodsTool = ref([
    { title: "综合", en: "" },
    { title: "销量", en: "buyCount" },
    { title: "评论数", en: "commentNum" },
    { title: "新品", en: "releaseTime" },
  ]);
  // 排序状态
  const sortIndex = ref(0);
  // 判断价格升序还是降序
  const sortPriceIndex = ref<any>(null);
  // 请求参数
  const apiParams = ref<any>({
    pageNumber: 1,
    pageSize: 20,
    categoryId: "",
    en: "",
    sort: "",
    order: "",
    keyword: "",
    promotionType: ""
  });
  const goodsData = ref<Array<any>>([]);
  const total = ref(0);


  // 搜索
  const handleSearch = (key: any) => {
    apiParams.value.keyword = key;
    route.query.keyword = key;
    apiParams.value.pageNumber = 1;
  };
  // 筛选条件回显
  const getParams = (val: any) => {
    Object.assign(apiParams.value, val);
    getGoodsList();
  };
  // 排序
  const orderBy = (data: any, index: any) => {
    apiParams.value.pageNumber = 1;
    sortIndex.value = index;
    apiParams.value.sort = data;
    apiParams.value.order = "desc";
    if (data === "price") {
      if (!sortPriceIndex.value) {
        sortPriceIndex.value = "asc";
      } else {
        sortPriceIndex.value === "desc" ? (sortPriceIndex.value = "asc") : (sortPriceIndex.value = "desc");
      }
      apiParams.value.order = sortPriceIndex.value;
    } else {
      sortPriceIndex.value = null;
    }
  };

  // 获取商品列表
  const getGoodsList = () => {
    loading.value = true;
    goodsList(apiParams.value).then((res) => {
      loading.value = false;
        if (res.data.success) {
          goodsData.value = res.data.result.records;
          total.value = res.data.result.total;
        }
      }).catch(() => {
      loading.value = false;
      });
  };
  // 跳转商品详情
  const goGoodsDetail = (skuId: any, goodsId: any) => {
    let routerUrl = router.resolve({path: "/goodsDetail", query: { skuId, goodsId }});
    window.open(routerUrl.href, "_blank");
  };

  onMounted(() => {
    if (route.query.categoryId) {
      let cateId = route.query.categoryId.split(",");
      Object.assign(apiParams, route.query);
      apiParams.value.categoryId = cateId[cateId.length - 1];
    } else {
      Object.assign(apiParams.value, route.query);
    }
    getGoodsList();
  });
  watch(() => [apiParams.value], (val)=>{
    getGoodsList();
    }, {deep: true}
  );
  watch(() => route.query.categoryId, (val) => {
    const keyword = route.query.keyword;
    if (keyword) {
      handleSearch(keyword);
    }
    if (route.query.categoryId) {
      let cateId = route.query.categoryId.split(",");
      Object.assign(apiParams.value, route.query);
      apiParams.value.categoryId = cateId[cateId.length - 1];
    }
    if (route.query.promotionType) {
      apiParams.value.promotionType = route.query.promotionType;
    }
    if (route.query.promotionsId) {
      apiParams.value.promotionsId = route.query.promotionsId;
    }
  })
</script>

<style scoped lang="less">
  .goods-container {
    width: 1200px;
    margin: 0 auto;
    padding: 20px 0;
  }
  // 商品展示容器
  .goods-list-box {

  }
  // 排序
  .goods-list-tool {
    background-color: #f1f2f3;
    border: 1px solid @light_border_color;
    margin: 10px 0;
    padding: 5px;
    box-sizing: border-box;
    font-size: 14px;
    color: #333333;
    display: flex;
    > div {
      height: 22px;
      border: 1px solid @light_border_color;
      background-color: #ffffff;
      display: flex;
      align-items: center;
      padding: 0 10px;
      cursor: pointer;
      > span {
        display: flex;
        align-items: flex-end;
      }
    }
    > div:hover {
      border: 1px solid @theme_color;
    }
    .goods-list-tool-active {
      color: #fff;
      background-color: @theme_color;
      border: 1px solid @theme_color;
    }
  }
  // 列表
  .goods-list {
    display: flex;
    flex-wrap: wrap;
    width: 1200px;
    margin: 0 auto;
    .goods-show-info {
      width: 232px;
      box-sizing: border-box;
      padding: 10px;
      margin-right: 10px;
      margin-bottom: 10px;
      position: relative;
      border: 1px solid #fff;
      cursor: pointer;
      background-color: #fff;
      font-size: 12px;
      .goods-show-price {
        font-size: 22px;
        font-weight: bold;
        margin: 5px 0;
      }
      .goods-show-detail {
        height: 36px;
        line-height: 18px;
        margin-bottom: 5px;
        font-size: 14px;
      }
      .goods-show-num {
        font-size: 12px;
        margin-bottom: 6px;
        color: #009688;
      }
      .goods-show-num span {
        color: #005aa0;
        font-weight: bold;
      }
      .goods-show-seller {
        margin: 5px 0;
      }
      .goods-show-right {

      }
    }
    .goods-show-info:hover {
      box-shadow: 1px 1px 5px #cccccc;
    }
    .goods-show-info:nth-of-type(5n) {
      margin-right: 0;
    }
    .goods-show-tag {
      height: 18px;
      width: 32px;
      line-height: 14px;
      white-space: nowrap;
      text-align: center;
      align-items: center;
      padding: 0 3px;
      margin-right: 6px;
    }
  }
  .paginationBox {
    background-color: @light_white_background_color;
    box-sizing: border-box;
    padding: 10px 20px;
  }
</style>
