<template>
  <a-card
    class="general-card"
    title="提现申请"
    :bordered="false"
  >
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getUserWithdrawApply"
      :api-params="apiParams"
      :bordered="true"
    >
      <template #btnList="{ data }">
        <a-space>
          <a-button
            v-if="data?.applyStatus == 'APPLY'"
            type="text"
            @click="toExamine(data)"
          >
            审核</a-button
          >
          <a-button v-else size="small" @click="seeCashWithdrawal(data)"
          type="text" status="success">查看</a-button
          >
        </a-space>
      </template>
    </tablePage>
    <!-- 审核 -->
    <a-modal
      v-model:visible="roleModalVisible"
      :align-center="false"
      width="500px"
    >
      <template #title> 审核 </template>
      <a-form ref="formRef"
        :style="{ width: '460px' }"
        layout="horizontal"
        auto-label-width
        :model="showList">
        <a-form-item label="申请编号：">
          <span>{{ showList.sn }}</span>
        </a-form-item>
        <a-form-item label="用户名称：">
          <span>{{ showList.memberName }}</span>
        </a-form-item>
        <a-form-item label="申请金额：">
          <span>{{ unitPrice(showList.applyMoney, '￥') }}</span>
        </a-form-item>
        <a-form-item label="提现状态：">
          <span>{{ 
          showList.applyStatus == 'APPLY'
              ? '申请中'
              : showList.applyStatus == 'VIA_AUDITING'
              ? '审核通过'
              : showList.applyStatus == 'SUCCESS'
              ? '提现成功'
              : '审核拒绝'
             }}</span>
        </a-form-item>
        <a-form-item label="申请时间：">
          <span>{{ showList.createTime }}</span>
        </a-form-item>
        <a-form-item label="审核备注：" required>
          <a-input v-model="audit" />
        </a-form-item>
      </a-form>
      <template #footer>
        <div style="text-align: right">
          <a-button @click="submitRole(false)"  status="danger">拒绝</a-button>
          <a-button
            style="margin-left: 10px"
            type="primary"
            @click="submitRole(true)"
            >通过</a-button
          >
        </div>
      </template>
    </a-modal>
    <!-- 查看 -->
    <a-modal
      v-model:visible="seeWithdrawal"
      :align-center="false"
      width="500px"
    >
      <template #title> 查看 </template>
      <a-form
        ref="formRefCheck"
        :style="{ width: '460px' }"
        layout="horizontal"
        auto-label-width
        :model="showList"
      >
        <a-form-item label="申请编号：">
          <span>{{ showList.sn }}</span>
        </a-form-item>
        <a-form-item label="用户名称：">
          <span>{{ showList.memberName }}</span>
        </a-form-item>
        <a-form-item label="申请金额：">
          <span>{{ unitPrice(showList.applyMoney, '￥') }}</span>
        </a-form-item>
        <a-form-item label="提现状态：">
          <span>{{
            showList.applyStatus == 'APPLY'
              ? '申请中'
              : showList.applyStatus == 'VIA_AUDITING'
              ? '审核通过'
              : showList.applyStatus == 'SUCCESS'
              ? '提现成功'
              : '审核拒绝'
          }}</span>
        </a-form-item>
        <a-form-item label="申请时间：">
          <span>{{ showList.createTime }}</span>
        </a-form-item>
        <a-form-item label="审核时间：">
          <span>{{ showList.inspectTime }}</span>
        </a-form-item>
        <a-form-item label="审核备注：">
          <span>{{ showList.inspectRemark }}</span>
        </a-form-item>
      </a-form>
      <template #footer>
        <div style="text-align: right">
          <a-button type="text" @click="seeWithdrawal = false">取消</a-button>
        </div>
      </template>
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">
  import { getUserWithdrawApply, withdrawApply } from '@/api/member';
import searchTable from '@/components/search-column/index.vue';
import tablePage from '@/components/table-pages/index.vue';
import { ColumnsDataRule, MethodsRule, SearchRule } from '@/types/global';
import { unitPrice } from '@/utils/filters';
import { memberApplyStatus } from '@/utils/tools';
import { Message } from '@arco-design/web-vue';
import { ref } from 'vue';

  const apiParams = ref({});
  const formRef = ref();
  const formRefCheck = ref();
  const roleModalVisible = ref<boolean>(false); // 审核
  const seeWithdrawal = ref<boolean>(false); // 查看
  const tablePageRef = ref<any>('');
  const audit = ref<string>(''); // 审核备注
  const showList = ref({
    // 可操作选择
    sn: '',
    memberName: '',
    applyMoney: 0 ,
    applyStatus: '',
    createTime: '',
    id: '',
    inspectTime: '',
    inspectRemark: '',
  });
  const columnsSearch: Array<SearchRule> = [
    {
      label: '会员名称',
      model: 'memberName',
      disabled: false,
      input: true,
    },
    {
      label: '审核状态',
      model: 'applyStatus',
      disabled: false,
      select: {
        options: memberApplyStatus,
      },
    },
    {
      label: '申请时间',
      disabled: false,
      datePicker: {
        type: 'range',
      },
    },
  ];

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '申请编号',
      dataIndex: 'sn',
    },
    {
      title: '用户名称',
      dataIndex: 'memberName',
    },

    {
      title: '申请金额',
      dataIndex: 'applyMoney',
      currency: true,
    },
    {
      title: '提现状态',
      dataIndex: 'applyStatus',
      slot: true,
      slotData: {
        badge: memberApplyStatus,
      },
    },
    {
      title: '申请时间',
      dataIndex: 'createTime',
    },
    {
      title: '审核时间',
      dataIndex: 'inspectTime',
    },
  ];

  const sortMethods: MethodsRule = {
    title: '操作',
    width: 300,
    methods: [
      {
        slot: true,
        slotTemplate: 'btnList',
      },
    ],
  };
  // 审核
  const toExamine = (v: any) => {
    showList.value = v;
    roleModalVisible.value = true;
    // formRef.value.resetFields();
    audit.value = '';
  };
  // 审核拒绝或通过
  const submitRole = (v: boolean) => {
    const params = {
      applyId: '',
      result: false,
      remark: '',
    };
    params.applyId = showList.value.id;
    params.result = v;
    params.remark = audit.value;
    if (v == false && !audit.value) {
      Message.error('审核备注不能为空');
    } else {
      withdrawApply(params).then((res: any) => {
        if (res.data) {
          Message.success('操作成功');
          roleModalVisible.value = false;
          tablePageRef.value.init();
        }
      });
    }
  };
  // 查看
  const seeCashWithdrawal = (v: any) => {
    showList.value = v;
    seeWithdrawal.value = true;
  };
</script>

