{"data": {"success": true, "message": "success", "code": 200, "timestamp": 1705655177257, "result": [{"id": "1367038467288072191", "createBy": "admin", "createTime": "2021-03-03 09:05:44", "updateBy": "admin", "updateTime": "2021-03-03 09:09:27", "deleteFlag": false, "title": "工作台", "icon": "icon-dashboard", "name": "dashboard", "path": "dashboard", "level": 0, "frontRoute": "null", "parentId": "0", "sortOrder": 0.0, "permission": "", "children": [{"id": "1367041332861730816", "createBy": "admin", "createTime": "2021-03-03 09:17:07", "updateBy": "admin", "updateTime": "2023-06-16 16:30:21", "deleteFlag": false, "title": "工作台", "name": "Workplace", "path": "workplace", "level": 1, "frontRoute": "views/dashboard/workplace/index", "parentId": "1367038467288072192", "sortOrder": 0.0}]}, {"id": "1367039950368800768", "createBy": "admin", "createTime": "2021-03-03 09:11:37", "updateBy": null, "updateTime": null, "deleteFlag": false, "title": "商品", "path": "goods-module", "name": "goods-module", "icon": "icon-email", "level": 0, "frontRoute": null, "parentId": "0", "sortOrder": 0.2, "permission": "/manager/goods*,/manager/common*", "children": [{"id": "1367044376391319552", "createBy": "admin", "createTime": "2021-03-03 09:29:12", "updateBy": "admin", "updateTime": "2023-03-17 20:20:29", "deleteFlag": false, "title": "商品发布", "name": "goods-operation", "path": "goods-operation", "level": 1, "frontRoute": "views/goods/goods-operation/index", "parentId": "1367039950368800768", "sortOrder": 0.0, "permission": "/manager/goods*,/manager/common*"}, {"id": "1367045630710513664", "createBy": "admin", "createTime": "2021-03-03 09:29:12", "updateBy": "admin", "updateTime": "2023-03-17 20:20:29", "deleteFlag": false, "title": "商品列表", "name": "goods-list", "path": "goods-list", "level": 1, "frontRoute": "views/goods/goods-list/list", "parentId": "1367039950368800768", "sortOrder": 1.0, "permission": "/manager/goods*,/manager/common*"}, {"id": "1367044657296441344", "createBy": "admin", "createTime": "2021-03-03 09:30:19", "updateBy": "admin", "updateTime": "2023-03-17 20:21:05", "deleteFlag": false, "title": "店铺分类", "name": "category-list", "path": "category-list", "level": 1, "frontRoute": "views/goods/category/list", "parentId": "1367039950368800768", "sortOrder": 1.0, "permission": "/manager/goods/category*,/manager/goods/brand*,/manager/goods/spec*,/manager/goods/parameters*"}]}, {"id": "1367039534616805376", "createBy": "admin", "createTime": "2021-03-03 09:09:58", "updateBy": "admin", "updateTime": "2021-05-18 10:51:12", "deleteFlag": false, "title": "订单", "icon": "icon-unordered-list", "name": "order-module", "path": "order-module", "level": 0, "frontRoute": "null", "parentId": "0", "sortOrder": 1.0, "permission": "/manager/order*", "children": [{"id": "1367043443917848576", "createBy": "admin", "createTime": "2021-03-03 09:25:30", "updateBy": "admin", "updateTime": "2023-03-17 20:18:57", "deleteFlag": false, "title": "商品订单", "name": "order-list", "path": "order-list", "level": 1, "frontRoute": "views/order/order-list/list", "parentId": "1367039534616805376", "sortOrder": 0.0, "permission": "/manager/order*"}, {"id": "1367043791105556480", "createBy": "admin", "createTime": "2021-03-03 09:25:30", "updateBy": "admin", "updateTime": "2023-03-17 20:18:57", "deleteFlag": false, "title": "虚拟订单", "name": "virtual-order-list", "path": "virtual-order-list", "level": 1, "frontRoute": "views/order/order-list/virtualOrderList", "parentId": "1367039534616805376", "sortOrder": 1.0, "permission": "/manager/order*"}, {"id": "1634835112821112834", "createBy": "admin", "createTime": "2021-03-19 02:11:30", "updateBy": "admin", "updateTime": "2023-03-17 20:19:12", "deleteFlag": false, "title": "评价管理", "name": "member-content-list", "path": "member-content-list", "level": 1, "frontRoute": "views/member/member-comment/list", "parentId": "1367039534616805376", "sortOrder": 4.0, "permission": "/manager/payment/outOrderLog*"}, {"id": "1367043505771249664", "createBy": "admin", "createTime": "2021-03-03 09:25:45", "updateBy": "admin", "updateTime": "2023-03-17 20:19:17", "deleteFlag": false, "title": "退货管理", "name": "return-goods", "path": "return-goods", "level": 1, "frontRoute": "views/order/return-goods/list", "parentId": "1367039534616805376", "sortOrder": 2.0, "permission": "/manager/order/afterSale*"}, {"id": "1367044121163726848", "createBy": "admin", "createTime": "2021-03-03 09:25:45", "updateBy": "admin", "updateTime": "2023-03-17 20:19:17", "deleteFlag": false, "title": "退款管理", "name": "return-money", "path": "return-money", "level": 1, "frontRoute": "views/order/return-money/list", "parentId": "1367039534616805376", "sortOrder": 3.0, "permission": "/manager/order/complain*"}, {"id": "1367044247978508288", "createBy": "admin", "createTime": "2021-03-03 09:25:45", "updateBy": "admin", "updateTime": "2023-03-17 20:19:17", "deleteFlag": false, "title": "投诉管理", "name": "order-complaint", "path": "order-complaint", "level": 1, "frontRoute": "views/order/order-complaint/list", "parentId": "1367039534616805376", "sortOrder": 4.0, "permission": "/manager/order/afterSaleReason*"}]}, {"id": "1367039534616805377", "createBy": "admin", "createTime": "2021-03-03 09:09:58", "updateBy": "admin", "updateTime": "2021-05-18 10:51:12", "deleteFlag": false, "title": "财务", "icon": "icon-file", "name": "finance-module", "path": "finance-module", "level": 0, "frontRoute": "null", "parentId": "0", "sortOrder": 0.4, "permission": "/manager/finance*", "children": [{"id": "1367043443917848554", "createBy": "admin", "createTime": "2021-03-03 09:25:30", "updateBy": "admin", "updateTime": "2023-03-17 20:18:57", "deleteFlag": false, "title": "财务对账", "name": "bill-verify", "path": "bill-verify", "level": 1, "frontRoute": "views/order/bill/verify-list", "parentId": "1367039534616805377", "sortOrder": 0.0, "permission": "/manager/finance*"}, {"id": "1367043443917848554", "createBy": "admin", "createTime": "2021-03-03 09:25:30", "updateBy": "admin", "updateTime": "2023-03-17 20:18:57", "deleteFlag": false, "title": "店铺结算", "name": "store-bill", "path": "store-bill", "level": 1, "frontRoute": "views/finance/store-bill/list", "parentId": "1367039534616805377", "sortOrder": 0.0, "permission": "/manager/finance*"}, {"id": "1367043443917848555", "createBy": "admin", "createTime": "2021-03-03 09:25:30", "updateBy": "admin", "updateTime": "2023-03-17 20:18:57", "deleteFlag": false, "title": "发票管理", "name": "receipt-list", "path": "receipt-list", "level": 1, "frontRoute": "views/finance/receipt/list", "parentId": "1367039534616805377", "sortOrder": 0.0, "permission": "/manager/finance*"}]}, {"id": "1367040067201138688", "createBy": "admin", "createTime": "2021-03-03 09:12:05", "updateBy": "admin", "updateTime": "2021-12-02 19:45:22", "deleteFlag": false, "title": "促销", "icon": "icon-tag", "name": "promotion-module", "path": "promotion-module", "level": 0, "frontRoute": "null", "parentId": "0", "sortOrder": 0.3, "permission": "/manager/goods/category/allChildren,/manager/goods/goods/sku/list*", "children": [{"id": "1367049712657498112", "createBy": "admin", "createTime": "2021-03-03 09:48:26", "updateBy": "admin", "updateTime": "2023-06-16 16:50:12", "deleteFlag": false, "title": "拼团活动", "name": "pin<PERSON>an", "path": "pin<PERSON>an", "level": 1, "frontRoute": "views/promotion/pintuan/list", "parentId": "1367040067201138688", "sortOrder": 1.0, "permission": "/manager/promotion/pintuan*"}, {"id": "1367049611578966016", "createBy": "admin", "createTime": "2021-03-03 09:48:26", "updateBy": "admin", "updateTime": "2023-06-16 16:50:12", "deleteFlag": false, "title": "秒杀活动", "name": "seckill", "path": "seckill", "level": 1, "frontRoute": "views/promotion/seckill/list", "parentId": "1367040067201138688", "sortOrder": 1.0, "permission": "/manager/promotion/seckill*,/manager/setting/setting/get/SECKILL_SETTING*,/manager/setting/setting/get/SECKILL_SETTING"}, {"id": "1367049214198022145", "createBy": "admin", "createTime": "2021-03-03 09:48:26", "updateBy": "admin", "updateTime": "2023-06-16 16:50:12", "deleteFlag": false, "title": "直播管理", "name": "live", "path": "live", "level": 1, "frontRoute": "views/promotion/live/list", "parentId": "1367040067201138688", "sortOrder": 0.0, "permission": "/manager/promotion/coupon*"}, {"id": "1367049214198022146", "createBy": "admin", "createTime": "2021-03-03 09:48:26", "updateBy": "admin", "updateTime": "2023-06-16 16:50:12", "deleteFlag": false, "title": "直播商品", "name": "live-goods", "path": "live-goods", "level": 1, "frontRoute": "views/promotion/live-goods/list", "parentId": "1367040067201138688", "sortOrder": 0.0, "permission": "/manager/promotion/coupon*"}, {"id": "1367049500782231552", "createBy": "admin", "createTime": "2021-03-03 09:48:26", "updateBy": "admin", "updateTime": "2023-06-16 16:50:12", "deleteFlag": false, "title": "满额活动", "name": "full-discount", "path": "full-discount", "level": 1, "frontRoute": "views/promotion/full-discount/list", "parentId": "1367040067201138688", "sortOrder": 1.0, "permission": "/manager/promotion/fullDiscount*"}, {"id": "1367049214198022144", "createBy": "admin", "createTime": "2021-03-03 09:48:26", "updateBy": "admin", "updateTime": "2023-06-16 16:50:12", "deleteFlag": false, "title": "优惠券", "name": "coupon", "path": "coupon", "level": 1, "frontRoute": "views/promotion/coupon/list", "parentId": "1367040067201138688", "sortOrder": 0.0, "permission": "/manager/promotion/coupon*"}, {"id": "1403988156444962818", "createBy": "admin", "createTime": "2021-03-03 09:48:26", "updateBy": "admin", "updateTime": "2023-06-16 16:50:12", "deleteFlag": false, "title": "分销商品", "name": "distribution-goods", "path": "distribution-goods", "level": 1, "frontRoute": "views/promotion/distribution/distribution-goods-list", "parentId": "1367040067201138688", "sortOrder": 1.0, "permission": "/manager/promotion/couponActivity*"}, {"id": "1430799171593535490", "createBy": "admin", "createTime": "2021-03-03 09:48:26", "updateBy": "admin", "updateTime": "2023-06-16 16:50:12", "deleteFlag": false, "title": "分销订单", "name": "distribution-order", "path": "distribution-order", "level": 1, "frontRoute": "views/promotion/distribution/distribution-order-list", "parentId": "1367040067201138688", "sortOrder": 1.0, "permission": "/manager/promotion/kan<PERSON><PERSON>G<PERSON>s*"}, {"id": "1407602441964244994", "createBy": "admin", "createTime": "2021-06-23 15:32:29", "updateBy": "admin", "updateTime": "2023-06-16 16:53:30", "deleteFlag": false, "title": "分销设置", "name": "distribution-setting", "path": "distribution-setting", "level": 1, "frontRoute": "views/promotion/distribution/distribution-setting", "parentId": "1367040067201138688", "sortOrder": 3.0, "permission": "/manager/promotion/points*"}]}, {"id": "1367040819248234496", "createBy": "admin", "createTime": "2021-03-03 09:15:04", "updateBy": null, "updateTime": null, "deleteFlag": false, "title": "统计", "name": "statistic-module", "path": "statistic-module", "icon": "icon-computer", "level": 0, "frontRoute": null, "parentId": "0", "sortOrder": 0.7, "permission": "/manager/store*,/manager/member*,/manager/statistics*", "children": [{"id": "1367052616634204160", "createBy": "admin", "createTime": "2021-03-03 10:01:57", "updateBy": "admin", "updateTime": "2023-06-16 17:41:13", "deleteFlag": false, "title": "商品统计", "name": "goods", "path": "goods", "level": 1, "frontRoute": "views/statistics/goods", "parentId": "1367040819248234496", "sortOrder": 0.0, "permission": "/manager/store*,/manager/member*,/manager/statistics*"}, {"id": "1367052915314786304", "createBy": "admin", "createTime": "2021-03-03 10:01:57", "updateBy": "admin", "updateTime": "2023-06-16 17:41:13", "deleteFlag": false, "title": "订单统计", "name": "order", "path": "order", "level": 1, "frontRoute": "views/statistics/order", "parentId": "1367040819248234496", "sortOrder": 0.0, "permission": "/manager/store*,/manager/member*,/manager/statistics*"}, {"id": "1367053087121866752", "createBy": "admin", "createTime": "2021-03-03 10:01:57", "updateBy": "admin", "updateTime": "2023-06-16 17:41:13", "deleteFlag": false, "title": "流量统计", "name": "preview", "path": "preview", "level": 1, "frontRoute": "views/statistics/preview", "parentId": "1367040819248234496", "sortOrder": 0.0, "permission": "/manager/store*,/manager/member*,/manager/statistics*"}]}, {"id": "1348810750596767744", "createBy": "admin", "createTime": "2021-01-12 09:55:17", "updateBy": "admin", "updateTime": "2021-01-15 09:42:50", "deleteFlag": false, "title": "设置", "name": "mine", "path": "mine", "icon": "icon-settings", "level": 0, "frontRoute": "1", "parentId": "0", "sortOrder": 2.0, "permission": null, "children": [{"id": "1349237207378714623", "createBy": "admin", "createTime": "2021-01-13 14:09:53", "updateBy": "admin", "updateTime": "2023-03-17 20:24:50", "deleteFlag": false, "title": "PC店铺装修", "name": "pc", "path": "pc", "level": 1, "frontRoute": "views/operation/decoration/pc/index", "parentId": "1348810750596767744", "sortOrder": 0.0, "permission": "/manager/passport/user*,/manager/permission*"}, {"id": "1349237207378714622", "createBy": "admin", "createTime": "2021-01-13 14:09:53", "updateBy": "admin", "updateTime": "2023-03-17 20:24:50", "deleteFlag": false, "title": "移动端店铺装修", "name": "app", "path": "app", "level": 1, "frontRoute": "views/operation/decoration/app/index", "parentId": "1348810750596767744", "sortOrder": 0.0, "permission": "/manager/passport/user*,/manager/permission*"}, {"id": "1349237207378714624", "createBy": "admin", "createTime": "2021-01-13 14:09:53", "updateBy": "admin", "updateTime": "2023-03-17 20:24:50", "deleteFlag": false, "title": "店铺设置", "name": "setting", "path": "setting", "level": 1, "frontRoute": "views/store/setting/index", "parentId": "1348810750596767744", "sortOrder": 0.0, "permission": "/manager/passport/user*,/manager/permission*"}, {"id": "1357873097859923969", "createBy": "admin", "createTime": "2021-01-13 14:09:53", "updateBy": "admin", "updateTime": "2023-03-17 20:24:50", "deleteFlag": false, "title": "OSS资源", "name": "ossManage", "path": "ossManage", "level": 1, "frontRoute": "views/store/ossManage/index", "parentId": "1348810750596767744", "sortOrder": 0.0, "permission": "/manager/passport/user*,/manager/permission*"}, {"id": "1349255214977015808", "createBy": "admin", "createTime": "2021-01-13 14:09:53", "updateBy": "admin", "updateTime": "2023-03-17 20:24:50", "deleteFlag": false, "title": "配送模板", "name": "ship-template", "path": "ship-template", "level": 1, "frontRoute": "views/store/ship-template/ship-template", "parentId": "1348810750596767744", "sortOrder": 0.0, "permission": "/manager/passport/user*,/manager/permission*"}, {"id": "1349255404425338880", "createBy": "admin", "createTime": "2021-01-13 14:09:53", "updateBy": "admin", "updateTime": "2023-03-17 20:24:50", "deleteFlag": false, "title": "物流公司", "name": "logistics", "path": "logistics", "level": 1, "frontRoute": "views/store/logistics/logistics", "parentId": "1348810750596767744", "sortOrder": 0.0, "permission": "/manager/passport/user*,/manager/permission*"}, {"id": "1349237129847005184", "createBy": "admin", "createTime": "2021-01-13 14:09:34", "updateBy": "admin", "updateTime": "2023-03-17 20:25:34", "deleteFlag": false, "title": "店员管理", "name": "clerk-manage", "path": "clerk-manage", "level": 1, "frontRoute": "views/store/system/clerk/clerk-manage", "parentId": "1348810750596767744", "sortOrder": 1.0, "permission": "/manager/setting*,/manager/common*"}, {"id": "1349246347597602816", "createBy": "admin", "createTime": "2021-01-13 14:09:53", "updateBy": "admin", "updateTime": "2023-03-17 20:24:50", "deleteFlag": false, "title": "部门管理", "name": "store-department-manage", "path": "store-department-manage", "level": 1, "frontRoute": "views/store/system/department/store-department-manage", "parentId": "1348810750596767744", "sortOrder": 0.0, "permission": "/manager/common/file*"}, {"id": "1349246468775239680", "createBy": "admin", "createTime": "2021-01-13 14:09:53", "updateBy": "admin", "updateTime": "2023-03-17 20:24:50", "deleteFlag": false, "title": "角色权限", "name": "store-role-manage", "path": "store-role-manage", "level": 1, "frontRoute": "views/store/system/role/store-role-manage", "parentId": "1348810750596767744", "sortOrder": 0.0, "permission": "/manager/setting/region*"}]}, {"id": "1348810864748945408", "createBy": "admin", "createTime": "2021-01-12 09:55:45", "updateBy": "admin", "updateTime": "2021-03-15 20:57:12", "deleteFlag": false, "title": "消息", "icon": "icon-message", "name": "message-module", "path": "message-module", "level": 0, "frontRoute": "null", "parentId": "0", "sortOrder": 3.0, "permission": "/manager/setting/log*", "children": [{"id": "1349237928434098176", "createBy": "admin", "createTime": "2021-01-14 06:13:03", "updateBy": "admin", "updateTime": "2023-08-28 17:03:09", "deleteFlag": false, "title": "日志管理", "name": "message", "path": "message", "level": 1, "frontRoute": "views/message/index", "parentId": "1348810864748945408", "sortOrder": 1.0, "permission": ""}]}]}}