<template>
  <div>
    <a-card :style="{ width: '100%' }">
      <a-form :model="liveForm" :style="{ width: '600px' }">
        <a-form-item label="直播标题" field="name">
          <a-input v-model="liveForm.name" disabled />
          <template #extra>
            <div class="tips"
              >直播间名字，最短3个汉字，最长17个汉字，1个汉字相当于2个字符</div
            >
          </template>
        </a-form-item>
        <a-form-item label="主播昵称" field="anchorName">
          <a-input v-model="liveForm.anchorName" disabled />
          <template #extra>
            <div class="tips"
              >主播昵称，最短2个汉字，最长15个汉字，1个汉字相当于2个字符</div
            >
          </template>
        </a-form-item>
        <a-form-item label="直播时间" field="startTime">
          <a-range-picker
            v-model="liveForm.rangeTime"
            disabled
            style="width: 254px; margin-bottom: 20px"
          />
        </a-form-item>
        <a-form-item label="主播微信号" field="anchorWechat">
          <a-input
            v-model="liveForm.anchorWechat"
            disabled
            placeholder="主播微信号"
          />
          <template #extra>
            <div class="tips"
              >主播微信号，如果未实名认证，需要先前往“小程序直播”小程序进行<a
                target="_black"
                href="https://res.wx.qq.com/op_res/9rSix1dhHfK4rR049JL0PHJ7TpOvkuZ3mE0z7Ou_Etvjf-w1J_jVX0rZqeStLfwh"
                >实名验证</a
              >
            </div>
          </template>
        </a-form-item>
        <!-- 分享卡片 -->
        <a-form-item label="分享卡片封面" field="feedsImg">
          <div v-if="liveForm.feedsImg" class="upload-list">
            <a-image width="100%" height="100%" :src="liveForm.feedsImg" />
          </div>
          <template #extra>
            直播间分享图，图片规则：建议像素800*640，大小不超过1M；
          </template>
        </a-form-item>
        <a-form-item label="直播间背景墙" field="coverImg">
          <div v-if="liveForm.coverImg" class="upload-list">
            <a-image width="100%" height="100%" :src="liveForm.coverImg" />
          </div>
          <template #extra>
            直播间背景图，图片规则：建议像素1080*1920，大小不超过1M
          </template>
        </a-form-item>
        <a-form-item label="直播间分享图" field="shareImg">
          <div v-if="liveForm.shareImg" class="upload-list">
            <a-image width="100%" height="100%" :src="liveForm.shareImg" />
          </div>
          <template #extra>
            直播间分享图，图片规则：建议像素800*640，大小不超过1M
          </template>
        </a-form-item>
        <a-form-item v-if="route.query.id" label="商品">
          <a-table
            class="goods-table"
            :columns="columns"
            :data="liveData"
            :pagination="false"
          >
            <template #goodsName="{ record, rowIndex }">
              <div class="flex-goods">
                <a-badge
                  v-if="rowIndex == 0 || rowIndex == 1"
                  status="danger"
                />
                <img
                  class="thumbnail"
                  :src="record.thumbnail || record.goodsImage"
                />
                {{ record.goodsName || record.name }}
              </div>
            </template>
            <template #price="{ record }">
              {{ unitPrice(record.price, '￥') }}
            </template>
          </a-table>
          <template #extra>
            <div class="tips">
              直播间商品中前两个商品将自动被选为封面，伴随直播间在直播列表中显示
            </div>
          </template>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { getLiveInfo } from '@/api/promotion';
  import { useRoute } from 'vue-router';
  import { unixToDate, unitPrice } from '@/utils/filters';

  const route = useRoute();
  const liveForm = ref<any>({
    name: '', // 直播标题
    anchorName: '', // 主播昵称
    anchorWechat: '', // 主播微信号
    feedsImg: '', // 分享卡片封面
    coverImg: '', // 直播间背景墙
    shareImg: '', // 分享图
    startTime: '',
    rangeTime: [],
  });
  const columns = [
    {
      title: '商品',
      dataIndex: 'goodsName',
      width: 300,
      slotName: 'goodsName',
    },
    {
      title: '价格',
      dataIndex: 'price',
      width: 300,
      slotName: 'price',
    },
    {
      title: '库存',
      dataIndex: 'quantity',
      width: 300,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 300,
    },
  ];
  const liveData = ref([]); // 直播商品集合
  const getLiveDetail = async () => {
    const res = await getLiveInfo(route.query.id);
    if (res.data.success) {
      const data = res.data.result;
      liveForm.value = data;
      liveData.value = data.commodityList;
      if (data.startTime && data.endTime) {
        liveForm.value.rangeTime = [unixToDate(data.startTime), unixToDate(data.endTime),  ];
      }
    }
  };
  onMounted(() => {
    getLiveDetail();
  });
</script>

<style lang="less" scoped>
  .action {
    display: flex;
  }

  .original-price {
    margin-left: 10px;
    color: #999;
    text-decoration: line-through;
  }

  .thumbnail {
    width: 50px;
    height: 50px;
    border-radius: 0.4em;
  }

  .flex-goods {
    margin: 10px;
    display: flex;

    align-items: center;

    > img {
      margin-right: 10px;
    }
  }

  .tips {
    color: #999;
    font-size: 12px;
  }

  .goods-table {
    width: 1000px;
    margin: 10px 0;
  }

  .upload-list {
    display: inline-block;
    width: 60px;
    height: 60px;
    text-align: center;
    line-height: 60px;
    border: 1px solid transparent;
    border-radius: 4px;
    overflow: hidden;
    background: #fff;
    position: relative;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
    margin-right: 4px;
  }

  .upload-list img {
    width: 100%;
    height: 100%;
  }

  .upload-list-cover {
    display: none;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.6);
  }

  .upload-list:hover .upload-list-cover {
    display: block;
  }

  .upload-list-cover i {
    color: #fff;
    font-size: 20px;
    cursor: pointer;
    margin: 0 2px;
  }
</style>
