<template>
  <div>
    <a-card :bordered="false">
      <a-form
        ref="modifyPriceForm"
        :model="form"
        :style="{ width: '100%' }"
        layout="horizontal"
        auto-label-width
        time-type="timestamp"
      >
        <div class="base-info-item">
          <h4>活动信息</h4>
          <div class="form-item-view">
            <a-form-item field="promotionName" label="活动名称">
              <a-input
                v-model="form.promotionName"
                allow-clear
                :style="{ width: '260px' }"
                disabled
              />
            </a-form-item>
            <a-form-item field="rangeTime" label="活动时间">
              <a-range-picker
                v-model="form.rangeTime"
                style="width: 254px; margin-bottom: 20px"
                disabled
              />
            </a-form-item>
            <a-form-item field="description" label="活动描述">
              <a-textarea
                v-model="form.description"
                allow-clear
                style="width: 260px"
                disabled
              />
            </a-form-item>
          </div>
          <h4>优惠设置</h4>
          <div class="form-item-view">
            <a-form-item field="fullMoney" label="优惠门槛">
              <a-input
                v-model="form.fullMoney"
                allow-clear
                :style="{ width: '260px' }"
                disabled
              />
              <span class="describe">消费达到当前金额可以参与优惠</span>
            </a-form-item>
            <a-form-item field="fullMoney" label="赠送优惠券">
              <a-radio-group v-model="form.discountType" type="button">
                <a-radio value="fullMinusFlag" disabled>减现金</a-radio>
                <a-radio value="fullRateFlag" disabled>打折</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item
              v-if="form.discountType == 'fullMinusFlag'"
              field="fullMinus"
              label="优惠金额"
            >
              <a-input
                v-model="form.fullMinus"
                allow-clear
                :style="{ width: '260px' }"
                disabled
              />
            </a-form-item>
            <a-form-item
              v-if="form.discountType == 'fullRateFlag'"
              field="fullRate"
              label="优惠折扣"
            >
              <a-input
                v-model="form.fullRate"
                allow-clear
                :style="{ width: '260px' }"
                disabled
              />
              <span class="describe">优惠折扣为0-10之间数字，可有一位小数</span>
            </a-form-item>
            <a-form-item field="fullRate" label="额外赠送">
              <a-checkbox v-model="form.freeFreightFlag" disabled
                >免邮费</a-checkbox
              >&nbsp;
              <a-checkbox v-model="form.couponFlag" disabled
                >送优惠券</a-checkbox
              >&nbsp;
              <a-checkbox v-model="form.giftFlag" disabled>送赠品</a-checkbox
              >&nbsp;
              <a-checkbox v-model="form.pointFlag" disabled>送积分</a-checkbox
              >&nbsp;
            </a-form-item>
            <a-form-item
              v-if="form.couponFlag"
              field="couponId"
              label="赠送优惠券"
            >
              <a-select
                v-model="form.couponId"
                allow-search
                style="width: 280px"
                :disabled="form.promotionStatus != 'NEW'"
                @search="handleSearchcoupon"
              >
                <a-option
                  v-for="item in couponList"
                  :key="item.id"
                  :value="item.id"
                >
                  {{ item.couponName }}
                </a-option>
              </a-select>
            </a-form-item>
            <a-form-item v-if="form.giftFlag" field="giftId" label="赠品">
              <a-select
                v-model="form.giftId"
                allow-search
                style="width: 280px"
                @search="handleSearchgift"
              >
                <a-option
                  v-for="item in giftList"
                  :key="item.id"
                  :value="item.id"
                >
                  {{ item.goodsName }}
                </a-option>
              </a-select>
            </a-form-item>
            <a-form-item v-if="form.pointFlag" field="point" label="赠积分">
              <a-input-number
                v-model="form.point"
                :style="{ width: '260px' }"
                disabled
              />
            </a-form-item>
            <a-form-item field="scopeType" label="使用范围">
              <a-radio-group v-model="form.scopeType" type="button">
                <a-radio value="ALL" disabled>全品类</a-radio>
                <a-radio value="PORTION_GOODS" disabled>指定商品</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item
              v-if="form.scopeType == 'PORTION_GOODS'"
              style="width: 100%"
            >
              <a-table :columns="columns" :data="form.promotionGoodsList">
                <template #goodsName="{ record }">
                  <div>
                    <a
                      class="mr_10"
                      @click="store.viewGoodsDetail(record.goodsId, record.skuId)"
                      >{{ record.goodsName }}</a
                    >
                  </div>
                </template>
              </a-table>
            </a-form-item>
            <div>
              <a-button @click="router.push({ name: 'full-discount' })"
                >返回</a-button
              >
            </div>
          </div>
        </div>
      </a-form>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { getGoodsListData } from '@/api/goods';
import { getFullDiscountById, getPlatformCouponList } from '@/api/promotion';
import { usePathJumpStore } from '@/store/index';
import getAssetsImages from '@/utils/assetsImages';
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

  const store = usePathJumpStore();
  const qrcode = getAssetsImages('qrcode.svg');
  const route = useRoute();
  const router = useRouter();
  const form = ref<any>({
    promotionName: '',
    rangeTime: [],
    description: '',
    fullMoney: '',
    discountType: '',
    fullMinus: '',
    fullRate: '',
    freeFreightFlag: false,
    couponFlag: false,
    giftFlag: false,
    pointFlag: false,
    promotionStatus: '',
    couponId: '',
    giftId: '',
    point: 0,
    scopeType: '',
    promotionGoodsList: [],
  });
  const columns = [
    {
      title: '商品名称',
      dataIndex: 'goodsName',
      width: 300,
      slotName: 'goodsName',
    },
    {
      title: '商品价格',
      dataIndex: 'price',
      width: 300,
    },
    {
      title: '库存',
      dataIndex: 'quantity',
      width: 300,
    },
  ];
  const couponList = ref([]) as any; // 优惠券列表
  const giftList = ref([]) as any; // 赠品列表
  // 获取活动详情
  const getDetail = async () => {
    const res = await getFullDiscountById(route.query.id);
    if (res.data.success) {
      const data = res.data.result;
      if (data.scopeType == 'ALL') {
        data.promotionGoodsList = [];
      }
      if (data.fullMinusFlag) {
        data.discountType = 'fullMinusFlag';
        delete data.fullMinusFlag;
      } else {
        data.discountType = 'fullMinusFlag';
        delete data.fullRateFlag;
      }
      data.rangeTime = [];
      data.rangeTime.push(new Date(data.startTime), new Date(data.endTime));
      form.value = data;
    }
  };
  const getCouponList = async (val?: any) => {
    const params = {
      pageSize: 10,
      pageNumber: 0,
      getType: 'ACTIVITY',
      storeId: '',
      couponName: val,
      promotionStatus: 'START',
    };
    const res = await getPlatformCouponList(params);
    if (res.data.success) {
      couponList.value = res.data.result.records;
    }
  };
  const getGiftList = async (val?: any) => {
    // 赠品列表
    const params = {
      pageSize: 10,
      pageNumber: 1,
      id: form.value.giftId ? null : form.value.giftFlag,
      goodsName: val === form.value.giftId ? null : val,
      marketEnable: 'UPPER',
      authFlag: 'PASS',
    };
    const res = await getGoodsListData(params);
    if (res.data.success) {
      giftList.value = res.data.result.records;
    }
  };
  // 赠送优惠券搜索
  const handleSearchcoupon = (val: any) => {
    getCouponList(val);
  };
  // 赠品搜索
  const handleSearchgift = (val: any) => {
    getGiftList(val);
  };
  onMounted(() => {
    if (route.query.id) {
      getDetail();
    }
    getCouponList();
    getGiftList();
  });
</script>

<style lang="less" scoped>
  h4 {
    margin-bottom: 10px;
    padding: 0 10px;
    border: 1px solid #ddd;
    background-color: #f8f8f8;
    font-weight: bold;
    color: #333;
    font-size: 14px;
    line-height: 40px;
    text-align: left;
  }

  .describe {
    font-size: 12px;
    margin-left: 10px;
    color: #999;
  }
</style>
