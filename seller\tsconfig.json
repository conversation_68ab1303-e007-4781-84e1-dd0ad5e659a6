{
  "compilerOptions": {
    "noImplicitAny": false, // 是否在表达式和申明上有隐含的any类型时报错
    "target": "ES2020",
    "module": "ES2020",
    "moduleResolution": "node",
    "strict": true,
    "jsx": "preserve",
    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "./src/*"
      ],
      "~/*": [
        "./src/*"
      ],
    },
    "lib": ["es2020", "dom"],
    "skipLibCheck": true
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "auto-imports.d.ts",
    "components.d.ts",
    "node_modules/arco-design-pro-vite/components.d.ts",
    "shims.d.ts"
  ],
  "exclude": ["node_modules"]
}
