<template>
  <a-card class="general-card" title="发票管理" :bordered="false">
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :api="getReceiptPage"
      :api-params="apiParams"
    />
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { getReceiptPage } from '@/api/order';
  import { receiptStatus, orderStatusList } from '@/utils/tools';
  import { ref } from 'vue';

  const tablePageRef = ref('');

  const columnsSearch: Array<SearchRule> = [
    {
      label: '订单编号',
      model: 'orderSn',
      disabled: false,
      input: true,
    },
    {
      label: '会员名称',
      model: 'memberName',
      disabled: false,
      input: true,
    },
    {
      label: '店铺名称',
      model: 'storeName',
      disabled: false,
      input: true,
    },
    {
      label: '发票抬头',
      model: 'receiptTitle',
      disabled: false,
      input: true,
    },
    {
      label: '状态',
      model: 'receiptStatus',
      disabled: false,
      select: {
        options: receiptStatus,
      },
    },
  ];

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '订单号',
      dataIndex: 'orderSn',
    },
    {
      title: '会员名称',
      dataIndex: 'memberName',
    },
    {
      title: '店铺名称',
      dataIndex: 'storeName',
    },
    {
      title: '发票抬头',
      dataIndex: 'receiptTitle',
      empty: '暂未填写',
    },
    {
      title: '纳税人识别号',
      dataIndex: 'taxpayerId',
      empty: '暂未填写',
    },
    {
      title: '发票内容',
      dataIndex: 'receiptContent',
    },
    {
      title: '发票金额',
      dataIndex: 'billPrice',
      currency: true,
    },
    {
      title: '发票状态',
      dataIndex: 'receiptStatus',
      slot: true,
      slotData: {
        tag: receiptStatus,
      },
    },
    {
      title: '订单状态',
      dataIndex: 'orderStatus',
      slot: true,
      slotData: {
        tag: orderStatusList,
      },
    },
  ];

  const apiParams = ref({
    billStatus: 'OUT',
  });
</script>

<style scoped></style>
