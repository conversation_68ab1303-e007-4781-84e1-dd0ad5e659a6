<template>
  <div>
    <div align="right">
      <a-button type="primary" status="warning" @click="nextStep('3')" size="small">上一步</a-button>
      <a-button type="primary" style="margin-left: 20px;" @click="nextStep('5')" size="small">下一步</a-button>
    </div>

    <a-form ref="salesSceneInfoFromRef" :model="mainForm">
      <a-form-item field="sales_scene_info.store_name" label="店铺名称" :rules="[REQUIRED]">
        <a-input style="width: 400px;" v-model="mainForm.sales_scene_info.store_name" placeholder="请输入店铺全称"/>
      </a-form-item>
      <a-form-item field="sales_scene_info.store_url" label="店铺链接">
        <a-input style="width: 400px;" v-model="mainForm.sales_scene_info.store_url" placeholder="请输入店铺来链接"/>
        <template #extra>
          <div>
            <p>1、店铺二维码or店铺链接二选一必填。</p>
            <p>2、请填写店铺主页链接、需符合网站规范。</p>
          </div>
        </template>
      </a-form-item>
      <a-form-item field="sales_scene_info.store_qr_code" label="店铺二维码">
        <div>
          <a-avatar :size="100" shape="square" @click="handleClickImg('store_qr_code')" style="margin-right: 20px;">
            <icon-plus v-if="!mainForm.sales_scene_info.store_qr_code" />
            <img alt="avatar" v-if="mainForm.sales_scene_info && mainForm.sales_scene_info.store_qr_code" :src="mainForm.sales_scene_info.store_qr_code" />
          </a-avatar>
        </div>
      </a-form-item>
      <a-form-item v-if="mainForm.organization_type !== '2401'" field="store_name" label="小程序appID">
        <a-input style="width: 400px;" v-model="mainForm.sales_scene_info.mini_program_sub_appid" placeholder="请输入小程序appID"/>
      </a-form-item>
    </a-form>

    <a-modal v-model:visible="showOssManager" :width="1100"  @ok="handleOss" title="oss资源管理" @cancel="showOssManager = false">
      <ossManages :initialize="showOssManager" @selected="changOssImage" :isSingle="true"></ossManages>
    </a-modal>

  </div>
</template>

<script setup lang="ts">
  import { onMounted, watch, computed, ref } from 'vue';
  import { REQUIRED, VARCHAR20 } from '@/utils/validator';
  import ossManages from '@/components/oss-manage/index.vue';
  import { useAppStore } from '@/store';

  const appStore = useAppStore();
  const emit = defineEmits(['callbackTab']);
  const props = defineProps({
    submitFrom: {
      type: Object,
      default: () => {},
    }
  });

  const salesSceneInfoFromRef = ref<any>('');
  const mainForm = ref<any>({
    contact_info: {}
  });  // 表单主体内容
  const showOssManager = ref<boolean>(false); // oss弹框
  const selectedFormBtnName = ref<string>(''); // 点击图片绑定form
  const picIndex = ref<any>(0); // 存储身份证图片下标，方便赋值
  const selectedSku = ref(); // 选择的sku

  // 选择图片
  const handleClickImg = (val: string, index?: number) => {
    selectedFormBtnName.value = val;
    picIndex.value = index;
    showOssManager.value = true;
  };
  // oss资源改变
  const changOssImage = (val:any) => {
    selectedSku.value = [];
    val.forEach((item:any)=>{
      selectedSku.value.push({url:item.url})
    })
  };
  // oss资源确定
  const handleOss = () => {
    showOssManager.value = false;
    let currentUrl = selectedSku.value[selectedSku.value.length -1].url;  // 当前选择的图片
    mainForm.value.sales_scene_info[selectedFormBtnName.value] = currentUrl;
  };

  // 上一步/下一步
  const nextStep = (name) => {
    emit("callbackTab", name);
  };
  // 表单校验
  const checkoutForm = async () => {
    const auth = await salesSceneInfoFromRef.value?.validate();
    if (!auth) {
      return true;
    } else {
      return false;
    }
  };
  // 组件暴露自己的属性
  defineExpose({
    checkoutForm
  });

  watch(() => props.submitFrom, (val) => {
    mainForm.value = val;
  }, { immediate: true, deep: true });
</script>

<style scoped lang="less">

</style>