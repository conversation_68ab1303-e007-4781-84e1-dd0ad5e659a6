import ArcoVue from '@arco-design/web-vue';
import ArcoVueIcon from '@arco-design/web-vue/es/icon';
import { autoAnimatePlugin } from '@formkit/auto-animate/vue';
import { createApp } from 'vue';
import print from 'vue3-print-nb';
import directive from './directive';
import router from './router';
import store from './store';

import '@/assets/style/global.less';
import '@arco-design/web-vue/dist/arco.css';
import { ColorPicker as TColorPicker } from 'tdesign-vue-next';
import App from './App.vue';
import storage from '@/utils/storage';
import { initRouterNode } from '@/hooks/roleRouter';
const app = createApp(App);
/** 引入uno.css，不引入不生效 */
import 'uno.css';

// 新增新页面打开
app.config.globalProperties.$openWindow = (path:any) => {
  const routerUrl = router.resolve(path);
  window.open(routerUrl.href, '_blank');
};


declare module 'vue' {
  export interface ComponentCustomProperties {
    $openWindow: any,
  }
}


const starter = async () => {
  if (storage.getAccessToken()) {
    await initRouterNode()
  }
  app.use(router).use(directive).mount('#app')
}

app.use(ArcoVue, {});
app.use(ArcoVueIcon);
app.use(TColorPicker),
app.use(print);
app.use(autoAnimatePlugin);
app.use(store);
starter()
