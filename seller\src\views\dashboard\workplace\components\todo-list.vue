<!-- eslint-disable vue/no-use-v-if-with-v-for,vue/no-confusing-v-for-v-if -->
<template>
  <a-card
    class="general-card"
    :header-style="{ paddingBottom: { value: 0, label: '' } }"
    :body-style="{
      paddingTop: '20px',
    }"
    title="待办事项"
  >
    <a-grid :cols="24" :row-gap="16" class="panel">
      <a-grid-item
        v-for="(item, index) in Object.keys(todoList.list)"
        :key="index"
        class="panel-col"
        :span="{ xs: 12, sm: 12, md: 12, lg: 12, xl: 12, xxl: 6 }"
      >
        <a-typography>
          <a-typography-title :heading="6">
            {{ todoList.list[item]._value }}
          </a-typography-title>
          <!-- 循环内容 -->
          <div class="flex todo-item">
            <div
              v-for="(child, childIndex) in Object.keys(
                todoList.list[item]
              ).filter((todo) => {
                return todo !== '_value';
              })"
              :key="childIndex"
            >
              <a-statistic
                :title="todoList.list[item][child].label"
                :value="todoList.list[item][child].value"
                :value-style="{
                  color: todoList.list[item][child].value > 0 ? '#1E80FF' : '',
                }"
                cursor-pointer
                :precision="0"
                :value-from="0"
                animation
                show-group-separator
                @click="$router.push({ name: todoList.list[item][child].name })"
              >
              </a-statistic>
            </div>
          </div>
        </a-typography>
      </a-grid-item>

      <a-grid-item :span="24">
        <a-divider class="panel-border" />
      </a-grid-item>
    </a-grid>
  </a-card>
</template>

<script lang="ts" setup>
  import { reactive, watch } from 'vue';

  const props = defineProps({
    res: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });

  const todoList: any = reactive({
    list: {
      // 交易前
      beforePay: {
        _value: '交易前',
        unPaidOrder: { value: 0, label: '待付款', name: 'order-list' },
      },
      // 交易中
      pay: {
        _value: '交易中',
        unDeliveredOrder: { value: 0, label: '待发货', name: 'order-list' },
        deliveredOrder: { value: 0, label: '待收货', name: 'order-list' },
      },
      // 交易后
      afterPay: {
        _value: '交易后',
        returnMoney: { value: 0, label: '退款', name: 'return-money' },
        returnGoods: { value: 0, label: '退货', name: 'return-goods' },
        memberEvaluation: {
          value: 0,
          label: '待评价',
          name: 'member-content-list',
        },
      },
      // 投诉
      complaint: {
        _value: '投诉',
        complaint: { value: 0, label: '待处理', name: 'order-complaint' },
      },

      // 商品
      goods: {
        _value: '商品',
        waitUpper: { value: 0, label: '待上架', name: 'goods-list' },
        waitAuth: { value: 0, label: '审核中', name: 'goods-list' },
      },
      // 其他
      other: {
        _value: '其他',
        seckillNum: { value: 0, label: '秒杀活动', name: 'seckill' },
        waitPayBill: {
          value: 0,
          label: '等待对账',
          name: 'bill-verify',
        },
      },
    },
  });
  watch(
    () => props.res,
    (res) => {
      if (res) {
        // 循环判断和后端返回的字段对应后进行赋值操作
        Object.keys(res).forEach((key: string) => {
          Object.keys(todoList.list).forEach((item: any) => {
            if (todoList.list[item][key]?.label && item != '_value') {
              todoList.list[item][key].value = res[key];
            }
          });
        });
      }
    },
    { deep: true, immediate: true }
  );
</script>

<style lang="less" scoped>
  .panel-col {
    padding-left: 43px;
    border-right: 1px solid rgb(var(--gray-2));
  }
  .todo-item {
    display: flex;
    > div {
      flex: 1;
    }
  }
</style>
