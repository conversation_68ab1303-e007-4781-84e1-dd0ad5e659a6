import request, { Method } from '@/utils/axios';
import { ParamsRule } from '@/types/global';

// eslint-disable-next-line import/prefer-default-export
// 获取所有物流公司
export function getLogistics(params: ParamsRule) {
  return request({
    url: '/other/logistics',
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 返回物流公司信息
export function getIsCheck(logisticsId: string | number) {
  return request({
    url: `other/logistics/${logisticsId}/getStoreLogistics`,
    method: Method.GET,
    needToken: true,
  });
}

// 开启物流公司
export function logisticsChecked(id: string | number, params: ParamsRule) {
  return request({
    url: `/other/logistics/${id}`,
    method: Method.POST,
    needToken: true,
    data: params,
    headers: { 'Content-Type': 'application/json' },
  });
}

// 关闭物流公司
export function logisticsUnChecked(id: string | number, params?: ParamsRule) {
  return request({
    url: `/other/logistics/${id}`,
    method: Method.DELETE,
    needToken: true,
    params,
  });
}

// 修改电子面单等信息
export function editChecked(logisticsId: string | number, params: ParamsRule) {
  return request({
    url: `/other/logistics/${logisticsId}/updateStoreLogistics`,
    method: Method.PUT,
    needToken: true,
    params,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  });
}

export function getCheckedOn(){
  return request({
    url: `/other/logistics/getCheckedFaceSheet`,
    method: Method.GET,
    needToken: true,
  });
}
