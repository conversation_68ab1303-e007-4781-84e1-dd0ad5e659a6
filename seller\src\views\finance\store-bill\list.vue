<template>
  <a-card class="general-card" title="店铺结算" :bordered="false">
    <a-tabs @change="(val)=>{apiParams.billStatus = val}" :default-active-key="billStatusVar">
      <a-tab-pane key="OUT" title="待对账"></a-tab-pane>
      <a-tab-pane key="CHECK" title="待结算"></a-tab-pane>
      <a-tab-pane key="COMPLETE" title="已完成"></a-tab-pane>
    </a-tabs>
    <searchTable
      :columns="columnsSearch"
      @reset="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
      @search="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
    ></searchTable>

    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getBillPage"
      :api-params="apiParams"
      :bordered="true"
      @detail="detailed"
    />
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { getBillPage } from '@/api/finance';
  import { billStatus, billStatusSelect } from '@/utils/tools';
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';

  const router = useRouter();

  const tablePageRef = ref('');
  const billStatusVar = ref<string>('OUT')
  const apiParams = ref<any>({billStatus:billStatusVar.value});

  const columnsSearch: Array<SearchRule> = [
    {
      label: '时间',
      disabled: false,
      datePicker: {
        type: 'range',
      },
    },
  ];

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '账单号',
      dataIndex: 'sn',
    },
    {
      title: '生成时间',
      dataIndex: 'createTime',
    },
    {
      title: '结算时间段',
      dataIndex: 'startTime',
    },
    {
      title: '结算金额',
      dataIndex: 'billPrice',
      currency: true,
    },
    {
      title: '状态',
      dataIndex: 'billStatus',
      slot: true,
      slotData: {
        badge: billStatus,
      },
    },
  ];

  const sortMethods: MethodsRule = {
    title: '操作',
    width: 100,
    methods: [
      {
        title: '查看',
        callback: 'detail',
        type: 'text',
        status: 'success',
      },
    ],
  };
  // 详细
  const detailed = (v: any) => {
    localStorage.setItem('settleID', v.record.id);
    router.push({
      name: 'store-bill-detail',
      query: {
        id: v.record.id,
      },
    });
  };
</script>

<style scoped></style>
