<template>
  <a-card class="general-card" title="满额活动" :bordered="false">
    <a-tabs @change="(val)=>{apiParams.promotionStatus = val}" :default-active-key="discountStatus">
      <a-tab-pane key="NEW" title="未开始"></a-tab-pane>
      <a-tab-pane key="START" title="已开始"></a-tab-pane>
      <a-tab-pane key="END" title="已结束"></a-tab-pane>
      <a-tab-pane key="CLOSE" title="已关闭"></a-tab-pane>
    </a-tabs>
    <searchTable :columns="columnsSearch" time-type="timestamp" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getFullDiscountList"
      :api-params="apiParams"
      :bordered="true"
    >
      <template #time="{ data }"
        >{{ data.startTime }}<span style="margin: 0 10px">-</span
        >{{ data.endTime }}</template
      >
      <template #view="{ data }">
        <a-button type="text" status="success" @click="view(data)"
          >查看</a-button
        >
      </template>
      <template #close="{ data }">
        <a-button
          v-if=" data.promotionStatus === 'NEW' || data.promotionStatus === 'START' "
          type="text"
          status="danger"
          @click="openOrClose(data)"
          >关闭</a-button
        >
      </template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
  import { getFullDiscountList, updateFullDiscount } from '@/api/promotion';
import searchTable from '@/components/search-column/index.vue';
import tablePage from '@/components/table-pages/index.vue';
import useCurrentInstance from '@/hooks/useCurrentInstance';
import { ColumnsDataRule, MethodsRule, SearchRule } from '@/types/global';
import {
fullMinusStatus,
promotionStatus,
promotionStatusSelect,
} from '@/utils/tools';
import { Message } from '@arco-design/web-vue';
import { ref } from 'vue';
import { useRouter } from 'vue-router';

  //  获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;
  const router = useRouter();
  const tablePageRef = ref<any>();
  const discountStatus = ref<string>('START')
  const apiParams = ref<any>({sort: 'startTime',promotionStatus:discountStatus.value});
  const columnsSearch: Array<SearchRule> = [
    {
      label: '活动名称',
      model: 'promotionName',
      disabled: false,
      input: true,
    },
    {
      label: '活动时间',
      model: 'selectDate',
      disabled: false,
      datePicker: {
        type: 'range',
      },
    },
  ];

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '活动名称',
      dataIndex: 'promotionName',
      width:200,
    },
    {
      title: '活动时间',
      dataIndex: 'time',
      width:200,
      slot: true,
      slotTemplate: 'time',
    },

    {
      title: '活动类型',
      dataIndex: 'fullMinusFlag',
      width:100,
      slot: true,
      slotData: {
        badge: fullMinusStatus,
      },
    },
    {
      title: '活动状态',
      dataIndex: 'promotionStatus',
      slot: true,
      width:100,
      slotData: {
        badge: promotionStatus,
      },
    },
  ];

  const sortMethods: MethodsRule = {
    title: '操作',
    width: 200,
    methods: [
      {
        title: '查看',
        callback: 'view',
        slot: true,
        slotTemplate: 'view',
      },
      {
        title: '关闭',
        callback: 'close',
        slot: true,
        slotTemplate: 'close',
      },
    ],
  };
  // 查看
  const view = (val: any) => {
    router.push({
      name: 'full-discount-detail',
      query: {
        id: val.id,
      },
    });
  };
  // 关闭
  const openOrClose = (row: any) => {
    let name = '开启';
    if (row.promotionStatus == 'START') {
      name = '关闭';
    }
    modal.confirm({
      title: '提示',
      content: `确认${name}此活动吗?需要一定时间才能生效，请耐心等待`,
      alignCenter: false,
      onOk: async () => {
        const res = await updateFullDiscount(row.id);
        if (res.data.success) {
          Message.success(`${name}成功`);
          tablePageRef.value.init();
        }
      },
    });
    console.log(row);
  };
</script>
