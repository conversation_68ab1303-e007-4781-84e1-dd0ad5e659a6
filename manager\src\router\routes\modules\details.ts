import { DEFAULT_LAYOUT } from '@/router/constans';

export default {
  path: '/detail',
  name: 'detail',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: '详情',
    requiresAuth: true,
    icon: 'icon-user',
    order: 0,
  },
  children: [
    {
      path: 'order-detail',
      name: 'order-detail',
      component: () => import('@/views/order/order/orderDetail.vue'),
      meta: {
        locale: '订单详情',
        requiresAuth: true,
        hideInMenu: true
      },
    },
    {
      path: 'after-order-detail',
      name: 'after-order-detail',
      component: () => import('@/views/order/after-sale/afterSaleOrder.vue'),
      meta: {
        locale: '售后管理详情',
        requiresAuth: true,
        hideInMenu: true
      },
    },
    {
      path: 'order-complaint-detail',
      name: 'order-complaint-detail',
      component: () => import('@/views/order/order-complaint/orderComplaintDetail.vue'),
      meta: {
        locale: '投诉详情',
        requiresAuth: true,
        hideInMenu: true
      },
    },
    {
      path: 'edit-platform-coupon',
      name: 'edit-platform-coupon',
      component: () => import('@/views/promotion/coupon/coupon-publish.vue'),
      meta: {
        locale: '编辑平台优惠券',
        requiresAuth: true,
        hideInMenu: true
      },
    },
    {
      path: 'add-coupon-activity',
      name: 'add-coupon-activity',
      component: () => import('@/views/promotion/coupon-activity/coupon-publish.vue'),
      meta: {
        locale: '编辑平台优惠券',
        requiresAuth: true,
        hideInMenu: true
      },
    },
    {
      path: 'coupon-receive',
      name: 'coupon-receive',
      component: () => import('@/views/promotion/coupon/coupon-receive.vue'),
      meta: {
        locale: '优惠券领取记录',
        requiresAuth: true,
        hideInMenu: true
      },
    },
    {
      path: 'coupon-info',
      name: 'coupon-info',
      component: () => import('@/views/promotion/coupon-activity/coupon-info.vue'),
      meta: {
        locale: '券活动详情',
        requiresAuth: true,
        hideInMenu: true
      },
    },
    {
      path: 'full-discount-detail',
      name: 'full-discount-detail',
      component: () => import('@/views/promotion/full-discount/full-discount-detail.vue'),
      meta: {
        locale: '券活动详情',
        requiresAuth: true,
        hideInMenu: true
      },
    },
    {
      path: 'manager-seckill-add',
      name: 'manager-seckill-add',
      component: () => import('@/views/promotion/seckill/seckill-add.vue'),
      meta: {
        locale: '编辑秒杀活动',
        requiresAuth: true,
        hideInMenu: true
      },
    }, {
      path: 'add-kanJia-activity-goods',
      name: 'add-kanJia-activity-goods',
      component: () => import('@/views/promotion/bargain-activity/kanjia-activity-add-goods.vue'),
      meta: {
        locale: '添加砍价活动',
        requiresAuth: true,
        hideInMenu: true
      },
    },
    {
      path: 'edit-kanJia-activity-goods',
      name: 'edit-kanJia-activity-goods',
      component: () => import('@/views/promotion/bargain-activity/kanjia-activity-edit-goods.vue'),
      meta: {
        locale: '编辑砍价活动',
        requiresAuth: true,
        hideInMenu: true
      },
    },

    {
      path: 'live-detail',
      name: 'live-detail',
      component: () => import('@/views/promotion/live/live-detail.vue'),
      meta: {
        locale: '观看直播',
        requiresAuth: true,
        hideInMenu: true
      },
    },
    {
      path: 'add-points-goods',
      name: 'add-points-goods',
      component: () => import('@/views/promotion/integrate-goods/points-goods-add.vue'),
      meta: {
        locale: '添加积分商品',
        requiresAuth: true,
        hideInMenu: true
      },
    },
    {
      path: 'edit-points-goods',
      name: 'edit-points-goods',
      component: () => import('@/views/promotion/integrate-goods/points-goods-edit.vue'),
      meta: {
        locale: '修改积分商品',
        requiresAuth: true,
        hideInMenu: true
      },
    },
    {
      path: 'group-list',
      name: 'group-list',
      component: () =>
        import(
          '@/views/operation/distribution/distrbution-staff/group-list.vue'
        ),
      meta: {
        locale: '团队列表',
        requiresAuth: true,
        hideInMenu: true,
      },
    },
    {
      path: 'distribution-member',
      name: 'distribution-member',
      component: () =>
        import(
          '@/views/operation/distribution/distrbution-staff/distribution-member.vue'
        ),
      meta: {
        locale: '下级用户',
        requiresAuth: true,
        hideInMenu: true,
      },
    },
    {
      path: 'sub-ledger-detail',
      name: 'sub-ledger-detail',
      component: () => import('@/views/order/out-order-log/detail.vue'),
      meta: {
        locale: '分账详情',
        requiresAuth: true,
        hideInMenu: true
      },
    },
    {
      path: 'member-detail',
      name: 'member-detail',
      component: () => import('@/views/member/member-list/index.vue'),
      meta: {
        locale: '会员详情',
        requiresAuth: true,
        hideInMenu: true
      },
    },
    {
      path: 'vip-user-detail',
      name: 'vip-user-detail',
      component: () => import('@/views/promotion/vipuser/detail.vue'),
      meta: {
        locale: 'VIP会员详情',
        requiresAuth: true,
        hideInMenu: true
      },
    },
    {
      path: 'goods-detail',
      name: 'goodsdetail',
      component: () => import('@/components/goods/goodsDetail.vue'),
      props: true,
      meta: {
        locale: '商品详情',
        requiresAuth: true,
        hideInMenu: true,
      },
    },
    {
      path: 'parameter',
      name: 'parameter',
      component: () => import('@/views/goods/category/module/parameter.vue'),
      props: true,
      meta: {
        locale: '参数绑定',
        requiresAuth: true,
        hideInMenu: true
      },
    }, {
      path: 'shop-detail',
      name: 'shop-detail',
      component: () => import('@/views/shop/shop-list/shopDetail.vue'),
      meta: {
        locale: '店铺详细',
        requiresAuth: true,
        hideInMenu: true
      }
    },
    {
      path: 'bill-detail',
      name: 'bill-detail',
      component: () => import('@/views/shop/shop-settlement/bill-detail.vue'),
      meta: {
        locale: '结算单详情',
        requiresAuth: true,
        hideInMenu: true
      }
    }, {
      path: 'shop-operation',
      name: 'shop-operation',
      component: () => import('@/views/shop/shop-list/shopOperation.vue'),
      meta: {
        locale: '店铺操作',
        requiresAuth: true,
        hideInMenu: true
      }
    },
    {
      path: 'progress-form-list',
      name: 'progress-form-list',
      component: () => import('@/views/shop/construction/applicationFormList.vue'),
      meta: {
        locale: '微信进件',
        requiresAuth: true
      }
    },
    {
      path: 'submit-application',
      name: 'submit-application',
      component: () => import('@/views/shop/construction/tabApplication.vue'),
      meta: {
        locale: '提交申请单',
        requiresAuth: true,
        hideInMenu: true
      }
    },
    {
      path: 'seckill-goods',
      name: 'seckill-goods',
      component: () => import('@/views/promotion/seckill/seckill-goods.vue'),
      meta: {
        locale: '秒杀商品',
        requiresAuth: true,
        hideInMenu: true
      },
    }, {
      path: 'pintuan-goods',
      name: 'pintuan-goods',
      component: () => import('@/views/promotion/pintuan/pintuan-goods.vue'),
      meta: {
        locale: '拼团商品',
        requiresAuth: true,
        hideInMenu: true
      },
    },
    {
      path: 'personal-center',
      name: 'personal-center',
      component: () => import('@/views/personal-center/index.vue'),
      meta: {
        locale: '个人中心',
        requiresAuth: true,
        hideInMenu: true
      },
    },
    {
      path: 'change-password',
      name: 'change-password',
      component: () => import('@/views/change-password/index.vue'),
      meta: {
        locale: '修改密码',
        requiresAuth: true,
        hideInMenu: true
      },
    },
    {
      path: 'add-sms-sign',
      name: 'add-sms-sign',
      component: () => import('@/views/operation/sms/smsSign.vue'),
      meta: {
        locale: '短信签名',
        requiresAuth: true,
        hideInMenu: true
      },
    },
    {
      path: 'vipconfig',
      name: 'vipconfig',
      component: () => import('@/views/promotion/vipconfig/list.vue'),
      meta: {
        locale: '会员配置',
        requiresAuth: true,
        hideInMenu: true,
      },
    },
    {
      path: 'add-vipconfig',
      name: 'add-vipconfig',
      component: () => import('@/views/promotion/vipconfig/list_add.vue'),
      meta: {
        locale: '编辑会员配置信息',
        requiresAuth: true,
        hideInMenu: true,
      },
    },
  ],
};
