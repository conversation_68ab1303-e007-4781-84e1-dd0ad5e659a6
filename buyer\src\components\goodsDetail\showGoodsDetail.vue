<template>
  <div class="show-goods-detail">
    <div class="goods-detail-nav">
      <a-tabs default-active-key="1">
        <a-tab-pane key="1" title="商品介绍">
          <div class="item-intro-img" ref="itemIntroGoods">
            <div class="item-intro"  v-if="skuDetail.intro" v-for="(item, __index) in skuDetail.intro.split(',')" :key="__index">
              <img :src="item" />
            </div>
            <div v-else style="margin:20px;">暂无商品介绍</div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="2" title="商品评价">
          <div>
            <a-space size="large" class="ml_20">好评率：<a-progress type="circle" :percent="skuDetail.grade/100" /></a-space>
            <div class="remarks-bar mt_10">
              <span @click="viewByGrade('')" :class="{selectedBar: commentParams.grade === ''}">全部({{commentTypeNum.all}})</span>
              <span @click="viewByGrade('GOOD')" :class="{selectedBar: commentParams.grade === 'GOOD'}">好评({{commentTypeNum.good}})</span>
              <span @click="viewByGrade('MODERATE')" :class="{selectedBar: commentParams.grade === 'MODERATE'}">中评({{commentTypeNum.moderate}})</span>
              <span @click="viewByGrade('WORSE')" :class="{selectedBar: commentParams.grade === 'WORSE'}">差评({{commentTypeNum.worse}})</span>
            </div>
            <div class="mt_20 mb_20" style="text-align: center;" v-if="commentList.length === 0">
              暂无评价数据
            </div>
            <div class="remarks-box" v-for="(item,index) in commentList" :key="index" v-else>
              <div class="remarks-user">
                <a-avatar :size="26"><img alt="avatar" :src="item.memberProfile"/></a-avatar>
                <span class="remarks-user-name">{{ secrecyMobile(item.memberName) }}</span>
              </div>
              <div class="remarks-content-box">
                <div style="height: 28px" class="star-box">
                  <div>物流评价：<a-rate disabled v-model="item.deliveryScore" /></div>
                  <div>服务评价：<a-rate disabled v-model="item.serviceScore" /></div>
                  <div>描述评价：<a-rate disabled v-model="item.descriptionScore" /></div>
                  <span v-if="item.grade === 'GOOD'">好评</span>
                  <span v-if="item.grade === 'MODERATE'">中评</span>
                  <span v-if="item.grade === 'WORSE'">差评</span>
                </div>
                <div class="remarks-content">{{item.content}}</div>
                <div class="comment-img" v-if="item.images">
                  <div v-for="(img, imgIndex) in item.images.split(',')" :key="imgIndex" class="flex mr_10">
                    <a-image :src="img" alt="" width="50" height="50" /></div>
                </div>
                <div v-if="item.reply" class="remarks-content" style="color: #666666;font-size: 12px;">商家回复 ：{{ item.reply }}</div>
                <div class="comment-img" v-if="item.replyImage">
                  <div v-for="(img, imgIndex) in item.replyImage.split(',')" :key="imgIndex" class="flex mr_10">
                    <a-image :src="img" alt="" width="50" height="50" /></div>
                </div>
                <p class="remarks-sub">
                  <span class="remarks-item">{{item.goodsName}}</span>
                  <span class="remarks-time">{{item.createTime}}</span>
                </p>
              </div>
            </div>
            <div class="paginationBox mb_20">
              <a-pagination :total="commentTotal" :current="commentParams.pageNumber" :page-size="commentParams.pageSize"
                            @change="(number) => {commentParams.pageNumber = number;}"></a-pagination>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="3" title="商品参数">
          <div v-if="detail.goodsParamsDTOList && detail.goodsParamsDTOList.length" style="padding-bottom: 100px;">
            <div class="goods-params" style="height:inherit;" v-for="item in detail.goodsParamsDTOList" :key="item.groupId">
              <span class="mr_20 group-name">{{item.groupName}}：</span>
              <div class="group-value">
                <div v-for="param in item.goodsParamsItemDTOList" :key="param.paramId">
                  <span>{{param.paramName}}</span>
                  <span>{{param.paramValue}}</span>
                </div>
              </div>
            </div>
          </div>
          <div v-else style="min-height: 300px;">暂无商品参数</div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted, computed } from 'vue';
  import { secrecyMobile } from '@/utils/filters';
  import { goodsComment, goodsCommentNum } from '@/api/member';

  /**
   * 接收父组件传值
   */
  const props = defineProps({
    // 商品数据
    detail: {
      type: Object,
      default: () => {return {}}
    },
  });

  const commentList = ref<any>([]); // 评论列表
  // 评论传参
  const commentParams = ref({
    pageNumber: 1,
    pageSize: 10,
    grade: '',
    goodsId: ''
  });
  const commentTypeNum = ref<any>({}); // 评论数量，包括好中差分别的数量
  const commentTotal = ref(0); // 评论总数
  const onceFlag = ref(true); // 只调用一次
  // 商品详情
  const skuDetail = computed(() => {
    return props.detail.data;
  });


  // 获取评论列表
  const getList = () => {
    commentParams.value.goodsId = skuDetail.value.goodsId;
    goodsComment(commentParams.value).then(res => {
      if (res.data.success) {
        commentList.value = res.data.result.records;
        commentTotal.value = res.data.result.total;
      }
    });
    goodsCommentNum(skuDetail.value.goodsId).then(res => {
      if (res.data.success) {
        commentTypeNum.value = res.data.result;
      }
    });
  };
  // 好中差评切换
  const viewByGrade = (grade: any) => {
    commentParams.value.grade = grade;
    commentParams.value.pageNumber = 1;
  };
  // 设置商品详情高度
  const changeHeight = (name: any) => {
    // let heightCss = window.getComputedStyle(this.$refs[name]).height;
    // heightCss = parseInt(heightCss.substr(0, heightCss.length - 2)) + 89;
    // this.$refs.itemIntroDetail.style.height = heightCss + 'px';
  };
  // 监听页面滚动
  const handleScroll = () => {
    if (onceFlag.value) {
      changeHeight('itemIntroGoods');
      onceFlag.value = false;
    }
  };

  onMounted(() => {
    // 手动设置详情高度，解决无法撑开问题
    // setTimeout(changeHeight('itemIntroGoods'), 2000);
    window.addEventListener('scroll', handleScroll);
    getList();
    if (skuDetail.value.grade === null || skuDetail.value.grade === undefined) {
      skuDetail.value.grade = 100;
    }
  })
  watch(() => [commentParams.value],
    (val) => {
      getList();
    }, { deep: true }
  );
</script>

<style scoped lang="less">
  .goods-detail-nav {
    width: 1200px;
    margin: 0 auto;
    height: 38px;
    background-color: @light_background_color;
    transition: 0.35s;
    line-height: 38px;
    color: #2c2c2c;
  }
  // 商品介绍
  .item-intro-img {
    width: 100%;
    min-height: 300px;
    .item-intro {
      img {
        display: block;
        margin:0 auto;
        max-width: 1000px;
      }
    }
  }
  // 商品评价
  :deep(.arco-progress-circle-bar) {
    stroke: #00b42a;
  }
  .remarks-bar {
    padding-left: 15px;
    height: 36px;
    line-height: 36px;
    color: #666666;
    background-color: #F7F7F7;
    .selectedBar{
      color: @theme_color;
    }
    span {
      margin-right: 15px;
      &:hover{
        color: @theme_color;
        cursor: pointer;
      }
    }
  }
  .remarks-box {
    padding: 10px 15px;
    display: flex;
    flex-direction: row;
    border-bottom: 1px #ccc dotted;
    .remarks-user {
      width: 180px;
    }
    .remarks-user-name {
      padding-left: 15px;
    }
    .remarks-content-box {
      width: calc(100% - 180px);
      .star-box {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        position: relative;
        color: #666666;
        > div {
          margin-right: 20px;
          display: flex;
          align-items: center;
        }
        > span {
          position: absolute;
          right: 100px;
        }
        :deep(.arco-rate-character) {
          margin-right: 5px;
        }
      }
      :deep(.arco-icon) {
        width: 0.7em;
        height: 0.7em;
      }
      .comment-img{
        display: flex;
      }
    }
    .remarks-content {
      font-size: 14px;
      color: #232323;
      line-height: 28px;
    }
    .remarks-sub {
      margin: 0;
      color: #ccc;
      height: 28px;
      line-height: 28px;
    }
    .remarks-time {
      margin-left: 15px;
    }
  }
  // 商品参数
  .goods-params {
    display: flex;
    border-bottom: 1px solid #eee;
    margin-left: 30px;
    .group-name {
      min-width: 100px;
      text-align: right;
      color: #333333;
    }
    .group-value {
      > div {
        color: #999999;
        span:nth-of-type(1) {
          display: inline-block;
          min-width: 80px;
          text-align: right;
          margin-right: 20px;
        }
        span:nth-of-type(2) {
          display: inline-block;
        }
      }
    }
  }
</style>
