<template>
  <div>
    <a-card style="margin-bottom: 10px;">
      <a-divider orientation="left">基本信息</a-divider>
      <a-row class="grid-demo" justify="space-between">
        <a-col :span="8" class="colBox">
          <div class="shopInfo">
            <div class="head-info">
              <a-avatar><img alt="avatar" :src="storeInfo.storeLogo"/></a-avatar>
              <span>
                <span v-if="storeInfo.storeName && storeInfo.storeName.length > 15" class="name"> {{ storeInfo.storeName.slice(0, 15) }}...</span>
                <span v-else class="name"> {{ storeInfo.storeName }}</span>
              </span>
            </div>
            <div class="bottom-info">
              <p v-if="storeInfo.createTime">{{storeInfo.createTime}}&nbsp;开店</p>
              <a-tag v-if="storeInfo.selfOperated == 1" color="#00b42a">自营</a-tag>
              <a-tag v-else color="#f53f3f">非自营</a-tag>
              <p>
                <a-switch v-model="storeInfo.storeDisable" checked-value="open" unchecked-value="close" checked-color="#ff5c58" unchecked-color="#515A6E" @change="shopStatusChange">
                  <template #checked> 启用 </template>
                  <template #unchecked> 禁用 </template>
                </a-switch>
              </p>
            </div>
            <div class="store-info">
              <p class="item"><span class="label">公司名称：</span><span class="info">{{ storeInfo.companyName }}</span></p>
              <p class="item"><span class="label">公司电话：</span><span class="info">{{ storeInfo.companyPhone }}</span></p>
              <p class="item"><span class="label">电子邮箱：</span><span class="info">{{ storeInfo.companyEmail }}</span></p>
              <p class="item"><span class="label">员工总数：</span><span class="info">{{ storeInfo.employeeNum }}人</span></p>
              <p class="item"><span class="label">注册资金：</span><span class="info">{{ storeInfo.registeredCapital }}万</span></p>
              <p class="item"><span class="label">电子邮箱：</span><span class="info">{{ storeInfo.companyEmail }}</span></p>
              <p class="item"><span class="label">联系人姓名：</span><span class="info">{{ storeInfo.linkName }}</span></p>
              <p class="item"><span class="label">联系人电话：</span><span class="info">{{ storeInfo.linkPhone }}</span></p>
              <p class="item">
                <span class="label">公司地址：</span>
                <span class="info">{{storeInfo.companyAddressPath || storeInfo.companyAddress ? storeInfo.companyAddressPath + storeInfo.companyAddress : '暂未完善'}}</span>
              </p>
            </div>
          </div>
        </a-col>
        <a-col :span="8" class="colBox">
          <div class="shopInfo">
            <div class="store-info">
              <p class="item"><span class="label">商家账号：</span><span class="info">{{ storeInfo.memberName }}</span></p>
              <p class="item"><span class="label">库存预警数：</span><span class="info">{{storeInfo.stockWarning ? storeInfo.stockWarning : '0'}}</span></p>
              <p class="item">
                <span class="label">店铺所在地：</span>
                <span class="info">{{storeInfo.storeAddressPath||storeInfo.storeAddressDetail?storeInfo.storeAddressPath+storeInfo.storeAddressDetail:'暂未完善'}}</span>
              </p>
              <p class="item">
                <span class="label">退货地址：</span>
                <span class="info">
                  {{
                      storeInfo.salesConsigneeName !== 'null' ?
                      storeInfo.salesConsigneeName : '' || storeInfo.salesConsigneeMobile !== 'null' ?
                      storeInfo.salesConsigneeMobile : '' || storeInfo.salesConsigneeAddressPath !== 'null' ?
                      storeInfo.salesConsigneeAddressPath : '' || storeInfo.salesConsigneeDetail !== 'null' ?
                      storeInfo.salesConsigneeDetail : '' ?
                      storeInfo.salesConsigneeName + storeInfo.salesConsigneeMobile + ' ' + storeInfo.salesConsigneeAddressPath + storeInfo.salesConsigneeDetail : '暂未完善'
                  }}
                </span>
              </p>
              <p class="item"><span class="label">店铺定位：</span><span class="info">{{storeInfo.storeCenter ? '已定位' : '未定位'}}</span></p>
              <p class="item">
                <span class="label">经营范围：</span>
                <span>
                  <a-checkbox-group v-model="checkAllGroup" :default-checked="true" disabled>
                    <a-checkbox v-for="(item, i) in categories" :key="i + 1" :label="item.id">{{ item.name }}</a-checkbox>
                  </a-checkbox-group>
                </span>
              </p>
              <p class="item"><span class="label">店铺简介：</span><span class="info">{{ storeInfo.storeDesc ? storeInfo.storeDesc : '暂未完善' }}</span></p>
            </div>
          </div>
        </a-col>
        <a-col :span="8" class="colBox">
          <div class="shopInfo">
            <div class="store-info">
              <p class="item"><span class="label">法人姓名：</span><span class="info">{{ storeInfo.legalName }}</span></p>
              <p class="item"><span class="label">法人身份证：</span><span class="info">{{ storeInfo.legalId }}</span></p>
              <p class="item">
                <span class="label">身份证照片：</span>
                <span class="info"><img v-for="item in storeInfo.legalPhoto" :key="item" style="height: 100px; width: 100px;margin-left: 10px;" class="mr_10" :src="item"/></span>
              </p>
              <p class="item"><span class="label">营业执照号：</span><span class="info">{{ storeInfo.licenseNum }}</span></p>
              <p class="item"><span class="label">法定经营范围：</span><span class="info">{{ storeInfo.scope }}</span></p>
              <p class="item">
                <span class="label">营业执照电子版：</span>
                <span class="info"><img style="height: 100px; width: 100px;" :src="storeInfo.licencePhoto"/></span>
              </p>
              <p class="item">
                <span class="label">银行名称：</span>
                <span class="info">{{storeInfo.settlementBankAccountName == 'null' || !storeInfo.settlementBankAccountName ? '' : storeInfo.settlementBankAccountName}}</span>
              </p>
              <p class="item">
                <span class="label">银行账号：</span>
                <span class="info">{{storeInfo.settlementBankAccountNum == 'null' || !storeInfo.settlementBankAccountNum ? '' : storeInfo.settlementBankAccountNum}}</span>
              </p>
              <p class="item" >
                <span class="label">开户支行名称：</span>
                <span class="info">{{storeInfo.settlementBankBranchName == 'null' || !storeInfo.settlementBankBranchName ? '' : storeInfo.settlementBankBranchName}}</span>
              </p>
              <p class="item">
                <span class="label">支行联行号：</span>
                <span class="info">{{storeInfo.settlementBankJointName == 'null' || !storeInfo.settlementBankJointName ? '' : storeInfo.settlementBankJointName}}</span>
              </p>
              <p class="item">
                <span class="label">结算周期：</span>
                <span v-if="storeInfo.settlementCycle" class="info">
                  <template v-for="item in storeInfo.settlementCycle.split(',')"><a-tag v-if="item !== ''" :key="item" :name="item" style="margin-left: 10px">{{ item }}</a-tag></template>
                </span>
              </p>
            </div>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <a-card class="mt_10">
      <a-tabs default-active-key="order"  @change="clickTabs">
        <a-tab-pane key="order" title="TA的订单">
          <searchTable :columns="columnsSearch" @reset="(val) => {orderApiParams = {...orderApiParams, ...val}}" @search="(val) => {orderApiParams = {...orderApiParams, ...val}}">
          </searchTable>
          <!--<tablePage ref="orderRef" :bordered="true" :columns="orderColumns" :api="getOrderList" :api-params="orderApiParams">-->
            <!--<template #flowPrice="{ record }">{{ unitPrice(record.flowPrice,'￥')}}</template>-->
          <!--</tablePage>-->
          <div class="order-box">
            <div class="order-tab">
              <p style="width: 30%">订单详情</p>
              <p style="width: 10%">订单来源</p>
              <p style="width: 10%;">店铺名称</p>
              <p style="width: 10%">应付</p>
              <p style="width: 10%">买家/收货人</p>
              <p style="width: 10%">订单状态</p>
              <p style="width: 10%">支付状态</p>
              <p style="width: 10%">操作</p>
            </div>
            <div class="order-lists">
              <div class="order-item" v-for="(order, orderIndex) in columnsTableList" :key="orderIndex">
                <div class="header">
                  <p>订单编号：<a-typography-text copyable bold><span style="color: #333;">{{order.sn}}</span></a-typography-text></p>
                  <p>下单时间：<span style="color: #333;">{{order.createTime}}</span></p>
                  <!--<p class="delete-order"></p>-->
                </div>
                <div class="body">
                  <div style="width: 30%">
                    <div class="goods">
                      <div v-for="(goods, goodsIndex) in order.orderItems" :key="goodsIndex">
                        <img class="hover-color" :src="goods.image" alt=""/>
                        <div class="goods-info">
                          <div style="width: 100%;" class="hover-color"><a-typography-text copyable bold>{{goods.name}}</a-typography-text></div>
                          <div class="tag" style="font-size: 12px;">
                            <span>售后状态: {{customAfterSaleStatusList(goods.afterSaleStatus)}}</span>
                            <span>投诉状态：{{customComplainStatusList(goods.complainStatus)}}</span>
                          </div>
                        </div>
                        <div class="goods-num">
                          <span class="global_color"> {{unitPrice(goods.goodsPrice, '￥')}} </span>
                          <span style="color: red;"> x{{goods.num}} </span></div>
                      </div>
                    </div>
                  </div>
                  <div style="width: 10%;line-height: 32px;">{{customClientTypeList(order.clientType)}}</div>
                  <div style="width: 10%;line-height: 32px;">{{order.storeName}}</div>
                  <div style="width: 10%;line-height: 32px;">{{unitPrice(order.flowPrice, '￥')}}</div>
                  <div style="width: 10%;line-height: 32px;">{{order.memberName}}</div>
                  <div style="width: 10%;line-height: 32px;">{{customOrderStatusList(order.orderStatus)}}</div>
                  <div style="width: 10%;line-height: 32px;">{{customPayStatusList(order.payStatus)}}</div>
                  <div style="width: 10%"><a-button type="text" status="success" @click="handleOrderDetails(order)">查看</a-button></div>
                </div>
              </div>
            </div>
          </div>
          <div class="paginationBox">
            <a-pagination :total="paginationParams.total" show-page-size :current="orderApiParams.pageNumber" :page-size="orderApiParams.pageSize"
                          @change="(number) => { orderApiParams.pageNumber = number; }"
                          @page-size-change="(number) => {orderApiParams.pageSize = number; orderApiParams.pageNumber = 1;}"
            ></a-pagination>
          </div>
        </a-tab-pane>
        <a-tab-pane key="refundGoods"  title="TA的退货单">
          <searchTable :columns="refundGoodsOrderSearchForm" @reset="(val) => {refundGoodsApiParams = {...refundGoodsApiParams, ...val}}"
                       @search="(val) => {refundGoodsApiParams = {...refundGoodsApiParams, ...val}}"></searchTable>
          <tablePage ref="refundGoodsRef" :bordered="true" :columns="refundGoodsOrderColumns" :api="afterSaleOrderPage" :api-params="refundGoodsApiParams"></tablePage>
        </a-tab-pane>
        <a-tab-pane key="refund" title="TA的退款单">
          <searchTable :columns="refundOrderSearchForm" @reset="(val) => {refundApiParams = {...refundApiParams, ...val}}"
                       @search="(val) => {refundApiParams = {...refundApiParams, ...val}}"></searchTable>
          <tablePage ref="refundRef" :bordered="true" :columns="refundGoodsOrderColumns" :api="afterSaleOrderPage" :api-params="refundApiParams"></tablePage>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { ref, reactive, onMounted, watch } from 'vue';
  import { getShopDetailData, enableBrand, disableShop } from '@/api/shops';
  import { getCategoryTree } from '@/api/goods';
  import { useRoute } from 'vue-router';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import {
    orderStatus,
    payStatus,
    orderType,
    orderClientType,
    orderClientStatus,
    orderSource,
    serviceStatus,
    afterSalesType,
    groupComplainStatus,
    groupAfterSaleStatus,
    paymentStatus
  } from '@/utils/tools';
  import { getOrderList, afterSaleOrderPage } from '@/api/order';
  import { unitPrice } from '@/utils/filters';
  import { useRouter } from 'vue-router';
  const router = useRouter();

  const route = useRoute();
  const orderRef = ref();
  const orderApiParams = ref<any>({storeId: route.query.id});
  const refundGoodsRef = ref();
  const refundGoodsApiParams = ref({serviceType: 'RETURN_MONEY', storeId: route.query.id});
  const refundRef = ref();
  const refundApiParams = ref({serviceType: 'RETURN_GOODS', storeId: route.query.id});
  const storeInfo = ref<any>({
    storeLogo: '',
    storeName: '',
    createTime: '',
    selfOperated: 0,
    companyName: '',
    companyPhone: 0,
    companyEmail: '',
    employeeNum: 0,
    registeredCapital: 0,
    linkName: '',
    linkPhone: 0,
    companyAddressPath: '',
    companyAddress: '',
    memberName: 0,
    stockWarning: '',
    storeAddressPath: '',
    storeAddressDetail: '',
    salesConsigneeName: '',
    salesConsigneeMobile: '',
    storeCenter: '',
    salesConsigneeAddressPath: '',
    salesConsigneeDetail: '',
    goodsManagementCategory: '',
    storeDesc: '',
    legalName: '',
    legalId: '',
    legalPhoto: '',
    licenseNum: '',
    scope: '',
    licencePhoto: '',
    settlementBankAccountName: '',
    settlementBankAccountNum: '',
    settlementBankBranchName: '',
    settlementBankJointName: '',
    settlementCycle: '',
    storeDisable: '',
  }); // 店铺信息
  // 查询海选列表
  const columnsSearch: Array<SearchRule> = [
    {
      label: '订单号',
      model: 'orderSn',
      disabled: false,
      input: true,
    },
    {
      label: '订单状态',
      model: 'orderStatus',
      disabled: false,
      select: {
        options: orderStatus,
      },
    },
    {
      label: '支付状态',
      model: 'payStatus',
      select: {
        options: paymentStatus,
      },
    },
    {
      label: '订单类型',
      model: 'orderType',
      select: {
        options: orderType,
      },
    },
    {
      label: '订单来源',
      model: 'clientType',
      select: {
        options: orderSource,
      },
    },
    {
      label: '创建时间',
      disabled: false,
      datePicker: {
        type: 'range',
      },
    },
  ];
  const refundGoodsOrderSearchForm: Array<SearchRule> = [
    {
      label: '订单编号',
      model: 'orderSn',
      disabled: false,
      input: true,
    },
    {
      label: '售后单号',
      model: 'sn',
      disabled: false,
      input: true,
    },
    {
      label: '售后状态',
      model: 'serviceStatus',
      select: {
        options: serviceStatus,
      },
    },
    {
      label: '商家名称',
      model: 'storeName',
      disabled: false,
      input: true,
    },
    {
      label: '会员名称',
      model: 'memberName',
      disabled: false,
      input: true,
    },
    {
      label: '申请时间',
      disabled: false,
      datePicker: {
        type: 'range',
      },
    },
  ];
  const refundOrderSearchForm: Array<SearchRule> = [
    {
      label: '订单编号',
      model: 'orderSn',
      disabled: false,
      input: true,
    },
    {
      label: '售后单号',
      model: 'sn',
      disabled: false,
      input: true,
    },
    {
      label: '售后状态',
      model: 'serviceStatus',
      select: {
        options: serviceStatus,
      },
    },
    {
      label: '商家名称',
      model: 'storeName',
      disabled: false,
      input: true,
    },
    {
      label: '会员名称',
      model: 'memberName',
      disabled: false,
      input: true,
    },
    {
      label: '申请时间',
      disabled: false,
      datePicker: {
        type: 'range',
      },
    },
  ];
  // 表头
  // 表格搜索列表
  const orderColumns: ColumnsDataRule[] = [
    {title: '订单编号', dataIndex: 'sn', width:280,},
    {title: '订单金额', dataIndex: 'flowPrice', slot: true, slotTemplate: 'flowPrice', width: 130,},
    {title: '订单类型', dataIndex: 'orderType', width: 160, slot: true, slotData: {badge: orderType,},},
    {title: '来源', dataIndex: 'clientType', width: 130, slot: true, slotData: {badge: orderClientType,},},
    {title: '订单状态', dataIndex: 'orderStatus', width: 130, slot: true, slotData: {badge: orderClientStatus,},},
    {title: '支付状态', width: 130, dataIndex: 'payStatus', slot: true, slotData: {badge: paymentStatus,},},
    {title: '售后状态', width: 130, dataIndex: 'groupComplainStatus', slot: true, slotData: {badge: groupAfterSaleStatus,},},
    {title: '投诉状态', width: 130, dataIndex: 'groupComplainStatus', slot: true, slotData: {badge: groupComplainStatus}},
    {title: '购买店铺', width: 160, dataIndex: 'storeName',},
    {title: '下单时间', width: 210, dataIndex: 'createTime',},
  ];
  const refundGoodsOrderColumns: ColumnsDataRule[] = [
    {title: '售后服务单号', dataIndex: 'sn', width: 270,},
    {title: '订单编号', dataIndex: 'orderSn', width: 270,},
    {
      title: '商品',
      dataIndex: 'goodsName',
      width: 220,
      slot: true,
      ellipsis: false,
      slotData: {goods: {goodsImage: 'goodsImage', goodsName: 'goodsName',},},
    },
    {title: '会员名称', dataIndex: 'memberName', width: 100,},
    {title: '商家名称', width: 150, dataIndex: 'storeName',},
    {title: '售后金额', width: 130, dataIndex: 'applyRefundPrice', currency: true,},
    {title: '售后类型', width: 130, dataIndex: 'serviceType', slot: true, slotData: {badge: afterSalesType,},},
    {title: '售后状态', width: 130, dataIndex: 'serviceStatus', slot: true, slotData: {badge: serviceStatus,},},
    {title: '申请时间', width: 200, dataIndex: 'createTime',},
  ];
  const checkAllGroup = ref([]) as any; // 选中的经营分类
  const categories = ref<Array<any>>([]); // 店铺经营范围

  const columnsTableList = ref();
  // 分页的属性配置
  const paginationParams = ref({
    total: 0
  });
  // 订单状态枚举
  const customOrderStatusList = (type: string) => {
    let result = null as any;
    if (type === 'UNDELIVERED') {
      result = '待发货';
    } else if (type === 'UNPAID') {
      result = '未付款'
    } else if (type === 'PAID') {
      result = '已付款'
    } else if (type === 'DELIVERED') {
      result = '已发货'
    }  else if (type === 'CANCELLED') {
      result = '已取消'
    }  else if (type === 'COMPLETED') {
      result = '已完成'
    }  else if (type === 'TAKE') {
      result = '待核验'
    }  else if (type === 'STAY_PICKED_UP') {
      result = '待自提'
    } else {
      result = ''
    }
    return result;
  };
  // 订单支付状态枚举
  const customPayStatusList = (type: string) => {
    let result = null as any;
    if (type === 'UNPAID') {
      result = '待付款'
    } else if (type === 'PAID') {
      result = '已付款'
    } else if (type === 'CANCEL') {
      result = '已取消'
    } else if (type === 'ACCOUNT') {
      result = '已对账'
    } else if (type === 'ACCOUNT_ERROR') {
      result = '对账失败'
    } else {
      result = ''
    }
    return result;
  };
  // 订单来源枚举
  const customClientTypeList = (type: string) => {
    let result = null as any;
    if (type === 'H5') {
      result = '移动端';
    } else if (type === 'PC') {
      result = 'PC端'
    } else if (type === 'WECHAT_MP') {
      result = '小程序端'
    } else if (type === 'APP') {
      result = '移动应用端'
    } else {
      result = ''
    }
    return result;
  };
  // 售后状态枚举
  const customAfterSaleStatusList = (type: string) => {
    let result = null as any;
    if (type === 'NEW') {
      result = '未申请';
    } else if(type === 'NOT_APPLIED') {
      result = '未申请';
    } else if(type === 'ALREADY_APPLIED') {
      result = '已申请';
    } else if (type === 'EXPIRED') {
      result = '已失效';
    } else {
      result = '';
    }
    return result;
  };
  // 投诉状态枚举
  const customComplainStatusList = (type: string) => {
    let result = null as any;
    if (type === 'NEW') {
      result = '未申请';
    } else if (type === 'NO_APPLY') {
      result = '未申请';
    } else if (type === 'APPLYING') {
      result = '申请中';
    } else if (type === 'COMPLETE') {
      result = '已完成';
    } else if (type === 'EXPIRED') {
      result = '已失效';
    } else if(type === 'CANCEL') {
      result = '取消投诉';
    } else {
      result = '';
    }
    return result;
  };

  // 商品订单列表
  const getMyOrder = async () => {
    const res = await getOrderList(orderApiParams.value);
    if (res.data.success) {
      columnsTableList.value = res.data.result.records;
      paginationParams.value = res.data.result;
    }
  };
  // 查看订单详情
  const handleOrderDetails = (val: any) => {
    const url = router.resolve({name: 'order-detail', query: {id: val.sn,}});
    window.open(url.href, '_blank');
  };

  // 店铺状态改变事件
  const shopStatusChange = (v: any) => {
    if (v == 'open') {
      enableBrand(route.query.id).then((res: any) => {});
    } else {
      console.log(v);
      disableShop(route.query.id).then((res: any) => {});
    }
  };
  // 查询会员信息
  const getStoreInfo = async () => {
    const res = await getShopDetailData(route.query.id);
    if (res.data.success) {
      storeInfo.value = res.data.result;
      storeInfo.value.storeDisable =
        storeInfo.value.storeDisable == 'OPEN' ? 'open' : 'close';
      if (storeInfo.value.goodsManagementCategory != null) {
        checkAllGroup.value =
          storeInfo.value.goodsManagementCategory.split(',');
      }
      storeInfo.value.legalPhoto = storeInfo.value.legalPhoto.split(',');
    }
  };
  // 查询分类
  const getCategories = async () => {
    const res = await getCategoryTree();
    if (res.data.success) {
      categories.value = res.data.result;
    }
  };

  // 查询TA的退款单
  const clickTabs = (val: any) => {
    // TA的订单
    if (val === 'order') {
      // orderRef.value.init();
      orderApiParams.value.pageNumber = 1;
    }
    // TA的退货单
    if (val === 'refundGoods') {
      refundGoodsRef.value.init();
    }
    // TA的退款单
    if (val === 'refund') {
      refundRef.value.init();
    }
  };
  onMounted(() => {
    // 查店铺基本信息
    getStoreInfo();
    // 查询店铺分类
    getCategories();
    // 查询TA的订单
    getMyOrder();
  });
  // 初始化监听
  watch(() => [orderApiParams.value],
      (val)=>{
        getMyOrder();
      }, {deep: true}
  );
</script>

<style lang="less" scoped>
  /*@import 'shopDetail.less';*/
  .colBox {
    box-sizing: border-box;
    /*padding: 20px;*/
    .shopInfo {
      font-weight: 500;
      padding-left: 0;
      margin-bottom: 0;
      width: 92%;
      .head-info {
        display: flex;
        height: 130px;
        background: #93b5e1;
        padding: 12px;
        border-radius: 4px;
        align-items: center;
        .name{
          margin-left: 10px;
          color: #fff;
          font-size: 18px;
        }
      }
      .bottom-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 12px;
        margin-top: 6px;
        height: 36px;
        background: #f5f5f5;
        border-radius: 4px;
      }
      .store-info {
        display: flex;
        flex-direction: column;
        margin-top: 20px;
        .item {
          width: 100%;
          padding: 0;
          margin: 0;
          line-height: 22px;
          margin: 4px 0;
          display: flex;
          align-items: start;
          .label {
            width: 115px;
            display: inline-block;
            color: rgba(0,0,0,0.4);
            flex-shrink: 0;
          }
        }
      }
    }
  }

  .order-box {
    .order-tab {
      width: 100%;
      height: 50px;
      background-color: #f3f4f5;
      color: #252931;
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      p {
        flex-shrink: 0;
        text-align: center;
      }
    }
    .order-lists {
      .order-item {
        box-sizing: border-box;
        border: 1px solid #eeeff0;
        margin-bottom: 10px;
        font-size: 14px;
        .header {
          width: 100%;
          height: 50px;
          background-color: #f8f9fa;
          color: #333;
          display: flex;
          align-items: center;
          box-sizing: border-box;
          padding: 0 20px;
          position: relative;
          font-size: 14px;
          p {
            margin-right: 30px;
          }
          .delete-order {
            position: absolute;
            top: 3px;
            right: 0;
            margin-right: 0;
          }
        }
        .body {
          display: flex;
          align-items: stretch;
          justify-content: space-between;
          text-align: center;
          color: #252931;
          > div {
            flex-shrink: 0;
            box-sizing: border-box;
            padding: 14px 0;
            /*border-left: 1px solid #e5e5e5;*/
          }
          > div:nth-of-type(1) {
            border-left: none;
            padding: 0;
            text-align: left;
          }
          .goods > div {
            width: 100%;
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            /*border-bottom: 1px solid #e5e5e5;*/
            box-sizing: border-box;
            padding: 10px;
            position: relative;
            img {
              width: 48px;
              height: 48px;
              flex-shrink: 0;
              border-radius: 4px;
            }
            .goods-info {
              flex-shrink: 1;
              width: 100%;
              box-sizing: border-box;
              padding: 0 15px;
              color: #252931;
              font-size: 14px;
              line-height: 18px;
              .tag {
                color: #aaaaaa;
                white-space: nowrap;
                margin-top: 12px;
                line-height: 12px;
                width: 100%;
                > span {
                  display: inline-block;
                }
                > span:nth-of-type(1) {
                  width: 126px;
                }
                > span:nth-of-type(3) {
                  color: #e4393c;
                  text-align: end;
                  position: absolute;
                  right: 10px;
                  bottom: 10px;
                }
              }
            }
            .goods-num {
              flex-shrink: 0;
              width: 25%;
              text-align: right;
            }
          }
          .goods > div:nth-last-of-type(1) {
            border-bottom: none;
          }
          .hover-color {
            /*overflow: hidden;*/
            /*-webkit-line-clamp: 2;*/
            /*text-overflow: ellipsis;*/
            /*display: -webkit-box;*/
            /*-webkit-box-orient: vertical;*/
            /*white-space: pre-wrap;*/
          }
        }
      }
    }
  }
  .paginationBox {
    margin-top: 18px;
    display: flex;
    flex-direction: row-reverse;
  }
</style>
