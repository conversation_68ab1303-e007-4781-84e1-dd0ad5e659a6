<template>
  <div>
    <a-page-header :style="{ background: 'var(--color-bg-2)' }" title="APP-Design" :show-back="false">
      <template #extra>
        <a-space>
          <!-- <a-button mr-10px>暂存</a-button> -->
          <a-button :loading="loading" @click="open" type="primary">保存内容</a-button>
        </a-space>
      </template>
    </a-page-header>
    <a-modal title="保存内容" v-model:visible="visible">
      <div v-auto-animate>
        <div v-if="!success">
          <a-form :style="{ width: '480px' }" :model="designForm" @submit="submit">
            <a-form-item asterisk-position="after" :rules="[REQUIRED]" tooltip="保存模板的名称" label="模板名称">
              <a-input v-model="designForm.name" />
            </a-form-item>
            <a-form-item tooltip="发布之后会替换原有的楼层装修内容" label="是否发布">
              <a-switch v-model="designForm.pageShow" checked-value="OPEN" unchecked-value="CLOSE" />
            </a-form-item>
            <a-form-item>
              <a-button type="primary" :loading="loading" html-type="submit">保存</a-button>
            </a-form-item>
          </a-form>
        </div>

      </div>
      <template #footer>
        <div>

        </div>
      </template>
    </a-modal>
  </div>
</template>
<script setup lang="ts">
import { opeartionHomePage } from '@/api/setting';
import { useDesign } from '@/store';
import { REQUIRED } from '@/utils/validator';
import { Message } from '@arco-design/web-vue';
import { ref } from 'vue';
import { useRoute } from 'vue-router';
const loading = ref<boolean>(false)
const visible = ref<boolean>(false);
const userDesign = useDesign();
const success = ref<boolean>(false);
const route = useRoute()
const designForm = ref<any>({
  pageShow: 'CLOSE',
  name:  route.query.moduleName || '模板名称',
  pageClientType: 'H5',
  pageType: route.query.pageType
})


function open() {
  const design = userDesign.app
  if (!design.length) {
    Message.error('请装修内容!')
    return false
  }
  visible.value = true
}


// 保存楼层装修保存的内容
async function submit() {
  loading.value = true
  // 用新版楼层装修内容提交的都是v3
  const data = {
    ...designForm.value,
    pageData: JSON.stringify({
      version: 'v3',
      data: userDesign.app
    })
  }
  try {
    let res: any
    // 修改
    if (route.query.id) {
      res = await opeartionHomePage({ ...data, id: route.query.id }, 'update');
    } else {
      res = await opeartionHomePage(data);
    }



    if (res.data.success) {
      visible.value = false
      loading.value = false
      Message.success('保存成功')

    }
  } catch (error) {
    loading.value = false
  }

}


</script>
