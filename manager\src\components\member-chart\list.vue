<template>
  <div style="height: 100%">
    <a-table :columns="columnsTable" :data="responseResult.chartList" stripe />
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, reactive, watch } from 'vue';
  import { getMemberStatistics } from '@/api/statisitics';
  import { useUserStore } from '@/store';
  import { PreViewParamsRule } from '@/types/global';

  interface ResponseRule {
    chartList: Array<any>;
  } // 传递的参数

  const columnsTable = [
    {
      title: '日期',
      dataIndex: 'createDate',
    },
    {
      title: '当前会员',
      dataIndex: 'memberCount',
    },
    {
      title: '新增会员',
      dataIndex: 'newlyAdded',
    },
    {
      title: '活跃会员',
      dataIndex: 'activeQuantity',
    },
  ];
  const props = defineProps({
    dateType: {
      type: Object,
      default: () => {
        return {
          recent: 'LAST_SEVEN',
          month: '',
        };
      },
    },
  });
  const responseResult = reactive<ResponseRule>({
    chartList: [],
  });

  // 订单请求参数
  const previewParams = reactive<PreViewParamsRule>({
    searchType: props.dateType.recent, // TODAY ,  YESTERDAY , LAST_SEVEN , LAST_THIRTY
  });

  // 初始化列表
  const initOrderChart = async () => {
    const res = await getMemberStatistics(previewParams);
    if (res.data.success) {
      responseResult.chartList = res.data.result;
    }
  };
  onMounted(() => {
    initOrderChart();
  });
  // 监听值的改变 父级值改变
  watch(
    () => props.dateType,
    (val) => {
      previewParams.searchType = val.recent;
      if (val.month) {
        // eslint-disable-next-line prefer-destructuring,no-nested-ternary
        previewParams.month = val.month.split('-')[1]
          ? val.month.split('-')[1].indexOf('0') != -1
            ? val.month.split('-')[1].substr(1)
            : ''
          : '';

        // eslint-disable-next-line prefer-destructuring
        previewParams.year = val.month.split('-')[0];
      }

      initOrderChart();
    },
    {
      deep: true,
      immediate: true,
    }
  );
</script>

<style scoped lang="less">
  // .previewChart {
  //   width: 100%;
  // }
</style>
