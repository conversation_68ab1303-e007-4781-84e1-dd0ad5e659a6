<template>
  <div style="background-color: #fff">
    <a-form ref="formRef" style="padding: 30px" :model="form" layout="horizontal" auto-label-width>
      <a-divider orientation="left">商品设置</a-divider>
      <!--<a-form-item field="isOpen" label="是否开启分销" :rules="[REQUIRED, VARCHAR20]" help="商品审核关闭后，商家添加商品则无需审核直接上架">-->
        <!--<a-switch v-model="form.goodsCheck" checked-color="#ff5c58" unchecked-color="#515A6E">-->
          <!--<template #checked> 开启 </template>-->
          <!--<template #unchecked> 关闭 </template>-->
        <!--</a-switch>-->
      <!--</a-form-item>-->
      <a-row :gutter="24">
        <a-col :span="20">
          <a-form-item field="isOpen" label="是否开启商品审核">
            <a-radio-group type="button" v-model="form.goodsCheck">
              <a-radio :value="true">开启</a-radio>
              <a-radio :value="false">关闭</a-radio>
            </a-radio-group>
            <div style="margin-left: 10px;color: #999;font-size: 12px;">商品审核关闭后，商家添加商品则无需审核直接上架</div>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="5">
          <a-form-item field="abbreviationPictureWidth" label="缩略图宽" :rules="[REQUIRED]" :validate-trigger="['change']">
            <a-input-number v-model="form.abbreviationPictureWidth" :style="{width:'120px'}" :min="0"><template #prefix>宽</template></a-input-number>
            <span style="margin-left: 10px;">px</span>
          </a-form-item>
          <a-form-item field="smallPictureWidth" label="小图宽" :rules="[REQUIRED]" :validate-trigger="['change']">
            <a-input-number v-model="form.smallPictureWidth" :style="{width:'120px'}" :min="0"><template #prefix>宽</template></a-input-number>
            <span style="margin-left: 10px;">px</span>
          </a-form-item>
          <a-form-item field="originalPictureWidth" label="原图宽" :rules="[REQUIRED]" :validate-trigger="['change']">
            <a-input-number v-model="form.originalPictureWidth" :style="{width:'120px'}" :min="0"><template #prefix>宽</template></a-input-number>
            <span style="margin-left: 10px;">px</span>
          </a-form-item>
        </a-col>
        <a-col :span="5">
          <a-form-item field="abbreviationPictureHeight" label="缩略图高" :rules="[REQUIRED]" :validate-trigger="['change']">
            <a-input-number v-model="form.abbreviationPictureHeight" :style="{width:'120px'}" :min="0"><template #prefix>高</template></a-input-number>
            <span style="margin-left: 10px;">px</span>
          </a-form-item>
          <a-form-item field="smallPictureHeight" label="小图高" :rules="[REQUIRED]" :validate-trigger="['change']">
            <a-input-number v-model="form.smallPictureHeight" :style="{width:'120px'}" :min="0"><template #prefix>高</template></a-input-number>
            <span style="margin-left: 10px;">px</span>
          </a-form-item>
          <a-form-item field="originalPictureHeight" label="原图高" :rules="[REQUIRED]" :validate-trigger="['change']">
            <a-input-number v-model="form.originalPictureHeight" :style="{width:'120px'}" :min="0"><template #prefix>高</template></a-input-number>
            <span style="margin-left: 10px;">px</span>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="14">
          <!--<a-button style="width: 120px; margin: 30px 220px" type="primary" status="danger" @click="handleSubmit">保存</a-button>-->
          <a-form-item>
            <a-button type="primary"  @click="handleSubmit">保存</a-button>
            <a-button type="primary" status="danger" style="margin-left: 40px;" @click="createIndexClick">重新生成所有商品索引</a-button>
          </a-form-item>
          <a-form-item>
            <a-progress v-if="showProgress" :percent="progressVal" :style="{width:'500px'}" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { getSetting, setSetting, createIndex, getProgress } from '@/api/operation';
import {ref, onMounted, reactive} from 'vue';
import useCurrentInstance from '@/hooks/useCurrentInstance';
import { VARCHAR255, REQUIRED, VARCHAR20 } from '@/utils/validator';
import { Message } from '@arco-design/web-vue';
import { FormInstance } from '@arco-design/web-vue/es/form';

const formRef = ref<FormInstance>();
// 获取modal
const modal = useCurrentInstance().globalProperties?.$modal;
interface formInterface {
  goodsCheck: boolean;
  abbreviationPictureWidth: any;
  abbreviationPictureHeight: any;
  smallPictureWidth: any;
  smallPictureHeight: any;
  originalPictureWidth: any;
  originalPictureHeight: any;
  [key: string]: any;
}
// 数据集
const form = ref<formInterface>({
  goodsCheck: true,
  abbreviationPictureWidth: '',
  abbreviationPictureHeight: '',
  smallPictureWidth: '',
  smallPictureHeight: '',
  originalPictureWidth: '',
  originalPictureHeight: '',
});
const showProgress = ref(false);
const intervalProgress = ref();
const progressVal = ref(0);

async function init() {
  const res = await getSetting('GOODS_SETTING');
  form.value = res.data.result;
}

//  保存商品设置
const handleSubmit = async () => {
  const auth = await formRef.value?.validate();
  if (!auth) {
    const result = await setSetting('GOODS_SETTING', form.value);
    if (result.data.success) {
      Message.success('设置成功!');
      init();
    }
  }
};

// 重新生成所有商品索引
const createIndexClick = () => {
  createIndex().then(res => {
    if (res.data.success) {
      Message.success("开始生成!");
      showProgress.value = true;
      setTimeout(() => {
        intervalProgress.value = setInterval(() => {
          getProgress().then((resp) => {
            const progressResult = resp.data.result;
            if (progressResult != null && progressResult.flag === 0) {
              clearInterval(intervalProgress.value);
              showProgress.value = false;
              Message.success("生成成功!");
            } else {
              progressVal.value = Number((progressResult.processed / progressResult.total).toFixed(2));
            }
          });
        }, 1000);
      }, 1000);
    } else if (res.data.code === 100000) {
      showProgress.value = true;
      intervalProgress.value = setInterval(() => {
        getProgress().then((resp) => {
          const progressResult = resp.data.result;
          if (progressResult != null && progressResult.flag === 0) {
            clearInterval(intervalProgress.value);
            showProgress.value = false;
            Message.success("生成成功!");
          } else {
            progressVal.value = Number((progressResult.processed / progressResult.total).toFixed(2))
          }
        });
      }, 1000);
    }
  });
};

onMounted(() => {
  init();
});
</script>
