<script setup lang="ts">
import { ref } from 'vue'

import { useRouter } from 'vue-router'
import { navigateTo } from './navigate.ts'
import { unitPrice } from '@/utils/filters'

// const active = ref<number>(0);
const props = defineProps<{
  res: any
}>()
const router = useRouter()
function handleClickItem(item: any) {
  const path = {
    path: '/goodsDetail',
    query: {
      skuId: item.id,
      goodsId: item.goodsId,
    },
  }

  window.open(router.resolve(path).href, '_blank')
}
</script>

<template>
  <div>
    <div w-full>
      <div :style="{ color: props.res.data.textColor, textAlign: props.res.data.align }" mb-25px block w-full text-31px
        font-400 line-height-37px>
        {{ props.res.data.text }}
      </div>
      <!-- 商品列表 -->
      <div flex flex-wrap gap-10px>
        <div v-for="(item, index) in props.res.data.list" v-if="props.res.data.list.length" :key="index"
          @click="handleClickItem(item)">
          <div mb-14.3px h-343px w-287px cursor-pointer rounded-9.8px class="goods-item">
            <div flex-j-c flex flex-a-c overflow-hidden text-center>
              <img mx-auto mt-16px max-h-183px :src="item.img || item.small" alt="">
            </div>
            <div class="goods-name">
              {{ item.title || item.goodsName }}
            </div>
            <div class="goods-price">
              ￥{{ unitPrice(item.price) }}
            </div>
          </div>
        </div>
        <div v-else flex-j-c h-100px w-full flex flex-a-c color-gray-300 />
      </div>
      <!-- <div cursor-pointer text-21px font-400 line-height-25px flex flex-a-c>
        <div @click="handleChangeCategory(item, index)" :class="{ 'active': index === active }" ml-28px
          v-for="(item, index) in props.res.data.list" :key="index">
          {{ item.category }}
        </div>
      </div> -->
    </div>
  </div>
</template>

<style scoped>
.active {
  color: #f31947;
}

.goods-item {
  text-align: center;
  background: #fff;
  box-shadow: 0 1px 13px 0 #e5e5e5;
}

.goods-name {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 2;
  font-weight: 400;
  text-align: center;
  letter-spacing: 0;
  -webkit-text-stroke: #979797 0.7px;
  font-size: 18px;
  line-height: 22px;
  width: 200px;
  margin: 12.4px auto 18px auto;
  color: #333;
}

.goods-price {
  font-size: 27px;
  font-weight: 400;
  line-height: 30px;
  text-align: center;
  letter-spacing: 0;
  color: #f31947;
  -webkit-text-stroke: #979797 0.7px;
}
</style>
