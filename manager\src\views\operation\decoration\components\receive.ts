export interface TabList {
  label: string, //展示标签
  value: string, //取值
  _bind: string,//绑定回显内容Key
}


export const tabList: Array<TabList> = [
  {
    label: '商品',
    value: "goods",
    _bind: "goodsName"
  },
  {
    label: '分类页',
    value: "category",
    _bind: "name"
  },
  // {
  //   label: '文章页',
  //   value: "pages",
  //   _bind: "title"
  // },
  {
    label: '微页面',
    value: "special",
    _bind: "name"
  },
  {
    label: '店铺',
    value: "shops",
    _bind: "storeName"
  },
  {
    label: 'H5页',
    value: "url",
    _bind: "label"
  },

  {
    label: '其他页',
    value: "other",
    _bind: "label"
  },
]

