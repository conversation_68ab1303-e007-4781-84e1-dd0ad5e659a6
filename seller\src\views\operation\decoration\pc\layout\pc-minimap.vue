<template>
  <div v-auto-animate  class="mini-map" p-10px :style="{ 'background-color': !show ? '#fff' : '#f7f7f7' }">
    <div @click="show = !show" bg-white hover:bg-gray-100 ease-in duration-200 mt-20px mr-10px drop-shadow-xl h-35px text-center
      line-height-35px rounded cursor-pointer v-if="show">组件管理</div>
    <div v-if="!show">
      <div flex flex-a-c flex-j-sb border-b-1>
        <div>
          <div>组件管理</div>
          <div text-12px color-gray mt-5px>可拖动调整组件顺序</div>
        </div>
        <icon-plus :rotate="45" cursor-pointer @click="show = !show" />
      </div>
      <div mt-10px v-auto-animate>
        <miniMap @delete="callbackDeleteMiniMap" @update="callbackUpdateMiniMap" class="mini-item card" :class="{ 'active': active === index }" @click="handleClickBlock(item, index)"
          v-for="(item, index) in models" :res="item" :id="index" :index="index" :text="item.name" :move-card="moveCard"
          :key="index">
        </miniMap>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { useDesign } from '@/store'
import { DragRule } from '@/views/operation/decoration/app/models/types';
import miniMap from '@/views/operation/decoration/components/minimap.vue'
// import useCurrentInstance from '@/hooks/useCurrentInstance';


const models: any = ref([])
// const message = useCurrentInstance().globalProperties?.$message;
const userDesign = useDesign();
const show = ref(false)
const active = ref<number | string>('')
// 监听装修楼层的变化
watch(() => userDesign.pc, (val) => {
  models.value = val
  // models.value = val.filter((item: any) => item.type !== 'topAdvert' && item.type !== 'bannerAdvert')
})
// 监听装修组件active index的变化
watch(() => userDesign.indexOfPc, (val) => {
  active.value = val
  show.value = false
})

// 点击块
function handleClickBlock(val: DragRule, index: number) {
  active.value = index
  // 将点击的内容传入到pinia中
  userDesign.setPcActiveIndex(index);
}
function callbackDeleteMiniMap(val: { data: any, index: number }) {
  models.value.splice(val.index, 1)
}
// 回调修改块
function callbackUpdateMiniMap(val: { data: any, index: number }) {
  console.log(val)

}

// 定义一个函数 moveCard，接收两个参数：dragIndex（拖动项的索引）和hoverIndex（悬停项的索引）
const moveCard = (dragIndex: number, hoverIndex: number) => {
  // 从 models 数组中获取拖动项（dragIndex 索引对应的项）
  const item = models.value[dragIndex]

  // 从 models 数组中移除拖动项（dragIndex 索引对应的项）
  models.value.splice(dragIndex, 1)

  // 将拖动项插入到悬停项（hoverIndex 索引对应的项）之前
  models.value.splice(hoverIndex, 0, item)

  // 将active的值设置为hoverIndex
  active.value = hoverIndex

  userDesign.setAppActiveIndex(hoverIndex);
}



</script>

<style scoped lang="less">
.mini-map {
  border-left: 1px solid #ededed;
  border-right: 1px solid #ededed;
  background: #f7f7f7;
}

.mini-item {
  user-select: none;
}

.active {
  color: rgb(var(--arcoblue-6));
  background-color: rgb(var(--arcoblue-1));
  font-weight: bold;
  border: 1px solid rgb(var(--arcoblue-6));
  box-sizing: border-box;
}

.no-border {
  border: none;
}

.indicator {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 2px;
  background: #000;

  &.first {
    top: 0;
    bottom: unset;
  }
}
</style>
