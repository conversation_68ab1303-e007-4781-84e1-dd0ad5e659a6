<template>
  <a-card :bordered="false">
    <a-space :size="54">
      <a-upload
        action="/"
        list-type="picture-card"
        :file-list="fileList"
        :limit="1"
      >
        <template #upload-item="{ fileItem }">
          <a-avatar :size="100" class="info-avatar">
            <img v-if="fileItem.url" :src="fileItem.url" />
            <!--<icon-plus v-else />-->
            <icon-user v-else />
          </a-avatar>
        </template>
      </a-upload>
      <a-descriptions
        :data="renderData"
        :column="2"
        :align="{ label: 'right' }"
        layout="inline-horizontal"
        :label-style="{
          width: '140px',
          fontWeight: 'normal',
          color: 'rgb(var(--gray-8))',
        }"
        :value-style="{
          maxWidth: '400px',
          paddingLeft: '8px',
          textAlign: 'left',
        }"
      >
        <template #label="{ label }">{{ label }} :</template>
        <template #value="{ value }">
          <span>{{ value }}</span>
        </template>
      </a-descriptions>
    </a-space>
  </a-card>
</template>

<script lang="ts">
  import { defineComponent, ref, computed } from 'vue';
  import { useUserStore } from '@/store';

  export default defineComponent({
    setup() {
      const store = useUserStore();
      const userInfo = computed(() => {
        return store.userInfo;
      });
      const file = {
        uid: '-2',
        name: 'avatar.png',
        url: userInfo.value.storeLogo,
      };
      const fileList = ref([file]);
      const {
        storeName,
        storeDesc,
        createTime,
        deliveryScore,
        descriptionScore,
        serviceScore,
      } = userInfo.value;
      const renderData: any = [
        {
          label: '店铺名称',
          value: storeName,
        },

        {
          label: '服务得分',
          value: serviceScore,
        },
        {
          label: '注册时间',
          value: createTime,
        },
        {
          label: '交货得分',
          value: deliveryScore,
        },
        {
          label: '店铺介绍',
          value: storeDesc,
        },
        {
          label: '评价得分',
          value: descriptionScore,
        },
      ];
      return {
        fileList,
        renderData,
        userInfo,
      };
    },
  });
</script>

<style scoped lang="less">
  .arco-card {
    padding: 14px 0 4px 4px;
    border-radius: 4px;
  }
  :deep(.arco-avatar-trigger-icon-button) {
    width: 32px;
    height: 32px;
    line-height: 32px;
    background-color: #e8f3ff;
    .arco-icon-camera {
      margin-top: 8px;
      color: rgb(var(--arcoblue-6));
      font-size: 14px;
    }
  }
</style>
