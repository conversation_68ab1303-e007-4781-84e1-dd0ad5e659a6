<template>
  <a-card class="general-card" title="店铺列表" :bordered="false">
    <a-tabs @change="(val)=>{apiParams.storeDisable = val}" :default-active-key="storeDisableVar">
      <a-tab-pane key="OPEN" title="开启中"></a-tab-pane>
      <a-tab-pane key="CLOSED" title="店铺关闭"></a-tab-pane>
      <a-tab-pane key="APPLY" title="申请开店"></a-tab-pane>
      <a-tab-pane key="REFUSED" title="审核拒绝"></a-tab-pane>
      <a-tab-pane key="APPLYING" title="待审核"></a-tab-pane>
    </a-tabs>
    <!-- 搜索 -->
    <searchTable  :rowSpan="isNormal ? 0 : 12"  :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <a-row style="margin-bottom: 16px">
      <a-col :span="16">
        <a-space>
          <a-button type="primary" @click="addModification()">
            添加
          </a-button>
        </a-space>
      </a-col>
    </a-row>
    <!-- 表格 -->
    <tablePage  @selectTableChange="chosenShop" :checkbox="isNormal ? false : true" :radio="isNormal ? false : true" ref="tablePageRef" :columns="columnsTable" :methods="sortMethods" :api="getShopListData" :api-params="apiParams" :bordered="true">
      <template #action="{ data }">
        <a-button type="text" status="success" @click="$openWindow({ name: 'shop-detail', query: { id: data.id } })">查看</a-button>
        <a-button type="text" status="warning"  @click="$openWindow( { name: 'shop-operation',query: { id: data.id }})" style="margin: 0 10px;">修改</a-button>
        <a-button type="text" status="danger" @click="disable(data)" v-if="data.storeDisable == 'OPEN'">关闭</a-button>
        <a-button type="text" status="success"  @click="enable(data)" v-if="data.storeDisable == 'CLOSED'">开启</a-button>
      </template>
    </tablePage>
  </a-card>
</template>
<script setup lang="ts">
import { disableShop, enableBrand, getShopListData } from '@/api/shops';
import searchTable from '@/components/search-column/index.vue';
import tablePage from '@/components/table-pages/index.vue';
import useCurrentInstance from '@/hooks/useCurrentInstance';
import { ColumnsDataRule, MethodsRule, SearchRule } from '@/types/global';
import {
selfOperated,
storeDisable,
} from '@/utils/tools';
import { Message } from '@arco-design/web-vue';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
//  获取modal
const modal = useCurrentInstance().globalProperties?.$modal;
// 组件模式
const props = defineProps({
  templateModel: {
    type: String,
    default: 'normal',
  },
})
const isNormal: boolean = props.templateModel === 'normal';

const router = useRouter();
const tablePageRef = ref<any>();
const storeDisableVar = ref<string>('OPEN');
const apiParams = ref<any>({storeDisable:storeDisableVar.value});
// 查询海选列表
const columnsSearch: Array<SearchRule> = [
  {
    label: '会员名称',
    model: 'memberName',
    disabled: false,
    input: true,
  },
  {
    label: '店铺名称',
    model: 'storeName',
    disabled: false,
    input: true,
  },
  {
    label: '创建时间',
    disabled: false,
    datePicker: {
      type: 'range',
    },
  },
];

// 表格搜索列表
const columnsTable: ColumnsDataRule[] = [
{
    title: '店铺ID',
    dataIndex: 'id',
    width: 100,
  },
  {
    title: '店铺名称',
    dataIndex: 'storeName',
    width: 160,
  },
  {
    title: '会员名称',
    dataIndex: 'memberName',
    width: 160,
  },
  {
    title: '店铺地址',
    dataIndex: 'storeAddressPath',
    width: 260
    //   currency: true,
  },
  {
    title: '是否自营',
    dataIndex: 'selfOperated',
    slot: true,
    width: 100,
    slotData: {
      badge: selfOperated,
    },
  },
  {
    title: '店铺状态',
    dataIndex: 'storeDisable',
    width: 100,
    slot: true,
    slotData: {
      badge: storeDisable,
    },
  },
  {
    title: '创建时间',
    width: 200,
    dataIndex: 'createTime',
  },
];
// 操作列表
const sortMethods: MethodsRule = {
  title: '操作',
  width: 280,
  methods: [
    {
      title: '操作',
      callback: 'action',
      slot: true,
      slotTemplate: 'action'
    },

  ],
};
const emit = defineEmits(['selectTableChange']);
// 单选商品的回调方法
function chosenShop(val: any) {
  emit('selectTableChange', { ...val[0], ___type: 'shops' })
}


// 添加
const addModification = () => {
  router.push({ name: 'shop-operation' }) 
}

// 关闭
const disable = (v: any) => {
  modal.confirm({
    title: '确认关闭',
    content: `您确认要关闭店铺${v.storeName}?`,
    alignCenter: false,
    onOk: async () => {
      const res = await disableShop(v.id);
      if (res.data.success) {
        Message.success('关闭成功');
        tablePageRef.value.init();
      }
    },
  });
}
// 开启
const enable = (v: any) => {
  modal.confirm({
    title: '确认开启',
    content: `您确认要开启店铺${v.storeName}?`,
    alignCenter: false,
    onOk: async () => {
      const res = await enableBrand(v.id);
      if (res.data.success) {
        Message.success('开启成功');
        tablePageRef.value.init();
      }
    },
  });
}
</script>

<style scoped></style>
