<template>
  <div>
    <Card _Title="商品投诉" :_Size="16"></Card>
    <div>
      <span class="light-text-color">订单号：</span>
      <span>{{$route.query.sn}}</span>
      <span class="light-text-color ml_20" v-if="order.order">{{order.order.paymentTime}}</span>
    </div>
    <!-- 添加订单评价  左侧商品详情  右侧评价框 -->
    <div class="goods-eval flex pb_20">
      <div class="goods-con">
        <img :src="orderGoods.image" class="hover-pointer" alt="" width="100" @click="goGoodsDetail(orderGoods.skuId, orderGoods.goodsId)">
        <p class="hover-pointer hover-color" @click="goGoodsDetail(orderGoods.skuId, orderGoods.goodsId)">{{orderGoods.goodsName}}</p>
        <p>{{ unitPrice(orderGoods.goodsPrice, '￥') }}</p>
      </div>
      <div class="eval-con">
        <div>
          <div class="mb_10">
            <span class="light-text-color">投诉主题：</span>
            <a-select :style="{width:'320px'}" placeholder="Please select ..." v-model="complainTopic">
              <a-option v-for="item in reasonList" :value="item.reason" :key="item.id">{{item.reason}}</a-option>
            </a-select>
          </div>
          <a-textarea placeholder="请输入投诉内容" v-model="orderGoods.content" :max-length="500" allow-clear show-word-limit :auto-size="{minRows:4,maxRows:6}" ></a-textarea>
          <a-upload list-type="picture-card" :action="uploadFile" :headers="{ accessToken: accessToken }" image-preview :limit="5"
                    :file-list="fileList" v-model="fileList" :onSuccess="uploadSuccess" @change="handleChange" @before-upload="beforeUpload"></a-upload>
          <span class="light-text-color">上传投诉凭证，最多5张</span>
        </div>
      </div>
    </div>
    <div class="mt_20">
      <a-button @click="handleSave"  size="small" status="danger" type="primary">提交</a-button>
    </div>


  </div>
</template>

<script setup lang="ts">
  import { useRouter, useRoute } from 'vue-router';
  import { ref, onMounted } from 'vue';
  import { unitPrice } from '@/utils/filters';
  import uploadFile from '@/api/common';
  import { orderDetail } from '@/api/order';
  import { afterSaleReason, handleComplain } from '@/api/member';
  import { beforeUpload, accessToken } from '@/utils/upload';
  import { Message } from '@arco-design/web-vue';

  const router = useRouter();
  const route = useRoute();
  const order = ref<any>({});
  const orderGoods = ref<any>({});
  // 投诉主题
  const complainTopic = ref<any>();
  // 投诉原因列表
  const reasonList = ref<any>([]);
  // 上传图片列表
  const fileList = ref<any>([]);


  // 获取订单详情
  const getOrderDetail = () => {
    orderDetail(route.query.sn).then(res => {
      if (res.data.success) {
        order.value = res.data.result;
        orderGoods.value = res.data.result.orderItems[Number(route.query.index)];
        fileList.value = [];
      }
    })
  };
  // 获取投诉原因列表
  const getAfterSaleReason = () => {
    afterSaleReason('COMPLAIN').then(res => {
      if (res.data.success) {
        reasonList.value = res.data.result;
        complainTopic.value = res.data.result[0].reason;
      }
    })
  };
  // 上传成功回调
  const uploadSuccess = () => {
    // console.log('上传成功回调', val);
  };
  const handleChange = (list: any, files: any) => {
    if (list && list.length) {
      fileList.value = list;
    } else {
      fileList.value = [];
    }
  };
  // 提交投诉信息
  const handleSave = () => {
    let goods = orderGoods.value as any;
    let params = {
      goodsId: goods.goodsId,
      complainTopic: complainTopic.value,
      content: goods.content,
      images: fileList.value.map((item: any) => item.response.result).toString(),
      orderSn: route.query.sn,
      skuId: goods.skuId
    };
    handleComplain(params).then(res => {
      if (res.data.success) {
        Message.success('投诉申请已提交，感谢您的反馈');
        router.push(`/user/home/<USER>/complainList`);
      }
    })
  };
  // 跳转商品详情
  const goGoodsDetail = (skuId: any, goodsId: any) => {
    let routeUrl = router.resolve({path: "/goodsDetail", query: { skuId, goodsId },});
    window.open(routeUrl.href, "_blank");
  };

  onMounted(() => {
    getOrderDetail();
    getAfterSaleReason();
  });

</script>

<style scoped lang="less">
  .goods-eval {
    margin-top: 40px;
    /*border-top: 1px solid #eeeeee;*/
    border-bottom: 1px solid #eeeeee;
    .goods-con {
      width: 30%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
    }
    .eval-con {
      width: 70%;
    }
  }
</style>
