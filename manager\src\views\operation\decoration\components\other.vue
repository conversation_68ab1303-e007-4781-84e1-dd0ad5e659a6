<template>
  <a-space wrap>
    <div class="div-block" text-12px :class="{active:index === active}" @click="handleClickBox(item, index)" cursor-pointer hover:bg-gray-3 h-80px bg-gray-100
      text-center line-height-80px v-for="(item, index) in list" :key="index">
      {{ item.label }}
    </div>
  </a-space>
</template>

<script setup lang="ts">
import { ref } from "vue"

interface List {
  label: string,
  value: string,
  icon?: string
}

// 禁止修改label的值
const list: Array<List> = [
  // 链接列表
  {
    label: "首页",
    icon: "md-home",
    value: "home",
  },
  {
    label: "购物车",
    icon: "md-cart",
    value: "cart",
  },
  {
    label: "收藏商品",
    icon: "md-heart",
    value: "collection",
  },
  {
    label: "我的订单",
    icon: "md-document",
    value: "order",
  },
  {
    label: "个人中心",
    icon: "md-person",
    value: "user",
  },
  {
    label: "拼团频道",
    icon: "md-flame",
    value: "group",
  },
  {
    label: "秒杀频道",
    icon: "md-flame",
    value: "seckill",
  },
  {
    label: "领券中心",
    icon: "md-pricetag",
    value: "coupon",
  },
  {
    label: "签到",
    icon: "md-happy",
    value: "sign",
  },
  {
    label: "小程序直播",
    icon: "ios-videocam",
    value: "live",
  },
  {
    label: "砍价",
    icon: "md-share-alt",
    value: "kanjia",
  },
  {
    label: "积分商城",
    icon: "ios-basket",
    value: "point",
  },
  {
    label: "充值中心-充话费",
    icon: "ios-basket",
    value: "voucher-phone",
  },
  {
    label: "充值中心-充流量",
    icon: "ios-basket",
    value: "voucher-flow",
  },
  {
    label: "充值中心-娱乐会员",
    icon: "ios-basket",
    value: "voucher-vip",
  },
]
const active = ref<String | number>('')
const emit = defineEmits(['selectTableChange'])
function handleClickBox(val: List, index: number) {
  active.value = index
  emit('selectTableChange', {...val,___type:'other'})

}

</script>

<style scoped>
.active{
  background: #d1d5db;
}
.div-block {
  min-width: 80px;
}
</style>
