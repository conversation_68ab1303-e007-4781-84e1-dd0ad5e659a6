import request, { commonUrl, Method } from '@/utils/axios';

/**
 * 通过id获取子地区
 */
export function getChildRegion(id: string | number) {
  return request({
    url: `${commonUrl}/common/common/region/item/${id}`,
    method: Method.GET,
    needToken: true,
  });
}

/**
 * 获取所有地址信息
 */
export const getAllRegion = () => {
  return request({
    url: `${commonUrl}/common/common/region/allCity`,
    method: Method.GET,
    needToken: true,
  });
};

/**
 * 点地图获取地址信息
 */
export const getRegion = (params: any) => {
  return request({
    url: `${commonUrl}/common/common/region/region`,
    method: Method.GET,
    needToken: true,
    params,
  });
};

/**
 * 获取系统基础信息
 * */
export const getBaseSite = () => {
  return request ({
    url: `${commonUrl}/common/common/site`,
    method: Method.GET,
    needToken: false
  })
};
