<template>
  <div style="background-color: #fff">
    <a-form ref="formRef" style="padding: 30px" :model="form" layout="horizontal" auto-label-width>
      <a-divider orientation="left">对象存储设置</a-divider>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item field="type" label="平台" :validate-trigger="['change']">
            <a-radio-group type="button" v-model="form.type">
              <a-radio value="ALI_OSS">阿里OSS</a-radio>
              <a-radio value="MINIO">MINIO</a-radio>
              <a-radio value="HUAWEI_OBS">华为云OBS</a-radio>
              <a-radio value="TENCENT_COS">腾讯云COS</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>


      <template v-if="form.type === 'ALI_OSS'">
        <a-divider orientation="left">阿里云存储</a-divider>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item field="aliyunOSSEndPoint" label="节点" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.aliyunOSSEndPoint" allow-clear/>
            </a-form-item>
            <a-form-item field="aliyunOSSBucketName" label="储存空间" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.aliyunOSSBucketName" allow-clear/>
            </a-form-item>
            <a-form-item field="aliyunOSSAccessKeyId" label="密钥id" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.aliyunOSSAccessKeyId" allow-clear/>
            </a-form-item>
            <a-form-item field="aliyunOSSAccessKeySecret" label="密钥" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.aliyunOSSAccessKeySecret" allow-clear/>
            </a-form-item>
          </a-col>
        </a-row>
      </template>
      <template v-if="form.type === 'MINIO'">
        <a-divider orientation="left">MINIO存储</a-divider>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item field="m_frontUrl" label="访问地址" :rules="[REQUIRED]" :validate-trigger="['change']">
              <div>
                <a-input v-model="form.m_frontUrl" allow-clear/>
                <div style="margin-left: 10px;color: #999;font-size: 12px;margin-top: 10px;">配置MINIO nginx前端访问转发地址，一般为完整域名，例如：https://minio.pickmall.cn</div>
              </div>
            </a-form-item>
            <a-form-item field="m_endpoint" label="endpoint" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.m_endpoint" allow-clear/>
            </a-form-item>
            <a-form-item field="m_accessKey" label="accessKey" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.m_accessKey" allow-clear/>
            </a-form-item>
            <a-form-item field="m_secretKey" label="secretKey" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.m_secretKey" allow-clear/>
            </a-form-item>
            <a-form-item field="m_bucketName" label="bucketName" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.m_bucketName" allow-clear/>
            </a-form-item>
          </a-col>
        </a-row>

      </template>
      <template v-if="form.type === 'HUAWEI_OBS'">
        <a-divider orientation="left">华为云存储</a-divider>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item field="huaweicloudOBSAccessKey" label="发起者的Access Key" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.huaweicloudOBSAccessKey" allow-clear/>
            </a-form-item>
            <a-form-item field="huaweicloudOBSSecretKey" label="密钥" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.huaweicloudOBSSecretKey" allow-clear/>
            </a-form-item>
            <a-form-item field="huaweicloudOBSEndPoint" label="节点" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.huaweicloudOBSEndPoint" allow-clear/>
            </a-form-item>
            <a-form-item field="huaweicloudOBSBucketName" label="桶" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.huaweicloudOBSBucketName" allow-clear/>
            </a-form-item>
          </a-col>
        </a-row>
      </template>
      <template v-if="form.type === 'TENCENT_COS'">
        <a-divider orientation="left">腾讯云存储</a-divider>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item field="tencentCOSSecretId" label="SecretId" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.tencentCOSSecretId" allow-clear/>
            </a-form-item>
            <a-form-item field="tencentCOSSecretKey" label="SecretKey" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.tencentCOSSecretKey" allow-clear/>
            </a-form-item>
            <a-form-item field="tencentCOSRegion" label="bucket的地域" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.tencentCOSRegion" allow-clear/>
            </a-form-item>
            <a-form-item field="tencentCOSBucket" label="bucket" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.tencentCOSBucket" allow-clear/>
            </a-form-item>
          </a-col>
        </a-row>
      </template>
      <a-form-item>
        <a-button type="primary" @click="setupSetting">保存</a-button>
      </a-form-item>
    </a-form>
  </div>

</template>

<script setup lang="ts">
import {getSetting, setSetting} from '@/api/operation';
import {onMounted, ref} from 'vue';
import useCurrentInstance from '@/hooks/useCurrentInstance';
import {Message} from '@arco-design/web-vue';
import {FormInstance} from '@arco-design/web-vue/es/form';
import { VARCHAR255, REQUIRED, VARCHAR20 } from '@/utils/validator';

const formRef = ref<FormInstance>();
// 获取modal
const modal = useCurrentInstance().globalProperties?.$modal;

interface formInterface {
  type: string;
  aliyunOSSAccessKeyId: string;
  aliyunOSSAccessKeySecret: string;
  aliyunOSSBucketName: string;
  aliyunOSSPicLocation: string;
  aliyunOSSEndPoint: string;
  huaweicloudOBSAccessKey: string;
  huaweicloudOBSSecretKey: string;
  huaweicloudOBSEndPoint: string;
  huaweicloudOBSBucketName: string;
  tencentCOSSecretId: string;
  tencentCOSSecretKey: string;
  tencentCOSRegion: string;
  tencentCOSBucket: string;
  tencentCOSEndPoint: string;
  [key: string]: any;
}

// 数据集
const form = ref<formInterface>({
  type: "ALI_OSS",
  aliyunOSSAccessKeyId: "",
  aliyunOSSAccessKeySecret: "",
  aliyunOSSBucketName: "",
  aliyunOSSPicLocation: "",
  aliyunOSSEndPoint: "",
  m_endpoint: "",
  m_accessKey: "",
  m_secretKey: "",
  m_bucketName: "",
  m_frontUrl: "",
  huaweicloudOBSAccessKey: "",
  huaweicloudOBSSecretKey: "",
  huaweicloudOBSEndPoint: "",
  huaweicloudOBSBucketName: "",
  tencentCOSSecretId: "",
  tencentCOSSecretKey: "",
  tencentCOSRegion: "",
  tencentCOSBucket: "",
  tencentCOSEndPoint: "",
});

async function init() {
  const res = await getSetting('OSS_SETTING');
  form.value = res.data.result;
  // form.value.type = 'ALI_OSS';
}

const setupSetting = async () => {
  const auth = await formRef.value?.validate();
  if (!auth) {
    const result = await setSetting('OSS_SETTING', form.value);
    if (result.data.success) {
      Message.success('设置成功!');
      init();
    }
  }
};

onMounted(() => {
  init();
});
</script>