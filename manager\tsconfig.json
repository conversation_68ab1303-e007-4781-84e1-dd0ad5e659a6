{
  "compilerOptions": {
    "noImplicitAny": false, // 是否在表达式和申明上有隐含的any类型时报错
    "baseUrl": ".",
    "module": "ESNext",
    "target": "es2016",
    "lib": [
      "DOM",
      "ESNext"
    ],
    "strict": true,
    "jsx": "preserve",
    "esModuleInterop": true,
    "skipLibCheck": true,
    "moduleResolution": "node",
    "resolveJsonModule": false,
    "noUnusedLocals": false,
    "strictNullChecks": true,
    "allowJs": true,
    "forceConsistentCasingInFileNames": true,
    "types": [
      "node",
      "vite/client",
      // "vite-plugin-pages/client",
      // "unplugin-vue-macros/macros-global"
    ],
    "paths": {
      "~/*": [
        "src/*"
      ],
      "@/*":["src/*"]
    },
    "outDir": "build"
  },
  "exclude": [
    "dist",
    "node_modules"
  ],
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "auto-imports.d.ts",
    "components.d.ts",
    "node_modules/arco-design-pro-vite/components.d.ts",
    "shims.d.ts",
    "src/global.d.ts",
  ]
}
