<template>
  <div class="search">
    <a-card :style="{ width: '100%' }">
      <a-button style="margin-bottom: 10px" @click="back()">返回</a-button>
      <searchTable
        time-type="timestamp"
        :columns="columnsSearch"
        @reset="
          (val) => {
            apiParams = { ...apiParams, ...val };
          }
        "
        @search="
          (val) => {
            apiParams = { ...apiParams, ...val };
          }
        "
      ></searchTable>
      <tablePage
        ref="tablePageRef"
        :columns="columns"
        :api="getCouponReceiveList"
        :api-params="apiParams"
        :bordered="true"
      >
        <template #time="{ data }">
          <span
            v-if="data?.getType == 'ACTIVITY' && data?.getType == 'DYNAMICTIME'"
          >
            长期有效
          </span>
          <span v-else>
            {{ data.startTime }}<br />
            {{ data.endTime }}
          </span>
        </template>
      </tablePage>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import searchTable from '@/components/search-column/index.vue';
  import { useRouter, useRoute } from 'vue-router';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { getCouponReceiveList } from '@/api/promotion';
  import {
    claimStatus,
    memberCouponStatus,
    couponType,
    promotionsScopeTypeRender,
  } from '@/utils/tools';

  const tablePageRef = ref('');
  const route = useRoute();
  const apiParams = ref({
    couponId: route.query.id ? route.query.id : null,
  });
  const columnsSearch: Array<SearchRule> = [
    {
      label: '优惠券名称',
      model: 'couponName',
      disabled: false,
      input: true,
    },
    {
      label: '会员名称',
      model: 'memberName',
      disabled: false,
      input: true,
    },
    {
      label: '获取方式',
      model: 'getType',
      disabled: false,
      select: {
        options: claimStatus,
      },
    },
    {
      label: '优惠券状态',
      model: 'memberCouponStatus',
      disabled: false,
      select: {
        options: memberCouponStatus,
      },
    },
    {
      label: '活动时间',
      model: 'selectDate',
      disabled: false,
      datePicker: {
        type: 'range',
      },
    },
  ];
  const columns: ColumnsDataRule[] = [
    {
      title: '会员名称',
      dataIndex: 'memberName',
    },
    {
      title: '优惠券名称',
      dataIndex: 'couponName',
    },
    {
      title: '发布店铺',
      dataIndex: 'storeName',
    },
    {
      title: '面额/折扣',
      dataIndex: 'price',
      currency: true,
    },
    {
      title: '使用门槛',
      dataIndex: 'consumeThreshold',
    },
    {
      title: '获取方式',
      dataIndex: 'getType',
      slot: true,
      slotData: {
        badge: claimStatus,
      },
    },
    {
      title: '会员优惠券状态',
      dataIndex: 'memberCouponStatus',
      slot: true,
      slotData: {
        badge: memberCouponStatus,
      },
    },
    {
      title: '优惠券类型',
      dataIndex: 'couponType',
      slot: true,
      slotData: {
        badge: couponType,
      },
    },
    {
      title: '品类描述',
      dataIndex: 'scopeType',
      slot: true,
      slotData: {
        badge: promotionsScopeTypeRender,
      },
    },
    {
      title: '有效时间',
      dataIndex: 'time',
      slot: true,
      slotTemplate: 'time',
      width: 180,
    },
  ];
  const router = useRouter();
  // 返回
  const back = () => {
    router.push({ name: 'coupon' });
  };
  onMounted(() => {});
</script>

<style lang="less" scoped></style>
