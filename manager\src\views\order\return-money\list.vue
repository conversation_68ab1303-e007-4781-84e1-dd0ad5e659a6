<template>
  <a-card class="general-card" title="退货管理" :bordered="false">
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>

    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="afterSaleOrderPage"
      :api-params="apiParams"
    />
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { afterSaleOrderPage } from '@/api/order';
  import { serviceStatus } from '@/utils/tools';
  import { ref } from 'vue';

  const tablePageRef = ref('');

  const columnsSearch: Array<SearchRule> = [
    {
      label: '商品',
      model: 'goodsName',
      disabled: false,
      input: true,
    },
    {
      label: '会员名称',
      model: 'memberName',
      disabled: false,
      input: true,
    },
    {
      label: '订单编号',
      model: 'sn',
      disabled: false,
      input: true,
    },
    {
      label: '申请时间',
      disabled: false,
      datePicker: {
        type: 'range',
      },
    },
  ];

  const apiParams = ref({
    serviceType: 'RETURN_MONEY',
  });

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '退款编号',
      dataIndex: 'sn',
    },
    {
      title: '订单号',
      dataIndex: 'orderSn',
    },

    {
      title: '商品',
      dataIndex: 'goodsName',
      width: 300,
      slot: true,
      ellipsis: false,
      slotData: {
        goods: {
          goodsImage: 'goodsImage',
          goodsName: 'goodsName',
        },
      },
    },
    {
      title: '申请退款金额',
      dataIndex: 'applyRefundPrice',
      currency: true,
    },
    {
      title: '会员名称',
      dataIndex: 'memberName',
    },
    {
      title: '申请时间',
      width: 200,
      dataIndex: 'createTime',
    },
    {
      title: '售后状态',
      dataIndex: 'serviceStatus',
      slot: true,
      slotData: {
        tag: serviceStatus,
      },
    },
  ];

  const sortMethods: MethodsRule = {
    title: '操作',
    width: 100,
    methods: [
      {
        title: '查看',
        callback: 'detail',
      },
    ],
  };
</script>
