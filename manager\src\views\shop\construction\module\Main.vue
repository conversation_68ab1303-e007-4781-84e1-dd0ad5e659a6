<template>
  <div class="main">
    <a-card class="main-item" :style="{ width: '360px' }" :title="item.title" hoverable v-for="(item, index) in mainWay" :key="index">
      <template #extra><a-button type="text" @click="handleClick(item)">选择</a-button></template>
      <div>{{item.desc}}</div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref, watch, onMounted, computed } from 'vue';
  import { useAppStore } from '@/store';

  const appStore = useAppStore();
  const emit = defineEmits(['callback']);
  // 主体类型
  const mainWay = computed(() => {
    return appStore.weChatApplyList;
  });
  // 选择主体类型
  const handleClick = (val) => {
    emit('callback', val.value)
  };
</script>

<style scoped lang="less">
  .main {
    display: flex;
    flex-wrap: wrap;
    .main-item {
      margin: 20px;
    }
  }
</style>