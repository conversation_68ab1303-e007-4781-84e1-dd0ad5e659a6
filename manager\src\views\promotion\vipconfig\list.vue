<template>
  <a-card class="general-card" title="会员配置" :bordered="false">
    <searchTable
      :columns="columnsSearch"
      time-type="timestamp"
      @reset="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
      @search="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
    ></searchTable>
    <a-row style="margin-bottom: 16px">
      <a-col :span="16">
        <a-space>
          <a-button type="primary" @click="add"> 添加 </a-button>
          <a-button
            type="primary"
            status="danger"
            :disabled="selectList.length === 0"
            @click="batchDelete"
          >
            批量删除
          </a-button>
        </a-space>
      </a-col>
    </a-row>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getVipConfingList"
      :api-params="apiParams"
      :checkbox="true"
      :bordered="true"
      @selectTableChange="selectTableChange"
    >
      <template #price="{ data }">{{
        data.price
          ? unitPrice(data.price, '¥')
          : (data.couponDiscount || 0) + '折'
      }}</template>
      <template #num="{ data }">
        {{
          data.usedNum +
          '/' +
          data.receivedNum +
          '/' +
          (data.publishNum === 0 ? '不限制' : data.publishNum)
        }}
      </template>
      <template #time="{ data }">
        <div
          v-if="
            data?.getType === 'ACTIVITY' && data?.rangeDayType === 'DYNAMICTIME'
          "
          >长期有效</div
        >
        <div v-else-if="data.startTime && data.endTime"
          >{{ data.startTime }}<div style="margin: 0"></div
          >{{ data.endTime }}</div
        >
      </template>
      <template #edit="{ data }">
        <a-button
          v-if="
            data.promotionStatus == 'CLOSE' || data.promotionStatus == 'NEW'
          "
          style="margin-right: 10px"
          type="text"
          status="warning"
          @click="see(data)"
          >编辑</a-button
        >
        <a-button
          v-else
          style="margin-right: 10px"
          type="text"
          status="success"
          @click="see(data, 'onlyView')"
          >查看</a-button
        >
        <a-button
          v-if="
            data.promotionStatus == 'START' || data.promotionStatus == 'NEW'
          "
          style="margin-right: 10px"
          type="text"
          status="danger"
          @click="close(data)"
          >关闭</a-button
        >
        <a-button
          style="margin-right: 10px"
          type="text"
          status="danger"
          @click="deleteItem(data)"
          >删除</a-button
        >

      </template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { getVipConfingList, deleteVipConfig, batchDeleteVipConfig } from '@/api/vipconfig';
  import { updatePlatformCouponStatus } from '@/api/promotion';  // 修改导入


  import { unitPrice } from '@/utils/filters';
  import { ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import useCurrentInstance from '@/hooks/useCurrentInstance';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const tablePageRef = ref<any>();
  const couponStatus = ref<string>('START')
  const apiParams = ref<any>({});
  // 获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;
  const selectList = ref([]); // 接受子组件传过来的值
  const columnsSearch: Array<SearchRule> = [
    {
      label: '会员名称',
      model: 'vipName',
      disabled: false,
      input: true,
    }
  ];

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '会员名称',
      dataIndex: 'vipName',
    },
    {
      title: '会员描述',
      dataIndex: 'description',
    },

    {
      title: '会员价格',
      dataIndex: 'price',
      slot: true,
      slotTemplate: 'price',
    },
    {
      title: '有效期',
      dataIndex: 'validityDays',
    },
  ];

  // todo 本页面操作列表为选择展示
  const sortMethods: MethodsRule = {
    title: '操作',
    width: 280,
    methods: [
      {
        title: '编辑',
        slot: true,
        slotTemplate: 'edit',
      },
    ],
  };
  // 父组件传过来的数据
  const selectTableChange = (val: any) => {
    if (val) {
      selectList.value = val;
    }
  };
  // 查看/编辑
  const see = (v: any, only?: any) => {
    let data: any;
    only ? (data = { onlyView: true, id: v.id }) : (data = { id: v.id });
    // 使用路由名称跳转，与会员列表保持一致
    router.push({
      name: 'add-vipconfig',
      query: data,
    });
  };

  // 关闭
  const close = (val: any) => {
    modal.confirm({
      title: '确认关闭',
      content: `确认要关闭此优惠券么?`,
      alignCenter: false,
      onOk: async () => {
        updatePlatformCouponStatus({  // 修改函数名
          couponIds: val.id,
          effectiveDays: 0,
        }).then((res: any) => {
          if (res.data.success) {
            Message.success('优惠券已关闭');
            tablePageRef.value.init();
          }
        });
      },
    });
  };
  // 添加VIP配置
  const add = (val: any) => {
    // 使用路由名称跳转，与会员列表保持一致
    router.push({
      name: 'add-vipconfig',
    });
  };
  // 优惠券领取记录
  const receivePage = (val?: any) => {
    val.id
      ? router.push({ name: 'coupon-receive', query: { id: val.id } })
      : router.push({ name: 'coupon-receive' });
  };

  // 删除单个配置
  const deleteItem = (item: any) => {
    modal.confirm({
      title: '确认删除',
      content: `确认要删除会员配置"${item.vipName}"吗？删除后将同时删除关联的商品信息。`,
      alignCenter: false,
      onOk: async () => {
        try {
          const result = await deleteVipConfig(item.id);
          if (result && result.data && result.data.success) {
            Message.success('删除成功');
            tablePageRef.value.init(); // 刷新表格
          } else {
            const errorMsg = result?.data?.message || '删除失败';
            Message.error(errorMsg);
          }
        } catch (error) {
          console.error('删除失败:', error);
          Message.error('删除失败');
        }
      },
    });
  };

  // 批量删除配置
  const batchDelete = () => {
    if (selectList.value.length === 0) {
      Message.warning('请选择要删除的配置');
      return;
    }

    const configNames = selectList.value.map((item: any) => item.vipName).join('、');
    modal.confirm({
      title: '确认批量删除',
      content: `确认要删除以下会员配置吗？删除后将同时删除关联的商品信息。\n\n${configNames}`,
      alignCenter: false,
      onOk: async () => {
        try {
          const ids = selectList.value.map((item: any) => item.id);
          const result = await batchDeleteVipConfig(ids);
          if (result && result.data && result.data.success) {
            Message.success(`成功删除${selectList.value.length}个配置`);
            selectList.value = []; // 清空选择
            tablePageRef.value.init(); // 刷新表格
          } else {
            const errorMsg = result?.data?.message || '批量删除失败';
            Message.error(errorMsg);
          }
        } catch (error) {
          console.error('批量删除失败:', error);
          Message.error('批量删除失败');
        }
      },
    });
  };
</script>
