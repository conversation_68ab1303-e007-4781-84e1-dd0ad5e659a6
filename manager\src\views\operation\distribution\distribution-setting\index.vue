<template>
  <a-card class="general-card" title="分销设置" :bordered="false">
    <a-form ref="formRef" style="padding: 30px" :model="form">
      <a-divider orientation="left">分销设置</a-divider>
      <a-form-item  field="isOpen"  label="是否开启分销" :rules="[REQUIRED, VARCHAR20]">
        <a-switch v-model="form.isOpen">
          <template #checked> 开启 </template>
          <template #unchecked> 关闭 </template>
        </a-switch>
      </a-form-item>
      <a-form-item field="level" label="分销层级">
        <a-radio-group type="button" v-model="form.level">
          <a-radio value="1">一级分销</a-radio>
          <a-radio value="2">二级分销</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item field="mode" label="分销模式">
        <a-radio-group type="button" v-model="form.mode">
          <a-radio value="1">指定分销</a-radio>
          <a-radio value="2">人人分销</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item field="binding" label="分销关系绑定">
        <a-radio-group type="button" v-model="form.binding">
          <a-radio value="ALL">所有用户</a-radio>
          <a-radio value="NEW">新用户</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item field="validity" label="分销绑定模式">
        <a-radio-group type="button" v-model="form.validity" @change="changeValidity">
          <a-radio value="FOREVER">永久</a-radio>
          <a-radio value="EXP">有效期</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item v-show="form.validity === 'EXP'" field="validityDay" label="有效期天数" :rules="[REQUIRED, VARCHAR20]">
        <a-input-number v-model="form.validityDay"  :style="{ width: '320px' }"  :min="1"  :max="365"/>
      </a-form-item>
      <a-divider orientation="left">返佣设置</a-divider>
      <a-form-item field="commissionModel" label="佣金模式">
        <a-radio-group type="button" v-model="form.commissionModel">
          <a-radio value="1">平台承担</a-radio>
          <a-radio value="2">商户承担</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item field="firstCommission"  label="一级佣金比例"  :rules="[REQUIRED, VARCHAR20]">
          <a-input-number v-model="form.firstCommission" :style="{ width: '320px' }" :min="0" :max="99999"/>
        </a-form-item>
        <a-form-item field="secondaryCommission"  label="二级分佣比例"  :rules="[REQUIRED, VARCHAR20]">
          <a-input-number v-model="form.secondaryCommission" :style="{ width: '320px' }" :min="0" :max="99999"/>
        </a-form-item>
        <a-form-item field="selfCommission" label="自购返佣">
        <a-radio-group type="button" v-model="form.selfCommission">
          <a-radio value="1">开启</a-radio>
          <a-radio value="2">关闭</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item field="promotion" label="推广返佣">
        <a-radio-group type="button" v-model="form.promotion">
          <a-radio value="1">开启</a-radio>
          <a-radio value="2">关闭</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item field="maxPromotionNum" label="每日最大推广数量" :rules="[REQUIRED, VARCHAR20]">
        <a-input-number v-model="form.maxPromotionNum" :style="{ width: '320px' }" :min="0" :max="1000"/>
      </a-form-item>
      <a-button style="width: 120px; margin: 30px 220px" type="primary"  @click="handleSubmit" >保存</a-button>
    </a-form>
  </a-card>
</template>

<script setup lang="ts">
  import { getSetting, setSetting } from '@/api/operation';
  import { ref, onMounted } from 'vue';
  import {  REQUIRED, VARCHAR20 } from '@/utils/validator';
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';

  const formRef = ref<FormInstance>();
  interface formInterface {
    isOpen: boolean;
    level:string;
    mode:string;
    binding:string;
    validity:string;
    validityDay?:number;
    commissionModel:string;
    firstCommission:number;
    secondaryCommission:number;
    selfCommission:string;
    promotion:string ;
    maxPromotionNum:number;
  }
  // 数据集
  const form = ref<formInterface>({
    isOpen: true,// 是否开启分销
    level: "1", // 分销层级
    mode: "2", // 分销模式（指定分销/人人分销）
    binding:"NEW",// 分销关系绑定（新用户/所有用户）
    validity:"FOREVER",// 分销关系绑定（有效期/永久）
    validityDay: 0, // 有效期天数
    commissionModel:"2", // 佣金模式（平台承担/商户承担）
    firstCommission:0, // 一级佣金比例
    secondaryCommission:0, // 二级佣金比例
    selfCommission:"2" , // 子购返佣
    promotion: "2", // 推广返佣
    maxPromotionNum: 0, // 每日最大推广数量
    // 表单提交数据
  });

  async function init() {
    const res = await getSetting('DISTRIBUTION_SETTING');
    if(res.data.success){
      form.value = res.data.result;
   }
  }
  const handleSubmit = async () => {
    console.log(form.value, 'form.value');
    const result = await setSetting('DISTRIBUTION_SETTING', form.value);
    if (result.data.success) {
      Message.success('设置成功!');
      init();
    }
  };
  
  // 分销绑定模式
  const changeValidity = (val: any) => {
    form.value.validity = val;
  };

  onMounted(() => {
    init();
  });
</script>

<script lang="ts">
  // eslint-disable-next-line import/export
  // export default {
  //   name: 'GoodsList',
  // };
</script>
