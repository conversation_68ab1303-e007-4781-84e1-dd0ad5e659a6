<template>
  <a-card class="general-card" title="回收站"  :bordered="false">
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <tablePage ref="tablePageRef" :columns="columnsTable" :methods="sortMethods" :api="getMemberListData" :bordered="true"
      :api-params="apiParams" @enabled="handleEnabled" @orderDetail="handleMemberDetails" @editor="handleEdit">
    </tablePage>
    <!-- 修改会员modal -->
    <editModal ref="show" @init="init"></editModal>
  </a-card>
</template>

<script setup lang="ts">
import { getMemberInfoData, getMemberListData, updateMemberStatus } from '@/api/member';
import searchTable from '@/components/search-column/index.vue';
import tablePage from '@/components/table-pages/index.vue';
import useCurrentInstance from '@/hooks/useCurrentInstance';
import { ColumnsDataRule, MethodsRule, SearchRule } from '@/types/global';
import { Message } from '@arco-design/web-vue';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import editModal from '../member-list/modal/index.vue';

const tablePageRef = ref<any>();
const router = useRouter();
const show = ref(null) as any
const modal = useCurrentInstance().globalProperties?.$modal;
const columnsSearch: Array<SearchRule> = [
  {
    label: '会员名称',
    model: 'username',
    disabled: false,
    input: true,
  },
  {
    label: '会员昵称',
    model: 'nickName',
    disabled: false,
    input: true,
  },
  {
    label: '联系方式',
    model: 'mobile',
    disabled: false,
    input: true,
  },
];

const columnsTable: ColumnsDataRule[] = [
  {
    title: '会员名称',
    dataIndex: 'username',
  },
  {
    title: '会员昵称',
    dataIndex: 'nickName',
  },

  {
    title: '联系方式',
    dataIndex: 'mobile',
  },
  {
    title: '注册时间',
    dataIndex: 'createTime',
  },
  {
    title: '积分数量',
    dataIndex: 'points',
  },
];

const sortMethods: MethodsRule = {
  title: '操作',
  width: 300,
  fixed: 'right',
  methods: [
    //   {
    //     title: '下载',
    //     callback: 'detail',
    //   }, 
    {
      title: '查看',
      callback: 'orderDetail',
      type:"text" ,
      status:"success"
    },
    {
      title: '编辑',
      callback: 'editor',
      type:"text" ,
      status:"warning"
    },
    {
      title: '启用',
      callback: 'enabled',
      type:"text" ,
      status:"success"
    },
  ],
};
// 回调启用
function handleEnabled(data: any) {
  const params = {
    memberIds: data.record.id,
    disabled: true
  };
  modal.confirm({
    title: '提示',
    content: '启用用此会员？',
    alignCenter: false,
    onOk: async () => {
      const res = await updateMemberStatus(params);
      if (res.data.success) {
        Message.success('启用成功');
        tablePageRef.value.init();
      }
    }
  });
}
// 编辑回显
const handleEdit = (val: any) => {
  console.log(11);
  getMemberInfoData(val.record.id).then((res: any) => {
    if (res.data.code == 200) {
      show.value.editMemberModal.data = res.data.result
    }
  })
  show.value.descFlag = true
}
const handleMemberDetails = (data: any) => {
  router.push({
    name: 'member-detail',
    query: {
      id: data.record.id
    }
  })
}
const init = (v: boolean) => {
  if (v == true) {
    tablePageRef.value.init();
  }
}
const apiParams = ref({disabled: 'CLOSE'});
</script>
