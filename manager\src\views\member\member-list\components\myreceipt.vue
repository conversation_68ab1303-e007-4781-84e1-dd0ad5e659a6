<template>
  <div>
    <searchTable :columns="columnsSearchReceipt" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <tablePage :api-params="apiParams" ref="tablePageRef" :columns="columnsReceiptRecordTable" :api="getReceiptPage">
      <template #orderSn="{ data }">
        <a href="javascript:;" style="text-decoration:none" @click="orderDetail(data)">{{ data.orderSn }}</a>
      </template>
    </tablePage>
  </div>
</template>

<script lang="ts" setup>
import searchTable from '@/components/search-column/index.vue';
import { useRoute, useRouter } from 'vue-router';
import { ref } from 'vue';
import { ColumnsDataRule, SearchRule } from '@/types/global';
import { getReceiptPage } from '@/api/member';
import tablePage from '@/components/table-pages/index.vue';

const route = useRoute()
const router = useRouter()
const tablePageRef = ref('');
// 传递的参数
const apiParams = ref({memberId: route.query.id});

// TA的发票搜索
const columnsSearchReceipt: Array<SearchRule> = [
  {
    label: '订单号',
    model: 'orderSn',
    disabled: false,
    input: true,
  },
]
// TA的发票
const columnsReceiptRecordTable: ColumnsDataRule[] = [
  {
    title: '订单编号',
    dataIndex: 'orderSn',
    slot: true,
    slotTemplate: 'orderSn',
  },
  {
    title: '发票抬头',
    dataIndex: 'receiptTitle',
  },
  {
    title: '纳税人识别号',
    dataIndex: 'taxpayerId',
  },
  {
    title: '发票金额',
    dataIndex: 'receiptPrice',
    currency: true,
  },
  {
    title: '发票内容',
    dataIndex: 'receiptContent',
  },
]
// 跳转到订单详情页面
const orderDetail = (data: any) => {
  router.push({
    name: 'order-detail', query: {
      id: data.orderSn
    }
  })
}
</script>

<style lang="scss" scoped>

</style>