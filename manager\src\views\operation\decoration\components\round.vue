<template>
  <div py-16px border-b-1>
    <div>
      <div>边框设置</div>
      <a-radio-group mt-20px v-model="props.res.border">
        <a-radio value="normal">默认</a-radio>
        <a-radio value="round">圆角</a-radio>
      </a-radio-group>
    </div>

    <a-space py-16px v-if="props.res.border === 'round'">
      <div w-50px>圆角</div>
      <div>
        <a-input-number allow-clear @clear="props.res.data.round = 0" hide-button v-model="props.res.data.round"
          :style="{ width: '150px' }" placeholder="请输入圆角" :min="0" :max="100">
          <template #suffix>
            px
          </template>
        </a-input-number>
        <a-slider mt-10px :max="1000" v-model="props.res.data.round" :style="{ width: '200px' }" />
      </div>
    </a-space>
  </div>
</template>


<script setup lang="ts">
import { DragRule } from '@/views/operation/decoration/app/models/types';
const props = defineProps<{
  res: DragRule
}>()



</script>

<style scoped>
</style>
