<template>
  <div>
    <a-spin :loading="loading" style="width: 100%">
      <div class="content">
          <div class="coupon-title">
            <router-link to="/"><img :src="logoImg" width="120" alt="" /></router-link>
            <p>领券中心</p>
            <a-input-search :style="{width:'320px'}" placeholder="搜索优惠券" v-model="params.couponName" search-button class="input-search" @search="search">
              <template #button-icon>
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 1024 1024"><path fill="currentColor" d="M1014.64 969.04L703.71 656.207c57.952-69.408 92.88-158.704 92.88-256.208c0-220.912-179.088-400-400-400s-400 179.088-400 400s179.088 400 400 400c100.368 0 192.048-37.056 262.288-98.144l310.496 312.448c12.496 12.497 32.769 12.497 45.265 0c12.48-12.496 12.48-32.752 0-45.263zM396.59 736.527c-185.856 0-336.528-150.672-336.528-336.528S210.734 63.471 396.59 63.471c185.856 0 336.528 150.672 336.528 336.528S582.446 736.527 396.59 736.527"/></svg>
              </template>
            </a-input-search>
          </div>
        <div class="fontsize-18 recommend">推荐好券</div>

        <div class="coupon-list flex">
          <div v-for="(item, index) in list" :key="index" class="coupon-item">
            <div class="c-left">
              <div>
                <span v-if="item.couponType === 'PRICE'" class="fontsize_12 price-color">{{ unitPrice(item.price, '￥') }}</span>
                <span v-if="item.couponType === 'DISCOUNT'" class="fontsize_12 price-color"><span class="fontsize-18">{{ item.couponDiscount }}</span>折</span>
                <span class="describe">满{{ item.consumeThreshold }}元可用</span>
              </div>
              <div style="line-height: 18px;margin: 10px 0;" class="ellipsis ellipsis-2">使用范围：{{ useScope(item.scopeType, item.storeName) }}</div>
              <div style="color: #999999; font-size: 13px;">有效期：{{ item.endTime }}</div>
            </div>
            <b></b>
            <a class="c-right hover-pointer" @click="receive(item)">立即领取</a>
            <i class="circle-top"></i>
            <i class="circle-bottom"></i>
          </div>
        </div>
        <!-- 分页 -->
        <div class="paginationBox">
          <a-pagination :total="total" :current="params.pageNumber" :page-size="params.pageSize" show-page-size
                        @change="(number) => {params.pageNumber = number;}" @page-size-change="(number) => {params.pageSize = number; params.pageNumber = 1;}" >
          </a-pagination>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { couponList, receiveCoupon } from "@/api/member";
  import { useUserStore } from '@/stores/user';
  import { storeToRefs } from 'pinia';
  import { unitPrice } from '@/utils/filters';
  import { Modal } from '@arco-design/web-vue';

  const router = useRouter();
  // 状态管理
  const store = useUserStore();
  const { logoImg } = storeToRefs(store);
  const loading = ref(false);
  const list = ref<Array<any>>([]); // 优惠券列表
  const total = ref(0); // 优惠券总数
  // 请求参数
  const params = ref<any>({
    getType: "FREE",
    pageNumber: 1,
    pageSize: 20,
  });

  // 获取优惠券列表
  const getList = () => {
    couponList(params.value).then((res) => {
        loading.value = true;
        if (res.data.success) {
          loading.value = false;
          list.value = res.data.result.records;
          total.value = res.data.result.total;
        }
      }).catch(() => {
      loading.value = false;
      });
  };
  // 搜索优惠券
  const search = (value: any) => {
    params.value.couponName = value;
    params.value.pageNumber = 1;
    getList();
  };
  // 领取优惠券
  const receive = (item: any) => {
    receiveCoupon(item.id).then((res) => {
      if (res.data.success) {
        Modal.confirm({
          title: '领取优惠券',
          content: `优惠券领取成功，可到我的优惠券页面查看`,
          okButtonProps: {type: "primary", status: "danger"},
          okText: '我的优惠券',
          cancelText: '立即使用',
          onOk: () => {
            router.push('/user/home/<USER>/coupons')
          },
          onCancel: () => {
            router.push({path: "/goodsList", query: { promotionsId: item.id, promotionType: "COUPON" },});
          },
        });
      }
    });
  };
  // 根据字段返回 优惠券适用范围
  const useScope = (type: any, storeName: any) => {
    let shop = "平台";
    let goods = "全部商品";
    if (storeName !== "platform") shop = storeName;
    switch (type) {
      case "ALL":
        goods = "全部商品";
        break;
      case "PORTION_GOODS":
        goods = "部分商品";
        break;
      case "PORTION_GOODS_CATEGORY":
        goods = "部分分类商品";
        break;
    }
    return `${shop}${goods}可用`;
  };
  onMounted(() => {
    getList();
  });
  watch(() => params.value, (val) => {
      getList();
    }, { deep: true }
  );
</script>

<style scoped lang="less">
  .content {
    width: 100%;
    background-color: #fff;
    margin-bottom: 200px;
    > div {
      margin: 10px auto;
      width: 1200px;
    }
  }
  .coupon-title {
    display: flex;
    align-items: center;
    height: 80px;
    p {
      font-size: 18px;
      margin-right: 620px;
      margin-left: 20px;
    }
    border-bottom: 2px solid @theme_color;
  }
  .recommend {
    margin: 20px auto;
    font-weight: bold;
    width: 200px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    color: @text_color;
  }
  /*优惠券列表*/
  .coupon-list {
    max-height: 250px;
    flex-wrap: wrap;
    .coupon-item {
      width: 370px;
      height: 110px;
      box-sizing: border-box;
      position: relative;
      border: 1px solid #eee;
      margin: 5px 15px;
      .c-left {
        padding: 15px 40px 15px 20px;
        .describe {
          background-color: #fff4ec;
          color: #F31947;
          padding: 0 5px;
          margin-left: 10px;
          font-size: 13px;
        }
      }
      .c-right {
        width: 40px;
        height: 110px;
        text-align: center;
        box-sizing: border-box;
        padding: 22px 8px;
        position: absolute;
        right: 0;
        top: 0;
        background-color: #F31947;
        color: #fff;
        font-size: 14px;
      }
      b {
        position: absolute;
        z-index: 2;
        top: 0;
        display: block;
        width: 3px;
        height: 100%;
        background: url("../assets/images/small-circle.png") top left repeat-y;
        right: 38px;
      }
      i {
        position: absolute;
        width: 15px;
        height: 15px;
        right: 32px;
        border: 1px solid #eee;
        background-color: #fff;
        border-radius: 20px;
        &:after {
          content: "";
          position: absolute;
          width: 18px;
          height: 9px;
          left: -2px;
          background-color: #fff;
        }
      }
      i.circle-top {
        top: -9px;
        &::after {top: -2px;}
      }
      i.circle-bottom {
        bottom: -9px;
        &::after {bottom: -2px;}
      }
      .used {
        position: absolute;
        top: 60px;
        right: 40px;
        width: 50px;
        height: 50px;
      }
    }
  }

</style>
