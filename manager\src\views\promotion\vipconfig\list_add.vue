<template>
  <div style="background-color: #fff">
    <div style="padding: 20px 30px 0 30px; display: flex; justify-content: space-between; align-items: center;">
      <h2>{{ pageTitle }}</h2>
      <a-button v-if="isViewMode" type="primary" @click="switchToEditMode">编辑</a-button>
    </div>
    <a-form ref="formRef" style="padding: 30px" :model="form.form" layout="horizontal" auto-label-width>
      <a-divider orientation="left">基本信息</a-divider>

      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item field="siteName" label="会员名称" :validate-trigger="['change']">
            <a-input v-model="form.form.vipName" :disabled="isViewMode" allow-clear />
          </a-form-item>

          <a-form-item field="staticPageAddress" label="会员介绍" :validate-trigger="['change']">
            <a-input v-model="form.form.description" :disabled="isViewMode" allow-clear />
          </a-form-item>

          <a-form-item field="staticPageWapAddress" label="会员价格" :validate-trigger="['change']">
            <a-input v-model="form.form.price" :disabled="isViewMode" allow-clear />
          </a-form-item>

          <a-form-item label="会员详情">
            <a-upload
              list-type="picture-card"
              :action="uploadFile"
              :headers="{ accessToken: accessToken }"
              image-preview
              :limit="1"
              :disabled="isViewMode"
              @before-upload="beforeUpload"
              :onSuccess="businessLicenseSuccess"
              :file-list="detailImagesList"
            ></a-upload>
          </a-form-item>


          <a-form-item field="staticPageWapAddress" label="协议规则" :validate-trigger="['change']">
            <a-textarea v-model="form.form.agreement" :disabled="isViewMode" placeholder="请输入协议规则" allow-clear />
          </a-form-item>

          <br>
          <a-divider orientation="left">使用限制</a-divider>

          <a-form-item field="validityPeriod" label="有效期" :validate-trigger="['change']">
            <a-radio-group v-model="selectedOption" :disabled="isViewMode" :options="options" @change="handleRadioChange" />
          </a-form-item>

          <a-form-item field="dateRange" label="日期范围" :validate-trigger="['change']">
            <a-range-picker v-model="dateRange" :disabled="isViewMode" style="width: 254px;" />
          </a-form-item>
<!--
          <a-form-item field="voucherSwitch" label="使用范围">
            <a-radio-group type="button" v-model="selectedCity">
              <a-radio value="1">全品类</a-radio>
              <a-radio value="2">指定商品</a-radio>
              <a-radio value="3">指定商品分类</a-radio>
            </a-radio-group>
          </a-form-item> -->


          <br>
          <a-divider orientation="left">配置优惠券</a-divider>
          <a-form-item field="scopeType" label="选择优惠券" v-if="!isViewMode">
            <a-button type="primary" @click="showSelector">选择优惠券</a-button>
          </a-form-item>

          <a-form-item field="scopeType" label="优惠券">
            <a-table :columns="columns" :data="selectCouponList">
              <template #scopeType="{ record }">
                {{ clientTypeWay(record.scopeType) }}
              </template>
              <template #price="{ record }">
                <span v-if="record.price">{{ unitPrice(record.price, '¥') }}</span>
                <span v-else>{{ `${record.couponDiscount}折` }}</span>
              </template>
              <template #sendNum="{ rowIndex }">
                <a-input v-model="yhqform.couponActivityItems[rowIndex].num" :disabled="isViewMode" placeholder="赠送数量"></a-input>
              </template>
              <template #couponName="{ rowIndex }">
                <a-button v-if="!isViewMode" type="text" status="danger" @click="delCoupon(rowIndex)">删除</a-button>
              </template>
            </a-table>
          </a-form-item>
          <br>
          <a-divider orientation="left">配置赠送权益商品</a-divider>
          <a-form-item field="scopeType" label="选择赠送商品" v-if="!isViewMode">
              <a-button type="primary" @click="openSkuList">选择商品</a-button>
          </a-form-item>

          <a-form-item field="scopeType" label="选择商品">
            <a-table row-key="skuId" :columns="shopcolumns" :data="promotionGoodsList" :row-selection="rowSelection"
              @selection-change="selectTableChange" :bordered="true">
              <template #action="{ rowIndex }">
                <a-button v-if="!isViewMode" type="text" status="danger" @click="delGoods(rowIndex)">删除</a-button>
              </template>
              <template #settlementPrice="{ rowIndex }">
                <a-input-number v-model="promotionGoodsList[rowIndex].settlementPrice" :disabled="isViewMode" />
              </template>
              <template #pointsGoodsCategory="{ rowIndex }">
                <a-select :style="{ width: '100px' }" :disabled="isViewMode" v-model="promotionGoodsList[rowIndex].pointsGoodsCategory">
                  <a-option v-for="item in categoryList" :key="item.id" :value="item.id"
                    @click="changeCategory(item.name, rowIndex)">{{ item.name }}</a-option>
                </a-select>
              </template>

              <template #activeStock="{ rowIndex }">
                <a-input-number v-model="promotionGoodsList[rowIndex].activeStock" :disabled="isViewMode" :style="{ width: '100px' }" />
              </template>
              <template #points="{ rowIndex }">
                <a-input-number v-model="promotionGoodsList[rowIndex].points" :disabled="isViewMode" :style="{ width: '100px' }" />
              </template>
            </a-table>
          </a-form-item>
          <div style="margin-top: 20px;">
            <a-button style="margin-right: 10px;" @click="goBack">返回</a-button>
            <a-button v-if="!isViewMode" type="primary" @click="handleSubmit">保存</a-button>
          </div>
        </a-col>
      </a-row>
    </a-form>
    <a-modal v-model:visible="showOssManager" :width="1100" @ok="handleOss" title="oss资源管理"
      @cancel="showOssManager = false">
      <ossManages @selected="changOssImage" :isSingle="true"></ossManages>
    </a-modal>

    <skuselect ref="skuSelect" @change="changSkuList" :goodsOrSku="true" :apiParams="apiParams"
      :defaultGoodsSelectedList="promotionGoodsList" />
    <couponselect ref="couponLayout" @couponlist="couponlist" :apiParams="apiParamsoupon" ></couponselect>
  </div>
</template>

<script setup lang="ts">
import { getSetting, setSetting } from '@/api/operation';
import { ref, onMounted, reactive, inject, watch } from 'vue';
import useCurrentInstance from '@/hooks/useCurrentInstance';
import { VARCHAR255, REQUIRED, VARCHAR20 } from '@/utils/validator';
import { Message,FileItem} from '@arco-design/web-vue';
import { FormInstance } from '@arco-design/web-vue/es/form';
import ossManages from '@/components/oss-manage/index.vue';
import { useFavicon } from '@vueuse/core'
import { saveVipConfing, getVipConfigById, updateVipConfig } from '@/api/vipconfig';
import skuselect from '@/components/goods-sku-selector/index.vue';
import couponselect from '@/components/coupon-selector/index.vue';
import { clientTypeWay, dayFormatHHssMM } from '@/utils/filters';
import { unitPrice } from '@/utils/filters';
import { useRoute, useRouter } from 'vue-router';  // 添加 useRouter 导入
import dayjs from 'dayjs';
import { config } from 'process';
import uploadFile from '@/api/index'
import store from '@/utils/storage'
import { clone, cloneDeep } from 'lodash-es'
import { getPlatformCoupon } from '@/api/goods';  // 导入获取优惠券详情的API
import { getGoodsSkuData } from '@/api/goods';  // 导入获取商品SKU详情的API

const fileFormat = ref<Array<string>>(['jpg', 'jpeg', 'png', 'gif', 'bmp'])
const accessToken = ref<string>(store.getAccessToken() || '')
const detailImagesList = ref<any[]>([])  // 图片文件列表

const onAdvUploadSuccess = (file: FileItem, index: number) => {
  if (file.status !== 'done') {
    return Message.error('上传失败！')
  }
  imgform.value.advList[index].img = file.response.result
}

interface FormData {
  show_plate: boolean
  plate_badge: string
  show_explosions: boolean
  explosions: ExplosionItem[]
  show_logo: boolean
  logoList: LogoItem[]
  advList: AdvItem[]
}

interface ExplosionItem {
  img: string,
  goods: any,
  link: any
}

interface LogoPlateProductItem {
  title: string
  sub_title: string
  price: string
  goods: any
}

interface LogoPlateItem {
  title: string
  products: LogoPlateProductItem[]
}

interface LogoItem {
  label: string
  img: string
  plates: LogoPlateItem[]
}

interface AdvItem {
  img: string
  link: any
}

const makeEmptyLogoItem = (label: string) => ({
  label,
  img: '',
  plates: [makeEmptyLogoPlateItem()]
})

const makeEmptyLogoPlateItem = () => ({
  title: '产品板块标题',
  products: [makeEmptyLogoPlateProductItem()]
})

const makeEmptyLogoPlateProductItem = () => ({
  title: '产品标题',
  sub_title: '产品副标题',
  price: '',
  goods: ''
})

const emptyForm: FormData = {
  show_plate: true,
  plate_badge: '',
  show_explosions: true,
  explosions: [
    {img: '', goods: '', link: ''}
  ] as ExplosionItem[],
  show_logo: false,
  logoList: [makeEmptyLogoItem('基础产品板块')] as LogoItem[],
  advList: [
    { img: '', link: '' },
    { img: '', link: '' },
    { img: '', link: '' },
  ] as AdvItem[]
}
const imgform = ref(clone(emptyForm))

const beforeUpload: any = (file: any) => {
  return new Promise((resolve, reject) => {
    if (!fileFormat.value.includes(file.name.split('.')[file.name.split('.').length - 1])) {
      reject(new Error('上传失败'))
      Message.error(
        `请选择 .jpg .jpeg .png .gif .bmp格式文件`
      )
    } else if (Number((file.size / 1024).toFixed(0)) > 1024) {
      reject(new Error('上传失败'))
      Message.error(`所选文件大小过大，不得超过1M`)
    } else {
      resolve(true)
    }
  })
}

  // 上传成功回调
  const businessLicenseSuccess = (res: any) => {
    if (res.response.success) {
      Message.success('上传成功');
      form.value.form.detailImages = res.response.result;
    }
  };

const router = useRouter()
const route = useRoute()
const reload: any = inject("reload");
const showOssManager = ref<boolean>(false); // oss弹框
const selectedSku = ref(); // 选择的sku
const currentImgType = ref<any>(); // 当前要上传图片的字段
const formRef = ref<FormInstance>();
// 获取modal
const modal = useCurrentInstance().globalProperties?.$modal;

// 查看模式相关
const isViewMode = ref(false); // 是否为查看模式
const isEditMode = ref(false); // 是否为编辑模式
const pageTitle = ref('新增会员配置'); // 页面标题
interface formInterface {
  form: {
    vipName: string;
    description: string;
    price: string;
    skuId: string;
    detailImages: string;
    agreement: string;
    validityDays: string;
    scope: string;
    coupons: string;
    rightsPackageId: string;
  }
}


// 数据集
const form = ref<formInterface>({
  form: {
    vipName: '',
    description: '',
    price: '',
    skuId: '',
    detailImages: '',
    agreement: '',
    validityDays: '',
    scope: '',
    coupons: "",
    rightsPackageId:  '',
  }
});

const options = [
  { label: '年(365天)', value: '1' },
  { label: '月(30天)', value: '2' }
];

const selectedCity = ref('1');

const selectedOption = ref<string>('1');
const dateRange = ref<[dayjs.Dayjs, dayjs.Dayjs]>([dayjs(), dayjs()]);

const handleRadioChange = () => {
  const today = dayjs();
  let endDate;
  // 设置有效期

  if (selectedOption.value === '1') {
    form.value.form.validityDays = "365";
    endDate = today.add(365, 'day');
  } else if (selectedOption.value === '2') {
    form.value.form.validityDays = "30";
    endDate = today.add(30, 'day');
  }

  dateRange.value = [today, endDate];
};

// 初始设置
handleRadioChange();

// 监听selectedOption的变化
watch(selectedOption, handleRadioChange);


const handleSubmit = async () => {
  const list: { id: number; num: number }[] = [];
  yhqform.value.couponActivityItems.forEach((e: any) => {
    list.push({
    id: e.couponId,
    num: e.num
  });
  form.value.form.coupons = JSON.stringify(list);
  })
  const auth = await formRef.value?.validate();
  if (!auth) {
    try {
      let result;

      // 根据是否为编辑模式调用不同的接口
      if (isEditMode.value && route.query.id) {
        // 编辑模式：调用PUT接口，传递ID参数
        result = await updateVipConfig(route.query.id as string, form.value.form);
      } else {
        // 新增模式：调用POST接口
        result = await saveVipConfing(form.value.form);
      }

      // 安全地检查响应数据
      if (result && result.data && result.data.success) {
        Message.success(isEditMode.value ? '编辑成功!' : '保存成功!');
        // 保存成功后返回列表页面
        goBack();
      } else {
        const errorMsg = result?.data?.message || (isEditMode.value ? '编辑失败！' : '保存失败！');
        Message.error(errorMsg);
      }
    } catch (error) {
      console.error('保存失败:', error);
      Message.error(isEditMode.value ? '编辑失败！' : '保存失败！');
    }
  }
};
const skuSelect = ref(null) as any// 商品选择器
const promotionGoodsList = ref([]) as any// 活动商品列表
const selectedGoods = ref<any>([])  // 已选商品列表，便于删除


const apiParams = {
  marketEnable: 'UPPER',
  authFlag: 'PASS'
}

const apiParamsoupon = ref({
  getType: '',
  promotionStatus: 'START',
});
// 选择商品
const openSkuList = () => {
  console.log('=== 打开商品选择器 ===');
  console.log('当前promotionGoodsList:', promotionGoodsList.value);
  console.log('当前权益包ID:', form.value.form.rightsPackageId);

  skuSelect.value.modalData.visible = true
}

// 选择优惠券
const showSelector = () => {
  couponLayout.value.visible = true
}

// 选择的商品
const changSkuList = (val: any) => {
  console.log('=== 选择商品组件返回数据 ===');
  console.log('原始返回数据:', val);
  console.log('返回数据长度:', val.length);

  if(val.length > 1){
    Message.error('只能选择一个权益商品');
    return;
  }

  const list: any = []
  val.forEach((e: any, index: number) => {
    console.log(`=== 处理第${index + 1}个商品数据 ===`);
    console.log('原始商品数据:', e);

    const obj = {
      settlementPrice: e.settlementPrice || 0,
      pointsGoodsCategoryId: e.pointsGoodsCategoryId || 0,
      pointsGoodsCategoryName: e.pointsGoodsCategoryName || "",
      activeStock: e.activeStock || 0,
      points: e.points || 0,
      skuId: e.id,
      goodsId: e.goodsId,
      originalPrice: e.price || 0,
      thumbnail: e.thumbnail || "",
      goodsName: e.goodsName || "",
      quantity: e.quantity || "",
      storeName: e.storeName || "",
      price: e.price || ""
    }

    console.log('转换后的商品对象:', obj);
    list.push(obj);
    form.value.form.rightsPackageId = e.id;
  })

  console.log('=== 最终渲染到列表的数据 ===');
  console.log('处理后的商品列表:', list);
  console.log('设置的权益包ID:', form.value.form.rightsPackageId);

  promotionGoodsList.value = list;

  console.log('=== 设置后的promotionGoodsList ===');
  console.log('promotionGoodsList.value:', promotionGoodsList.value);
  console.log('promotionGoodsList长度:', promotionGoodsList.value.length);
}


// oss资源确定
const handleOss = () => {
  showOssManager.value = false;
  form.value.form[currentImgType.value] = selectedSku.value[selectedSku.value.length - 1].url;
};
// oss资源改变
const changOssImage = (val: any) => {
  selectedSku.value = [];
  val.forEach((item: any) => {
    selectedSku.value.push({ url: item.url })
  })
};
// 配置优惠券
const couponLayout = ref(null) as any // 优惠券选择器
const selectCouponList = ref([]) as any // 选择的优惠券列表

const reSelectCoupon = () => {
  yhqform.value.couponActivityItems = selectCouponList.value.map((item: any) => {
    return {
      num: 0,
      couponId: item.id,
    }
  })
}

const yhqform = ref<any>({
  promotionName: "", // 活动名称
  activityScope: "ALL", // 活动范围 ，默认全体发券
  couponActivityType: "REGISTERED", // 触发活动方式 默认新人赠券
  couponFrequencyEnum: "MONTH", //选择周期
  startTime: "", // 开始时间
  endTime: "", // 结束时间
  memberDTOS: [], // 指定会员范围
  couponActivityItems: [

  ], // 优惠券列表
});
// 优惠卷列表
const columns: any = [
  {
    title: '优惠券名称',
    dataIndex: 'couponName',
    width: 200
  },
  {
    title: '面额/折扣',
    dataIndex: 'price',
    width: 200,
    slotName: 'price'
  },
  {
    title: '赠送数量',
    dataIndex: 'couponName',
    width: 200,
    slotName: 'sendNum'
  },
  {
    title: '操作',
    dataIndex: 'couponName',
    width: 200,
    slotName: 'couponName'
  },
]

// 表头
const shopcolumns = [
  {
    title: '商品名称',
    dataIndex: 'goodsName',
    width: 160
  },

  {
    title: '商品价格',
    dataIndex: 'price',
    width: 200
  },
  {
    title: '库存',
    dataIndex: 'quantity',
    width: 200
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 200,
    slotName: 'action'
  },
]



// 删除
const delCoupon = (idx: any) => {
  selectCouponList.value.splice(idx, 1)
  reSelectCoupon()
}

const couponlist = (val: any) => {
  selectCouponList.value.push(...val)
  const map = new Map();
  selectCouponList.value = selectCouponList.value.map((item: any) => {
    return map.set(item.id, item);
  })
  selectCouponList.value = [...map.values()];
  reSelectCoupon()

}

// 选择商品
const delSelectGoods = () => {
  if (selectedGoods.value.length <= 0) {
    Message.warning('您还未选择要删除的数据');
    return
  }
  modal.confirm({
    title: '确认删除',
    content: `您确认要删除所选商品吗?`,
    alignCenter: false,
    onOk: async () => {
      promotionGoodsList.value = promotionGoodsList.value.filter((item: any) => {
        return !selectedGoods.value.includes(item.skuId);
      });
    },
  });
}

const rowSelection = reactive<any>({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});

// 传过来的商品
const selectTableChange = (val: any) => {
  selectedGoods.value = val
}

// 单选删除
const delGoods = (idx: any) => {
  promotionGoodsList.value.splice(idx, 1)
}

// 分类列表
const categoryList = ref([]) as any

// 选择商品
const changeCategory = (val: string, index: number) => {
  promotionGoodsList.value[index].pointsGoodsCategoryName = val
}

// 初始化页面模式
const initPageMode = () => {
  const { onlyView, id } = route.query;

  if (onlyView) {
    isViewMode.value = true;
    pageTitle.value = '查看会员配置';
  } else if (id) {
    isEditMode.value = true;
    pageTitle.value = '编辑会员配置';
  } else {
    pageTitle.value = '新增会员配置';
  }

  // 如果有ID，获取详情数据
  if (id) {
    getVipConfigDetail(id as string);
  }
}

// 获取VIP配置详情
const getVipConfigDetail = async (id: string) => {
  try {
    const result = await getVipConfigById(id);
    if (result.data.success) {
      const data = result.data.result;

      // 填充表单数据，确保数据类型正确
      form.value.form = {
        ...form.value.form,
        ...data,
        price: String(data.price || ''),  // 确保价格是字符串类型
        validityDays: String(data.validityDays || ''),  // 确保有效期是字符串类型
        coupons: data.coupons || '',
        rightsPackageId: data.rightsPackageId || '',
      };

      // 处理图片数据回显
      if (data.detailImages) {
        detailImagesList.value = [{
          uid: '1',
          name: 'detail-image',
          status: 'done',
          url: data.detailImages,
          response: {
            result: data.detailImages
          }
        }];
      }

      // 处理优惠券数据
      if (data.coupons) {
        try {
          const couponData = JSON.parse(data.coupons);
          // 根据优惠券ID获取详细信息
          await loadCouponDetails(couponData);
        } catch (e) {
          console.error('解析优惠券数据失败:', e);
        }
      }

      // 处理商品SKU数据
      if (data.rightsPackageId) {
        await loadSkuDetails(data.rightsPackageId);
      }

      // 删除这段冲突的代码
      // if (data.rightsPackageId) {
      //   // 这里可以根据需要获取权益商品详情
      //   promotionGoodsList.value = data.promotionGoodsList || [];
      // }

      // 处理有效期数据
      if (data.validityDays) {
        selectedOption.value = data.validityDays === '365' ? '1' : '2';
        handleRadioChange();
      }

    } else {
      Message.error('获取会员配置详情失败');
    }
  } catch (error) {
    console.error('获取会员配置详情失败:', error);
    Message.error('获取会员配置详情失败');
  }
}

// 加载优惠券详情
const loadCouponDetails = async (couponData: any[]) => {
  try {
    console.log('优惠券原始数据:', couponData);

    const couponPromises = couponData.map(async (item: any) => {
      try {
        const couponResult = await getPlatformCoupon(item.id);
        if (couponResult.data.success) {
          // 保留原始数量信息
          return {
            ...couponResult.data.result,
            num: String(item.num || ''),  // 确保数量是字符串类型
            couponName: couponResult.data.result.couponName || ''
          };
        }
        return null;
      } catch (error) {
        console.error(`获取优惠券详情失败 ID: ${item.id}`, error);
        return null;
      }
    });

    const couponDetails = await Promise.all(couponPromises);
    selectCouponList.value = couponDetails.filter(item => item !== null);

    console.log('优惠券详情数据:', selectCouponList.value);

    // 设置优惠券活动项，包含数量信息
    yhqform.value.couponActivityItems = selectCouponList.value.map((item: any) => {
      return {
        num: String(item.num || ''),  // 确保数量是字符串类型
        couponId: item.id,
      }
    });

    console.log('优惠券活动项:', yhqform.value.couponActivityItems);
  } catch (error) {
    console.error('加载优惠券详情失败:', error);
  }
};

// 加载商品SKU详情
const loadSkuDetails = async (rightsPackageId: string) => {
  try {
    console.log('开始加载商品SKU详情，rightsPackageId:', rightsPackageId);

    // 通过SKU列表API查询，带上rightsPackageId参数精确查询
    const skuResult = await getGoodsSkuData({
      pageNumber: 1,
      pageSize: 10,
      id: rightsPackageId  // 使用rightsPackageId参数查询特定的SKU
    });

    console.log('SKU查询结果:', skuResult.data);

    if (skuResult.data.success && skuResult.data.result.records.length > 0) {
      // 由于使用了精确的id查询，直接取第一个结果
      const skuData = skuResult.data.result.records[0];

      console.log('找到SKU数据:', skuData);

      // 设置商品列表数据
      promotionGoodsList.value = [{
        skuId: skuData.id,
        goodsId: skuData.goodsId,
        goodsName: skuData.goodsName,
        skuName: skuData.spuName || skuData.goodsName,  // 使用spuName或goodsName
        price: skuData.price,
        quantity: skuData.quantity || 0,  // 库存字段，对应表格列定义
        thumbnail: skuData.thumbnail || skuData.small || skuData.original,  // 尝试多个图片字段
        settlementPrice: skuData.price,  // 默认结算价格等于商品价格
        activeStock: skuData.quantity || 0,  // 活动库存
        points: 0,  // 默认积分
        pointsGoodsCategory: '',  // 积分商品分类
        pointsGoodsCategoryName: ''  // 积分商品分类名称
      }];

      // 设置表单中的权益包ID
      form.value.form.rightsPackageId = skuData.id;

      console.log('商品列表数据设置成功:', promotionGoodsList.value);
      console.log('当前promotionGoodsList长度:', promotionGoodsList.value.length);
      console.log('表格应该显示的数据:', JSON.stringify(promotionGoodsList.value, null, 2));
    } else {
      console.warn('SKU查询结果为空或失败，rightsPackageId:', rightsPackageId);
      if (skuResult.data.result) {
        console.log('查询结果:', skuResult.data.result);
      }
    }
  } catch (error) {
    console.error('加载商品SKU详情失败:', error);
  }
};

// 切换到编辑模式
const switchToEditMode = () => {
  isViewMode.value = false;
  isEditMode.value = true;
  pageTitle.value = '编辑会员配置';

  // 更新路由参数，移除onlyView参数
  router.replace({
    name: 'add-vipconfig',
    query: { id: route.query.id }
  });
}

// 返回列表页面
const goBack = () => {
  // 使用路由名称跳转，与会员列表保持一致
  router.push({
    name: 'vipconfig',
  });
}

// 监听promotionGoodsList变化
watch(() => promotionGoodsList.value, (newValue, oldValue) => {
  console.log('=== promotionGoodsList数据变化监听 ===');
  console.log('旧值:', oldValue);
  console.log('新值:', newValue);
  console.log('新值长度:', newValue.length);
  if (newValue.length > 0) {
    console.log('第一个商品数据:', newValue[0]);
  }
}, { deep: true });

// 页面初始化
onMounted(() => {
  initPageMode();
});

</script>
