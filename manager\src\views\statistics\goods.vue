<template>
  <div>
    <a-card title="商品统计" hoverable :style="{ width: '100%', marginBottom: '20px' }">
      <recent-time
        :date-type="defaultDateType.date"
        @on-change="handleClickTimeChange"
      ></recent-time>
    </a-card>

    <a-card hoverable>
      <hot-goods-order :date-type="defaultDateType.date"></hot-goods-order>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import recentTime from '@/components/recent-time/index.vue';
  import hotGoodsOrder from '@/components/hot-goods-order/index.vue';
  import { defaultDateType, handleClickTimeChange } from '@/hooks/statistics';
</script>

<style scoped></style>
