<template>
  <div style="background-color: #fff">
    <a-form ref="formRef" style="padding: 30px" :model="form" layout="horizontal" auto-label-width>
      <a-divider orientation="left">积分设置</a-divider>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item
              field="consumer"
              label="消费1元赠送积分"
              :validate-trigger="['change']"
          >
            <a-input-number v-model="form.consumer" allow-clear>
              <template #append>
                <span>积分</span>
              </template>
            </a-input-number>
          </a-form-item>

          <a-form-item
              field="register"
              label="注册账号"
              :validate-trigger="['change']"
          >
            <a-input-number v-model="form.register" allow-clear>
              <template #append>
                <span>积分</span>
              </template>
            </a-input-number>
          </a-form-item>

          <a-form-item
              field="signIn"
              label="每日签到积分"
              :validate-trigger="['change']"
          >
            <a-input-number v-model="form.signIn" allow-clear>
              <template #append>
                <span>积分</span>
              </template>
            </a-input-number>
          </a-form-item>

          <a-form-item
              field="comment"
              label="订单评价赠送积分"
              :validate-trigger="['change']"
          >
            <a-input-number v-model="form.comment" allow-clear>
              <template #append>
                <span>积分</span>
              </template>
            </a-input-number>
          </a-form-item>
          <a-form-item label="选择商品" class="form-item-sku">
            <a-button @click="onSkuPicker">
              {{ form.pointGoodsItems && form.pointGoodsItems.length > 0 ? '重新选择商品' : '选择商品' }}
            </a-button>
            <div class="sku-list">
              <a-tag
                v-for="(item, index) in form.pointGoodsItems"
                :key="item.skuId"
                closable
                @close="onDeleteSku(index)"
              >{{ item.name }}</a-tag>
            </div>
          </a-form-item>
          <a-form-item v-for="(point,index) in form.pointSettingItems" :key="index"
                :label="'签到设置'+(index+1)">
                <a-space>
                    <a-input-number w-100px :min="1" :max="99999" @change="compare($event,index)" :model-value="point.day"></a-input-number>
                    <a-input-number w-100px :min="0" :max="99999" v-model="point.points"></a-input-number>
                    <a-button ghost type="primary" @click="handleClickDeleteItem(index)">删除</a-button>
                  <span text-12px>签到<span >{{ point.day }}</span>天，赠送<span
                   >{{ point.points }}</span>积分</span>
                </a-space>
          </a-form-item>
          <a-form-item label="操作">
            <a-button type="primary" @click="handleAddPoint">添加</a-button>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleSubmit">保存</a-button>
          </a-form-item>

        </a-col>
      </a-row>
    </a-form>
  </div>
  <SkuPicker
    ref="skuPickerRef"
    @change="handleSkuPickerConfirm"
    :goodsOrSku="true"
  ></SkuPicker>
</template>

<script setup lang="ts">
import { getSetting, setSetting } from '@/api/operation';
import { Message } from '@arco-design/web-vue';
import { FormInstance } from '@arco-design/web-vue/es/form';
import { onMounted, ref } from 'vue';
import SkuPicker from '@/components/goods-sku-selector/index.vue'

  const formRef = ref<FormInstance>();

  // 数据集
  const form = ref<any>({
    form: {
      consumer: 0,
      register: 0,
      signIn: 0,
      comment: 0
    },
    pointGoodsItems: []
  });

  function compare(val:any,event:any){
    const regex = /^-?\d+$/;
    if(!regex.test(val)){
      Message.error('请输入正整数')
      return
    }
    // 每次添加
    const isTrue = form.value.pointSettingItems.find((item:any)=>{
      return item.day == val
    })
    if(isTrue){
      Message.error('签到天数不能重复')
      return
    }else{
      form.value.pointSettingItems[event].day = val
    }
  }

  async function init() {
    const res = await getSetting('POINT_SETTING');
    form.value = res.data.result;
  }
  function handleAddPoint(){
   
    if (form.value.pointSettingItems.length >= 4) {
        Message.error('最多设置4项签到设置');
        return false;
      }
    form.value.pointSettingItems.push({
      points: "0",
      day: form.value.pointSettingItems.length,
    });
  }

  function handleClickDeleteItem(index:number) {
    form.value.pointSettingItems.splice(index, 1);
  }

  const handleSubmit = async () => {
    // console.log(form.value, 'form.value');
    const result = await setSetting('POINT_SETTING', form.value);
    if (result.data.success) {
      Message.success('设置成功!');
      init();
    }
  };

  onMounted(() => {
    init();
  });

  const skuPickerRef = ref<InstanceType<typeof SkuPicker>>()
  const onSkuPicker = () => {
    skuPickerRef.value!.modalData.visible = true
  }
  const onDeleteSku = (index: number | string) => {
    form.value.pointGoodsItems.splice(index, 1)
  }
  const handleSkuPickerConfirm = (val: any) => {
    if (!val || val.length === 0) {
      form.value.pointGoodsItems = []
    } else {
      form.value.pointGoodsItems = val.map(item => ({
        skuId: item.id,
        name: item.goodsName
      }))
    }
  }
</script>

<style lang="less" scoped>
.form-item-sku {
  :deep(.arco-form-item-content) {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  .sku-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 10px;
  }
}
</style>
