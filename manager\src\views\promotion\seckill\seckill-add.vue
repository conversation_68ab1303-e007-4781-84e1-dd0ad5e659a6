<template>
  <div>
    <a-card :bordered="false">
      <a-form ref="modifyPriceForm" :model="form" :style="{ width: '100%' }" layout="horizontal" auto-label-width>
        <div class="base-info-item">
          <h4>基本信息</h4>
          <div class="form-item-view">
            <a-form-item field="promotionName" label="活动名称" :rules="[REQUIRED]">
              <a-input v-model="form.promotionName" allow-clear :style="{ width: '260px' }" />
            </a-form-item>
            <a-form-item field="applyEndTime" label="报名截止时间" :rules="[REQUIRED]">
              <a-date-picker v-model="form.applyEndTime" style="width: 200px;" allow-clear show-time />
            </a-form-item>
            <a-form-item field="startTime" label="活动开始时间" :rules="[REQUIRED]">
              <a-date-picker v-model="form.startTime" style="width: 200px;" allow-clear show-time />
            </a-form-item>
            <a-form-item field="seckillPeriod" label="抢购时间段" :rules="[REQUIRED]">
              <a-tag closable v-for="item in form.seckillPeriod" :key="item" @close="removePeriodTime(item)">
                {{ item >= 10 ? item : "0" + item }}:00
              </a-tag>
              <a-input-number v-model="periodTime" v-show="showAddPeriod" style="width: 100px;margin-left: 10px;"
                class="input-demo" :min="0" :max="23" @blur="addPeriodTime" />
              <a-button style="margin-left: 10px;" type="outline" @click="addPeriod">添加时间段</a-button>
            </a-form-item>
            <a-form-item field="seckillRule" label="申请规则" :rules="[REQUIRED]">
              <a-input v-model="form.seckillRule" allow-clear :style="{ width: '260px' }" />
            </a-form-item>
          </div>
          <div class="foot-btn">
            <a-button style="margin-right: 5px" @click="closeCurrentPage">返回</a-button>
            <a-button type="primary"  @click="handleSubmit">提交</a-button>
          </div>
        </div>
      </a-form>
    </a-card>
</div>
</template>

<script setup lang='ts'>
import { seckillDetail } from '@/api/promotion';
import { onMounted, ref } from "vue";
import { useRoute, useRouter } from 'vue-router';

import { updateSeckill } from '@/api/promotion';
import { REQUIRED } from '@/utils/validator';
import { Message } from '@arco-design/web-vue';
import { FormInstance } from '@arco-design/web-vue/es/form';



const modifyPriceForm = ref<FormInstance>() as any;
const route = useRoute()
const router = useRouter()
const form = ref<any>({
  promotionName: '',
  applyEndTime: '',
  startTime: '',
  seckillPeriod: [],
  seckillRule: '',
  hours: '',
  createTime: '',
  updateTime: '',
  endTime: '',
  seckillApplyList: '',
})
// const periodTime = ref<null>(null) as any
const periodTime = ref<any>();
const showAddPeriod = ref<boolean>(false)
// 获取详情数据
const getData = async () => {
  const res = await seckillDetail(route.query.id);
  if (res.data.success) {
    const data = res.data.result
    data.seckillPeriod = res.data.result.hours.split(",");
    form.value = data
  }
}
onMounted(() => {
  getData()

})
// 关闭
const removePeriodTime = (item: any) => {
  form.value.seckillPeriod = form.value.seckillPeriod.filter((i: any) => i != item);
}
// 添加秒杀时间段
const addPeriodTime = () => {
  showAddPeriod.value = false;
  // if (periodTime.value != null && !form.value.seckillPeriod.includes(periodTime.value)) {
  //   form.value.seckillPeriod.push(periodTime.value)
  // }
  // 获取活动开始时间小时数
  const currentHours = new Date(form.value.startTime).getHours();
  if (periodTime.value != null && !form.value.seckillPeriod.includes(periodTime.value)) {
    if (currentHours >  periodTime.value) {
      Message.error('抢购时间段不能小于活动开始时间');
      periodTime.value = '';
      return false;
    }
    form.value.seckillPeriod.push(periodTime.value);
  }
}
// 添加时间段
const addPeriod = () => {
  addPeriodTime()
  showAddPeriod.value = true;
  periodTime.value = '';
}
// 返回
const closeCurrentPage = () => {
  router.push({ name: 'seckill' })
}
// 提交
const handleSubmit = async () => {
  const auth = await modifyPriceForm.value?.validate();
  if (!auth) {
    // 获取活动开始时间小时数
    const currentHours = new Date(form.value.startTime).getHours();
    let result = true;
    Object.values(form.value.seckillPeriod).map((item:any)=>{
      if (Number(currentHours) > Number(item)) {
        result = false;
      }
    });
    if (result) {
      form.value.hours = form.value.seckillPeriod.toString()
      delete form.value.createTime
      delete form.value.updateTime
      delete form.value.endTime
      delete form.value.seckillApplyList
      const params = form.value
      updateSeckill(params).then((res: any) => {
        if (res.data && res.data.success) {
          Message.success(`编辑成功`);
          router.push({ name: 'seckill' })
        }
      })
    } else {
      Message.error('抢购时间段不能小于活动开始时间');
    }
  }
}
</script>

<style lang="less" scoped>
h4 {
  margin-bottom: 10px;
  padding: 0 10px;
  border: 1px solid #ddd;
  background-color: #f8f8f8;
  font-weight: bold;
  color: #333;
  font-size: 14px;
  line-height: 40px;
  text-align: left;
}


:deep(.arco-tag-size-medium) {
  margin-left: 10px;
}
</style>
