<template>
  <div py-30px border-b-1>
    <a-space>
      <div w-90px>图片</div>
      <div>
        <div v-if="(isRes && props!.res!.data.list![0]!.img) || (!isRes && props.item?.img)">
          <a-image :src="!!props.res ? props.res.data.list![0]!.img : props.item?.img" width="100">
          </a-image>
          <a-tooltip content="清空图片">
            <div @click="clear()" color-gray h-35px line-height-35px cursor-pointer text-center>
              清空
            </div>
          </a-tooltip>
        </div>
        <div flex items-end gap-8px>
          <a-button flex-1 @click="openUpload()"><template #icon><icon-image /></template>本地上传</a-button>
          <a-button flex-1 @click="open()"><template #icon><icon-image /></template>图库选择</a-button>
        </div>
      </div>
    </a-space>
    <uploadPicker :limit="1" @onChange="callback" ref="uploadRef" />
    <openOss @callback="callback" ref="ossRef" />
  </div>
</template>

<script setup lang="ts">
import configs from '@/config/index';
import openOss from '@/views/operation/decoration/components/open-oss.vue'
import uploadPicker from '@/components/upload-pic/draw-upload.vue'
import { DragRule } from '@/views/operation/decoration/app/models/types';
const props = defineProps<{
  res?: DragRule,
  item?: {
    img: string,
    url: string
  }
}>()
import { ref } from 'vue'
import { computed } from 'vue';

const isRes = computed<boolean>(() => !!props.res)
const uploadRef = ref<any>(null)
const ossRef = ref<any>(null)
function open() {
  ossRef.value?.open()

}
// 回调图片
function callback(url: string) {

  if(!url) return
  if (!props.item) {
    props!.res!.data.list![0]!.img = url
  } else {
    props.item.img = url
  }
}

// 清空上传图片
function clear() {
  if (!props.item) {
    props!.res!.data.list![0]!.img = configs.INVENT.EMPTY_IMAGE
  }
  else {
    props.item.img = configs.INVENT.EMPTY_IMAGE
  }
}

const openUpload = () => {
  uploadRef.value.open();
}

</script>

<style scoped>
</style>
