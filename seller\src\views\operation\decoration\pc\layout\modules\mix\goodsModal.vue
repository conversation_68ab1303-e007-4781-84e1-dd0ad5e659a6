<template>
  
  <div w-584px h-343px rounded-10px flex bg-white class="box-shadow" v-if="props.res">
    <div class="left-side">
      <img :src="props!.res!.data!.list![0].img">
    </div>
    <div w-387px v-auto-animate>
      <div flex flex-a-c justify-end >
        <div class="badge" v-if="props!.res!.data!.text">{{ props.res.data.text }}</div>
      </div>
      <div class="goods-list" v-if="props.res.data.goodsList.length">
        <div class="goods-item" :key="index" v-for="(item, index) in props.res.data.goodsList">
          <div>
            <img w-90px h-90px :src="item.img || item.thumbnail">
          </div>
          <div>
            <div class="goods-title" line-clamp-2>{{ item.title }}</div>
            <div class="goods-price">￥{{ unitPrice(item.price) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DragRule } from '@/views/operation/decoration/app/models/types';
import { unitPrice } from '@/utils/filters'
const props = defineProps<{
  res: DragRule,
}>()
</script>

<style scoped lang="less">
.left-side {
  overflow: hidden;
  width: 196.7px;
  height: 343.7px;

  >img {
    width: 100%;
    height: 100%;
  }
}

.badge {
  min-width: 50px;
  padding: 0 10px;
  height: 27px;
  line-height: 27px;
  border-radius: 13.3px 0 0 13.3px;
  opacity: 1;
  background: #f31947;
  font-size: 12.6px;
  font-weight: 400;
  text-align: center;
  letter-spacing: 0;
  color: #fff;
  margin-top: 26px;
  margin-bottom: 17px;

}

.goods-list {
  flex-wrap: wrap;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 0 16px;
  display: flex;
}

.goods-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 173.6px;
  height: 119px;
  border-radius: 9.8px;
  opacity: 1;
  -webkit-transition: .35s;
  transition: .35s;
  background: #fff;
  margin-bottom: 9px;
}

.goods-title {
  font-size: 13px;
  font-weight: 400;
  line-height: 16px;
  text-align: center;
  letter-spacing: 0;
  color: #333;

}

.goods-price {
  font-size: 14px;
  font-weight: 400;
  line-height: 17px;
  letter-spacing: 0;
  color: #f31947;
  margin-top: 8px;
  margin-bottom: 10px;
}
</style>
