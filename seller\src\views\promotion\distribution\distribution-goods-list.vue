<template>
  <a-card class="general-card" title="分销商品" :bordered="false">
    <searchTable
      :columns="columnsSearch"
      time-type="timestamp"
      @reset="
        (val) => {
          apiParamsTable = { ...apiParamsTable, ...val };
        }
      "
      @search="
        (val) => {
          apiParamsTable = { ...apiParamsTable, ...val };
        }
      "
    >
    </searchTable>
    <a-row style="margin-bottom: 16px">
      <a-col :span="16">
        <a-space>
          <a-button type="primary" @click="selectGoods">
            添加分销商品
          </a-button>
        </a-space>
      </a-col>
    </a-row>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getDistributionGoods"
      :bordered="true"
      :api-params="apiParamsTable"
      @delete="deleteDistributionGoods"
    />
    <skuselect
      ref="skuSelect"
      :goods-or-sku="true"
      :api-params="apiParams"
      :default-goods-selected-list="selectList"
      @change="changSkuList"
    ></skuselect>
    <!-- <goodsSkuTemplate ref="goodsSkuRef" /> -->
    <!--<a-modal-->
    <!--v-model:visible="modalVisible"-->
    <!--title="保存分销商品"-->
    <!--@cancel="modalVisible = false"-->
    <!--@ok="handleSubmit"-->
    <!--&gt;-->
    <!--<a-form :model="form">-->
    <!--<a-form-item field="commission" label="分销佣金">-->
    <!--<a-input-number v-model="form.commission" :precision="2" />-->
    <!--</a-form-item>-->
    <!--</a-form>-->
    <!--</a-modal>-->
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import {
    getDistributionGoods,
    distributionGoodsCancel,
    distributionGoodsCheck,
  } from '@/api/promotion';
  import useCurrentInstance from '@/hooks/useCurrentInstance';
  import skuselect from '@/components/goods-sku-selector/index.vue';
  import { Message } from '@arco-design/web-vue';
  import { ref, reactive } from 'vue';

  const tablePageRef = ref<any>();
  const apiParamsTable = ref({});
  const modalVisible = ref(false);
  const form = reactive({
    commission: 1,
  }) as any;
  const apiParams = {
    marketEnable: 'UPPER',
    authFlag: 'PASS',
  };
  const skuId = ref('');
  const skuSelect = ref(null) as any; // 商品选择器
  const selectList = ref<any>();
  //  获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;
  const columnsSearch: Array<SearchRule> = [
    {
      label: '商品名称',
      model: 'goodsName',
      disabled: false,
      input: true,
    },
  ];

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '商品名称',
      dataIndex: 'goodsName',
      slot: true,
      width: 500,
      ellipsis: false,
      slotData: {
        goods: {
          goodsImage: 'thumbnail',
          goodsName: 'goodsName',
        },
      },
    },
    {
      title: '商品价格',
      dataIndex: 'price',
      currency: true,
    },
    // {
    //   title: '库存',
    //   dataIndex: 'quantity',
    // },
    // {
    //   title: '店铺名称',
    //   dataIndex: 'storeName',
    // },
    {
      title: '佣金金额',
      dataIndex: 'commission',
      currency: true,
    },
  ];

  const sortMethods: MethodsRule = {
    title: '操作',
    // width: 300,
    methods: [
      {
        title: '删除',
        callback: 'delete',
        type: 'text',
        status: 'danger',
      },
    ],
  };
  // 删除分销商品
  const deleteDistributionGoods = async (v: any) => {
    modal.confirm({
      title: '确认删除',
      content: '您确认要删除此分销商品么',
      alignCenter: false,
      onOk: async () => {
        const res = await distributionGoodsCancel(v.record.id);
        if (res.data.success) {
          Message.success('删除成功');
          tablePageRef.value.init();
        }
      },
    });
  };
  // 提交
  const handleOk = () => {};

  // 添加商品
  const handleSubmit = (val: any) => {
    skuId.value = val[0].id;
    const params = { skuId: val[0].id };
    // form.skuId = skuId.value;
    distributionGoodsCheck(params).then((res: any) => {
      if (res.data.success) {
        Message.success('添加成功');
        modalVisible.value = false;
        tablePageRef.value.init();
      }
    });
    selectList.value = [];
  };
  // 选择商品
  const selectGoods = () => {
    skuSelect.value.modalData.visible = true;
  };
  // 选择的商品
  const changSkuList = (val: any) => {
    // modalVisible.value = true;
    // skuId.value = val[0].id;
    // form.commission = 1.0;
    selectList.value = val;
    handleSubmit(val);
  };
</script>
