<template>
  <div>
    <Card _Title="修改密码" :_Size="16"></Card>

    <a-row class="account-safe">
      <a-col :span="1"></a-col>
      <a-col :span="2">
        <svg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 24 24"><path fill="#ef4444" d="M17 14h-4.341a6 6 0 1 1 0-4H23v4h-2v4h-4zM7 14a2 2 0 1 0 0-4a2 2 0 0 0 0 4"/></svg>
      </a-col>
      <a-col :span="16">
        <div>验证密码</div>
        <div class="global-color mt_10">通过验证当前登录密码方式，修改密码</div>
      </a-col>
      <a-col :span="4">
        <a-button size="small" type="outline" status="danger" class="mr_10" :loading="loading" @click="toLoginPwd">修改密码</a-button>
      </a-col>
    </a-row>
    <a-row class="account-safe">
      <a-col :span="1"></a-col>
      <a-col :span="2">
        <svg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 24 24"><path fill="#ef4444" d="M17 14h-4.341a6 6 0 1 1 0-4H23v4h-2v4h-4zM7 14a2 2 0 1 0 0-4a2 2 0 0 0 0 4"/></svg>
      </a-col>
      <a-col :span="16">
        <div>验证手机号</div>
        <div class="global-color mt_10">通过登录手机号验证码方式，修改密码。</div>
      </a-col>
      <a-col :span="4">
        <a-button size="small" type="outline" status="danger" class="mr_10" :loading="loading" @click="toLoginPwdByMobile">修改密码</a-button>
      </a-col>
    </a-row>


  </div>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router';
  import { ref } from 'vue';

  const router = useRouter();
  const loading = ref(false);

  // 通过旧密码修改登录密码
  const toLoginPwd = () => {
    router.push({ path: '/user/home/<USER>/loginPwd', query: {status: 1} });
  };
  // 通过手机验证码修改登录密码
  const toLoginPwdByMobile = () => {
    router.push({ path: '/user/home/<USER>/mobilePwd', query: {status: 1} });
  };
</script>

<style scoped lang="less">
  .account-safe {
    border-bottom: 1px solid @light_border_color;
    align-items: center;
    padding: 20px 0;
  }
</style>
