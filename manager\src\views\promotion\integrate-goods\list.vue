<template>
  <a-card class="general-card" title="积分商品" :bordered="false">
    <a-tabs @change="(val) => { apiParams.promotionStatus = val }" :default-active-key="integrateStatus">
      <a-tab-pane key="NEW" title="未开始"></a-tab-pane>
      <a-tab-pane key="START" title="已开始"></a-tab-pane>
      <a-tab-pane key="END" title="已结束"></a-tab-pane>
      <a-tab-pane key="CLOSE" title="已关闭"></a-tab-pane>
    </a-tabs>
    <searchTable :columns="columnsSearch" @reset="(val) => { apiParams = { ...apiParams, ...val } }"
      @search="(val) => { apiParams = { ...apiParams, ...val } }">
    </searchTable>
    <a-row style="margin-bottom: 16px">
      <a-col :span="16">
        <a-space>
          <a-button type="primary" @click="addPointsGoods">添加积分商品</a-button>
        </a-space>
      </a-col>
    </a-row>
    <tablePage ref="tablePageRef" :columns="columnsTable" :methods="sortMethods" :api="getPointsGoodsList"
      :api-params="apiParams" @delete="handleDelete" :bordered="true">
      <template #price="{ data }">{{
        data.price
          ? unitPrice(data.price, '¥')
          : (data.couponDiscount || 0) + '折'
      }}</template>

      <template #time="{ data }">{{ data.startTime }}<span style="margin: 0 10px">-</span>{{ data.endTime }}</template>
      <template #action="{ data }">
        <a-button v-if="data.promotionStatus === 'CLOSE' || data.promotionStatus === 'NEW'" type="text" status="warning"
          @click="edit(data)" style="margin-right: 10px;">编辑</a-button>
        <a-button v-if="data.promotionStatus === 'START' || data.promotionStatus === 'NEW'" type="text" status="danger"
          @click="statusChanged(data.id, 'CLOSE')" style="margin-right: 10px;">关闭</a-button>
        <a-button v-if="data.promotionStatus === 'CLOSE' || data.promotionStatus === 'END'" type="text" status="danger"
          @click="handleDelete(data.id)" style="margin-right: 10px;">删除</a-button>
      </template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
import searchTable from '@/components/search-column/index.vue';
import tablePage from '@/components/table-pages/index.vue';
import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
import { getPointsGoodsList, deletePointsGoods, editPointsGoodsStatus } from '@/api/promotion';
import useCurrentInstance from '@/hooks/useCurrentInstance';
import { Message } from '@arco-design/web-vue';
import {
  promotionsStatusRender,
  promotionsScopeTypeRender,
  couponType,
  promotionStatusSelect
} from '@/utils/tools';
import { unitPrice } from '@/utils/filters';
import { useRouter } from 'vue-router';
import { ref } from 'vue';


const router = useRouter()
const tablePageRef = ref<any>();
const integrateStatus = ref<string>('START');
const apiParams = ref<any>({ sort: 'startTime', promotionStatus: integrateStatus.value });
// 获取modal
const modal = useCurrentInstance().globalProperties?.$modal;
const columnsSearch: Array<SearchRule> = [
  {
    label: '商品名称',
    model: 'goodsName',
    disabled: false,
    input: true,
  },
  {
    label: '积分区间',
    model: 'pointsS',
    disabled: false,
    input: true,
  },
  {
    label: 'SKU编码',
    model: 'skuId',
    disabled: false,
    input: true,
  },
];

const columnsTable: ColumnsDataRule[] = [
  {
    title: '商品名称',
    dataIndex: 'goodsName',
    slot: true,
    width: 250,
    slotData: {
      goods: {
        goodsImage: 'thumbnail',
        goodsName: 'goodsName',
      },
    },
  },
  {
    title: '市场价',
    dataIndex: 'originalPrice',
    currency: true,
    width: 100,
  },

  {
    title: '结算价',
    dataIndex: 'settlementPrice',
    currency: true,
    width: 100,
    // slot: true,
    // slotTemplate: 'price',
  },
  {
    title: '库存数量',
    dataIndex: 'activeStock',
    width: 100,

  },
  {
    title: '活动剩余库存',
    dataIndex: 'activeStock',
    width: 150,
  },
  {
    title: '兑换积分',
    dataIndex: 'points',
    width: 100,
  },
  {
    title: '所属店铺',
    dataIndex: 'storeName',
    width: 100,
    // slot: true,
    // slotData: {
    //   badge: couponType,
    // },
  },
  {
    title: '活动时间',
    width: 350,
    ellipsis: false,
    dataIndex: 'startTime',
    slot: true,
    slotTemplate: 'time',
  },
  {
    title: '状态',
    dataIndex: 'promotionStatus',
    width: 100,
    slot: true,
    slotData: {
      badge: promotionsStatusRender,
    },
  },
  {
    title: '分类',
    dataIndex: 'pointsGoodsCategoryName',
  },

];

// todo 本页面操作列表为选择展示
const sortMethods: MethodsRule = {
  title: '操作',
  width: 260,
  methods: [
    {
      title: '操作',
      callback: 'actipn',
      slot: true,
      slotTemplate: 'action'
    },
  ],
};

// 回调删除
function handleDelete(id: any) {
  modal.confirm({
    title: '确认删除',
    content: `您确认要删除此积分商品?`,
    alignCenter: false,
    onOk: async () => {
      const res = await deletePointsGoods(id);
      if (res.data.success) {
        Message.success('删除成功');
        tablePageRef.value.init();
      }
    },
  });
}
// 跳转添加商品页面
const addPointsGoods = () => {
  router.push({ name: 'add-points-goods' })
}
// 编辑
const edit = (val: any) => {
  router.push({
    name: 'edit-points-goods', query: {
      id: val.id
    }
  })
}
// 关闭
const statusChanged = (id: any, status: any) => {
  let text = "";
  if (status == 'START') {
    text = "开启";
  } else {
    text = "关闭";
  }
  modal.confirm({
    title: `确认${text}`,
    content: `您确认要${text}此积分商品?`,
    alignCenter: false,
    onOk: async () => {
      const res = await editPointsGoodsStatus(id);
      if (res.data.success) {
        Message.success(`${text}成功`);
        tablePageRef.value.init();
      }
    },
  });
}
</script>
