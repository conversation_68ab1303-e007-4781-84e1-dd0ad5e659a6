<template>
  <div style="background-color: #fff">
    <a-form ref="formRef" style="padding: 30px" :model="form" layout="horizontal" auto-label-width>
      <a-divider orientation="left">订单设置</a-divider>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item
              field="autoCancel"
              label="订单自动取消"
              :validate-trigger="['change']"
          >
            <a-input-number
                v-model="form.autoCancel"
                placeholder="发起订单后，多少分钟未操作取消订单"
                allow-clear
            >
              <template #append>
                <span>分钟</span>
              </template>
            </a-input-number>
          </a-form-item>

          <a-form-item
              field="autoReceive"
              label="订单自动收货"
              :validate-trigger="['change']"
          >
            <a-input-number
                v-model="form.autoReceive"
                placeholder="发货后多少天自动完成收货"
                allow-clear
            >
              <template #append>
                <span>天</span>
              </template>
            </a-input-number>
          </a-form-item>

          <a-form-item
              field="autoEvaluation"
              label="自动好评"
              :validate-trigger="['change']"
          >
            <a-input-number
                v-model="form.autoEvaluation"
                placeholder="订单发货后，多少天后自动好评"
                allow-clear
            >
              <template #append>
                <span>天</span>
              </template>
            </a-input-number>
          </a-form-item>

          <a-form-item
              field="closeAfterSale"
              label="已完成订单允许退单"
              :validate-trigger="['change']"
          >
            <a-input-number
                v-model="form.closeAfterSale"
                placeholder="订单完成后，多少天内允许退单，如果天数为0,则不允许退单"
                allow-clear
            >
              <template #append>
                <span>天</span>
              </template>
            </a-input-number>
          </a-form-item>

          <a-form-item
              field="closeComplaint"
              label="已完成订单允许投诉"
              :validate-trigger="['change']"
          >
            <a-input-number
                v-model="form.closeComplaint"
                placeholder="订单完成后，多少天内允许投诉，如果天数为0,则不允许投诉"
                allow-clear
            >
              <template #append>
                <span>天</span>
              </template>
            </a-input-number>
          </a-form-item>
          <a-form-item
              field="settleCompleted"
              label="已完成订单发起结算"
              :validate-trigger="['change']"
          >
            <a-input-number
                v-model="form.settleCompleted"
                placeholder="订单完成后，多少天内发起结算，如果天数为0,则当天结算"
                allow-clear
            >
              <template #append>
                <span>天</span>
              </template>
            </a-input-number>
          </a-form-item>
          <a-form-item
              field="offlineAutoCancel"
              label="线下支付自动取消"
              :validate-trigger="['change']"
          >
            <a-input-number
                v-model="form.offlineAutoCancel"
                placeholder="线下支付订单自动取消天数"
                allow-clear
            >
              <template #append>
                <span>天</span>
              </template>
            </a-input-number>
          </a-form-item>

          <a-form-item
              field="offlineBankAccountName"
              label="线下收款-银行账号"
              :validate-trigger="['change']"
          >
            <a-input
                v-model="form.offlineBankAccountName"
                placeholder="线下收款-银行账号"
                allow-clear
            />
          </a-form-item>

          <a-form-item
              field="offlineBankAccountNum"
              label="线下收款-银行卡号"
              :validate-trigger="['change']"
          >
            <a-input-number
                v-model="form.offlineBankAccountNum"
                placeholder="线下收款-银行卡号"
                allow-clear
            />
          </a-form-item>

          <a-form-item
              field="offlineBankBranchName"
              label="线下收款-银行开户支行名称"
              :validate-trigger="['change']"
          >
            <a-input
                v-model="form.offlineBankBranchName"
                placeholder="线下收款-银行开户支行名称"
                allow-clear
            />
          </a-form-item>

          <a-form-item>
            <a-button type="primary" @click="handleSubmit">保存</a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>

<script setup lang="ts">
  import { getSetting, setSetting } from '@/api/operation';
  import { ref, onMounted, reactive } from 'vue';
  import useCurrentInstance from '@/hooks/useCurrentInstance';
  import { VARCHAR255, REQUIRED, VARCHAR20 } from '@/utils/validator';
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';

  const formRef = ref<FormInstance>();
  // 获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;
  interface formInterface {
    autoCancel: number;
    autoReceive: number;
    autoEvaluation: number;
    closeAfterSale: number;
    closeComplaint: number;
    settleCompleted: number;
    offlineAutoCancel: number;
    offlineBankAccountName: string;
    offlineBankAccountNum: number;
    offlineBankBranchName: string;
  }
  // 数据集
  const form = ref<formInterface>({
    autoCancel: 0,
    autoReceive: 0,
    autoEvaluation: 0,
    closeAfterSale: 0,
    closeComplaint: 0,
    settleCompleted: 0,
    offlineAutoCancel: 0,
    offlineBankAccountName: '',
    offlineBankAccountNum: 0,
    offlineBankBranchName: '',
  });
  async function init() {
    const res = await getSetting('ORDER_SETTING');
    form.value = res.data.result;
  }
  const handleSubmit = async () => {
    // console.log(form.value, 'form.value');
    const result = await setSetting('ORDER_SETTING', form.value);
    if (result.data.success) {
      Message.success('设置成功!');
      init();
    }
  };

  onMounted(() => {
    init();
  });
</script>
