<template>
  <div py-30px border-b-1>
    <a-space>
      <div w-90px>{{ props.text }}</div>
      <div>
        <a-radio-group v-model="props.res.data.goodsType">
          <a-radio my-12px v-for="(item, index) in list" :key="index" :value="item.value">{{ item.label }}</a-radio>
        </a-radio-group>

      </div>
    </a-space>
  </div>
</template>


<script setup lang="ts">
import { DragRule } from '@/views/operation/decoration/app/models/types';

const props = defineProps<{
  res: DragRule,
  text: string
}>()
/**
 * goodsType 设置
 * one 一行一个
 * two 一行两个
 * three 一行三个
 * big 大图模式
 * scroll 横向滚动
 * flag 标识设置图片变大
 */
const list = [
  {
    label: "一行一个",
    value: 'one'
  },
  {
    label: "一行两个",
    value: 'two'
  },
  {
    label: "一行三个",
    value: 'three'
  },
  {
    label: "大图模式",
    value: 'big'
  },
  {
    label: "横向滚动",
    value: 'scroll'
  },
  // {
  //   label: "标识图片",
  //   value: 'flag'
  // },
]
</script>

<style scoped>
</style>
