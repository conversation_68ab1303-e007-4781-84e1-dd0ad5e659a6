<template>
  <a-card class="general-card" title="退货管理" :bordered="false">
    <a-tabs @change="(val)=>{apiParams.serviceStatus = val}" :default-active-key="serviceStatus">
      <a-tab-pane key="ALL" title="全部"></a-tab-pane>
      <a-tab-pane key="REFUSE" title="退货待处理（0）"></a-tab-pane>
      <a-tab-pane key="COMPLETED" title="退货待商家收货（0）"></a-tab-pane>
      <a-tab-pane key="CANCELLED" title="已关闭"></a-tab-pane>
    </a-tabs>
    <searchTable
      :columns="columnsSearch"
      time-show
      @reset="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
      @search="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
    ></searchTable>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api-params="apiParams"
      :api="afterSaleOrderPage"
      :bordered="true"
      @detail="detail"
    />
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { afterSaleOrderPage } from '@/api/order';
  import {  afterSaleStatusList } from '@/utils/tools';
  import { ref, reactive } from 'vue';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const tablePageRef = ref('');
  const serviceStatus = ref<string>('ALL')
  const columnsSearch: Array<SearchRule> = [
    {
      label: '商品',
      model: 'goodsName',
      disabled: false,
      input: true,
    },
    {
      label: '会员名称',
      model: 'memberName',
      disabled: false,
      input: true,
    },
    {
      label: '订单编号',
      model: 'orderSn',
      disabled: false,
      input: true,
    },
    {
      label: '申请时间',
      disabled: false,
      datePicker: {
        type: 'range',
      },
    },
  ];

  const apiParams = ref<any>({
    serviceType: 'RETURN_GOODS',
    serviceStatus:serviceStatus.value
  });

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '售后单号',
      dataIndex: 'sn',
    },
    {
      title: '订单号',
      dataIndex: 'orderSn',
    },

    {
      title: '商品名称',
      dataIndex: 'goodsName',
      width: 300,
      slot: true,
      ellipsis: false,
      slotData: {
        goods: {
          goodsImage: 'goodsImage',
          goodsName: 'goodsName',
        },
      },
    },
    {
      title: '申请退款金额',
      dataIndex: 'applyRefundPrice',
      currency: true,
    },
    {
      title: '会员名称',
      dataIndex: 'memberName',
    },
    {
      title: '状态',
      dataIndex: 'serviceStatus',
      slot: true,
      width: 180,
      slotData: {
        badge: afterSaleStatusList,
      },
    },
    {
      title: '申请时间',
      width: 200,
      dataIndex: 'createTime',
    },
  ];

  const sortMethods: MethodsRule = {
    title: '操作',
    width: 100,
    methods: [
      {
        title: '查看',
        callback: 'detail',
        type: 'text',
        status: 'success',
      },
    ],
  };
  // 查看
  const detail = (v: any) => {
    router.push({
      name: 'return-goods-detail',
      query: {
        id: v.record.sn,
      },
    });
  };
</script>
