import { mergeConfig } from 'vite';

import path from 'node:path';
import Components from 'unplugin-vue-components/vite';

import baseConfig from './vite.config.base';
export default mergeConfig(
  {
    mode: 'development',
    define: {
      __VUE_PROD_DEVTOOLS__: true,
    },
    server: {
      port: 10003,
      open: true,
      fs: {
        strict: true,
      },
    },
    plugins: [
      Components(),

    ],
    resolve: {
      alias: {
        '~/': `${path.resolve(__dirname, 'src')}/`,
        '@': path.resolve(__dirname, 'src')
        // '~@': `${path.resolve(__dirname, 'src')}/`,
      },
    },
  },
  baseConfig
);
