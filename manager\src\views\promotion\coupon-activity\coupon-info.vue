<template>
  <div>
    <div class="content-goods-publish">
      <a-form layout="horizontal" auto-label-width :model="couponActivity">
        <div class="base-info-item">
          <h4>优惠券活动详情</h4>
          <div class="form-item-view">
            <a-form-item label="活动名称">
              <span>{{ couponActivity.promotionName }}</span>
            </a-form-item>
            <a-form-item label="活动类型">
              <span v-if="couponActivity.couponActivityType === 'REGISTERED'">新人赠券</span>
              <span v-else-if="couponActivity.couponActivityType === 'SPECIFY'">精确发券</span>
              <span v-else-if="couponActivity.couponActivityType === 'AUTO_COUPON'">自动赠券</span>
              <span v-else-if="couponActivity.couponActivityType === 'INVITE_NEW'">邀新赠券</span>
            </a-form-item>
            <a-form-item
              label="活动范围"
              v-if="couponActivity.couponActivityType === 'SPECIFY'"
            >
              <span v-if="couponActivity.activityScope === 'ALL'"
                >全部会员</span
              >
              <span v-else> 指定会员 </span>
            </a-form-item>
            <a-form-item label="活动时间">
              <span
                >{{ couponActivity.startTime }}～{{
                  couponActivity.endTime
                }}</span
              >
            </a-form-item>
            <a-form-item label="活动状态">
              <span v-if="couponActivity.promotionStatus === 'NEW'"
                >未开始</span
              >
              <span v-if="couponActivity.promotionStatus === 'START'"
                >已开始</span
              >
              <span v-if="couponActivity.promotionStatus === 'END'"
                >已结束</span
              >
              <span v-if="couponActivity.promotionStatus === 'CLOSE'"
                >已关闭</span
              >
            </a-form-item>
          </div>
          <h4>优惠券列表</h4>
          <a-table
            :columns="couponColumn"
            :data="couponData"
            :pagination="false"
          >
          <template #price="{ record }">
              <span v-if="record.couponType == 'DISCOUNT'">
                {{ record.couponDiscount }}折
              </span>
              <span v-else-if="record.couponType == 'PRICE'">
                ¥ {{ record.price }}
              </span>
              <span v-else>
                未知
              </span>
            </template>
            <template #couponType="{ record }">
              <span v-if="record.couponType == 'DISCOUNT'">
                <a-badge color="blue" text="打折"></a-badge>
              </span>
              <span v-else-if="record.couponType == 'PRICE'">
                <a-badge color="purple" text="减免现金"></a-badge>
              </span>
              <span v-else>
                <a-badge color="purple" text="未知"></a-badge>
              </span>
            </template>
          </a-table>
          <template
            v-if="couponActivity.activityScopeInfo && memberData.length > 0"
          >
            <h4 class="mt_10">会员列表</h4>
            <a-table
              :columns="memberColumn"
              :data="memberData"
              :pagination="false"
            />
          </template>
        </div>
      </a-form>
    </div>
    <div class="footer">
      <a-button type="primary" status="danger" @click="back"
        >返回活动列表</a-button
      >
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { getCouponActivity } from '@/api/promotion';
  import { useRoute, useRouter } from 'vue-router';

  const router = useRouter();
  const route = useRoute();
  const couponActivity = ref({
    promotionName: '',
    couponActivityType: '',
    activityScope: '',
    startTime: '',
    endTime: '',
    promotionStatus: '',
    activityScopeInfo: '',
  }); // 券活动
  // 优惠券列表
  const couponColumn = [
    {
      title: '优惠券名称',
      dataIndex: 'couponName',
    },
    {
      title: '优惠券金额',
      dataIndex: 'price',
      slotName:'price'
    },
    {
      title: '优惠券类型',
      dataIndex: 'couponType',
      slotName: 'couponType'
    },
    {
      title: '赠送数量',
      dataIndex: 'num',
    },
  ];
  // 会员列表
  const memberColumn = [
    // {
    //   title: '会员id',
    //   dataIndex: 'id',
    // },
    {
      title: '昵称',
      dataIndex: 'nickName',
    },
  ];
  const couponData = ref([]); // 优惠券列表表格
  const memberData = ref([]); // 会员列表表格

  // 初始化
  const init = () => {
    getCouponActivity(route.query.id).then((res: any) => {
      if (res.data.success) {
        couponActivity.value = res.data.result;
        const { couponActivityItems, activityScopeInfo } = res.data.result;
        couponData.value = couponActivityItems;
        memberData.value = JSON.parse(activityScopeInfo);
      }
    });
  };
  // 返回
  const back = () => {
    router.push({ name: 'coupon-activity' });
  };
  onMounted(() => {
    init();
  });
</script>

<style lang="less" scoped>
  .content-goods-publish {
    padding: 15px;
    margin: 0 auto;
    text-align: center;
    border: 1px solid #ddd;
    background: none repeat 0 0 #fff;
    height: 100%;
    margin-bottom: 20px;
  }

  div.base-info-item {
    h4 {
      margin-bottom: 10px;
      padding: 0 10px;
      border: 1px solid #ddd;
      background-color: #f8f8f8;
      font-weight: bold;
      color: #333;
      font-size: 14px;
      line-height: 40px;
      text-align: left;
    }

    .form-item-view {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: space-between;
      padding-left: 40px;

      .shop-category-text {
        font-size: 12px;
      }
    }
  }

  /** 底部步骤 */
  .footer {
    width: 86.2%;
    padding: 10px;
    background-color: #ffc;
    position: fixed;
    bottom: 0px;
    left: 10%;
    text-align: center;
    z-index: 9999;
    margin-left: 2.6%;
  }
</style>
