// 计算图片长宽比
export function imageScaling(imgWidth: number, imgHeight: number, containerWidth: number, containerHeight: number) {
  let tempWidth = 0, tempHeight = 0;

  if (imgWidth > 0 && imgHeight > 0) {
    //原图片宽高比例 大于 指定的宽高比例，这就说明了原图片的宽度必然 > 高度
    if (imgWidth / imgHeight >= containerWidth / containerHeight) {
      if (imgWidth > containerWidth) {
        tempWidth = containerWidth
        // 按原图片的比例进行缩放
        tempHeight = (imgHeight * containerWidth) / imgWidth
      } else {
        // 按照图片的大小进行缩放
        tempWidth = imgWidth
        tempHeight = imgHeight
      }
    } else {  // 原图片的高度必然 > 宽度
      if (imgHeight > containerHeight) {

        tempHeight = containerHeight
        // 按原图片的比例进行缩放
        tempWidth = (imgWidth * containerHeight) / imgHeight
      } else {
        // 按原图片的大小进行缩放
        tempWidth = imgWidth
        tempHeight = imgHeight
      }
    }
  }

  return { tempWidth, tempHeight }
}

// 获取图片大小
export function getScalImageSize(src: string, containerWidth: number, containerHeight: number): Promise<{ tempHeight: number; tempWidth: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = src;

    img.onload = () => {
      const { width, height } = img;
      try {
        const { tempHeight, tempWidth } = imageScaling(width, height, containerWidth, containerHeight);
        resolve({ tempHeight, tempWidth });
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = (error) => {
      reject(error);
    };
  });
}
