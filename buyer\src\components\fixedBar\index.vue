<template>
  <div class="shadow-box">
    <div class="shadow-item flex flex-a-c flex-j-c" v-for="(item,index) in iconList" @click="handleClickIcon(item)" :key="index">
      <img class="icon" :src="`/src/assets/images/iconfont/${item.icon}.png`">
      <div class="shadow-label">{{item.label}}</div>
      <div class="line" v-if="iconList.length-1 !== index"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const iconList = ref([
    { icon:"user", label:"会员中心", path:"/user/home" },
    { icon:"carts", label:"购物车", path:"/cart" },
    { icon:"notification", label:"消息", path:"/user/home/<USER>/messageList" },
    { icon:"collage", label:"收藏", path:"/user/home/<USER>/myFavorites" },
    { icon:"back", label:"顶部", path:"back" },
  ]);
  // 侧栏bar点击事件
  const handleClickIcon = (val: any) => {
    if(val.path === 'back'){
      scrollToTop();
    }else{
      router.push(val.path);
    }
  };
  // 定义一个方法来处理滚动到顶部的操作
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' }); // 平滑滚动到顶部
  };
  onMounted(() => {

  })
</script>

<style scoped lang="less">
  .shadow-box{
    width: 64px;
    height: 354px;
    border-radius: 10px;
    opacity: 1;

    background: #FFFFFF;

    box-shadow: 0px 1px 10px 0px rgba(154, 154, 154, 0.5);
  }
  .shadow-label{
    font-size: 13px;
    font-weight: normal;
    line-height: 16px;
    text-align: center;
    letter-spacing: 0px;
    color: #666666;
  }
  .icon{
    margin-top: 15px;
    width: 20px;
    height: 20px;
    margin-bottom: 5px;
  }
  .shadow-item{
    cursor: pointer;
    flex-direction: column;
    height: 70px;
    >.line{
      width: 34px;
      height: 1px;
      opacity: 1;
      margin-top: 17px;
      border: 0.7px solid #EAEAEA;
    }
  }
</style>
