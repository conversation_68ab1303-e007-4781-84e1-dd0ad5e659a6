<template>
  <div>
    <a-form ref="firstFormRef" :model="firstForm" @submit="next" auto-label-width layout="horizontal">
      <h4>基础信息</h4>
      <div :style="{ width: '500px', paddingLeft: '50px'}" >
        <a-form-item field="companyName" label="公司名称" :rules="[REQUIRED, VARCHAR2TO100]">
          <a-input v-model="firstForm.companyName" placeholder="请填写公司名称"></a-input>
        </a-form-item>
        <a-form-item field="companyAddressIdPath" label="公司所在地" :rules="[REQUIRED]">
          <span>{{ firstForm.companyAddressPath || '暂无地址' }}</span>
          <a-button @click="openAddress()" size="mini" type="outline" status="danger" class="ml_10">选择</a-button>
        </a-form-item>
        <a-form-item field="companyAddress" label="公司详细地址" :rules="[REQUIRED]">
          <a-input v-model="firstForm.companyAddress" placeholder="请填写公司详细地址"></a-input>
        </a-form-item>
        <a-form-item field="employeeNum" label="员工总数" :rules="[REQUIRED, INTEGER]">
          <a-input v-model="firstForm.employeeNum" placeholder="请填写员工总数"><template #append>人</template></a-input>
        </a-form-item>
        <a-form-item field="companyPhone" label="公司电话" :rules="[REQUIRED, MOBILE]">
          <a-input v-model="firstForm.companyPhone" placeholder="请填写公司电话"></a-input>
        </a-form-item>
        <a-form-item field="registeredCapital" label="注册资金" :rules="[REQUIRED, INTEGER]">
          <a-input v-model="firstForm.registeredCapital" placeholder="请填写注册资金"><template #append>万元</template></a-input>
        </a-form-item>
        <a-form-item field="linkName" label="联系人姓名" :rules="[REQUIRED, VARCHAR20]">
          <a-input v-model="firstForm.linkName" placeholder="请填写联系人姓名"></a-input>
        </a-form-item>
        <a-form-item field="linkPhone" label="联系人电话" :rules="[REQUIRED, MOBILE]">
          <a-input v-model="firstForm.linkPhone" placeholder="请填写联系人电话"></a-input>
        </a-form-item>
        <a-form-item field="companyEmail" label="电子邮箱" :rules="[REQUIRED, EMAIL]">
          <a-input v-model="firstForm.companyEmail" placeholder="请填写电子邮箱"></a-input>
        </a-form-item>
      </div>
      <h4>营业执照信息</h4>
      <div :style="{ width: '500px', paddingLeft: '50px'}" >
        <a-form-item field="licenseNum" label="营业执照号" :rules="[REQUIRED, LICENSENUM]">
          <a-input v-model="firstForm.licenseNum" placeholder="请填写营业执照号"></a-input>
        </a-form-item>
        <a-form-item field="scope" label="法定经营范围" :rules="[REQUIRED]">
          <a-textarea v-model="firstForm.scope" :max-length="200" allow-clear show-word-limit :auto-size="{minRows:4,maxRows:6}" placeholder="请输入营业执照所示经营范围"></a-textarea>
        </a-form-item>
        {{ uploadFile }}==uploadFile
        <a-form-item field="licencePhoto" label="营业执照电子版" :rules="[REQUIRED]">
          <div>
            <a-upload list-type="picture-card" :action="uploadFile" :headers="{ accessToken: accessToken }" image-preview :limit="2"
                      :file-list="firstForm.licencePhoto" @success="licencePhotoSuccess" @before-remove="licencePhotoRemove" @before-upload="beforeUpload"></a-upload>
            <div class="describe">请压缩图片在2M以内，格式为gif，jpg，png，并确保文字清晰，以免上传或审核失败</div>
          </div>
        </a-form-item>
      </div>
      <h4>法人信息</h4>
      <div :style="{ width: '500px', paddingLeft: '50px'}" >
        <a-form-item field="legalName" label="法人姓名" :rules="[REQUIRED, VARCHAR20]">
          <a-input v-model="firstForm.legalName" placeholder="请填写法人姓名"></a-input>
        </a-form-item>
        <a-form-item field="legalId" label="法人证件号" :rules="[REQUIRED, IDCARD]">
          <a-input v-model="firstForm.legalId" placeholder="请填写法人证件号"></a-input>
        </a-form-item>
        <a-form-item field="legalPhoto" label="法人证件电子版" :rules="[REQUIRED]">
          <div>
            <a-upload list-type="picture-card" :action="uploadFile" :headers="{ accessToken: accessToken }" image-preview :limit="2"
                      :file-list="firstForm.legalPhoto" @success="legalPhotoSuccess" @before-remove="legalPhotoRemove" @before-upload="beforeUpload"></a-upload>
            <div class="describe">请压缩图片在2M以内，身份证正反面两张照片，确保图片清晰无缺角</div>
          </div>
        </a-form-item>
        <a-form-item>
          <a-button html-type="submit" status="danger" type="primary" :loading="loading">填写财务资质信息</a-button>
        </a-form-item>
      </div>
    </a-form>
    <MultipleMap ref="multipleMap" @callback="addressCallback"></MultipleMap>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { REQUIRED, MOBILE, INTEGER, EMAIL, LICENSENUM, IDCARD, VARCHAR20, VARCHAR2TO100 } from '@/utils/validator';
  import uploadFile from '@/api/common';
  import { accessToken, beforeUpload } from '@/utils/upload';
  import { applyFirst } from '@/api/shopentry';

  const props = defineProps({
    content: {
      content: Object,
      default: () => {return {licencePhoto: '', legalPhoto: ''}}
    },
  });
  const emit = defineEmits(['handleChange']);

  const multipleMap = ref();
  const loading = ref(false); // 加载状态
  const firstFormRef = ref();
  const firstForm = ref<any>({
    licencePhoto: [], // 营业执照电子版
    legalPhoto: [], // 法人证件电子版
  }); // 表单数据

  // 选择公司所在地
  const openAddress = () => {
    if (multipleMap.value) {
      multipleMap.value.open();
    }
  };
  // 选择地址回调
  const addressCallback = (val: any) => {
    if(val.type === 'select'){
      const paths = val.data.map((item: any) => item.name).join(',');
      const ids = val.data.map((item: any) => item.id).join(',');
      firstForm.value.companyAddressIdPath = ids;
      firstForm.value.companyAddressPath = paths;
    }
  };

  // 上传图片--营业执照电子版
  const licencePhotoSuccess = (res: any, file: any) => {
    firstForm.value.licencePhoto.push({url: res.response.result});
  };
  // 上传图片----营业执照电子版
  const licencePhotoRemove = (file: any) => {
    firstForm.value.licencePhoto = firstForm.value.licencePhoto.filter((i: any) => i.url !== file.url);
  };
  // 上传成功--法人证件电子版
  const legalPhotoSuccess = (res: any) => {
    firstForm.value.legalPhoto.push({url: res.response.result});
  };
  // 删除图片--法人证件电子版
  const legalPhotoRemove = (file: any) => {
    firstForm.value.legalPhoto = firstForm.value.legalPhoto.filter((i: any) => i.url !== file.url);
  };

  // 下一步--填写财务资质信息
  const next = async () => {
    const auth = await firstFormRef.value?.validate();
    if (!auth) {
      if(firstForm.value.legalPhoto.length < 2){
        Message.warning('请上传法人身份证正反面');
        return;
      }
      loading.value = true;
      let params = JSON.parse(JSON.stringify(firstForm.value));
      params.legalPhoto = firstForm.value.legalPhoto.map((item: any) => item.url).toString();
      params.licencePhoto = firstForm.value.licencePhoto.map((item: any) => item.url).toString();
      applyFirst(params).then((res) => {
        loading.value = false;
        if (res.data.success) emit('handleChange', 2);
      }).catch(() => {loading.value = false;});
    }
  };
  onMounted(() => {
    // 处理回显数据
    if (props.content && Object.keys(props.content).length) {
      firstForm.value = JSON.parse(JSON.stringify(props.content));
      // 营业执照电子版
      if (Array.isArray(props.content.licencePhoto.split(','))) {
        let params = props.content.licencePhoto.split(',');
        firstForm.value.licencePhoto = params.map((item: any) => {return {url: item}});
      } else {
        firstForm.value.licencePhoto = [];
      }
      // 法人证件电子版
      if (Array.isArray(props.content.legalPhoto.split(','))) {
        let params = props.content.legalPhoto.split(',');
        firstForm.value.legalPhoto = params.map((item: any) => {return {url: item}});
      } else {
        firstForm.value.legalPhoto = [];
      }
    }
  });

</script>

<style scoped lang="less">
  h4 {
    margin-bottom: 10px;
    padding: 0 10px;
    border: 1px solid #ddd;
    background-color: #f8f8f8;
    font-weight: bold;
    color: #333;
    font-size: 14px;
    line-height: 40px;
    text-align: left;
  }
  .describe {
    font-size: 12px;
    color: #999;
    margin: 8px 0;
  }
</style>
