import request, { commonUrl, Method } from '@/utils/axios';
import { ParamsRule } from '@/types/global';
/**
 * 通过id获取子地区
 */
export function getChildRegion(id: string | number) {
  return request({
    url: `${commonUrl}/common/common/region/item/${id}`,
    method: Method.GET,
    needToken: true,
  });
}

/**
 * 获取所有地址信息
 */
export const getAllRegion = () => {
  return request({
    url: `${commonUrl}/common/common/region/allCity`,
    method: Method.GET,
    needToken: true,
  });
};

/**
 * 点地图获取地址信息
 */
export const getRegion = (params: any) => {
  return request({
    url: `${commonUrl}/common/common/region/region`,
    method: Method.GET,
    needToken: true,
    params,
  });
};

/**
 * 获取IM接口前缀
 */
export const getIMDetail = () => {
  return request({
    url: `${commonUrl}/common/common/IM`,
    method: Method.GET,
    needToken: true,
  });
};

/**
 * 分页获取文件数据
 */
export const getFileListData = (params: any) => {
  return request({
    url: `${commonUrl}/common/common/file`,
    method: Method.GET,
    needToken: true,
    params,
  });
};

// 删除文件
export const deleteFile = (id: number | string) => {
  return request({
    url: `${commonUrl}/common/common/file/delete/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
};

// 获取文件目录列表
export function getFileDirectory() {
  return request({
    url: `${commonUrl}/common/resource/fileDirectory`,
    method: Method.GET,
    needToken: true,
  });
}

// 重命名文件
export function renameFile(params: any) {
  return request({
    url: `${commonUrl}/common/file/rename`,
    method: Method.POST,
    needToken: true,
    params,
  });
}

// 添加文件目录
export function addFileDirectory(params: ParamsRule) {
  return request({
    url: `${commonUrl}/common/resource/fileDirectory`,
    method: Method.POST,
    needToken: true,
    data: params,
  });
}

// 修改文件目录
export function updateFileDirectory(params: ParamsRule) {
  return request({
    url: `${commonUrl}/common/resource/fileDirectory`,
    method: Method.PUT,
    needToken: true,
    data: params,
  });
}

// 删除文件目录
export function delFileDirectory(id: number | string) {
  return request({
    url: `${commonUrl}/common/resource/fileDirectory/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}

// 获取图片logo
export function getBaseSite() {
  return request({
    url: `${commonUrl}/common/common/site`,
    method: Method.GET,
    needToken: false,
  });
}

const uploadFile = `${commonUrl}/common/common/upload/file`;
export default uploadFile;
