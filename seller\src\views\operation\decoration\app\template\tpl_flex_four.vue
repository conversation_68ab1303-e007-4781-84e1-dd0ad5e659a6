<template>
  <div>
    <div flex flex-a-c>
      <div>
        <img h-91px w-91px :src="props.res.data.list[0].img" />
      </div>
      <div>
        <img h-91px w-91px :src="props.res.data.list[1].img" />
      </div>
      <div>
        <img h-91px w-91px :src="props.res.data.list[2].img" />
      </div>
      <div>
        <img h-91px w-91px :src="props.res.data.list[3].img" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  res: any
}>()
</script>

<style scoped>

img{
  display: block;
}
</style>
