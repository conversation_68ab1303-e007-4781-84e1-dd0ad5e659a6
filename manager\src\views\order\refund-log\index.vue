<template>
  <a-card class="general-card" title="退款流水" :bordered="false">
    <a-tabs @change="(val)=>{apiParams.refundStatus = val}" :default-active-key="refundStatusVar">
      <a-tab-pane key="REFUND" title="已退款"></a-tab-pane>
      <a-tab-pane key="UNREFUND" title="待退款"></a-tab-pane>
      <a-tab-pane key="ACCOUNT" title="已对账"></a-tab-pane>
      <a-tab-pane key="ACCOUNT_ERROR" title="对账失败"></a-tab-pane>
    </a-tabs>
    <!-- 搜索 -->
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <a-button style="margin-bottom: 15px" type="primary" @click="reconciliation">
      对账
    </a-button>
    <!-- 表格 -->
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :api="refundLog"
      :api-params="apiParams"
      :bordered="true"
    />
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, ColumnsDataRule } from '@/types/global';
  import { refundLog, refundReconciliation} from '@/api/order';
  import {
    refundStatus,
    refundStatusSearch
  } from '@/utils/tools';
  import { ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import useCurrentInstance from '@/hooks/useCurrentInstance';

  const refundStatusVar = ref<string>('REFUND');
  const apiParams = ref<any>({refundStatus:refundStatusVar.value});
  const tablePageRef = ref<any>();
  // 获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;
  // 查询海选列表
  const columnsSearch: Array<SearchRule> = [
    {
      label: '订单编号',
      model: 'orderSn',
      disabled: false,
      input: true,
    },
    {
      label: '退款时间',
      disabled: false,
      datePicker: {
        type: 'range',
      },
    },
  ];

  // 表格搜索列表
  const columnsTable: ColumnsDataRule[] = [
    {
      title: '售后单号',
      dataIndex: 'afterSaleNo',
    },
    
    {
      title: '订单号',
      dataIndex: 'orderSn',
    },
   
    {
      title: '第三方付款流水',
      dataIndex: 'paymentReceivableNo',
    },
    {
      title: '第三方退款流水',
      dataIndex: 'receivableNo',
    },
    {
      title: '退款金额',
      dataIndex: 'totalAmount',
      currency: true,
    },
    
    {
      title: '申请时间',
      width: 200,
      dataIndex: 'createTime',
    },
    {
      title: '退款状态',
      width: 120,
      dataIndex: 'refundStatus',
      slot: true,
      slotData: {
        badge: refundStatus,
      },
    },
  ];

    // 对账
  function reconciliation() {
    modal.confirm({
      title: '提示',
      content: '您确定要退款单对账？此操作需异步进行，等待约一分钟刷新列表查看',
      alignCenter: false,
      onOk: async () => {
        const res = await refundReconciliation();
        if (res.data.success) {
          Message.success('对账成功!');
          tablePageRef.value.init();
        }
      },
    });
  }
</script>

<style scoped></style>
