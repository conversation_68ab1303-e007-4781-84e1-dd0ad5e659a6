<template>
  <div>
    <a-card :style="{ width: '100%' }">
      <a-form ref="formRef" :model="form" :style="{ width: '100%' }">
        <div class="base-info-item">
          <h4>积分商品信息</h4>
          <a-form-item label="商品名称">
            <div>{{ form.goodsSku.goodsName }}</div>
          </a-form-item>
          <a-form-item label="SKU编码">
            <div>{{ form.goodsSku.id }}</div>
          </a-form-item>
          <a-form-item label="店铺名称">
            <div>{{ form.goodsSku.storeName }}</div>
          </a-form-item>
          <a-form-item label="商品价格">
            <div>{{ unitPrice(form.goodsSku.price, '￥') }}</div>
          </a-form-item>
          <a-form-item label="库存">
            <div>{{ form.goodsSku.quantity }}</div>
          </a-form-item>
          <a-form-item field="settlementPrice" label="结算价格" :rules="[REQUIRED]">
            <a-input-number v-model="form.settlementPrice" :style="{ width: '280px' }" placeholder="请填写结算价格"
              class="input-demo" allow-clear />
          </a-form-item>
          <a-form-item field="pointsGoodsCategoryId" label="分类" :rules="[REQUIRED]">
            <a-select :style="{ width: '280px' }" v-model="form.pointsGoodsCategoryId">
              <a-option v-for="item in categoryList" :key="item.id" :value="item.id"
                @click="changeCategory(item.id, item.name)">{{ item.name }}</a-option>
            </a-select>
          </a-form-item>
          <a-form-item field="activeStock" label="活动库存" :rules="[REQUIRED]">
            <a-input-number v-model="form.activeStock" :style="{ width: '280px' }" placeholder="请填写活动库存"
              class="input-demo" allow-clear />
          </a-form-item>
          <a-form-item field="points" label="兑换积分" :rules="[REQUIRED]">
            <a-input-number v-model="form.points" :style="{ width: '280px' }" placeholder="请填写兑换积分" class="input-demo"
              allow-clear />
          </a-form-item>
          <a-form-item field="points" label="活动开始时间" :rules="[REQUIRED]">
            <a-range-picker v-model="form.rangeTime" style="width: 254px; margin-bottom: 20px;" />
          </a-form-item>
          <div class="footerButton">
            <a-button style="margin-right: 5px" @click="closeCurrentPage">返回</a-button>
            <a-button type="primary"  :loading="submitLoading" @click="handleSubmit">保存</a-button>
          </div>
        </div>
      </a-form>
    </a-card>
  </div>
</template>

<script setup lang='ts'>
import { ref, reactive, onMounted } from "vue"
import { useRoute } from 'vue-router';
import { REQUIRED } from '@/utils/validator';
import { updatePointsGoods, getPointsGoodsById, getPointsGoodsCategoryList } from '@/api/promotion';
import { FormInstance } from '@arco-design/web-vue/es/form';
import { unitPrice } from '@/utils/filters';
import { Message } from '@arco-design/web-vue';
import { useRouter } from 'vue-router';
import { dayFormatHHssMM } from '@/utils/filters';

const formRef = ref<FormInstance>() as any;
const router = useRouter()
const submitLoading = ref<boolean>(false)
const categoryList = ref<Array<any>>([]) // 分类列表
const route = useRoute()
const form = ref<any>({
  /** 活动名称 */
  promotionName: "",
  /** 报名截止时间 */
  applyEndTime: "",
  /** 活动开始时间 */
  startTime: "",
  /** 抢购时间段 */
  seckillPeriod: [],
  /** 申请规则 */
  seckillRule: "",
  endTime: '',
  goodsSku: {
    goodsName: '',
    id: '',
    storeName: '',
    price: 0,
    quantity: ''
  },
  promotionStatus: "NEW",
  settlementPrice: 0,
  pointsGoodsCategoryId: '',
  pointsGoodsCategoryName: '',
  activeStock: 0,
  points: 0,
  rangeTime: [],

})
// 分类
const changeCategory = (id: string, label: string) => {
  form.value.pointsGoodsCategoryId = id
  form.value.pointsGoodsCategoryName = label
}
const getData = async () => {
  const res = await getPointsGoodsById(route.query.id)
  if (res.data.success) {
    const data = res.data.result
    form.value = data
    data.rangeTime = []
    if (data.startTime && data.endTime) {
      data.rangeTime.push(data.startTime.substring(0, 10), data.endTime.substring(0, 10));
    }
  }
}
// 获取分类
const getCategory = async () => {
  const res = await getPointsGoodsCategoryList()
  if (res.data.success) {
    categoryList.value = res.data.result.records
  }
}
onMounted(() => {
  getCategory()
  if (route.query.id) {
    getData()
  }
})
// 提交
const handleSubmit = async () => {
  const auth = await formRef.value?.validate();
  if (!auth) {
    const [startTime, endTime] = form.value.rangeTime
    form.value.startTime = dayFormatHHssMM(startTime)
    form.value.endTime = dayFormatHHssMM(endTime)
    submitLoading.value = true
    updatePointsGoods(form.value).then((res: any) => {
      submitLoading.value = false
      if (res.data.success) {
        submitLoading.value = false
        Message.success('积分商品修改成功');
        router.push({ name: 'integrate-goods' })
      }
    })
  }
}
// 返回
const closeCurrentPage = () => {
  router.push({ name: 'integrate-goods' })
}
</script>

<style lang="less" scoped>
.footerButton {
  display: flex;
  justify-content: center;
  margin-right: 20%;
}

.el-form {
  padding-bottom: 80px;

  .el-form-item {
    width: 100%;
    color: gray;
    text-align: left;
  }
}

/*平铺*/
div.base-info-item>div {
  margin-left: 5%;
}

div.base-info-item {
  margin-bottom: 10px;

  h4 {
    margin-bottom: 10px;
    padding: 0 10px;
    border: 1px solid #ddd;
    background-color: #f8f8f8;
    font-weight: bold;
    color: #333;
    font-size: 14px;
    line-height: 40px;
    text-align: left;
  }

  .form-item-view {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
    padding-left: 80px;

    .shop-category-text {
      font-size: 12px;
    }
  }

  .item-goods-properts-row {
    display: flex;
    flex-direction: row;
    word-break: break-all;
    white-space: normal;
    width: 300px;
    height: 100px;
  }

  .item-goods-properts {
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
  }

  .form-item {
    display: flex;
    align-items: center;
  }

  /** 审核信息-拒绝原因 */
  .auth-info {
    color: red;
  }

  .el-form-item {
    width: 30%;
    min-width: 300px;
  }

  .goods-name-width {
    width: 50%;
    min-width: 300px;
  }

  .el-form-item__content {
    margin-left: 120px;
    text-align: left;
  }

  p.goods-group-manager {
    padding-left: 7.5%;
    text-align: left;
    color: #999;
    font-size: 13px;
  }

  /*teatarea*/
  :deep(.el-textarea) {
    width: 150%;
  }

  .seo-text {
    width: 150%;
  }
}

/*商品描述*/
.goods-intro {
  line-height: 40;
}

/** 底部步骤 */
.footer {
  width: 85%;
  padding: 10px;
  background-color: #ffc;
  position: fixed;
  bottom: 0px;
  right: 0;
  text-align: center;
  z-index: 9999;
}

/*图片上传组件第一张图设置封面*/
.goods-images {
  :deep(li.el-upload-list__item:first-child) {
    position: relative;
  }

  :deep(li.el-upload-list__item:first-child:after) {
    content: "封";
    color: #fff;
    font-weight: bold;
    font-size: 12px;
    position: absolute;
    left: -15px;
    top: -6px;
    width: 40px;
    height: 24px;
    padding-top: 6px;
    background: #13ce66;
    text-align: center;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    -webkit-box-shadow: 0 0 1pc 1px rgba(0, 0, 0, 0.2);
    box-shadow: 0 0 1pc 1px rgba(0, 0, 0, 0.2);
  }
}

.el-form-item__label {
  word-break: break-all;
}

.step-view {
  width: 33%;
  height: 40px;
  font-size: 19px;
  text-align: center;
  display: flex;
  background-color: #fff;
  justify-content: center;
  align-items: center;
}

.page {
  margin-top: 2vh;
  margin-bottom: 5vh;
}
</style>
