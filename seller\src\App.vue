<template>
  <div ref="el">
    <a-config-provider>
      <DndProvider :backend="HTML5Backend">
        <router-view v-if="isRouterActive" />
        <global-setting />
      </DndProvider>
    </a-config-provider>
  </div>
</template>

<script lang="ts" setup>
  import GlobalSetting from '@/components/global-setting/index.vue';
  import { HTML5Backend } from 'react-dnd-html5-backend';
  import { DndProvider } from 'vue3-dnd';
  import { useResizeObserver } from '@vueuse/core';
  import { useAppStore } from '@/store';
  import { ref, provide, nextTick } from 'vue';
  const el = ref(null);
  const appStore = useAppStore();

  // 获取当前窗口的宽高
  // @ts-ignore
  useResizeObserver(el, (entries) => {
    const entry = entries[0];
    const { width, height } = entry.contentRect;
    appStore.updateWindowSizeObserver({ width, height });
  });

  // 刷新页面
  const isRouterActive = ref(true);
  provide('reload', () => {
    isRouterActive.value = false;
    nextTick(() => {
      isRouterActive.value = true;
    });
  });
</script>
