/* eslint-disable no-useless-escape */
/* eslint-disable no-lonely-if */

/**
 * 对于arco form的rules验证
 * ----------------------------------------------------------------
 */

// 手机号(mobile phone)中国(最宽松), 只要是1开头即可, 如果你的手机号是用来接收短信, 优先建议选择这一条"
export const MOBILE = {
  match: /^(?:(?:\+|00)86)?1\d{10}$/,
  message: '请输入正确电话号码',
};

// 正整数
export const INTEGER = {
  match: /^[0-9]\d{0,10}|0$/,
  message: '请输入正整数',
};
// 正整数
export const NUMBER = {
  match: /^(\-|\+)?\d{0,10}$/,
  message: '请输入数字',
};
export const VARCHAR5 = {
  match: /^.{1,5}$/,
  message: '长度应该限制在1-5个字符',
};

export const VARCHAR20 = {
  match: /^.{1,20}$/,
  message: '长度应该限制在1-20个字符',
};

export const VARCHAR255 = {
  match: /^.{1,255}$/,
  message: '超出最大长度限制',
};

export const VARCHAR2TO20 = {
  match: /^.{2,20}$/,
  message: '长度应该限制在2-20个字符',
};

export const VARCHAR2TO200 = {
  match: /^.{2,200}$/,
  message: '长度应该限制在2-200个字符',
};

export const VARCHAR6TO200 = {
  match: /^.{6,200}$/,
  message: '长度应该限制在6-200个字符',
};

export const URL200 = {
  match: /[a-zA-z]+\:\/\/[^\s]{1,190}/,
  message: '请输入长度不超过200的URL地址',
};
export const REQUIRED = {
  required: true,
  message: '请填写参数',
};

// 营业执照号
export const LICENSENUM = {
  match: /(^(?:(?![IOZSV])[\dA-Z]){2}\d{6}(?:(?![IOZSV])[\dA-Z]){10}$)|(^\d{15}$)/,
  message: '请输入正确的营业执照号'
};

// 身份证
export const IDCARD = {
  match: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
  message: '请输入正确的身份证号'
};

// 银行卡号
export const BANKCARD = {
  match: /^\d{16,19}$/,
  message: '请输入正确的银行卡号'
};

// 手机号或座机固话
export const TELEPHONE = {
  match: /^((0\d{2,3}-\d{7,8})|(1[3584]\d{9}))$/,
  message: '请填写正确的号码'
};

// 电子邮箱
export const EMAIL = {
  match: /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,
  message: '请填写正确的电子邮箱'
}

/** ---------------------------------------------------------------- */
