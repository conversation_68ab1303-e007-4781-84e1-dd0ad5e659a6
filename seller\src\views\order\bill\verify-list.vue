<template>
  <a-card class="general-card" title="财务对账" :bordered="false">
    <searchTable :columns="columnsSearch" @reset="(val) => {
    apiParams = { ...apiParams, ...val };
  }
    " @search="(val) => {
    apiParams = { ...apiParams, ...val };
  }
    "></searchTable>
    <tablePage ref="tablePageRef" :columns="columnsTable" :methods="sortMethods" :api-params="apiParams"
      :api="getBillPage" :bordered="true" @detail="(val:any) => $openWindow({
    name: 'store-bill-detail',
    query: {
      id: val.record.id
    }
  })">
      <template #range="{ data }">

        <span>{{ data.startTime + "~" + data.endTime }}</span>

      </template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
import searchTable from '@/components/search-column/index.vue';
import tablePage from '@/components/table-pages/index.vue';
import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
import { getBillPage } from '@/api/finance';
import { billStatus } from '@/utils/tools';
import { ref, } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const tablePageRef = ref('');
const columnsSearch: Array<SearchRule> = [
  {
    label: '结算时间',
    disabled: false,
    datePicker: {
      type: 'range',
    },
  },
];

const apiParams = ref({
  billStatus: 'OUT',
});

const columnsTable: ColumnsDataRule[] = [
  {
    title: '账单号',
    dataIndex: 'sn',
    width: 250,
  },
  {
    title: '生成时间',
    dataIndex: 'createTime',
    width: 150
  },

  {
    title: '结算时间段',
    dataIndex: 'startTime',
    width: 300,
    slot: true,
    ellipsis: false,
    slotTemplate: 'range'
  },
  {
    title: '结算金额',
    dataIndex: 'billPrice',
    currency: true,

  },

  {
    title: '状态',
    dataIndex: 'billStatus',
    slot: true,
    width: 180,
    slotData: {
      badge: billStatus,
    },
  },
];

const sortMethods: MethodsRule = {
  title: '操作',
  width: 100,
  methods: [
    {
      title: '查看',
      callback: 'detail',
      type: 'text',
      status: 'success',
    },
  ],
};

</script>
