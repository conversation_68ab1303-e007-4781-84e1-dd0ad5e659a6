<template>
  <a-card title="热卖店铺TOP10">
    <!-- 表格 -->
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :api="hotShops"
      :custom-api-params="goodsData.params"
      :enable-pagination="false"
      :max="10"
      :bordered="true"
    >
      <template #index="{ rowIndex }">
        {{ rowIndex + 1 }}
      </template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
  import tablePage from '@/components/table-pages/index.vue';
  import { ColumnsDataRule, PreViewParamsRule } from '@/types/global';
  import { hotShops } from '@/api/statisitics';
  import { reactive, ref } from 'vue';

  const tablePageRef = ref('');

  interface GoodsDataRule {
    params: PreViewParamsRule;
  }
  const goodsData = reactive<GoodsDataRule>({
    params: {
      searchType: 'LAST_SEVEN', // TODAY ,  YESTERDAY , LAST_SEVEN , LAST_THIRTY
      type: 'NUM',
    },
  });

  // 表格搜索列表
  const columnsTable: ColumnsDataRule[] = [
    {
      title: '排名',
      dataIndex: 'index',
      slot: true,
      slotTemplate: 'index',
    },
    {
      title: '店铺名称',
      dataIndex: 'storeName',
    },
    {
      title: '销售数量',
      dataIndex: 'num',
    },
    {
      title: '价格',
      dataIndex: 'price',
      currency: true,
    },
  ];
</script>

<style scoped></style>
