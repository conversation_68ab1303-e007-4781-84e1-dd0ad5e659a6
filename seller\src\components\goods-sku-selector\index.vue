<template>
  <a-modal
    v-model:visible="modalData.visible"
    :align-center="false"
    title-align="start"
    :width="800"
    draggable
    @ok="confirm"
    @cancel="close"
  >
    <template #title> 商品选择器 </template>
    <div class="modal-wrapper">
      <searchTable
        :row-span="12"
        :columns="columnsSearch"
        @reset="reset"
        @search="
          (val) => {
            modalData.apiParams = { ...modalData.apiParams, ...val };
          }
        "
        @category-list="changeCategory"
      >
      </searchTable>
      <a-radio-group
        v-model:model-value="modalData.queryType"
        style="margin-bottom: 10px"
        type="button"
      >
        <a-radio value="goodsList">商品库</a-radio>
        <!--<a-badge :count="modalData.goodsSelectedList.length">-->
        <!--<a-radio value="selectedGoods">已选择</a-radio>-->
        <!--</a-badge>-->
      </a-radio-group>
      <span style="margin-left: 20px"
        >已选择({{ modalData.goodsSelectedList.length }})</span
      >
      <tablePage
        v-if="modalData.visible"
        ref="tablePageRef"
        :checkbox="true"
        :default-selected-keys="modalData.goodsSelectedList"
        :columns="columnsTable"
        :api-params="modalData.apiParams"
        :api="
          props.goodsOrSku == true ? getGoodsSkuData : getGoodsListDataSeller
        "
        @select-table-change="selectTableChange"
      />
      <!--<tablePage v-if="modalData.visible" ref="tablePageRef" :checkbox="true" :columns="columnsTable" -->
      <!--:api-params="modalData.apiParams" :api="props.goodsOrSku == true ? getGoodsSkuData : getGoodsListDataSeller" -->
      <!--@select-table-change="selectTableChange" />-->
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { getGoodsListDataSeller, getGoodsSkuData } from '@/api/goods';
  import {
    reactive,
    ref,
    watch,
    onMounted,
  } from 'vue';
  import { ColumnsDataRule, SearchRule } from '@/types/global';
  import { goodsType, salesModel } from '@/utils/tools';

  interface ModalRule {
    visible: boolean;
    categoryPath: string | number;
    queryType: string;
    goodsSelectedList: any[];
    apiParams: {
      pageSize: number;
      categoryPath: string | number;
      [key: string]: unknown;
    };
    selectedKeys: any[];
  }
  const tablePageRef = ref();
  const props = defineProps({
    apiParams: {
      type: null,
      default: {},
    },
    goodsOrSku: {
      type: null,
      default: '',
    },
    defaultGoodsSelectedList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  });

  const columnsSearch: Array<SearchRule> = [
    {
      label: '商品名称',
      model: 'goodsName',
      disabled: false,
      input: true,
    },
    {
      label: '商品分类',
      model: 'goodsName',
      disabled: false,
      category: true,
    },
  ];

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '商品',
      dataIndex: 'goods',
      width: 300,
      slot: true,
      slotData: {
        goods: {
          goodsImage: 'thumbnail',
          goodsName: 'goodsName',
        },
      },
    },
    {
      title: '价格',
      dataIndex: 'price',
      currency: true,
    },

    {
      title: '销售模式',
      dataIndex: 'salesModel',
      slot: true,
      slotData: {
        badge: salesModel,
      },
    },
    {
      title: '商品类型',
      dataIndex: 'goodsType',
      slot: true,
      slotData: {
        badge: goodsType,
      },
    },

    {
      title: '库存',
      dataIndex: 'quantity',
    },
  ];

  const modalData: ModalRule = reactive({
    visible: false, // 是否可见
    categoryPath: '',
    queryType: 'goodsList',
    goodsSelectedList: [], // 选择的商品列表
    apiParams: { pageSize: 6, categoryPath: '', ...props.apiParams },
    selectedKeys: [],
  });
  const emit = defineEmits<{
    (e: 'change', val: any): void;
  }>();
  const selectTableChange = (goodsList: any) => {
    modalData.goodsSelectedList = goodsList;
  };

  // 点击分类回调内容
  const changeCategory: any = (id: string | number) => {
    modalData.apiParams.categoryPath = id;
  };

  // 重制搜索条件
  const reset = (val: any) => {
    changeCategory('');
    // tablePageRef.value.init(val);
    modalData.apiParams = { ...modalData.apiParams, ...val };
  };

  // 初始化内容
  const init = () => {
    modalData.visible = true;
  };

  // 确认
  const confirm = () => {
    emit('change', modalData.goodsSelectedList);
  };

  // 取消
  const close = () => {
    modalData.visible = false;
  };

  watch(
    () => props.defaultGoodsSelectedList,
    (newValue, oldValue) => {
      if (newValue) {
        modalData.goodsSelectedList = newValue;
        modalData.selectedKeys = newValue.map((item: any) => {
          return item.skuId || item.id;
        });
      } else {
        modalData.goodsSelectedList = [];
      }
    },
    { deep: true, immediate: true }
  );

  onMounted(() => {
    modalData.goodsSelectedList = props.defaultGoodsSelectedList;
    modalData.selectedKeys = props.defaultGoodsSelectedList.map((item: any) => {
      return item.skuId || item.id;
    });
  });

  defineExpose({
    init,
    close,
    modalData,
  });
</script>

<style lang="less" scoped>
  .modal-wrapper {
    height: 650px;
  }
</style>
