<template>
  <a-card
    class="general-card"
    title="平台公告"
    :header-style="{ paddingBottom: '0' }"
    :body-style="{ paddingTop: '15px' }"
  >
    <a-space direction="vertical" :size="6">
      <a-space
        v-for="(item, idx) in noticeData.list"
        :key="idx"
        @click="handleClickNotice(item.id)"
      >
        <a-tag size="small">{{ item.articleCategoryName }}</a-tag>
        <a-typography-text class="item-content" ellipsis>
          {{ item.title }}
        </a-typography-text>
      </a-space>
    </a-space>
  </a-card>
  <a-modal v-model:visible="noticeData.visible" :align-center="false">
    <template #title>{{ noticeData.detail.title }}</template>
    <div v-html="noticeData.detail.content"></div>
  </a-modal>
</template>

<script setup lang="ts">
  import { reactive, onMounted } from 'vue';
  import { getSellerArticleList, getSellerArticleDetail } from '@/api/shops';

  const noticeData = reactive<any>({
    list: [],
    detail: {},
    visible: false,
  });
  // 获取商家公告
  const fetchNoticeList = async (): Promise<any> => {
    const res = await getSellerArticleList();
    noticeData.list = res.data.result.records;
  };
  // 点击公告获取详情
  const handleClickNotice = async (id: number | string): Promise<any> => {
    const res = await getSellerArticleDetail(id);
    noticeData.detail = res.data.result;
    noticeData.visible = true;
  };

  onMounted(() => {
    fetchNoticeList();
  });
</script>

<style scoped lang="less">
  .item-content {
    margin-bottom: 0;
    font-size: 14px;
    color: rgb(var(--gray-8));
    cursor: pointer;
  }
</style>
