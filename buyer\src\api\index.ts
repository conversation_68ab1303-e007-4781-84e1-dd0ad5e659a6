// eslint-disable-next-line import/no-cycle
import { ParamsRule } from '@/types/global';
import request, { Method } from '@/utils/axios';

// 获取首页楼层装修数据
export function indexData(params: any) {
  return request({
    url: "/other/pageData/getIndex",
    method: Method.GET,
    needToken: false,
    params,
  });
}

/**
 * 获取楼层数据
 * @param client_type
 * @param page_type
 */
export function getTopicData(id: string) {
  return request({
    url: `/other/pageData/get/${id}`,
    method: "GET",
  });
}

/**
 * 专题内容
 */
export function getSpecial(id: number | string) {
  return request({
    url: `/other/pageData/get/${id}`,
    method: Method.GET,
  });
}

// 获取自动发券
export function getAutoCoup() {
  return request({
    url: "/promotion/coupon/activity",
    method: Method.GET,
    needToken: true,
  });
}

/**
 * 楼层装修数据
 * @param pageClientType 客户端类型,可用值:PC,H5,WECHAT_MP,APP
 * @param pageType 页面类型,可用值:INDEX,STORE,SPECIAL
 */
export function pageData(params: ParamsRule) {
  return request({
    url: `/other/pageData`,
    method: Method.GET,
    needToken: false,
    params,
  });
}

/**
 * 刷新token
 */
export function handleRefreshToken(token: number | string) {
  return request({
    url: `/passport/member/refresh/${token}`,
    method: Method.GET,
    needToken: false,
  });
}

/**
 * 获取店铺楼层数据
 */
export function getFloorStoreData(params: any) {
  return request({
    url: `/other/pageData?pageClientType=PC`,
    method: "get",
    params,
  });
}

