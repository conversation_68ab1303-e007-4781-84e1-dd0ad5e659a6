<template>
  <div>
    <a-card :title="titles">
      <a-form ref="modifyPriceForm" :model="form" :style="{ width: '100%' }" layout="horizontal" auto-label-width>
        <a-form-item label="活动名称" field="promotionName" :rules="[REQUIRED]" extra="活动名称将显示在对人拼团活动列表中，方便商家管理使用，最多输入二十五个字符">
          <a-input v-model="form.promotionName" allow-clear :style="{ width: '360px' }"></a-input>
        </a-form-item>
        <a-form-item label="活动时间" field="rangeTime" :rules="[REQUIRED]">
          <a-range-picker v-model="form.rangeTime" style="width: 360px" show-time format="YYYY-MM-DD HH:mm:ss"></a-range-picker>
        </a-form-item>
        <a-form-item label="参团人数" field="requiredNum" :rules="[REQUIRED]" :style="{ width: '360px' }" extra="参团人数不少于2人，不得超过10人">
          <a-input-number v-model="form.requiredNum" :min="2" :max="10" hide-button>
            <template #append><span>人</span></template>
          </a-input-number>
        </a-form-item>
        <a-form-item label="限购数量" field="limitNum" :rules="[REQUIRED]" :style="{ width: '360px' }" extra="如果设置为0则视为不限制购买数量">
          <a-input-number v-model="form.limitNum" :min="0" :max="100" hide-button>
            <template #append><span>件/人</span></template>
          </a-input-number>
        </a-form-item>
        <a-form-item label="虚拟成团" field="fictitious" :rules="[REQUIRED]" extra="开启虚拟成团后，24小时人数未满的团，系统将会模拟匿名买家凑满人数，使该团成团；您只需要对已付款参团的真实买家发货；建议合理开启以提高">
          <a-radio-group v-model="form.fictitious" type="button" :style="{ width: '115px' }">
            <a-radio :value="1">开启</a-radio>
            <a-radio :value="0">关闭</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item field="pintuanRule" label="拼团规则" extra="拼团规则描述不能为空且不能大于255个字，会在WAP拼团详情页面显示">
          <a-textarea v-model="form.pintuanRule" :max-length="255" :style="{ width: '360px' }" allow-clear show-word-limit auto-size></a-textarea>
        </a-form-item>
      </a-form>
      <a-button style="margin-right: 5px" @click="closeCurrentPage">返回</a-button>
      <a-button type="primary" @click="handleSubmit">提交</a-button>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { dayFormatHHssMM } from '@/utils/filters';
  import { savePintuan, getPintuanDetail, editPintuan } from '@/api/promotion';
  import { Message } from '@arco-design/web-vue';
  import { REQUIRED } from '@/utils/validator';

  const route = useRoute();
  const router = useRouter();
  const titles = ref('');
  const modifyPriceForm = ref<any>();
  const form = ref({
    promotionName: '',
    promotionTitle: '',
    pintuanRule: '',
    requiredNum: '',
    fictitious: 0,
    limitNum: '',
    startTime: '',
    endTime: '',
  }) as any;
  // 返回上一页
  const routerpreviousPage = () => {
    router.push({ name: 'pintuan' });
  };
  // 返回
  const closeCurrentPage = () => {
    routerpreviousPage();
  };
  // 提交
  const handleSubmit = async () => {
    const auth = await modifyPriceForm.value?.validate();
    if (!auth) {
      const params = JSON.parse(JSON.stringify(form.value));
      params.startTime = dayFormatHHssMM(form.value.rangeTime[0]);
      params.endTime = dayFormatHHssMM(form.value.rangeTime[1]);
      params.fictitious ? (params.fictitious = true) : (params.fictitious = false);
      if (!route.query.id) {
        delete params.id;
        savePintuan(params).then((res) => {
          if (res.data.success) {
            Message.success('拼团活动发布成功');
            closeCurrentPage();
          }
        });
      } else {
        editPintuan(params).then((res) => {
          if (res.data.success) {
            Message.success('操作成功');
            closeCurrentPage();
          }
        });
      }
    }
  };
  // 获取活动详情
  const getDetail = async () => {
    const res = await getPintuanDetail(route.query.id);
    if (res.data.success) {
      const data = res.data.result;
      data.rangeTime = [];
      data.rangeTime.push(new Date(data.startTime), new Date(data.endTime));
      form.value = data;
      form.value.fictitious
        ? (form.value.fictitious = 1)
        : (form.value.fictitious = 0);
      // form.value.rangeTime=[unixToDate(data.startTime), unixToDate(data.endTime)]
    }
  };
  onMounted(() => {
    if (route.query.id) {
      titles.value = '编辑拼团活动';
      getDetail();
    } else {
      titles.value = '添加拼团活动';
    }
  });
</script>
