<template>
  <div class="preview-chart">
    <a-card
      title="流量统计"
      :bordered="!props.isInline"
      :style="{ width: '100%', marginBottom: props.isInline ? '0px' : '20px' }"
      hoverable
    >
      <recent-time
        :date-type="defaultDateType.date"
        @on-change="handleClickTimeChange"
      ></recent-time>
    </a-card>
    <a-card hoverable title="流量概括" :bordered="!props.isInline">
      <preview-generalize :date-type="defaultDateType.date" />
    </a-card>
    <a-card
      hoverable
      title="流量趋势"
      :bordered="!props.isInline"
      :style="{ width: '100%', marginTop: props.isInline ? '0px' : '20px' }"
    >
      <preview-chart :date-type="defaultDateType.date" />
    </a-card>
    <a-card
      hoverable
      title="会员流量报表"
      :bordered="!props.isInline"
      :style="{ width: '100%', marginTop: props.isInline ? '0px' : '20px' }"
    >
      <preview-list :date-type="defaultDateType.date" />
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import recentTime from '@/components/recent-time/index.vue';
  import previewChart from '@/components/preview-chart/index.vue';
  import previewGeneralize from '@/components/preview-chart/generalize.vue';
  import previewList from '@/components/preview-chart/list.vue';
  import { defaultDateType, handleClickTimeChange } from '@/hooks/statistics';
  import { ColumnsDataRule } from '@/types/global';
  // 传递的参数
  const apiParams = {
    searchType: 'LAST_SEVEN',
  };
  const columnsTable: ColumnsDataRule[] = [
    {
      title: '日期',
      dataIndex: 'date',
    },
    {
      title: '浏览量',
      dataIndex: 'pvNum',
    },
    {
      title: '访客数',
      dataIndex: 'uvNum',
    },
  ];
  const props = defineProps({
    // 是否内嵌形式
    isInline: {
      type: Boolean,
      default: false,
    },
  });
</script>

<style scoped lang="less">
</style>
