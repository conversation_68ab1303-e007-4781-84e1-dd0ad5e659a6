import { defineStore } from 'pinia';
import { DragRule } from '@/views/operation/decoration/app/models/types';
import { DesignState } from './types';

const useDesignData = defineStore('userDesignData', {
  state: (): DesignState => ({
    // 移动端数据集合
    app: [],
    // 移动端数据索引
    indexOfApp: '',
    pc: {},
    // pc端数据集合
    pinPc: [],
    // 移动端数据索引
    indexOfPc: '',
    // 当前点击pc楼层装修内容
    currentDesignOfPc: '',
  }),
  persist: true,

  getters: {},

  actions: {
    // 设置移动端装修数据 active 状态
    setPinPc(val: any) {
      this.pinPc = val;
    },
    // 设置移动端装修数据 active 状态
    setAppActiveIndex(index: number) {
      this.indexOfApp = index;
    },
    // 设置移动端装修数据
    setAppDesign(val: DragRule) {
      this.app = val;
    },

    // 设置移动端装修数据 active 状态
    setPcActiveIndex(index: number) {
      this.indexOfPc = index;
    },
    // 设置移动端装修数据
    setPcDesign(val: DragRule) {
      this.pc = val;
    },
    // 设置移动端装修数据
    setCurrentPcDesign(val: any) {
      this.currentDesignOfPc = val;
    },
  },
});

export default useDesignData;
