<template>
  <div class="invent">
    <navbar border-b-1 class="navbar"> </navbar>

    <div flex h-100vh class="invent-box">
      <widgetDrag w-168px border-r-1></widgetDrag>
      <widgetPc flex-1 />
      <widgetMiniMap w-160px />
      <div w-400px v-auto-animate overflow-y-auto>
        <widgetModule border-l-1 overflow-hidden />

      </div>
      <!-- <widgetMenu @click-menu="(val) => { show = val }"></widgetMenu> -->
    </div>
  </div>
</template>

<script setup lang="ts">
// import { ref } from 'vue'
import navbar from './layout/pc-navbar.vue'
// import widgetMenu from './layout/pc-menu.vue'
import widgetDrag from './layout/pc-drag.vue'
import widgetPc from './layout/pc-content.vue'
import widgetModule from './layout/pc-module.vue'
import widgetMiniMap from './layout/pc-minimap.vue'

// const show = ref<string>('widget')


</script>

<style lang="less" scoped>
.invent {
  width: 100%;
  height: 100%;

}

.navbar {
  width: 100%;
  position: sticky;
}

.invent-box {
  height: calc(100vh - 66px);
}
</style>
