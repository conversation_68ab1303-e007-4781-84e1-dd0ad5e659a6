<template>
  <a-card class="general-card" title="店铺结算" :bordered="false">
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getBillPage"
      :api-params="apiParams"
    />
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { getBillPage } from '@/api/finance';
  import { billStatus, billStatusSelect } from '@/utils/tools';
  import { ref } from 'vue';

  const tablePageRef = ref('');
  const apiParams = ref({});
  const columnsSearch: Array<SearchRule> = [
    {
      label: '时间',
      disabled: false,
      datePicker: {
        type: 'range',
      },
    },
    {
      label: '状态',
      model: 'billStatus',
      select: {
        options: billStatusSelect,
      },
    },
  ];

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '账单号',
      dataIndex: 'sn',
    },
    {
      title: '生成时间',
      dataIndex: 'createTime',
    },
    {
      title: '结算时间段',
      dataIndex: 'startTime',
    },
    {
      title: '结算金额',
      dataIndex: 'billPrice',
      currency: true,
    },
    {
      title: '状态',
      dataIndex: 'billStatus',
      slot: true,
      slotData: {
        tag: billStatus,
      },
    },
  ];

  const sortMethods: MethodsRule = {
    title: '操作',
    width: 100,
    methods: [
      {
        title: '查看',
        callback: 'detail',
      },
    ],
  };
</script>

<style scoped></style>
