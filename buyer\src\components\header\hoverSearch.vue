<template>
  <div>
    <div  class="hover-search-con">
      <!-- 商品分类 -->
      <CateNav :showNavBar="false" :showAlways="false" useClass="fixed-show"></CateNav>
      <!-- 搜索框、logo -->
      <Search :showLogo="false" :showTag="false" useClass="fixed-show" />
      <!-- 购物车 -->
      <div class="cart-box" @click="goToPay()" @mouseenter.native="getCartList">
        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 24 24" class="hover-pointer">
          <path fill="#ef4444" d="M4.004 6.417L.762 3.174L2.176 1.76l3.243 3.243H20.66a1 1 0 0 1 .958 1.287l-2.4 8a1 1 0 0 1-.958.713H6.004v2h11v2h-12a1 1 0 0 1-1-1zm2 .586v6h11.512l1.8-6zm-.5 16a1.5 1.5 0 1 1 0-3a1.5 1.5 0 0 1 0 3m12 0a1.5 1.5 0 1 1 0-3a1.5 1.5 0 0 1 0 3"/>
        </svg>
        <div class="circle hover-pointer">
          {{cartNum < 100 ? cartNum : 99}}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import storage from '@/utils/storage';
  import { useRouter } from 'vue-router';
  import { cartCount } from "@/api/cart";
  import Cookies from 'js-cookie';

  const router = useRouter();
  // 购物车商品数量
  const cartNum = computed(() => {
    return Number(storage.getCartNum()) || 0;
  });
  // 获取购物车列表
  const getCartList = () => {
    if (storage.getUserInfo()) {
      cartCount().then((res) => {
        storage.setCartNum(res.data.result);
        Cookies.set("cartNum",res.data.result);
      });
    }
  };
  // 跳转购物车
  const goToPay = () => {
    let url = router.resolve({path: '/cart'});
    window.open(url.href, '_blank');
  };
</script>

<style scoped lang="less">
  .hover-search-con {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    width: 1200px;
    margin: 0 auto;
  }
  /*购物车*/
  .cart-box {
    width: 30px;
    height: 30px;
    position: relative;
    margin-top: 15px;
    margin-right: 70px;
    .circle {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      color: #fff;
      background: @theme_color;
      line-height: 20px;
      text-align: center;
      font-size: 12px;
      position: absolute;
      top: -5px;
      right: -10px;
    }
  }
</style>
