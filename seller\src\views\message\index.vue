<template>
  <a-card class="general-card" title="系统消息" :bordered="false">
    <a-row style="margin-bottom: 16px">
      <div style="width: 100%">
        <a-badge :count="unReadCounts">
          <a-button
            :type="currentMessageType === 'unread' ? 'primary' : 'secondary'"
            @click="setCurrentMesType('unread')"
            >未读消息</a-button
          >
        </a-badge>
        <a-button
          :type="currentMessageType === 'unread' ? 'secondary' : 'primary'"
          style="margin-left: 20px"
          @click="setCurrentMesType('read')"
          >已读消息</a-button
        >
        <a-button
          v-if="currentMessageType === 'unread'"
          type="primary"
          status="danger"
          style="float: right"
          @click="readAlls"
          >全部已读</a-button
        >
        <a-button
          v-else
          type="primary"
          status="danger"
          style="float: right"
          @click="deleteAlls"
          >全部删除</a-button
        >
      </div>
    </a-row>
    <!-- 表格 -->
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :bordered="true"
      :api="getUserMessages"
      :api-params="params"
    >
      <template #status="{ data }">
        <a-badge
          v-if="data.status == 'ALREADY_READY'"
          color="green"
          text="已读"
        ></a-badge>
        <a-badge
          v-if="data.status == 'UN_READY'"
          color="orangered"
          text="未读"
        ></a-badge>
      </template>
      <template #asread="{ data }">
        <a-button
          v-if="data.status == 'UN_READY'"
          type="text"
          status="success"
          @click="markAsReadBtn(data)"
          >阅读消息</a-button
        >
        <a-button
          v-if="data.status == 'ALREADY_READY'"
          type="text"
          status="danger"
          @click="deleteMesBtn(data)"
          >删除消息</a-button
        >
      </template>
    </tablePage>
    <a-modal v-model:visible="showMessageDetail" :title="mesDeatil.title">
      {{ mesDeatil.content }}
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { MethodsRule, ColumnsDataRule } from '@/types/global';
  import {
    deleteMessage,
    unReadCount,
    getUserMessages,
    read,
    readAll,
    deleteAll,
  } from '@/api/message';
  import { Message } from '@arco-design/web-vue';
  import useCurrentInstance from '@/hooks/useCurrentInstance';

  const tablePageRef = ref();
  const unReadCounts = ref(0); // 未读消息数量
  const hasReadCounts = ref(0); // 已读消息数量
  const showMessageDetail = ref(false); // 消息详情模态框
  const currentMessageType = ref('unread'); // 当前列表消息状态
  const modal = useCurrentInstance().globalProperties?.$modal; // 获取modal
  const mesDeatil = ref<any>({}); // 消息详情
  const params = ref({
    status: 'UN_READY',
    sort: 'createTime', // 默认排序字段
    order: 'desc', // 默认排序方式
  });
  // 表格列表
  const columnsTable: ColumnsDataRule[] = [
    { title: '标题', dataIndex: 'title',width: 400 },
    { title: '状态', dataIndex: 'status',width: 200, slot: true, slotTemplate: 'status' },
    { title: '发送时间', dataIndex: 'createTime',width: 200 },
  ];
  // 操作列表
  const sortMethods: MethodsRule = {
    title: '操作',
    methods: [{ slot: true, slotTemplate: 'asread' }],
  };

  // 获取全部数据
  const getUnReadCount = () => {
    unReadCount(params.value).then((res) => {
      if (res.data.success) {
        // 未读消息数量
        unReadCounts.value = res.data.result;
      }
    });
  };
  // 设置当前消息分类
  const setCurrentMesType = (type: string) => {
    currentMessageType.value = type;
    if (type == 'unread') {
      params.value.status = 'UN_READY';
    } else if (type === 'read') {
      params.value.status = 'ALREADY_READY';
    }
  };
  // 阅读消息
  const markAsReadBtn = (v: any) => {
    mesDeatil.value = v;
    showMessageDetail.value = true;
    read(v.id).then((res) => {
      if (res.data.success) {
        Message.success('已读消息');
        tablePageRef.value.init();
        getUnReadCount();
      }
    });
  };
  // 删除消息
  const deleteMesBtn = (v: any) => {
    deleteMessage(v.id).then((res) => {
      if (res.data.success) {
        Message.success('删除成功');
        tablePageRef.value.init();
      }
    });
  };
  // 阅读所有
  const readAlls = () => {
    readAll().then((res) => {
      if (res.data.success) {
        Message.success('全部已读');
        tablePageRef.value.init();
        getUnReadCount();
      }
    });
  };
  // 删除所有
  const deleteAlls = () => {
    modal.confirm({
      title: '确认删除',
      content: `确认要删除全部消息?`,
      alignCenter: false,
      onOk: async () => {
        const res = await deleteAll();
        if (res.data.success) {
          Message.success('删除成功！');
          tablePageRef.value.init();
        }
      },
    });
  };

  onMounted(() => {
    getUnReadCount();
  });
</script>

<style scoped></style>
