<template>
  <div>
    <div>
      <a-upload ref="uploadRef" :action="props.api()" :headers="{ accessToken: accessToken, ...props.headersParams }"
        :show-file-list="false" :onSuccess="success" :limit="props.limit" :onError="handleError"
        @before-upload="beforeUpload($event, props.size)" :data="props.apiParams">
        <template #upload-button>
          <a-space>
            <a-button type="primary" :loading="loading">上传文件</a-button>
          </a-space>
        </template>
      </a-upload>
    </div>
  </div>
</template>

<script setup lang="ts">
import { withDefaults, ref } from 'vue'
import { handleSuccess, handleError, beforeUpload } from './upload.ts';

const loading = ref(false);
const props = withDefaults(defineProps<{
  headersParams?: Record<string, string> //上传请求附加的头信息
  dataParams?: Record<string, string> //上传请求附加的数据
  limit?: number // 上传文件大小限制
  accessToken: string, // 请求头token
  size?: string, // 上传文件大小限制 默认为5MB
  api(): any // 上传文件api
}>(), {
  limit: 1,
  size: "5",
  accessToken: '',
  dataParams: {},
  headersParams: {},
})

const emit = defineEmits<{
  (e: 'onSuccess', val: any): void; // 请求成功之后回调方法
}>();


function success(res: any) {
  handleSuccess(res);
  emit('onSuccess', true);
}

</script>

<style lang="less" scoped></style>
