<template>
  <div class="box">
    <div class="left-side">
      <div class="panel">
        <Banner />
        <a-spin style="display: block">
          <DataPanel v-if="result.data" :res="result.data" />
        </a-spin>
        <a-spin style="display: block" :loading="loading">
          <TodoList v-if="!loading && result.todoList" :res="result.todoList" />
        </a-spin>
      </div>
      <a-grid :cols="24" :col-gap="16" :row-gap="16" style="margin-top: 16px">
        <a-grid-item :span="{ xs: 24, sm: 24, md: 24, lg: 24, xl: 24, xxl: 24 }">
          <!-- <Preview :is-inline="true" /> -->
          <Generalize  :res="result.data" />
        </a-grid-item>
        <a-grid-item :span="{ xs: 24, sm: 24, md: 24, lg: 24, xl: 24, xxl: 24 }">
          <HistoryMemberChart :is-inline="true" />
          <!-- <Generalize v-if="!loading" :res="result.data" /> -->
        </a-grid-item>
        <a-grid-item :span="{ xs: 24, sm: 24, md: 24, lg: 12, xl: 12, xxl: 12 }">
          <!-- <Preview :is-inline="true" /> -->
          <a-card hoverable title="流量趋势" :bordered="!props.isInline">
            <preview-chart :date-type="defaultDateType.date" />
          </a-card>
        </a-grid-item>
        <a-grid-item :span="{ xs: 24, sm: 24, md: 24, lg: 12, xl: 12, xxl: 12 }">
          <!-- <OrderChart :is-inline="true" /> -->
          <a-card hoverable title="交易趋势" :bordered="!props.isInline">
            <order-chart :date-type="defaultDateType.date" />
          </a-card>
        </a-grid-item>
        <a-grid-item :span="{ xs: 24, sm: 24, md: 24, lg: 24, xl: 24, xxl: 24 }">
        <a-card hoverable title="热卖商品TOP10" >
          <hotGoodsOrder :max="10" />
          </a-card>
          <!-- <PopularContent /> -->
        </a-grid-item>
        <a-grid-item :span="{ xs: 24, sm: 24, md: 24, lg: 24, xl: 24, xxl: 24 }">
          <HotSaleShop />
        </a-grid-item>
      </a-grid>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, reactive } from 'vue';
  import { getDashboardData,getNoticePage } from '@/api/statisitics';
  import { defaultDateType } from '@/hooks/statistics';
  import useLoading from '@/hooks/loading';
  import orderChart from '@/components/order-chart/index.vue';
  import previewChart from '@/components/preview-chart/index.vue';
  import hotGoodsOrder from '@/components/hot-goods-order/index.vue';
  import Banner from './components/banner.vue';
  import DataPanel from './components/data-panel.vue';
  import HistoryMemberChart from './components/historyMemberChart.vue';
  import TodoList from './components/todo-list.vue';
  import Generalize from './components/generalize.vue'
  import HotSaleShop from './components/hotSaleShop.vue';
  // import Announcement from './components/announcement.vue';

  
  const { loading, setLoading } = useLoading(false);
  const result = reactive({
    data: {},
    todoList:{},
  });
  const props = defineProps({
    // 是否内嵌形式
    isInline: {
      type: Boolean,
      default: false,
    },
  });
  
  const fetchData = async () => {
    // setLoading(true);
    const res = await getDashboardData();
    if (res.data.success) {
      result.data = res.data.result;
      // setLoading(false);
    }
  };

 const awaitTodo = async () => {
    setLoading(true);
    const res = await getNoticePage();
    if(res.data.success){
      result.todoList = res.data.result;
      setLoading(false);
    }
 }
  onMounted(() => {
    fetchData();
    awaitTodo();
  });
</script>

<!--<script lang="ts">-->
<!--  export default {-->
<!--    name: 'Dashboard', // If you want the include property of keep-alive to take effect, you must name the component-->
<!--  };-->
<!--</script>-->

<style lang="less" scoped>
  .box {
    background-color: var(--color-fill-2);
    display: flex;
  }

  .left-side {
    flex: 1;
    overflow: auto;
  }

  .right-side {
    width: 280px;
    margin-left: 16px;
  }

  .panel {
    background-color: var(--color-bg-2);
    border-radius: 4px;
    overflow: auto;
  }
  :deep(.panel-border) {
    margin-bottom: 0;
    border-bottom: 1px solid rgb(var(--gray-2));
  }
  .moduler-wrap {
    border-radius: 4px;
    background-color: var(--color-bg-2);
    :deep(.text) {
      font-size: 12px;
      text-align: center;
      color: rgb(var(--gray-8));
    }

    :deep(.wrapper) {
      margin-bottom: 8px;
      text-align: center;
      cursor: pointer;

      &:last-child {
        .text {
          margin-bottom: 0;
        }
      }
      &:hover {
        .icon {
          color: rgb(var(--arcoblue-6));
          background-color: #e8f3ff;
        }
        .text {
          color: rgb(var(--arcoblue-6));
        }
      }
    }

    :deep(.icon) {
      display: inline-block;
      width: 32px;
      height: 32px;
      margin-bottom: 4px;
      color: rgb(var(--dark-gray-1));
      line-height: 32px;
      font-size: 16px;
      text-align: center;
      background-color: rgb(var(--gray-1));
      border-radius: 4px;
    }
  }
</style>

<style lang="less" scoped>
  // responsive
  .mobile {
    .container {
      display: block;
    }
    .right-side {
      // display: none;
      width: 100%;
      margin-left: 0;
      margin-top: 16px;
    }
  }
</style>
