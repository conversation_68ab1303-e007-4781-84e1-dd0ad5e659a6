<template>
  <a-card class="general-card" title="充值记录"  :bordered="false">
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :api="getUserRecharge"
      :api-params="apiParams"
      :bordered="true"
    >
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { rechargeWay, payStatus } from '@/utils/tools';
  import { getUserRecharge } from '@/api/member';
  import { ref } from 'vue';

  const apiParams = ref({});
  const tablePageRef = ref('');
  const columnsSearch: Array<SearchRule> = [
    {
      label: '会员名称',
      model: 'memberName',
      disabled: false,
      input: true,
    },
    {
      label: '订单号',
      model: 'rechargeSn',
      disabled: false,
      input: true,
    },
    {
      label: '支付时间',
      disabled: false,
      datePicker: {
        type: 'range',
      },
    },
  ];

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '会员名称',
      dataIndex: 'memberName',   
    },
    {
      title: '订单号',
      dataIndex: 'rechargeSn',
    
    },

    {
      title: '充值金额',
      dataIndex: 'rechargeMoney',
      currency: true,
    },
    {
      title: '充值方式',
      dataIndex: 'rechargeWay',
      slot: true,
      slotData: {
        badge: rechargeWay,
      },
    },
     {
      title: '支付状态',
      dataIndex: 'payStatus',
      slot: true,
      slotData: {
        badge: payStatus,
      },
    },
     {
      title: '充值时间',
      dataIndex: 'createTime',
    },
     {
      title: '支付时间',
      dataIndex: 'payTime',
    },
  ];

</script>
