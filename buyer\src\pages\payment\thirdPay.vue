<template>
  <div class="payment-box">
    <div class="wrapper-head">
      <div class="head-left">
        <div class="left-tips">订单提交成功，请尽快付款！</div>
        <div class="left-tips-time">请您尽快完成支付，否则订单会被自动取消</div>
      </div>
      <div class="head-right">
        <div>应付金额 <span class="price">{{ route.query.price? unitPrice(Number(route.query.price)):'0.00' }}</span>元</div>
      </div>
    </div>
    <div class="content">
      <div class="pay-way">{{params.paymentMethod === 'ALIPAY' ? '支付宝支付' : '微信支付'}}</div>
      <div class="qrcode">
        <div style="width:200px;height:200px;border:1px solid #eee;">
          <vue-qrcode v-if="qrcode" :value="qrcode" :options="{ width: 200 }"></vue-qrcode>
        </div>
        <div class="intro flex">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"><path fill="#FFFFFF" d="M15 3h6v5h-2V5h-4zM9 3v2H5v3H3V3zm6 18v-2h4v-3h2v5zm-6 0H3v-5h2v3h4zM3 11h18v2H3z"/></svg>
          &nbsp;请使用{{params.paymentMethod === 'ALIPAY' ? '支付宝' : '微信'}}扫码付款
        </div>
      </div>
      <div class="btn-div">
        <p class="mb_10">支付成功后自动跳转，如未跳转请点击按钮手动跳转。。。</p>
        <div>
          <a-button type="secondary" @click="handlePay" class="mr_10">重新支付</a-button>
          <a-button type="outline" status="success" @click="router.push('/payment/payDone')">支付成功</a-button>
        </div>
      </div>
      <a @click="router.back()" class="other-payment">选择其他支付方式></a>
    </div>


  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { unitPrice } from '@/utils/filters';
  import { useRoute, useRouter } from 'vue-router';
  import { payCallback, pay } from '@/api/pay';
  import VueQrcode from '@chenfengyuan/vue-qrcode';

  const route = useRoute();
  const router = useRouter();
  const qrcode = ref(''); // 二维码
  const params = route.query; // 参数
  const interval = ref<any>(null); // 定时器
  const num = ref(0); // 商品数


  // 获取支付二维码
  const handlePay = () => {
    const params = route.query;
    pay(params).then(res => {
      if (res.data.success) {
        qrcode.value = res.data.result;
        num.value = 0;
        interval.value = setInterval(callback, 5000);
      } else {
        Message.error(res.data.message);
      }
    });
  };
  // 支付回调接口
  const callback = () => {
    num.value++;
    if (num.value >= 7) {
      clearInterval(interval.value);
      interval.value = null;
    }
    let params = JSON.parse(JSON.stringify(route.query));
    delete params.paymentMethod;
    delete params.paymentClient;
    payCallback(params).then(res => {
      if (res.data.result) {
        clearInterval(interval.value);
        interval.value = null;
        router.push({ path: '/payment/payDone', query: {orderType: route.query.orderType} });
      }
    });
  };

  onMounted(() => {
    handlePay();
  })
</script>

<style scoped lang="less">
  .payment-box {
    background-color: @light_background_color;
    overflow: hidden;
  }
  .wrapper-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: 1.75;
    color: #515A6E;
    .head-left {
      font-weight: bold;
      .left-tips {
        font-size: 21px;
        .left-tips-time {
          font-size: 16px;
        }
        .left-tips-count-down {
          font-size: 10px;
          color: @theme_color;
        }
      }
    }
    .head-right {
      font-weight: bold;
      font-size: 18px;
      .price {
        font-size: 18px;
        font-weight: bold;
        color: @price_color;
      }
    }
  }
  .wrapper-head {
    padding: 20px 40px;
    width: 1200px;
    margin: 20px auto;
  }

  .content {
    padding: 20px 40px;
    width: 1000px;
    margin: 20px auto;
    background-color: #fff;
    box-shadow: 0 6px 10px #ddd;
    position: relative;
    display: flex;
    .pay-way {
      font-weight: bold;
      font-size: 18px;
      color: #515A6E;
    }
    .qrcode {
      margin: 30px 0 0 70px;
    }
    .intro {
      background-color: #ff7674;
      height: 40px;
      line-height: 40px;
      margin-top: 10px;
      color: #fff;
      font-size: 16px;
      text-align: center;
      align-items: center;
      justify-content: center;
    }
    .btn-div {
      margin:120px 0 0 30px;
      color: #515A6E;
    }
    a {
      position: absolute;
      right: 20px;
      top: 20px;
    }
  }

  .other-payment {
    color: @link_color;
    cursor: pointer;
  }
</style>
