<template>
  <div class="card">
    <div class="title">最近48小时在线人数（整点为准）</div>
    <a-spin style="display: block" :loading="loading">
      <div id="historyMemberChart"></div>
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, reactive } from 'vue';
  import { Chart } from '@antv/g2';
  import useLoading from '@/hooks/loading';
  import { historyMemberChartList } from '@/api/statisitics';

  interface PreviewNumber {
    date: string;
    num?: number;
    title: string;
    lastNum?: number;
    lastNumNum?: number;
    res?: number;
  }
  interface ResponseRule {
    chartList: Array<any>;
    historyMemberChart: any;
    nums: number;
    lastNums: number;
    res?: number;
  }

  const { loading, setLoading } = useLoading();
  const responseResult = reactive<ResponseRule>({
    chartList: [],
    historyMemberChart: '',
    nums: 0, // 访客数
    lastNums: 0, // 浏览量
  });

  // 加载图表
  const chart = () => {
    const num: Array<PreviewNumber> = [];
    const lastNum: Array<PreviewNumber> = [];
    const chartData = responseResult.chartList;

    chartData.forEach((item: any) => {
      num.push({
        date: item.date,
        res: item.num || 0,
        lastNum: item.num || 0,
        title: '最近48小时',
      });
      lastNum.push({
        date: item.date,
        res: item.lastNum || 0,
        lastNum: item.lastNum || 0,
        title: '上一周期',
      });
    });

    const data = [...num, ...lastNum];

    responseResult.historyMemberChart.data(data);
    responseResult.historyMemberChart.scale({
      activeQuantity: {
        range: [0, 1],
        nice: true,
      },
    });
    responseResult.historyMemberChart.tooltip({
      showCrosshairs: true,
      shared: true,
    });

    responseResult.historyMemberChart
      .line()
      .position('date*lastNum')
      .color('title')
      .label('lastNum')
      .shape('smooth');

    responseResult.historyMemberChart
      .point()
      .position('date*lastNum')
      .color('title')
      .label('lastNum')
      .shape('circle')
      .style({
        stroke: '#fff',
        lineWidth: 1,
      });
    responseResult.historyMemberChart
      .area()
      .position('date*lastNum')
      .color('title')
      .shape('smooth');

    responseResult.historyMemberChart.render();
  };
  // 初始化流量的图表
  const initOrderChart = async () => {
    setLoading(true);
    const res = await historyMemberChartList();
    if (res.data.success) {
      responseResult.chartList = res.data.result;
      // res.data.result.forEach((item: any) => {
      //   responseResult.nums += item.numNum;
      //   responseResult.lastNums += item.lastNumNum;
      // });
      if (!responseResult.historyMemberChart) {
        responseResult.historyMemberChart = new Chart({
          container: 'historyMemberChart',
          autoFit: true,
          height: 500,
          padding: [70, 70, 70, 70],
        });
      }
    }
    chart();
    setLoading(false);
  };
  onMounted(() => {
    initOrderChart();
  });
</script>

<style scoped lang="less">
  .card {
    background-color: var(--color-bg-2);
  }
  .historyMemberChart {
    width: 100%;
  }
  .title {
    padding: 20px;
    font-size: 18px;
  }
</style>
