<template>
  <div>
    <a-radio-group
      v-model:model-value="goodsData.params.type"
      style="margin-bottom: 20px"
      type="button"
      @change="typeChange"
    >
      <a-radio value="NUM"> 商品订单数量 </a-radio>
      <a-radio value="PRICE"> 商品订单金额 </a-radio>
    </a-radio-group>

    <!-- 表格 -->
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :api="goodsStatistics"
      :custom-api-params="goodsData.params"
      :enable-pagination="false"
      :max="props.max"
    >
      <template #index="{ rowIndex }">
        {{ rowIndex + 1 }}
      </template>
    </tablePage>
  </div>
</template>

<script setup lang="ts">
  import tablePage from '@/components/table-pages/index.vue';
  import { ColumnsDataRule, PreViewParamsRule } from '@/types/global';
  import { goodsStatistics } from '@/api/statisitics';
  import { reactive, ref, watch } from 'vue';
  import { useUserStore } from '@/store';

  const tablePageRef = ref<any>();

  interface GoodsDataRule {
    params: PreViewParamsRule;
  }
  const props = defineProps({
    dateType: {
      type: Object,
      default: () => {
        return {
          recent: 'LAST_SEVEN',
          month: '',
        };
      },
    },
    // 最多显示多少内容，如果不设置则不显示
    max: {
      type: null,
      default: '',
    },
  });

  const goodsData = reactive<GoodsDataRule>({
    params: {
      searchType: props.dateType.recent, // TODAY ,  YESTERDAY , LAST_SEVEN , LAST_THIRTY
      year: props.dateType.month || new Date().getFullYear(),
      storeId: useUserStore().userInfo.id,
      type: 'NUM',
    },
  });

  // 表格搜索列表
  const columnsTable: ColumnsDataRule[] = [
    {
      title: '排名',
      dataIndex: 'index',
      slot: true,
      slotTemplate: 'index',
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
    },

    {
      title: '销售数量',
      dataIndex: 'num',
    },
    {
      title: '销售金额',
      dataIndex: 'price',
      currency: true,
    },
  ];
  const fetchData = () => {
    if (tablePageRef.value) {
      tablePageRef.value?.init();
    }
  };

  const typeChange: any = (contentType: string) => {
    goodsData.params.type = contentType;

    fetchData();
  };
  // 监听值的改变 父级值改变
  watch(
    () => props.dateType,
    (val) => {
      goodsData.params.searchType = val.recent;
      if (val.month) {
        // eslint-disable-next-line prefer-destructuring,no-nested-ternary
        goodsData.params.month = val.month.split('-')[1]
          ? val.month.split('-')[1].indexOf('0') != -1
            ? val.month.split('-')[1].substr(1)
            : ''
          : '';

        // eslint-disable-next-line prefer-destructuring
        goodsData.params.year = val.month.split('-')[0];
      }

      fetchData();
    },
    {
      deep: true,
      immediate: true,
    }
  );
</script>

<style scoped></style>
