<template>
  <a-modal v-model:visible="visible" @ok="handleOk" @cancel="handleCancel">
    <template #title> 添加一级分类 </template>
    <div>
      <a-form
        ref="formRef"
        :model="formAdd"
        :style="{ width: '450px' }"
        @submit="handleSubmit"
      >
        <a-form-item
          field="parentId"
          :rules="[{ required: true, message: '请填写参数' }]"
          :validate-trigger="['change', 'input']"
          label="上级分类"
        >
          <a-input
            v-model="formAdd.parentId"
            :style="{ width: '320px' }"
            allow-clear
          />
        </a-form-item>
        <a-form-item field="image" label="分类名称">
          <a-input v-model="formAdd.image" />
        </a-form-item>
        <a-form-item field="sortOrder" label="排序值">
          <a-input-number
            v-model="formAdd.sortOrder"
            :style="{ width: '320px' }"
            class="input-demo"
            :min="10"
            :max="999"
          />
        </a-form-item>
      </a-form>
      <!-- <a-space>
        <a-button html-type="submit">Submit</a-button>
      </a-space> -->
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { FormRules } from '../index';

  const visible = ref(false);
  const formAdd = reactive<FormRules>({
    parentId: '',
    level: '',
    image: '',
    isRead: '',
    sortOrder: 0,
    commissionRate: 0,
    deleteFlag: '',
  });
  const handleSubmit = ({ values, errors }: any) => {
    console.log('values:', values, '\nerrors:', errors);
  };
  const handleClick = () => {
    visible.value = true;
  };
  const handleOk = () => {
    console.log(formAdd, 'formAdd');

    visible.value = false;
  };
  const handleCancel = () => {
    visible.value = false;
  };
  // 暴露方法变量
  defineExpose({
    visible,
  });
</script>

<style scoped lang="less"></style>
