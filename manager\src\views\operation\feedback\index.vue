<template>
  <a-card class="general-card" title="意见反馈" :bordered="false">
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getMemberFeedback"
      @detail="seeCashWithdrawal"
      :bordered="true"
    >
    </tablePage>
    <!-- 查看 -->
    <a-modal
      v-model:visible="seeWithdrawal"
      :align-center="false"
      width="500px"
    >
      <template #title> 详细信息 </template>
      <a-form
        ref="formRef"
        :style="{ width: '460px' }"
        layout="horizontal"
        auto-label-width
        :model="form"
      >
        <a-form-item label="用户名：">
          <span>{{ form.userName }}</span>
        </a-form-item>
        <a-form-item label="手机号：">
          <span>{{ form.mobile }}</span>
        </a-form-item>
        <a-form-item label="类型：">
          <span>{{
            form.type == 'FUNCTION'
              ? '功能建议'
              : form.type == 'OPTIMIZE'
              ? '优化反馈'
              : '其他意见'
          }}</span>
        </a-form-item>
        <a-form-item label="反馈内容：">
          <!-- <span>{{ form.context }}</span> -->
          <a-textarea v-model="form.context" disabled></a-textarea>
        </a-form-item>
        <a-form-item label="相关材料：">
          <span v-if="!form.images">暂无</span>
          <span v-for="(item, index) in form.images" v-else :key="index">
            <!-- <a-avatar shape="square" :size="120" style="margin-right: 10px">
              <img alt="avatar" :src="item" /> </a-avatar
          > -->
          <span v-if="item">
            <a-image width="80" height="80" :src="item" style="margin-right: 10px"/>
          </span>
          </span>
        </a-form-item>
      </a-form>
      <template #footer>
        <div style="text-align: right">
          <a-button type="text" @click="seeWithdrawal = false">取消</a-button>
        </div>
      </template>
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">
  import tablePage from '@/components/table-pages/index.vue';
  import { MethodsRule, ColumnsDataRule, SearchRule } from '@/types/global';
  import { getMemberFeedback, getMemberFeedbackDetail } from '@/api/operation';
  import { feedbackType } from '@/utils/tools';
  import { ref, reactive } from 'vue';

  const tablePageRef = ref('');
  const seeWithdrawal = ref<boolean>(false); // 查看
  interface formInterface {
    userName: string;
    mobile: string;
    type: string;
    context: string;
    images: Array<string | undefined>;
  }
  const columnsTable: ColumnsDataRule[] = [
    {
      title: '会员名称',
      dataIndex: 'userName',
    },
    {
      title: '手机号码',
      dataIndex: 'mobile',
    },
    {
      title: '反馈内容',
      dataIndex: 'context',
    },
    {
      title: '类型',
      dataIndex: 'type',
      slot: true,
      slotData: {
        badge: feedbackType,
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
    },
  ];

  const sortMethods: MethodsRule = {
    title: '操作',
    width: 250,
    fixed: 'right',
    methods: [
      {
        title: '查看',
        callback: 'detail',
        type:'text',
        status:'success'
      },
    ],
  };
  // 数据集
  const form = ref<formInterface>({
    userName: '',
    mobile: '',
    type: '',
    context: '',
    images: [],
    // 表单提交数据
  });
  // 查看
  const seeCashWithdrawal = async (v: any) => {
    const res = await getMemberFeedbackDetail(v.record.id);
    if (res.data.code == 200) {
      form.value = res.data.result;

      form.value.images = (res.data.result.images || '').split(',');
      seeWithdrawal.value = true;
    }
  };
</script>

<script lang="ts">
  // eslint-disable-next-line import/export
  // export default {
  //   name: 'GoodsList',
  // };
</script>
