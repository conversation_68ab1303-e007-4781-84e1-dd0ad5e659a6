<template>
  <div flex flex-a-c p-16px>
    <div text-center class="title" cursor-pointer @click="handleClick(item, index)" v-for="(item, index) in props.res.data.category"
      :key="index">
      <div>
        <div font-bold text-16px
          :style="{ color: current === index ? props.res.data.activeTColor : props.res.data.titleColor }">{{ item.title
          }}
        </div>
        <div text-12px mt-8px font-bold
          :style="{ color: current === index ? props.res.data.activeDColor : props.res.data.descColor }">{{ item.desc }}
        </div>
      </div>
    </div>
  </div>
  <div>
    <tpl_goods_only :model="props.res.data.category[current].goods" :res="props.res" />
  </div>
</template>

<script setup lang="ts">
import tpl_goods_only from './tpl_goods_only.vue';
import { ref } from 'vue'
const props = defineProps<{
  res: any
}>()

const current = ref<number>(0)

function handleClick(val: any, index: number) {
  current.value = index
}
</script>

<style scoped>
.title {
  width: 25%;
}
</style>
