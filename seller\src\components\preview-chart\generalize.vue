<template>
  <div>
    <a-spin style="display: block" :loading="loading">
      <a-statistic
        title="访客数UV"
        style="margin: 0 60px"
        :value="responseResult.uvs"
        show-group-separator
      />
      <a-statistic
        title="浏览量PV"
        style="margin: 0 60px"
        :value="responseResult.pvs"
        show-group-separator
      />
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
  import { defineProps, onMounted, reactive, watch } from 'vue';
  import useLoading from '@/hooks/loading';
  import { getPreviewChart } from '@/api/statisitics';
  import { useUserStore } from '@/store';
  import { PreViewParamsRule } from '@/types/global';

  interface ResponseRule {
    chartList: Array<any>;
    previewChart: any;
    uvs: number;
    pvs: number;
  }

  const { loading, setLoading } = useLoading();
  const props = defineProps({
    dateType: {
      type: Object,
      default: () => {
        return {
          recent: 'LAST_SEVEN',
          month: '',
        };
      },
    },
  });
  const responseResult = reactive<ResponseRule>({
    chartList: [],
    previewChart: '',
    uvs: 0, // 访客数
    pvs: 0, // 浏览量
  });

  // 订单请求参数
  const previewParams = reactive<PreViewParamsRule>({
    searchType: props.dateType.recent, // TODAY ,  YESTERDAY , LAST_SEVEN , LAST_THIRTY
    year: props.dateType.month || new Date().getFullYear(),
    storeId: useUserStore().userInfo.id,
  });
  // 初始化流量的图表
  const initOrderChart = async () => {
    setLoading(true);
    const res = await getPreviewChart(previewParams);
    if (res.data.success) {
      // responseResult.chartList = res.data.result;
      res.data.result.forEach((item: any) => {
        responseResult.uvs += item.uvNum;
        responseResult.pvs += item.pvNum;
      });
    }
    setLoading(false);
  };
  onMounted(() => {
    initOrderChart();
  });
  // 监听值的改变 父级值改变
  watch(
    () => props.dateType,
    (val) => {
      previewParams.searchType = val.recent;
      if (val.month) {
        // eslint-disable-next-line prefer-destructuring,no-nested-ternary
        previewParams.month = val.month.split('-')[1]
          ? val.month.split('-')[1].indexOf('0') != -1
            ? val.month.split('-')[1].substr(1)
            : ''
          : '';

        // eslint-disable-next-line prefer-destructuring
        previewParams.year = val.month.split('-')[0];
      }

      initOrderChart();
    },
    {
      deep: true,
      immediate: true,
    }
  );
</script>

<style scoped lang="less">
  .previewChart {
    width: 100%;
  }
</style>
