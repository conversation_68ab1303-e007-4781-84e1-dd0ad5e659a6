<template>
  <div>
    <Card _Title="订单评价" :_Size="16"></Card>
    <div>
      <span class="light-text-color">订单号：</span>
      <span>{{$route.query.sn}}</span>
      <span class="light-text-color ml_20" v-if="order.order">{{order.order.paymentTime}}</span>
    </div>
    <!-- 物流评分、服务评分 -->
    <div class="delivery-rate">
      <div class="title">物流服务评价：</div>
      <div class="rate-list flex">
        <span>物流评价：<a-rate v-model="form.deliveryScore" /></span>
        <span>服务评价：<a-rate v-model="form.serviceScore" /></span>
        <span>描述评价：<a-rate v-model="form.descriptionScore" /></span>
      </div>
    </div>
    <!-- 添加订单评价  左侧商品详情  右侧评价框 -->
    <div class="goods-eval flex pt_20 pb_20">
      <div class="goods-con">
        <img :src="orderGoods.image" class="hover-pointer" alt="" width="100" @click="goGoodsDetail(orderGoods.skuId, orderGoods.goodsId)">
        <p class="hover-pointer hover-color" @click="goGoodsDetail(orderGoods.skuId, orderGoods.goodsId)">{{orderGoods.goodsName}}</p>
        <p>{{ unitPrice(orderGoods.goodsPrice, '￥') }}</p>
      </div>
      <div class="eval-con">
        <div>
          <span class="light-text-color">商品评价：</span>
          <a-radio-group v-model="orderGoods.grade" type="button" class="mb_10">
            <a-radio value="GOOD">好评</a-radio>
            <a-radio value="MODERATE">中评</a-radio>
            <a-radio value="WORSE">差评</a-radio>
          </a-radio-group>
          <a-textarea placeholder="请输入您的评价" v-model="orderGoods.content" :max-length="500" allow-clear show-word-limit :auto-size="{minRows:4,maxRows:6}" ></a-textarea>
          <a-upload list-type="picture-card" :action="uploadFile" :headers="{ accessToken: accessToken }" image-preview
                    :file-list="fileList" v-model="fileList" :onSuccess="uploadSuccess" @change="handleChange" @before-upload="beforeUpload"></a-upload>
        </div>
      </div>
    </div>
    <div class="mt_20">
      <a-button @click="handleSave"  size="small" status="danger" type="primary">发表</a-button>
    </div>

  </div>
</template>

<script setup lang="ts">
  import { useRouter, useRoute } from 'vue-router';
  import { ref, onMounted } from 'vue';
  import { unitPrice } from '@/utils/filters';
  import { orderDetail } from '@/api/order';
  import uploadFile from '@/api/common';
  import { beforeUpload, accessToken } from '@/utils/upload';
  import { Message } from '@arco-design/web-vue';
  import { addEvaluation } from '@/api/member';

  const router = useRouter();
  const route = useRoute();
  const order = ref<any>({});
  const orderGoods = ref<any>({});
  const loading = ref(false);
  // 评分展示
  const form = ref({
    deliveryScore: 5,
    serviceScore: 5,
    descriptionScore: 5
  });
  // 上传图片列表
  const fileList = ref([]);


  const getOrderDetail = () => {
    orderDetail(route.query.sn).then(res => {
      if (res.data.success) {
        order.value = res.data.result as any;
        orderGoods.value = res.data.result.orderItems[Number(route.query.index)];
        orderGoods.value.grade = 'GOOD';
        fileList.value = [];
      }
    })
  };
  // 上传成功回调
  const uploadSuccess = () => {
    // console.log('上传成功回调', val);
  };
  const handleChange = (list: any, files: any) => {
    if (list && list.length) {
      fileList.value = list;
    } else {
      fileList.value = [];
    }
  };

  // 发表
  const handleSave = () => {
    if (!form.value.serviceScore || !form.value.deliveryScore || !form.value.descriptionScore) {
      Message.warning('物流服务评价不能为空');
    return false;
  }

    let goods = orderGoods.value;
    let params = {
      goodsId: goods.goodsId,
      orderItemSn: goods.sn,
      skuId: goods.skuId,
      descriptionScore: form.value.descriptionScore,
      serviceScore: form.value.serviceScore,
      deliveryScore: form.value.deliveryScore,
      grade: goods.grade,
      content: goods.content || '',
      images: fileList.value.map((item: any) => item.response.result).toString(),
    };
    addEvaluation(params).then(res => {
      loading.value = false;
      if (res.data.success) {
        Message.success('评价成功');
        router.push('/user/home/<USER>/commentList');
      }
    }).catch(() => {
      loading.value = false;
    })
  };
  // 跳转商品详情
  const goGoodsDetail = (skuId: any, goodsId: any) => {
    let routeUrl = router.resolve({path: "/goodsDetail", query: { skuId, goodsId },});
    window.open(routeUrl.href, "_blank");
  };


  onMounted(() => {
    getOrderDetail();
  });
</script>

<style scoped lang="less">
  .delivery-rate {
    display: flex;
    align-items: flex-end;
    margin: 30px 0;
    .title {
      font-size: 18px;
    }
    .rate-list > span {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      height: 20px;
      /*line-height: 20px;*/
      margin-right: 30px;
      color: @light_text_color;
    }
  }
  .goods-eval {
    border-top: 1px solid #eeeeee;
    border-bottom: 1px solid #eeeeee;
    .goods-con {
      width: 30%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
    }
    .eval-con {
      width: 70%;
    }
  }
</style>
