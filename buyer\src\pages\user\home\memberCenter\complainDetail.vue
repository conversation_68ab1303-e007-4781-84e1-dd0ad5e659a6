<template>
  <div>
    <Card _Title="投诉详情" :_Size="16"></Card>
    <div class="flex" style="align-items: center;">
      <div class="global-color fontsize-18">{{ statusLabel[complainDetail.complainStatus] }}</div>
      <div class="light-text-color ml_20">创建时间：{{ complainDetail.createTime }}</div>
    </div>

    <a-alert type="warning" class="mt_20 mb_10">我的申诉信息</a-alert>
    <div class="reason-list">
      <div>
        <div>投诉商品</div>
        <div @click="goodsDetail(complainDetail.skuId, complainDetail.goodsId)">
          <a-image :src="complainDetail.goodsImage" width="60" alt="" /><span class="ml_10">{{ complainDetail.goodsName }}</span>
        </div>
      </div>
      <div><div>投诉主题</div><div>{{ complainDetail.complainTopic }}</div></div>
      <div><div>投诉内容</div><div>{{ complainDetail.content }}</div></div>
      <div>
        <div>补充内容</div>
        <div style="display:flex;align-items:center;">
          <template v-if="complainDetail.images">
            <div class="demo-upload-list" v-for="(img, index) in complainDetail.images.split(',')" :key="index">
              <a-image :src="img" width="60" height="60" class="mr_10" />
            </div>
          </template>
          <div v-else>暂无</div>
        </div>
      </div>
    </div>
    <a-alert type="warning" class="mt_20 mb_10">商家申诉信息</a-alert>
    <div class="reason-list">
      <div><div>申诉时间</div><div>{{ complainDetail.appealTime || '暂无' }}</div></div>
      <div><div>申诉内容</div><div>{{ complainDetail.appealContent || '暂无'}}</div></div>
      <div>
        <div>申诉凭证</div>
        <div style="display:flex;align-items:center;">
          <template v-if="complainDetail.appealImages">
            <div class="demo-upload-list" v-for="(img, index) in complainDetail.appealImages.split(',')" :key="index">
              <a-image :src="img" width="60" height="60" class="mr_10" />
            </div>
          </template>
          <div v-else>暂无</div>
        </div>
      </div>
    </div>
    <a-alert type="warning" class="mt_20 mb_10">平台仲裁</a-alert>
    <div class="reason-list">
      <div><div>仲裁意见</div><div>{{ complainDetail.arbitrationResult || '暂无' }}</div></div>
    </div>
    <a-alert type="warning" class="mt_20 mb_10">对话详情</a-alert>
    <div class="speak-way" v-if="complainDetail.orderComplaintCommunications && complainDetail.orderComplaintCommunications.length">
      <div :class="{'speak-buyer': item.owner == 'BUYER', 'speak-platform': item.owner == 'PLATFORM', 'speak-seller': item.owner == 'STORE',}" :key="i"
           class="speak-msg seller" v-for="(item, i) in complainDetail.orderComplaintCommunications">
        {{item.owner == "PLATFORM" ? "平台" : item.owner == "BUYER" ? "买家" : "卖家"}}[{{ item.createTime }}]：
        <span>{{ item.content }}</span>
      </div>
    </div>
    <div v-else>暂无对话</div>
    <!--回复-->
    <a-form ref="recoverRef" :model="recoverForm" size="large" layout="horizontal" auto-label-width :style="{ width: '400px'}" class="mt_20" @submit-success="handleSubmit"
            v-if="complainDetail.complainStatus !== 'COMPLETE' && complainDetail.complainStatus !== 'CANCEL'">
      <a-form-item field="content" label="回复" :rules="[REQUIRED]">
        <a-input v-model="recoverForm.content" allow-clear placeholder="请填写对话内容"></a-input>
      </a-form-item>
      <a-form-item field="" label="">
        <a-button html-type="submit" size="small" status="danger" type="primary" :loading="loading">回复</a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
  import { useRouter, useRoute } from 'vue-router';
  import { ref, onMounted } from 'vue';
  import { REQUIRED } from '@/utils/validator';
  import { Message } from '@arco-design/web-vue';
  import { getComplainDetail } from '@/api/member';
  import { communication } from '@/api/order';

  const route = useRoute();
  const router = useRouter();
  const loading = ref(false);
  const complainDetail = ref<any>({}); // 评价详情
  // 状态
  const statusLabel = ref<any>({
    NO_APPLY: '未申请',
    APPLYING: '申请中',
    COMPLETE: '已完成',
    EXPIRED: '已失效',
    CANCEL: '已取消',
    NEW: '待审核'
  });
  const recoverRef = ref<any>(); // 回复ref
  const recoverForm = ref<any>({
    complainId: '',
    content: '',
  });  // 回复表单

  const getDetail = () => { // 获取投诉详情
    getComplainDetail(route.query.id).then(res => {
      if (res.data.success) complainDetail.value = res.data.result;
    })
  };
  // 回复
  const handleSubmit = async () => {
    const auth = await recoverRef.value?.validate();
    if (!auth) {
      loading.value = true;
      recoverForm.value.complainId = route.query.id;
      communication(recoverForm.value).then((res) => {
        loading.value = false;
        if (res.data.success) {
          Message.success('对话成功');
          recoverForm.value.content = '';
          getDetail();
        }
      });
    }
  };
  // 跳转商品详情
  const goodsDetail = (skuId: any, goodsId: any) => {
    let routeUrl = router.resolve({path: "/goodsDetail", query: { skuId, goodsId },});
    window.open(routeUrl.href, "_blank");
  };

  onMounted(() => {
    getDetail();
  })
</script>

<style scoped lang="less">
  .reason-list {
    border: 1px solid @light_border_color;
    border-bottom: none;
    color: #666666;
    > div {
      display: flex;
      border-bottom: 1px solid @light_border_color;
      > div:nth-of-type(1) {
        width: 120px;
        padding: 10px 30px 10px 20px;
        border-right: 1px solid @light_border_color;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
      > div:nth-of-type(2) {
        padding: 10px 20px 10px 30px;
        color: @light_text_color;
      }
    }
  }

  .speak-way {
    background-color: @light_white_background_color;
    width: 100%;
    border: 1px solid @light_border_color;
    color: #666666;
    .speak-msg {
      height: 40px;
      line-height: 40px;
      padding: 0 15px;
      border-radius: 5px;
      margin: 5px;
      background-color: #eee;
    }
    .speak-seller {
      background-color: #fff2c6;
    }
    .speak-platform {
      background-color: #ffe2d1;
    }
    .speak-buyer {
      background-color: #c1d6d5;
    }
  }

</style>
