<template>
  <div class="preview-chart">
    <a-card
      title="会员统计"
      :bordered="!props.isInline"
      :style="{ width: '100%', marginBottom: props.isInline ? '0px' : '20px' }"
      hoverable
    >
      <recent-time
        :date-type="defaultDateType.date"
        @on-change="handleClickTimeChange"
      ></recent-time>
    </a-card>
    <a-card
      hoverable
      title="客户增长趋势"
      :bordered="!props.isInline"
      :style="{ width: '100%', marginTop: props.isInline ? '0px' : '20px' }"
    >
      <member-chart :date-type="defaultDateType.date" />
    </a-card>
    <a-card
      hoverable
      title="客户增长报表"
      :bordered="!props.isInline"
      :style="{ width: '100%', marginTop: props.isInline ? '0px' : '20px' }"
    >
      <member-list :date-type="defaultDateType.date" />
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import recentTime from '@/components/recent-time/index.vue';
  import memberChart from '@/components/member-chart/index.vue';
  import memberList from '@/components/member-chart/list.vue';
  import { defaultDateType, handleClickTimeChange } from '@/hooks/statistics';
  import { ColumnsDataRule } from '@/types/global';
  // 传递的参数
  const apiParams = {
    searchType: 'LAST_SEVEN',
  };
  const columnsTable: ColumnsDataRule[] = [
    {
      title: '日期',
      dataIndex: 'date',
    },
    {
      title: '浏览量',
      dataIndex: 'pvNum',
    },
    {
      title: '访客数',
      dataIndex: 'uvNum',
    },
  ];
  const props = defineProps({
    // 是否内嵌形式
    isInline: {
      type: Boolean,
      default: false,
    },
  });
</script>

<style scoped lang="less">
</style>
