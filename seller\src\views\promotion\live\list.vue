<template>
  <a-card class="general-card" title="直播管理" :bordered="false">
    <a-tabs @change="(val)=>{apiParams.status = val}" :default-active-key="liveStatusStr">
      <a-tab-pane key="NEW" title="未开始"></a-tab-pane>
      <a-tab-pane key="START" title="直播中"></a-tab-pane>
      <a-tab-pane key="END" title="已结束"></a-tab-pane>
    </a-tabs>
    <a-row style="margin-bottom: 16px">
      <a-col :span="16">
        <a-space>
          <a-button type="primary" @click="createLive"> 创建直播 </a-button>
        </a-space>
      </a-col>
    </a-row>

    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getLiveList"
      :api-params="params"
      :bordered="true"
      @detail="getLiveDetail"
    >
      <template #endTime="{ record }">
        {{ unixToDate(record) }}
      </template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
  import tablePage from '@/components/table-pages/index.vue';
  import { MethodsRule, ColumnsDataRule } from '@/types/global';
  import { getLiveList } from '@/api/promotion';
  import { liveStatus } from '@/utils/tools';
  import { ref, watch, reactive } from 'vue';
  import { unixToDate } from '@/utils/filters';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const tablePageRef = ref<any>();
  const type = ref<string>('NEW');
  const liveStatusStr = ref<string>('NEW')
  const apiParams = ref<any>({status:liveStatusStr.value});
  const columnsTable: ColumnsDataRule[] = [
    {
      title: '直播标题',
      dataIndex: 'name',
    },
    {
      title: '主播昵称',
      dataIndex: 'anchorName',
    },

    {
      title: '直播开始时间',
      dataIndex: 'createTime',
    },
    {
      title: '直播结束时间',
      dataIndex: 'endTime',
      slot: true,
      slotTemplate: 'endTime',
    },
  ];

  const sortMethods: MethodsRule = {
    title: '操作',
    width: 150,
    methods: [
      {
        title: '查看/添加商品',
        callback: 'detail',
        type: 'text',
      },
    ],
  };

  const params = reactive({
    status: type,
  });
  watch(type, (val) => {
    params.status = val;
    tablePageRef.value.init();
  });

  // 查看
  const getLiveDetail = (val: any) => {
    router.push({
      name: 'add-live',
      query: {
        liveStatus: type.value,
        ...val.record,
      },
    });
  };
  // 创建直播
  const createLive = () => {
    router.push({
      name: 'add-live',
    });
  };
</script>
