import { DragRule } from './types'
const goodsModel: Array<DragRule> = [{
  type: "advert",
  name: "横幅广告",
  roles: ['img', 'link', 'height'],
  models: [{
    'label': '背景颜色',
    'model': 'background',
    'bind': 'background'
  }],
  data: {
    list: [
      {
        img: "https://lilishop-oss.oss-cn-beijing.aliyuncs.com/9fc491bd51484690a405ce0fab1bb118.jpeg",
        url: "",
      }
    ],
    height: 80,
    round: 0,
  }
}, {
  type: "recommend",
  name: "推荐",
  roles: ['background'],
  models: [{
    label: "菜单标题",
    model: "text"
  }, {
    label: "背景颜色",
    model: "background",
    bind: "background"
  }, {
    'label': '中间',
    'model': 'mix-model',
  }, {
    'label': '最近热卖',
    'model': 'goods-choice',
    'bind': 'sellList',
    'max': 4
  },],
  data: {
    text: "为你推荐",
    textColor: "#333333",
    background: "#F5F5F7",
    align: "center",
    leftData: {
      img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/mix-bg3.png",
      url: "",
    },
    list: [
      {
        img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/example1.png",
        url: "",
        title: "时尚精致女装",
        desc: "经典呈现"
      },
      {
        img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/example2.png",
        url: "",
        title: "时尚精致女装",
        desc: "经典呈现"
      },
      {
        img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/example3.png",
        url: "",
        title: "时尚精致女装",
        desc: "经典呈现"
      },
      {
        img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/example4.png",
        url: "",
        title: "时尚精致女装",
        desc: "经典呈现"
      },
    ],
    sellList: [

    ],

  }
}, {
  type: "goods",
  name: "商品",
  roles: [],
  models: [{
    label: "菜单标题",
    model: "text"
  }, {
    'label': '商品选品',
    'model': 'goods-choice'
  },],
  data: {
    list: [
    ],
    text: '标题',
    textColor: "#333333",
    align: "center"
  }
}, {
  type: "mix",
  name: "混合模块",
  roles: [],
  models: [],
  has: ['brandModel', 'cardGoodsModel'],
  data: {
    leftModal: 'cardGoodsModel',
    leftData: {
      type: "cardGoods",
      name: "商品",
      roles: ['img', 'link', 'text'],
      models: [{
        'label': '商品选品',
        'model': 'goods-choice',
        'bind': 'goodsList',
        'max': 4
      }],
      data: {
        list: [{
          img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/mix-bg1.png",
          url: "",
        }],
        goodsList: [],
        text: '精选',
      }
    },
    rightData: {
      type: "brand",
      name: "品牌",
      roles: ['img', 'link'],
      models: [{
        label: '图片',
        model: 'mix-model',
        bind: 'brandList'
      }],
      data: {
        list: [{
          img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/mix-bg2.png",
          url: "",
        }],
        brandList: []
      }
    },
    rightModal: 'brandModel',
  }
},
{
  type: "flexThree",
  name: "一行三列",
  roles: [],
  models: [{
    label: '图片',
    model: 'mix-model',
  }],
  data: {
    list: [
      {
        img: "https://lilishop-oss.oss-cn-beijing.aliyuncs.com/44072692ecfc4ba787ee35faf5b5618b.png",
        url: "",
      },
      {
        img: "https://lilishop-oss.oss-cn-beijing.aliyuncs.com/44072692ecfc4ba787ee35faf5b5618b.png",
        url: "",
      },
      {
        img: "https://lilishop-oss.oss-cn-beijing.aliyuncs.com/44072692ecfc4ba787ee35faf5b5618b.png",
        url: "",
      },
    ],
  }
}, {
  type: "flexFour",
  name: "一行四列",
  roles: [],
  models: [{
    label: '图片',
    model: 'mix-model',
  }],
  data: {
    list: [
      {
        img: "https://lilishop-oss.oss-cn-beijing.aliyuncs.com/6069e7dced154e85b24b79e8e17c0743.jpg?x-oss-process=style/400X400",
        url: "",
        title: "新品首发",
        desc: "每天都有好物上新",
      },
      {
        img: "https://lilishop-oss.oss-cn-beijing.aliyuncs.com/0f9fc5610ccb448d9ada49e960598c4c.jpg?x-oss-process=style/400X400",
        url: "",
        title: "新品首发",
        desc: "每天都有好物上新",
      },
      {
        img: "https://lilishop-oss.oss-cn-beijing.aliyuncs.com/6069e7dced154e85b24b79e8e17c0743.jpg?x-oss-process=style/400X400",
        url: "",
        title: "新品首发",
        desc: "每天都有好物上新",
      },
      {
        img: "https://lilishop-oss.oss-cn-beijing.aliyuncs.com/0f9fc5610ccb448d9ada49e960598c4c.jpg?x-oss-process=style/400X400",
        url: "",
        title: "新品首发",
        desc: "每天都有好物上新",
      },

    ],
  }
},
]

// 默认图片链接List
export const defaultList = {
  img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/img.png",
  url: "",
}
// 默认文字链接List
export const defaultTextList = {
  title: "",
  url: "",
}

// 品牌模块
export const brandModel: DragRule = {
  type: "brand",
  name: "品牌",
  roles: ['img', 'link'],
  models: [{
    label: '图片',
    model: 'mix-model',
    bind: 'brandList'
  }],
  data: {
    list: [{
      img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/mix-bg2.png",
      url: "",
    }],
    brandList: []
  }
}
// 商品
export const cardGoodsModel: DragRule = {
  type: "cardGoods",
  name: "商品",
  roles: ['img', 'link', 'text'],
  models: [{
    'label': '商品选品',
    'model': 'goods-choice',
    'bind': 'goodsList',
    'max': 4
  }],
  data: {
    list: [{
      img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/mix-bg1.png",
      url: "",
    }],
    goodsList: [],
    text: '精选',
  }
}

export const topAdvert: DragRule = {
  type: "topAdvert",
  name: "顶部广告",
  roles: [],
  models: [{
    'label': '背景颜色',
    'model': 'background',
    'bind': 'background'
  }],
  data: {
    list: [
      {
        img: "https://lilishop-oss.oss-cn-beijing.aliyuncs.com/9fc491bd51484690a405ce0fab1bb118.jpeg",
        url: "",
      }
    ],
    height: 80,
    background: "#ffffff",
  }
}
// 
export const topNav: DragRule = {
  type: "topNav",
  name: "顶部导航",
  roles: ['round'],
  models: [{
    'label': '文字颜色',
    'model': 'background',
    'bind': 'textColor'
  }, {
    'label': '背景颜色',
    'model': 'background',
    'bind': 'background'
  }, {
    'label': '菜单',
    'model': 'mix-model',
  }],
  border:'normal',
  data: {
    list: [
      {
        title: "菜单",
        url: "",

      }
    ],
    background: "#ffffff",
    textColor: "#333333",
    round: 10,
  
  }
}
export const bannerAdvert: DragRule = {
  type: "bannerAdvert",
  name: "横幅广告",
  roles: [],
  models: [{
    label: '图片',
    model: 'mix-model',
    delete: true
  }],
  data: {
    list: [
      {
        img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/ias_c8d0b1587c24057ea1ec040ddf1dd2da_750x400_80.jpg",
        url: "",
      },
      {
        img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/img.png",
        url: "",
      },
    ],
    round: 0,
  }
}

export default goodsModel
