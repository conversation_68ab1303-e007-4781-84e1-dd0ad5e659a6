<template>
  <a-card class="general-card" title="短信" :bordered="false">
    <a-tabs default-active-key="LIST" @change="clickTabPane">
      <a-tab-pane key="LIST" title="发送任务列表">
        <a-col :span="16" style="margin-bottom: 10px">
          <a-space>
            <a-button type="primary" @click="sendBatchSmsModal"
              >发送短信</a-button
            >
          </a-space>
        </a-col>
        <tablePage
          ref="smsTablePageRef"
          :columns="smsColumns"
          :methods="smsMethods"
          :api="getSmsPage"
          :api-params="smsFormData.smsApiParams"
          :bordered="true"
        >
          <template #detailTemplate="{ data }"
            ><a-button @click="detailTemplateClick(data)" type="text" status="success"
              >详细</a-button
            ></template
          >
        </tablePage>
      </a-tab-pane>
      <a-tab-pane key="TEMPLATE" title="短信模板">
        <a-col :span="16" style="margin-bottom: 10px">
          <a-space>
            <a-button type="primary" @click="addTemplate"
              >添加短信模板</a-button
            >
            <a-button  @click="syncTemplateClick">同步</a-button>
          </a-space>
        </a-col>
        <tablePage
          ref="templateTablePageRef"
          :columns="templateColumns"
          :methods="templateMethods"
          :api="getSmsTemplatePage"
          :api-params="templateFormData.templateApiParams"
          :bordered="true"
        >
          <template #templateStatus="{ data }">
            <span v-if="data.templateStatus == 0">审核中</span>
            <span v-else-if="data.templateStatus == 1">审核通过</span>
            <span v-else-if="data.templateStatus == 2">审核失败</span>
          </template>
          <template #editTemplate="{ data }"
            ><a-button
              :disabled="data.templateStatus == 2 ? false : true"
              @click="editTemplateClick(data)"   type='text' status="warning"
              >编辑</a-button
            ></template
          >
          <template #deleteTemplate="{ data }"
            ><a-button @click="deleteTemplateClick(data)"  type='text' status="danger"
              >删除</a-button
            ></template
          >
        </tablePage>
      </a-tab-pane>
      <a-tab-pane key="SIGN" title="短信签名">
        <a-col :span="16" style="margin-bottom: 10px">
          <a-space>
            <a-button type="primary" @click="addSignClick"
              >添加短信签名</a-button
            >
            <a-button @click="syncSignClick">同步</a-button>
          </a-space>
        </a-col>
        <tablePage
          ref="signTablePageRef"
          :columns="signColumns"
          :methods="signMethods"
          :api="getSmsSignPage"
          :api-params="signFormData.signApiParams"
          :bordered="true"
        >
          <template #signStatus="{ data }">
            <span v-if="data.signStatus == 0">审核中</span>
            <span v-if="data.signStatus == 1">审核通过</span>
            <span v-if="data.signStatus == 2"
              >审核拒绝
              <a-popover
                ><a-button type="text">【原因】</a-button
                ><template #content
                  ><p>{{ data.reason }}</p></template
                ></a-popover
              >
            </span>
          </template>
          <template #editSign="{ data }"
            ><a-button
              :disabled="data.signStatus != 2"
              @click="editSignClick(data)"  type='text' status="warning"
              >编辑</a-button
            ></template
          >
          <template #deleteSign="{ data }"
            ><a-button
              :disabled="data.signStatus != 2"
              @click="deleteSignClick(data)" type='text' status="danger"
              >删除</a-button
            ></template
          >
        </tablePage>
      </a-tab-pane>
    </a-tabs>

    <!--添加短信模板-->
    <a-modal
      v-model:visible="templateFormData.templateModalVisible"
      :align-center="false"
      :footer="false"
    >
      <template #title>{{ templateFormData.templateModalTitle }}</template>
      <a-form
        ref="templateFormRef"
        :model="templateFormData.templateForm"
        auto-label-width
      >
        <a-form-item field="templateName" label="模板名称" :rules="[REQUIRED]">
          <a-input
            v-model="templateFormData.templateForm.templateName"
            placeholder="请输入模板名称，不超过30字符"
            :max-length="30"
          ></a-input>
        </a-form-item>
        <a-form-item
          field="templateContent"
          label="模板内容"
          :rules="[REQUIRED]"
        >
          <a-textarea
            v-model="templateFormData.templateForm.templateContent"
            allow-clear
            :max-length="500"
            placeholder="请输入短信内容，不超过500字符，不支持【】、★、 ※、 →、 ●等特殊符号；"
          />
        </a-form-item>
        <a-form-item field="remark" label="申请说明" :rules="[REQUIRED]">
          <a-textarea
            v-model="templateFormData.templateForm.remark"
            allow-clear
            :max-length="100"
            placeholder="请描述您的页数使用场景，不超过100字符，如：用于春节集五福"
          />
        </a-form-item>
        <a-form-item label="">
          <a-button type="primary" @click="templateFormSubmit()">提交</a-button>
        </a-form-item>
      </a-form>
    </a-modal>

    <!--发送短信模态框-->
    <a-modal
      v-model:visible="smsFormData.sendSmsModal"
      :align-center="false"
      :footer="false"
      :width="900"
    >
      <template #title>短信发送</template>
      <div class="send-setting">
        <div class="left-show">{{ smsFormData.smsContent }}</div>
        <div class="send-form">
          <a-form ref="smsFormRef" :model="smsFormData.smsForm">
            <a-form-item field="signName" label="短信签名" :rules="[REQUIRED]">
              <a-select
                v-model="smsFormData.smsForm.signName"
                :style="{ width: '260px' }"
                @change="selectSmsSign"
              >
                <a-option
                  v-for="item of smsFormData.smsSigns"
                  :key="item.signName"
                  :value="item.signName"
                  :label="item.signName"
                />
              </a-select>
            </a-form-item>
            <a-form-item
              field="messageCode"
              label="短信模板"
              :rules="[REQUIRED]"
            >
              <a-select
                v-model="smsFormData.smsForm.messageCode"
                :style="{ width: '260px' }"
                @change="selectSmsTemplate"
              >
                <a-option
                  v-for="item of smsFormData.smsTemplates"
                  :key="item.templateCode"
                  :value="item.templateCode"
                  :label="item.templateName"
                />
              </a-select>
            </a-form-item>
            <a-form-item field="context" label="短信内容" :rules="[REQUIRED]">
              <a-textarea
                v-model="smsFormData.smsForm.context"
                allow-clear
                max-length="50"
                :auto-size="{ maxRows: 3, minRows: 3 }"
                show-word-limit
                disabled
              />
            </a-form-item>
            <a-form-item field="smsRange" label="接收人">
              <div>
                <p style="margin: 0 0 4px 0; line-height: 32px">
                  已选<span style="color: #f56c1d">{{
                    smsFormData.memberNum
                  }}</span
                  >人，预计耗费条数<span style="color: #f56c1d">{{
                    smsFormData.smsForm.num
                  }}</span
                  >条
                </p>
                <a-radio-group
                  v-model="smsFormData.smsForm.smsRange"
                  type="button"
                  @change="smsRangeChange"
                >
                  <a-radio value="1">全部会员</a-radio>
                  <a-radio value="2">自定义选择</a-radio>
                </a-radio-group>
              </div>
            </a-form-item>
            <a-form-item>
              <div
                v-if="smsFormData.smsForm.smsRange == '2'"
                class="choose-member"
              >
                <div class="source-member" :style="{ marginBottom: '5px' }">
                  <a-input
                    v-model="smsFormData.memberSearchParam.mobile"
                    placeholder="请输入手机号码"
                    @change="getMembersChange"
                  ></a-input>
                </div>
                <div class="scroll-card">
                  <a-list
                    :max-height="300"
                    :scrollbar="scrollbar"
                    @reach-bottom="fetchData"
                  >
                    <!--<template #header>List title</template>-->
                    <template #scroll-loading>
                      <div v-if="bottom">暂无更多会员</div>
                      <a-spin v-else />
                    </template>
                    <a-list-item
                      v-for="(item, index) in smsFormData.members"
                      :key="index"
                      class="list-item-members"
                      :class="{ active: item.selected }"
                      @click="selectMember(item)"
                    >
                      <span v-if="item.mobile" class="mobile">{{
                        item.mobile
                      }}</span>
                      <span>{{ item.nickName }}</span>
                    </a-list-item>
                  </a-list>
                  <div class="traget-member" style="overflow: auto">
                    <a-list :max-height="300">
                      <a-list-item
                        v-for="(item, index) in smsFormData.alreadyCheckShow"
                        :key="index"
                        class="list-item-members"
                      >
                        <span class="list-item-span"
                          ><span>{{ item.mobile || item.nickName }}</span
                          ><icon-close @click="alreadyCheckClose(item, index)"
                        /></span>
                        <!--<a-tag class="checkbox-tag" closable @close="alreadyCheckClose(item, index)">{{item.mobile || item.nickName}}</a-tag>-->
                      </a-list-item>
                    </a-list>
                  </div>
                </div>
              </div>
            </a-form-item>
            <a-form-item label="操作">
              <a-button type="primary" @click="sendSmsSubmit">发送</a-button>
            </a-form-item>
          </a-form>
        </div>
      </div>
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">
  import tablePage from '@/components/table-pages/index.vue';
  import { MethodsRule, ColumnsDataRule } from '@/types/global';
  import { ref, watch, reactive, onMounted } from 'vue';
  import useCurrentInstance from '@/hooks/useCurrentInstance';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { Message } from '@arco-design/web-vue';
  import { getSmsSignPage, getSmsTemplatePage } from '@/api/operation';
  import {
    deleteSign,
    syncSign,
    addSmsTemplate,
    editSmsTemplate,
    deleteSmsTemplate,
    syncSmsTemplate,
    getSmsPage,
    sendSms,
  } from '@/api/setting';
  import { getMemberNum, getMemberListData } from '@/api/member';
  import { useRouter } from 'vue-router';
  import { VARCHAR255, REQUIRED, VARCHAR20, MOBILE } from '@/utils/validator';

  const router = useRouter();

  // 获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;

  // 发送任务列表
  const current = ref(1);
  const bottom = ref(false);
  const scrollbar = ref(true);
  const smsTablePageRef = ref<any>();
  const smsFormRef = ref<any>();
  const smsColumns: ColumnsDataRule[] = [
    { title: '模板名称', dataIndex: 'smsName' },
    { title: '签名', dataIndex: 'signName' },
    { title: '短信内容', dataIndex: 'context' },
    { title: '预计发送条数', dataIndex: 'num' },
  ];
  const smsMethods: MethodsRule = {
    title: '操作',
    width: 250,
    methods: [
      {
        title: '详细',
        callback: 'detailTemplate',
        slot: true,
        slotTemplate: 'detailTemplate',
      },
    ],
  };
  interface smsFormInterface {
    sendSmsModal: boolean;
    smsSigns: Array<any>;
    smsTemplates: Array<any>;
    signSearchForm: {
      pageNumber: number;
      pageSize: number;
      [key:string]: any;
    };
    templateSearchForm: {
      pageNumber: number;
      pageSize: number;
      [key:string]: any;
    };
    smsForm: {
      smsName: string;
      signName: string;
      messageCode: string;
      context: string;
      smsRange: string;
      num: number;
      [key:string]: any;
    };
    memberNum: number;
    memberSearchParam: {
      pageNumber: number;
      pageSize: number;
      disabled: string;
      [key:string]: any;
    };
    memberSearchFrom: {
      disabled: string;
      [key:string]: any;
    };
    memberPage: number;
    members: Array<any>;
    alreadyCheck: Array<any>;
    alreadyCheckShow: Array<any>;
    smsTemplateContent: string;
    smsApiParams: {
      sort: string;
      order: string;
      pageNumber: number;
      pageSize: number;
      [key:string]: any;
    };
    smsData: Array<any>;
    smsTotal: number;
    smsContent: string;
    [key:string]: any;
  }
  const smsFormData = reactive<smsFormInterface>({
    sendSmsModal: false,
    smsSigns: [], // 短信签名
    smsTemplates: [], // 短信模板
    // 短信签名搜索框初始化对象
    signSearchForm: {
      pageNumber: 1,
      pageSize: 10,
    },
    // 短信模板搜索框初始化对象
    templateSearchForm: {
      pageNumber: 1,
      pageSize: 10,
    },
    // 短信发送表单数据
    smsForm: {
      smsName: '',
      signName: '',
      messageCode: '',
      context: '',
      smsRange: '1',
      num: 0,
    },
    memberNum: 0,
    // 会员查询条件
    memberSearchParam: {
      pageNumber: 1,
      pageSize: 8,
      disabled: 'OPEN', // 会员状态
    },
    // 会员条数查询form
    memberSearchFrom: {
      disabled: 'OPEN', // 会员状态
    },
    // 会员信息
    memberPage: 0,
    // 所有会员
    members: [],
    // 已选择的会员
    alreadyCheck: [],
    // 已选择的会员--负责显示
    alreadyCheckShow: [],
    // 短信模板内容
    smsTemplateContent: '',
    // 任务列表查询form
    smsApiParams: {
      sort: 'createTime',
      order: 'desc',
      pageNumber: 1,
      pageSize: 10,
    },
    // 任务列表表单记录
    smsData: [],
    // 任务列表表单数据总数
    smsTotal: 0,
    // 效果预览
    smsContent: `效果预览`,
  });
  // 短信模板
  const templateTablePageRef = ref<any>();
  const templateFormRef = ref<FormInstance>();
  const templateColumns: ColumnsDataRule[] = [
    { title: '模板code', dataIndex: 'templateCode' },
    { title: '模板名称', dataIndex: 'templateName' },
    { title: '模板内容', dataIndex: 'templateContent' },
    {
      title: '状态',
      dataIndex: 'templateStatus',
      slot: true,
      slotTemplate: 'templateStatus',
    },
  ];
  const templateMethods: MethodsRule = {
    title: '操作',
    width: 250,
    methods: [
      {
        title: '编辑',
        callback: 'editTemplate',
        slot: true,
        slotTemplate: 'editTemplate',
      },
      {
        title: '删除',
        callback: 'deleteTemplate',
        slot: true,
        slotTemplate: 'deleteTemplate',
      },
    ],
  };
  interface formInterface {
    templateApiParams: {
      sort: string;
      order: string;
      [key:string]: any;
    };
    templateModalVisible: boolean;
    templateModalTitle: string;
    templateModalType: string;
    templateForm: any;
  }
  const templateFormData = reactive<formInterface>({
    templateApiParams: {
      sort: '',
      order: '',
    },
    templateModalVisible: false,
    templateModalTitle: '添加短信模板',
    templateModalType: 'add',
    templateForm: {},
  });
  // 短信签名
  const signTablePageRef = ref<any>();
  const signColumns: ColumnsDataRule[] = [
    { title: '签名名称', dataIndex: 'signName' },
    { title: '申请说明', dataIndex: 'remark' },
    {
      title: '状态',
      dataIndex: 'signStatus',
      slot: true,
      slotTemplate: 'signStatus',
    },
  ];
  const signMethods: MethodsRule = {
    title: '操作',
    methods: [
      {
        title: '编辑',
        callback: 'editSign',
        slot: true,
        slotTemplate: 'editSign',
      },
      {
        title: '删除',
        callback: 'deleteSign',
        slot: true,
        slotTemplate: 'deleteSign',
      },
    ],
  };
  interface signFormInterface {
    signApiParams: {
      sort: string;
      order: string;
      [key:string]: any;
    };
  }
  const signFormData = reactive<signFormInterface>({
    signApiParams: {
      sort: '',
      order: '',
    },
  });

  // 查询会员条数
  const getMembersNum = () => {
    getMemberNum(smsFormData.memberSearchFrom).then((res) => {
      if (res.data.success) {
        smsFormData.memberNum = res.data.result;
        smsFormData.smsForm.num = smsFormData.memberNum; // 全部会员则会员数就等于条数
      }
    });
  };
  // 分页查询会员信息
  const getMembers = () => {
    getMemberListData(smsFormData.memberSearchParam).then((res) => {
      if (res.data.success) {
        res.data.result.records.forEach((item: any) => {
          item.selected = false;
          smsFormData.members.push(item);
        });
        smsFormData.memberPage = res.data.result.pages;
        if (
          res.data.result.current * res.data.result.pages >=
          res.data.result.total
        ) {
          bottom.value = true;
        }
      }
    });
  };
  // 弹出发送短信模态框
  const sendBatchSmsModal = () => {
    smsFormData.sendSmsModal = true;
    // 查询短信签名
    smsFormData.signSearchForm.signStatus = 1; // 审核通过
    getSmsSignPage(smsFormData.signSearchForm).then((res) => {
      if (res.data.success) {
        smsFormData.smsSigns = res.data.result.records;
      }
    });
    // 查询短信模板
    smsFormData.templateSearchForm.templateStatus = 1; // 审核通过
    getSmsTemplatePage(smsFormData.templateSearchForm).then((res) => {
      if (res.data.success) {
        smsFormData.smsTemplates = res.data.result.records;
      }
    });
    getMembersNum();
  };
  // 短信签名变化方法
  const selectSmsSign = (v: any) => {
    if (v && v != '') {
      smsFormData.smsContent = v + smsFormData.smsTemplateContent;
    } else {
      smsFormData.smsContent = `效果预览${smsFormData.smsTemplateContent}`;
    }
  };
  // 短信模板变化方法
  const selectSmsTemplate = (v: any) => {
    smsFormData.smsTemplates.forEach((e) => {
      if (smsFormData.smsForm.messageCode === e.templateCode) {
        smsFormData.smsTemplateContent = e.templateContent;
        smsFormData.smsForm.smsName = e.templateName;
      }
      // 效果预览
      if (smsFormData.smsForm.signName && smsFormData.smsForm.signName != '') {
        smsFormData.smsContent =
          smsFormData.smsForm.signName + smsFormData.smsTemplateContent;
      } else {
        smsFormData.smsContent = smsFormData.smsTemplateContent;
      }
      smsFormData.smsForm.context = smsFormData.smsTemplateContent;
    });
  };
  // 选择接收人事件
  const smsRangeChange = (v: any) => {
    smsFormData.memberNum = 0;
    smsFormData.smsForm.num = 0;
    smsFormData.alreadyCheck = [];
    smsFormData.alreadyCheckShow = [];
    if (v == 1) {
      // 全部会员
      getMembersNum();
    } else if (v == 2) {
      // 自定义选择
      // getMembers();
      if (smsFormData.members && smsFormData.members.length) {
        smsFormData.members.forEach((item: any) => {
          item.selected = false;
        });
      }
    }
  };
  // 根据手机号分页查询会员信息
  const getMembersChange = () => {
    smsFormData.memberSearchParam.pageNumber = 1;
    smsFormData.members = [];
    getMembers();
  };
  // 会员列表触底操作
  const fetchData = () => {
    window.setTimeout(() => {
      // console.log('会员信息/当前会员页数', smsFormData.memberPage, smsFormData.memberSearchParam.pageNumber);
      if (smsFormData.memberPage != smsFormData.memberSearchParam.pageNumber) {
        smsFormData.memberSearchParam.pageNumber += 1;
        getMembers();
      } else {
        bottom.value = true;
      }
    }, 2000);
  };
  // 选择会员
  const selectMember = (item: any) => {
    if (!item.mobile) {
      Message.error('当前用户暂无手机号绑定');
    } else {
      item.selected = true;
      if (smsFormData.alreadyCheck.length == 0) {
        smsFormData.alreadyCheck.push(item.mobile);
        smsFormData.alreadyCheckShow.push(item);
        smsFormData.smsForm.num += 1;
        smsFormData.memberNum += 1;
      } else {
        // 如果已选择数组内存在此用户则不在进行选择
        const result = smsFormData.alreadyCheck.indexOf(item.mobile);
        if (result < 0) {
          smsFormData.smsForm.num += 1;
          smsFormData.memberNum += 1;
          smsFormData.alreadyCheck.push(item.mobile);
          smsFormData.alreadyCheckShow.push(item);
        }
      }
    }
  };
  // 已经选择的人取消选中
  const alreadyCheckClose = (val: any, index: any) => {
    smsFormData.alreadyCheck.splice(index, 1);
    smsFormData.alreadyCheckShow.splice(index, 1);
    smsFormData.members.forEach((item: any) => {
      if (item.selected && item.mobile === val.mobile) {
        item.selected = false;
      }
    });
    smsFormData.smsForm.num -= 1;
    smsFormData.memberNum -= 1;
  };
  // 发送短信详细
  const detailTemplateClick = (data: any) => {
    // console.log('发送短信详细', data);
    smsFormData.smsForm = data;
    smsFormData.alreadyCheck = data.mobile;
    selectSmsSign(data.signName);
    selectSmsTemplate(data.smsName);
    smsFormData.sendSmsModal = true;
  };
  // 发送短信
  const sendSmsSubmit = async () => {
    const auth = await smsFormRef.value?.validate();
    const mobile = smsFormData.alreadyCheck;
    smsFormData.smsForm.mobile = mobile;
    // console.log('发送短信', smsFormData.smsForm);
    if (!auth) {
      sendSms(smsFormData.smsForm).then((res) => {
        if (res.data.success) {
          Message.success('发送成功！');
          smsTablePageRef.value.init();
          smsFormData.sendSmsModal = false;
        }
      });
    }
  };

  // 添加短信模板
  const addTemplate = () => {
    templateFormData.templateModalVisible = true;
    templateFormData.templateModalTitle = '添加短信模板';
    templateFormData.templateModalType = 'add';
    templateFormData.templateForm = {};
  };
  // 编辑短信模板
  const editTemplateClick = (data: any) => {
    templateFormData.templateModalVisible = true;
    templateFormData.templateModalTitle = '修改短信模板';
    templateFormData.templateModalType = 'edit';
    templateFormData.templateForm = data;
  };
  // 短信模板提交
  const templateFormSubmit = async () => {
    const auth = await templateFormRef.value?.validate();
    if (!auth) {
      if (templateFormData.templateModalType === 'add') {
        // 添加短信模板
        addSmsTemplate(templateFormData.templateForm).then((res) => {
          if (res.data.success) {
            Message.success('添加成功！');
            templateFormData.templateModalVisible = false;
            templateTablePageRef.value.init();
          }
        });
      } else if (templateFormData.templateModalType === 'edit') {
        // 提交短信模板
        editSmsTemplate(templateFormData.templateForm).then((res) => {
          if (res.data.success) {
            Message.success('修改成功！');
            templateTablePageRef.value.init();
          }
        });
      }
    }
  };
  // 删除短信模板
  const deleteTemplateClick = (data: any) => {
    modal.confirm({
      title: '确认删除',
      content: '您确认要删除次短信签名？',
      alignCenter: false,
      onOk: async () => {
        const params = { templateCode: data.templateCode };
        deleteSmsTemplate(params).then((res) => {
          if (res.data.success) {
            Message.success('删除成功！');
            templateTablePageRef.value.init();
          }
        });
      },
    });
  };
  // 同步短信模板
  const syncTemplateClick = () => {
    syncSmsTemplate().then((res) => {
      if (res.data.success) {
        Message.success('同步成功！');
        templateTablePageRef.value.init();
      }
    });
  };

  // 添加短信签名
  const addSignClick = () => {
    router.push({ name: 'add-sms-sign' });
  };
  // 编辑短信签名
  const editSignClick = (data: any) => {
    router.push({ name: 'add-sms-sign', query: { id: data.id } });
  };
  // 删除短信签名
  const deleteSignClick = (data: any) => {
    modal.confirm({
      title: '确认删除',
      content: '您确认要删除此短信签名？',
      alignCenter: false,
      onOk: async () => {
        deleteSign(data.id).then((res) => {
          if (res.data.success) {
            Message.success('删除成功！');
            signTablePageRef.value.init();
          }
        });
      },
    });
  };
  // 同步短信签名
  const syncSignClick = () => {
    syncSign().then((res) => {
      if (res.data.success) {
        Message.success('同步成功！');
        signTablePageRef.value.init();
      }
    });
  };
  // 短信tab栏切换
  const clickTabPane = (name: any) => {
    if (name == 'LIST') {
      // 发送任务列表
    } else if (name == 'TEMPLATE') {
      // 短信模板
      templateTablePageRef.value.init();
    } else if (name == 'SIGN') {
      // 短信签名
      signTablePageRef.value.init();
    }
  };
  // 初始化
  onMounted(() => {
    // 分页获取短信记录数据
    smsTablePageRef.value.init();
    // 短信模板初始化
    templateTablePageRef.value.init();
    // 短信签名初始化
    signTablePageRef.value.init();
  });
</script>

<style scoped lang="less">
  .send-setting {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    .left-show {
      width: 230px;
      padding: 20px 30px 20px 10px;
      border: 1px solid #eee;
      background-color: #fafafa;
    }
    .send-form {
      flex: 1;
      padding: 10px;
      border: 1px solid #eee;
      margin-left: 24px;
      background-color: #fafafa;
    }
  }

  .choose-member {
    width: 460px;
    height: 300px;
    margin-bottom: 20px;
  }
  .source-member {
    width: 50%;
  }
  .scroll-card {
    height: 300px;
    width: 100%;
    display: flex;
    align-items: start;
    justify-content: space-between;
    > div {
      width: 49%;
    }
    .list-item-members {
      width: 96%;
      height: 34px;
      line-height: 34px;
      font-size: 12px;
      padding: 0 20px !important;
      color: #999999;
      cursor: pointer;
      border: 1px solid #e5e6eb;
      box-sizing: border-box;
      margin: 2%;
      border-radius: 4px;

      :deep(.arco-list-item-content) {
        display: flex;
        justify-content: space-between;
      }
    }
    .active {
      border: 1px solid #165dff;
    }
    .traget-member {
      .list-item-members {
        border: none;
        padding: 0 !important;
        .list-item-span {
          width: 100%;
          height: 34px;
          background-color: #f2f3f5;
          color: #1d2129;
          font-size: 12px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 20px;
        }
      }
      .checkbox-tag {
        height: 34px;
        width: 100%;
        border: 1px solid transparent;
      }
    }
  }
</style>
