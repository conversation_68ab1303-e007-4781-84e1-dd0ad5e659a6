<template>
  <div class="order-chart">
    <a-card
      title="订单统计"
      class="general-card"
      hoverable
      :bordered="!props.isInline"
      :style="{ width: '100%', marginBottom: props.isInline ? '0px' : '20px' }"
    >
      <recent-time
        :date-type="defaultDateType.date"
        @on-change="handleClickTimeChange"
      ></recent-time>
    </a-card>
    <a-card hoverable title="交易概况" :bordered="!props.isInline">
      <order-generalize :date-type="defaultDateType.date" />
    </a-card>
    <a-card
      hoverable
      title="交易趋势"
      :bordered="!props.isInline"
      :style="{ width: '100%', marginTop: props.isInline ? '0px' : '20px' }"
    >
      <order-chart :date-type="defaultDateType.date" />
    </a-card>
    <a-card
      hoverable
      title="订退单统计"
      :bordered="!props.isInline"
      :style="{ width: '100%', marginTop: props.isInline ? '0px' : '20px' }"
    >
      <order-list :date-type="defaultDateType.date" />
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import recentTime from '@/components/recent-time/index.vue';
  import orderChart from '@/components/order-chart/index.vue';
  import orderList from '@/components/order-chart/list.vue';
  import orderGeneralize from '@/components/order-chart/generalize.vue';
  import { defaultDateType, handleClickTimeChange } from '@/hooks/statistics';

  const props = defineProps({
    // 是否内嵌形式
    isInline: {
      type: Boolean,
      default: false,
    },
  });
</script>

<style scoped lang="less">
</style>
