<template>
  <div class="order-chart">
    <a-spin style="display: block" :loading="loading">
      <div id="orderChart"></div>
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, reactive, watch } from 'vue';
  import { Chart } from '@antv/g2';
  import useLoading from '@/hooks/loading';
  import { PreViewParamsRule } from '@/types/global';
  import { getOrderChart } from '@/api/statisitics';
  import { useUserStore } from '@/store';

  interface ResponseRule {
    chartList: Array<any>;
    orderChart: any;
  }
  const { loading, setLoading } = useLoading();
  const props = defineProps({
    dateType: {
      type: Object,
      default: () => {
        return {
          recent: 'LAST_SEVEN',
          month: '',
        };
      },
    },
  });
  const responseResult = reactive<ResponseRule>({
    chartList: [],
    orderChart: '',
  });

  // 订单请求参数
  const orderParams = reactive<PreViewParamsRule>({
    searchType: props.dateType.recent, // TODAY ,  YESTERDAY , LAST_SEVEN , LAST_THIRTY
    // year: props.dateType.month || new Date().getFullYear(),
    // storeId: useUserStore().userInfo.id,
  });

  // 加载图表
  const chart = () => {
    // 默认已经加载 legend-filter 交互
    const data = responseResult.chartList;

    data.forEach((item: any) => {
      // eslint-disable-next-line prefer-destructuring
      item.createTime = item.createTime.split(' ')[0];
      item.title = '交易额';
    });
    responseResult.orderChart.data(data);

    responseResult.orderChart.tooltip({
      showCrosshairs: true,
      shared: true,
    });

    responseResult.orderChart
      .line()
      .position('createTime*price')
      .label('price')
      .color('title')
      .shape('smooth');

    responseResult.orderChart
      .point()
      .position('createTime*price')
      .label('price')
      .color('title')
      .shape('circle')
      .style({
        stroke: '#fff',
        lineWidth: 1,
      });

    responseResult.orderChart
      .area()
      .position('createTime*price')
      .color('title')
      .shape('smooth');
    responseResult.orderChart.render();
  };
  // 初始化订单的图表
  const initOrderChart = async () => {
    setLoading(true);
    const res = await getOrderChart(orderParams);
    if (res.data.success) {
      responseResult.chartList = res.data.result;
      if (!responseResult.orderChart) {
        responseResult.orderChart = new Chart({
          container: 'orderChart',
          autoFit: true,
          height: 500,
          padding: [70, 70, 70, 70],
        });
      }
    }
    chart();
    setLoading(false);
  };
  onMounted(() => {
    initOrderChart();
  });
  // 监听值的改变 父级值改变
  watch(
    () => props.dateType,
    (val) => {
      orderParams.searchType = val.recent;
      if (val.month) {
        // eslint-disable-next-line prefer-destructuring,no-nested-ternary
        orderParams.month = val.month.split('-')[1]
          ? val.month.split('-')[1].indexOf('0') != -1
            ? val.month.split('-')[1].substr(1)
            : ''
          : '';

        // eslint-disable-next-line prefer-destructuring
        orderParams.year = val.month.split('-')[0];
      }

      initOrderChart();
    },
    {
      deep: true,
      immediate: true,
    }
  );
</script>

<style scoped lang="less">
  #orderChart {
    width: 100%;
  }
  .order-chart {
    background: #fff;
  }
</style>
