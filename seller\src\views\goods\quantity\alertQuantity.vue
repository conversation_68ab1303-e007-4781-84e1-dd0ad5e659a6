<template>
    <div>
      <a-card class="general-card" title="库存预警" :bordered="false">
        <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = { ...apiParams, ...val };}" @search="(val) => {apiParams = { ...apiParams, ...val };}"></searchTable>
        <a-tabs v-model:active-key="stockType" @change="tabsChange">
          <a-tab-pane key="warnList" title="预警商品">
            <a-table ref="warnDataRef" :columns="columnsTable" :data="warnData" :pagination="false">
              <template #quantitySlot="{ record }">{{record.quantity?record.quantity:0}}</template>
              <template #alertQuantitySlot="{ record }">{{record.alertQuantity?record.alertQuantity:0}}</template>
              <template #action="{ record }">
                <a-button type="text" status="normal" @click="openUpdataStockModal(record)">库存</a-button>
              </template>
            </a-table>
            <div class="paginationBox">
              <a-pagination :total="warnDetail.total" :current="apiParams.pageNumber" :page-size="apiParams.pageSize"
                            @change="(number) => {apiParams.pageNumber = number;}"
                            @page-size-change="(number) => {apiParams.pageSize = number;}" ></a-pagination>
            </div>
          </a-tab-pane>
          <a-tab-pane key="warnSetting" title="设置预警">
            <a-table ref="warnSettingRef" :columns="settingColumns" :data="skuAllData" :pagination="false">
              <template #quantitySlot="{ record }">{{record.quantity?record.quantity:0}}</template>
              <template #alertQuantitySlot="{ rowIndex, record }">
                <a-input-number v-model="skuAllData[rowIndex].alertQuantity" class="input-demo" :min="1" :max="100"
                                @change="checkVal(record)" @blur="updateWarnStock(record)" model-event="input"></a-input-number>
              </template>
            </a-table>
            <div class="paginationBox">
              <a-pagination :total="skuAllDetail.total" :current="apiParams.pageNumber" :page-size="apiParams.pageSize"
                            @change="(number) => {apiParams.pageNumber = number;}"
                            @page-size-change="(number) => {apiParams.pageSize = number;}" ></a-pagination>
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-card>

      <!--设置库存-->
      <a-modal v-model:visible="updateStockModalVisible" title="更新库存" ok-text="更新" :width="600"
          @cancel="updateStockModalVisible = false" @ok="updateStock">
        <a-table ref="tablePageRef" :columns="updateStockColumns" :data="stockList">
          <template #authFlagTemplate="{ record }">
            <span v-if="record.authFlag === 'TOBEAUDITED'">待审核</span>
            <span v-else-if="record.authFlag === 'PASS'">通过</span>
            <span v-else-if="record.authFlag === 'REFUSE'">审核拒绝</span>
          </template>
          <template #action="{ record }">
            <a-input-number v-model="record.quantity" :style="{ width: '120px' }" class="input-demo" :min="1" :max="100"
                            @on-change="updateQuantity($event, record)"></a-input-number>
          </template>
        </a-table>

      </a-modal>

    </div>
</template>

<script lang="ts" setup>
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { gradeList, commentStatus, replyStatus } from '@//utils/tools';
  import { Message } from '@arco-design/web-vue';
  import { ref, onMounted, watch } from 'vue';
  import store from '@/utils/storage';
  import uploadFile from '@/api/common';
  import { getGoodsListDataByStockSeller, getGoodsSkuListDataSeller } from '@/api/goods';

  const columnsSearch: Array<SearchRule> = [
    {label: '会员名称', model: 'memberName', disabled: false, input: true,},
    {label: '商品分类', model: 'goodsName', disabled: false, category: true,},
  ];
  const apiParams = ref({
    pageNumber: 1,
    pageSize: 10
  });
  const stockType = ref('warnList');
  const columnsTable = [
    {title: '商品名称', minWidth: 300, dataIndex: 'goodsName',},
    {title: '库存', width: 200, dataIndex: 'quantity', slotName: 'quantitySlot'},
    {title: '预警值', width: 200, dataIndex: 'alertQuantity', slotName: 'alertQuantitySlot',},
    {title: '操作', width: 200, slotName: 'action'},
  ];
  const warnData = ref([]);
  const warnDetail = ref({ total: 0 });
  const updateStockModalVisible = ref(false); // 更新库存模态框显隐
  const updateStockColumns = [
    {title: "sku规格", minWidth: 200, dataIndex: 'simpleSpecs'},
    {title: "审核状态", width: 100, dataIndex: 'authFlag', slotName: 'authFlagTemplate'},
    {title: '操作', width: 120, slotName: 'action'},
  ];
  const stockList = ref<Array<any>>([]);
  const selectedSku = ref<Array<any>>();

  const settingColumns = [
    {title: '商品名称', minWidth: 300, dataIndex: 'goodsName',},
    {title: '库存', width: 300, dataIndex: 'quantity', slotName: 'quantity'},
    {title: '预警值', width: 300, dataIndex: 'alertQuantity', slotName: 'alertQuantitySlot',},
  ];
  const skuAllData = ref<any>([]);
  const skuAllDetail = ref({ total: 0 });



  // 获取商品列表数据
  const init = async (params = apiParams.value) => {
    if(stockType.value == 'warnList'){
      const res = await getGoodsListDataByStockSeller(params);
      if (res.data.success) {
        warnData.value = res.data.result.goodsSkuPage.records;
        warnDetail.value = res.data.result.goodsSkuPage;
      }
    } else if (stockType.value == 'warnSetting') {
      //调用获取全部sku
      const res = await getGoodsSkuListDataSeller(params);
      if (res.data.success) {
        skuAllData.value = res.data.result.records;
        skuAllDetail.value = res.data.result;
      }
    }
  };
  // tab切换
  const tabsChange = (val: any) => {
    // 重置搜索条件
    // 重新加载数据
    init();
  };
  // 设置库存
  const openUpdataStockModal = (row: any) => {
    console.log('设置库存');
    stockList.value = [];
    selectedSku.value = JSON.parse(JSON.stringify(row));
    stockList.value.push(selectedSku.value);
    updateStockModalVisible.value = true;
  };
  const updateQuantity = (event: any, row: any) => {
    row.quantity = event;
  };
  const updateStock = () => {
    console.log('更新ok', stockList.value);
  };

  // 检测输入值是否正确
  const checkVal = (row: any) => {
    console.log('检测输入值是否正确');
  };
  // 修改预警值
  const updateWarnStock = (row: any) => {
    console.log('修改预警值');
  };
  onMounted(() => {
    init();
  });
  watch(
      () => [apiParams],
      (val: any) => {
        init();
      },
      { deep: true }
  );
</script>

<style lang="less" scoped>
  .paginationBox {
    margin-top: 18px;
    display: flex;
    flex-direction: row-reverse;
  }
</style>