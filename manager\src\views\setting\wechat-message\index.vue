<template>
  <a-card class="general-card" title="微信消息" :bordered="false">
    <a-tabs default-active-key="WECHAT">
      <a-tab-pane key="WECHAT" title="微信消息">
        <a-col :span="16" style="margin-bottom: 10px">
          <a-space>
            <a-button type="primary" @click="weChatSync">
              同步微信消息
            </a-button>
          </a-space>
        </a-col>
        <tablePage
          ref="tablePageRef"
          :columns="WechatColumnsTable"
          :methods="sortMethods"
          :api="getWechatMessagePage"
          @delete="wechatHandleDelete"
          @update="wechatHandleEdit"
          :bordered="true"
        >
          <template #enable="{ data }">
            <a-tag>{{ data.enable == true ? '开启' : '关闭' }}</a-tag>
          </template>
        </tablePage>
      </a-tab-pane>
      <a-tab-pane key="WECHATMP" title="微信小程序订阅消息">
        <a-col :span="16" style="margin-bottom: 10px">
          <a-space>
            <a-button type="primary" @click="weChatSync('mp')">
              同步微信小程序订阅消息
            </a-button>
          </a-space>
        </a-col>
        <tablePage
          ref="tablePageRef"
          :columns="WechatColumnsTable"
          :methods="sortMethods"
          :api="getWechatMPMessagePage"
          @delete="wechatmpHandleDelete"
          @update="wechatmpHandleEdit"
          :bordered="true"
        >
          <template #enable="{ data }">
            <a-tag>{{ data.enable == true ? '开启' : '关闭' }}</a-tag>
          </template>
        </tablePage>
      </a-tab-pane>
    </a-tabs>
    <!-- 编辑微信消息modal -->
    <a-modal
      v-model:visible="wechatFormData.enableAddModal"
      :align-center="false"
      :footer="false"
    >
      <template #title> 微信设置 </template>
      <a-form ref="formRef" :model="wechatFormData.form" @submit="handleAddOk">
        <a-form-item
          field="name"
          label="模板名称"
          :validate-trigger="['change']"
        >
          <a-input v-model="wechatFormData.form.name" :disabled="true" />
        </a-form-item>
        <a-form-item
          field="first"
          label="头部信息"
          :validate-trigger="['change']"
        >
          <a-input v-model="wechatFormData.form.first" />
        </a-form-item>
        <a-form-item field="remark" label="备注" :validate-trigger="['change']">
          <a-textarea
            v-model="wechatFormData.form.remark"
            :max-length="100"
            allow-clear
            show-word-limit
          />
        </a-form-item>
        <a-form-item
          field="enable"
          label="是否开启"
          :validate-trigger="['change']"
        >
          <a-radio-group v-model="wechatFormData.form.enable" type="button">
            <a-radio :value="true">开启</a-radio>
            <a-radio :value="false">关闭</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="操作">
          <a-button :loading="wechatFormData.formLoading" html-type="submit"
          type="primary">保存</a-button
          >
        </a-form-item>
      </a-form>
    </a-modal>
    <!-- 编辑微信小程序订阅消息modal -->
    <a-modal
      v-model:visible="wechatMPFormData.enableAddModal"
      :align-center="false"
      :footer="false"
    >
      <template #title> 微信小程序订阅消息 </template>
      <a-form
        ref="formRef"
        :model="wechatMPFormData.form"
        @submit="wechatMPHandleOk"
      >
        <a-form-item
          field="enable"
          label="是否开启"
          :validate-trigger="['change']"
        >
          <a-radio-group v-model="wechatMPFormData.form.enable" type="button">
            <a-radio :value="true">开启</a-radio>
            <a-radio :value="false">关闭</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="操作">
          <a-button :loading="wechatMPFormData.formLoading" html-type="submit"
          type="primary">保存</a-button
          >
        </a-form-item>
      </a-form>
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">
  import tablePage from '@/components/table-pages/index.vue';
  import { MethodsRule, ColumnsDataRule } from '@/types/global';
  import {
    wechatMessageSync,
    getWechatMessagePage,
    editWechatMessageTemplate,
    delWechatMessageTemplate,
    wechatMPMessageSync,
    getWechatMPMessagePage,
    editWechatMPMessageTemplate,
    delWechatMPMessageTemplate,
  } from '@/api/setting';
  import { ref, watch, reactive } from 'vue';
  import useCurrentInstance from '@/hooks/useCurrentInstance';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { Message } from '@arco-design/web-vue';

  const type = ref<string>('RESOURCE');
  const formRef = ref<FormInstance>();

  // 获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;
  interface formInterface {
    enableAddModal: boolean;
    formLoading: boolean;
    fid: string | number;
    form: {
      enable: boolean;
      [key: string]: any;
    };
    [key: string]: any;
  }
  const tablePageRef = ref<any>();
  const modalTitle = ref<string>('');
  const WechatColumnsTable: ColumnsDataRule[] = [
    {
      title: '模板编号',
      dataIndex: 'code',
    },
    {
      title: '是否开启',
      dataIndex: 'enable',
      slot: true,
      slotTemplate: 'enable',
    },

    {
      title: '模板名称',
      dataIndex: 'name',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
    },
  ];

  const sortMethods: MethodsRule = {
    title: '操作',
    width: 250,
    methods: [
      {
        title: '编辑',
        callback: 'update',
        type:'text',
        status:'warning'
      },
      {
        title: '删除',
        callback: 'delete',
        type:'text',
        status:'danger'
      },
    ],
  };
  // 数据集
  const wechatFormData = reactive<formInterface>({
    enableAddModal: false,
    formLoading: false,
    fid: '', // 当前form的ids
    form: {
      name: '',
      first: '',
      remark: '',
      enable: false,
    }, // 表单提交数据
  });
  // 数据集
  const wechatMPFormData = reactive<formInterface>({
    enableAddModal: false,
    formLoading: false,
    fid: '', // 当前form的ids
    form: {
      enable: false,
    }, // 表单提交数据
  });
  // 编辑微信消息模板
  const wechatHandleEdit = (val: any) => {
    wechatFormData.enableAddModal = true;
    if (val) {
      Object.keys(val.record).forEach((key) => {
        // eslint-disable-next-line no-unused-expressions,no-prototype-builtins
        wechatFormData.form.hasOwnProperty(key)
          ? (wechatFormData.form[key] = val.record[key])
          : '';
      });
      wechatFormData.fid = val.record.id;
      wechatFormData.enableAddModal = true;
    }
  };

  // 微信设置保存
  async function handleAddOk() {
    const res = await editWechatMessageTemplate(
      wechatFormData.fid,
      wechatFormData.form
    );
    if (res.data.success) {
      Message.success('微信模板修改成功');
      wechatFormData.enableAddModal = false;
      tablePageRef.value.init();
    }
  }
  async function wechatMPHandleOk() {
    const res = await editWechatMPMessageTemplate(
      wechatMPFormData.fid,
      wechatMPFormData.form
    );
    if (res.data.success) {
      Message.success('微信消息订阅模板修改成功');
      wechatMPFormData.enableAddModal = false;
      tablePageRef.value.init();
    }
  }
  // 删除微信消息模板
  const wechatHandleDelete = (v: any) => {
    modal.confirm({
      title: '确认删除',
      content: `确定删除此微信消息模板?`,
      alignCenter: false,
      onOk: async () => {
        console.log(v.id, 'vvvvvv');
        const res = await delWechatMessageTemplate(v.record.id);
        if (res.data.success) {
          Message.success('微信模板删除成功');
          tablePageRef.value.init();
        }
      },
    });
  };
  // 编辑微信小程序订阅消息
  const wechatmpHandleEdit = (val: any) => {
    wechatMPFormData.enableAddModal = true;
    if (val) {
      Object.keys(val.record).forEach((key) => {
        // eslint-disable-next-line no-unused-expressions,no-prototype-builtins
        wechatMPFormData.form.hasOwnProperty(key)
          ? (wechatMPFormData.form[key] = val.record[key])
          : '';
      });
      wechatMPFormData.fid = val.record.id;
      wechatMPFormData.enableAddModal = true;
    }
  };
  // 删除微信小程序订阅消息
  const wechatmpHandleDelete = (v: any) => {
    modal.confirm({
      title: '确认删除',
      content: `确定删除此微信消息订阅模板?`,
      alignCenter: false,
      onOk: async () => {
        const res = await delWechatMPMessageTemplate(v.record.id);
        if (res.data.success) {
          Message.success('微信消息订阅删除成功');
          tablePageRef.value.init();
        }
      },
    });
  };
  // 同步消息
  const weChatSync = (mp: any) => {
    modal.confirm({
      title: '提示',
      content: `确认要初始化微信小程序消息?`,
      alignCenter: false,
      onOk: async () => {
        if (mp == 'mp') {
          const res = await wechatMPMessageSync();
          if (res.data.success) {
            Message.success('微信小程序消息订阅初始化');
            tablePageRef.value.init();
          }
        } else {
          const res = await wechatMessageSync();
          if (res.data.success) {
            Message.success('微信消息模板初始化成功');
            tablePageRef.value.init();
          }
        }
      },
    });
  };
</script>
