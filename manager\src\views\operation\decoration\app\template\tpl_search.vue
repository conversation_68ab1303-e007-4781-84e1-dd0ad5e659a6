<template>
  <div class="search" px-16px
    :style="{ lineHeight: props.res.data.height + 'px', textAlign: props.res.data.align, color: props.res.data.textColor, height: props.res.data.height + 'px', backgroundColor: props.res.data.background, borderRadius: (props.res.border === 'round' ? props.res.data.round : 0) + 'px' }">
    {{ props.res.data.text }}
  </div>
</template>

<script setup lang="ts">

import { DragRule } from '@/views/operation/decoration/app/models/types';
const props = defineProps<{
  res: DragRule
}>()


</script>

<style scoped>
.search {

  width: 100%;
}
</style>
