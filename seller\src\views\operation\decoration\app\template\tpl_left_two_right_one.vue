<template>
  <div flex flex-a-c>
    <div>
      <div>
        <img w-182px h-84px :src="props.res.data.list[0].img" />
      </div>
      <div>
        <img w-182px h-84px :src="props.res.data.list[1].img" />
      </div>
    </div>
    <div>
      <img w-182px h-168px :src="props.res.data.list[2].img" />
    </div>

  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  res: any
}>()
</script>

<style scoped>
img {
  display: block;
}
</style>
