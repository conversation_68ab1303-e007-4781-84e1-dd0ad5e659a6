<script lang="jsx">
import { defineEmits, defineExpose } from 'vue'
import { Button, Notification } from '@arco-design/web-vue' // 根据实际情况调整导入路径

export default {
  emits: ['click'],
  setup(props, { emit }) {
    const handleClick = (id, talkId) => {
      Notification.remove(id)
      emit('click', talkId)
    }

    const tips = (title, content, talkId) => {
      const id = Date.now()

      // 使用一个局部组件来处理按钮的点击事件
      const FooterButton = {
        render() {
          return (
            <Button
              type="primary"
              size="small"
              onClick={() => handleClick(id, talkId)}
            >
              查看
            </Button>
          )
        },
      }

      Notification.info({
        title,
        content,
        id,
        closable: true,
        duration: 8000,
        footer: <FooterButton />, // 使用局部组件作为footer
      })
    }

    defineExpose({ tips })

    return { tips }
  },
}
</script>

<template>
  <div />
</template>
