<template>
  <a-card class="general-card" title="商品模板" :bordered="false">
    <searchTable
      :columns="columnsSearch"
      @reset="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
      @search="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
    ></searchTable>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getDraftGoodsListData"
      :api-params="apiParams"
      :bordered="true"
      @delete="handleDelete"
      @edit="editGoods"
    />
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { getDraftGoodsListData, deleteDraftGoods } from '@/api/goods';
  import useCurrentInstance from '@/hooks/useCurrentInstance';
  import { Message } from '@arco-design/web-vue';
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const tablePageRef = ref<any>();
  // 获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;
  const columnsSearch: Array<SearchRule> = [
    {
      label: '商品名称',
      model: 'goodsName',
      disabled: false,
      input: true,
    },
    {
      label: '商品编号',
      model: 'id',
      disabled: false,
      input: true,
    },
  ];

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '编号',
      dataIndex: 'id',
    },
    {
      title: '商品',
      dataIndex: 'goodsName',
      slot: true,
      slotData: {
        goods: {
          goodsName: 'goodsName',
          goodsImage: 'original',
        },
      },
    },
    {
      title: '商品价格',
      dataIndex: 'price',
      currency: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
    },
  ];

  const sortMethods: MethodsRule = {
    title: '操作',
    width: 200,
    methods: [
      {
        title: '编辑',
        callback: 'edit',
        type: 'text',
        status: 'warning',
      },
      {
        title: '删除',
        callback: 'delete',
        type: 'text',
        status: 'danger',
      },
    ],
  };
  // 删除商品模板
  const handleDelete = (v: any) => {
    modal.confirm({
      title: '确认删除',
      content: `确定删除id为${v.record.id}模板吗?`,
      alignCenter: false,
      onOk: async () => {
        const res = await deleteDraftGoods(v.record.id);
        if (res.data.success) {
          Message.success('删除成功');
          tablePageRef.value.init();
        }
      },
    });
  };
  // 查看详情
  const editGoods = (v: any) => {
    router.push({
      name: 'goods-operation',
      query: {
        draftId: v.record.id,
      },
    });
  };
  const apiParams = ref({
    saveType: 'TEMPLATE',
  });
</script>

<style scoped></style>
