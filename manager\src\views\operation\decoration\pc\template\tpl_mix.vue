<template>
  <div flex flex-a-c flex-j-sb>
    <div w-584px h-343px v-auto-animate>
      <goodsModal :res="props.res.data.leftData" />
      <!-- <goodsModal :res="props.res.data.leftData" v-if="props.res.data.leftModal === 'cardGoodsModel'" />
      <brandModal :res="props.res.data.leftData" v-else /> -->
    </div>
    <div w-584px h-343px v-auto-animate rounded-10px class="box-shadow">
      <brandModal :res="props.res.data.rightData" />
      <!-- <goodsModal :res="props.res.data.rightData" v-if="props.res.data.rightModal === 'cardGoodsModel'" />
      <brandModal :res="props.res.data.rightData" v-else /> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import goodsModal from '@/views/operation/decoration/pc/layout/modules/mix/goodsModal.vue';
import brandModal from '@/views/operation/decoration/pc/layout/modules/mix/brandModal.vue';
import { DragRule } from '@/views/operation/decoration/app/models/types';

const props = defineProps<{
  res: DragRule,
}>()

</script>

<style lang="less" scoped>
.box-shadow {
  box-shadow: 0 1px 13px 0 #e5e5e5;
}


</style>
