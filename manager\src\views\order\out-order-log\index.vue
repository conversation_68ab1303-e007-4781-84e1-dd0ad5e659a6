<template>
    <a-card class="general-card" title="分账记录" :bordered="false">
      <a-tabs @change="(val)=>{apiParams.status = val}" :default-active-key="outOrderStatus">
        <a-tab-pane key="PROCESSING" title="处理中"></a-tab-pane>
        <a-tab-pane key="FINISHED" title="分账完成"></a-tab-pane>
      </a-tabs>
      <!-- 搜索 -->
      <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
      </searchTable>
      <!-- 表格 -->
      <tablePage
        ref="tablePageRef"
        :columns="columnsTable"
        :methods="sortMethods"
        :api="outOrderLog"
        @detail = "(v:any)=> $openWindow({
          name: 'sub-ledger-detail',
          query: {id: v.record.outOrderNo},
        })"
        :api-params="apiParams"
        :bordered="true"
      />
    </a-card>
  </template>
  
  <script setup lang="ts">
    import { outOrderLog } from '@/api/order';
    import searchTable from '@/components/search-column/index.vue';
    import tablePage from '@/components/table-pages/index.vue';
    import { ColumnsDataRule, MethodsRule, SearchRule } from '@/types/global';
    import { outOrderSelect } from '@/utils/tools';
    import { ref } from 'vue';

    const outOrderStatus = ref<string>('FINISHED');
    const apiParams = ref<any>({status:outOrderStatus.value});
    const tablePageRef = ref('');
    const columnsSearch: Array<SearchRule> = [
      {
        label: '订单单号',
        model: 'orderSn',
        disabled: false,
        input: true,
      },
      {
        label: '二级商户号',
        model: 'subMchid',
        disabled: false,
        input: true,
      },
      {
        label: '创建时间',
        disabled: false,
        datePicker: {
          type: 'range',
        },
      },
    ];
  
    // 表格搜索列表
    const columnsTable: ColumnsDataRule[] = [
      {
        title: '微信分账单号',
        dataIndex: 'orderId',
      },
      
      {
        title: '订单单号',
        dataIndex: 'orderSn',
      },
      {
        title: '商户分账单号',
        dataIndex: 'outOrderNo',
      },
      
      {
        title: '分账金额',
        dataIndex: 'amount',
        currency: true,
      },
      {
        title: '分账单状态',
        dataIndex: 'status',
        slot: true,
        slotData: {
          badge: outOrderSelect,
        },
      },
      {
        title: '二级商户号',
        dataIndex: 'subMchid',
      },
      {
        title: '微信支付订单号',
        dataIndex: 'transactionId',
      },
      
      {
        title: '创建时间',
        width: 200,
        dataIndex: 'createTime',
      },
    ];
  // 操作列表  
  const sortMethods: MethodsRule = {
    title: '操作',
    width: 100,
    methods: [
      {
        title: '查看',
        callback: 'detail',
        type:"text", 
        status:"success"
      },
    ],
  };

  </script>
  
  <style scoped></style>
  