import { ParamsRule } from '@/types/global';
import request, { Method } from '@/utils/axios';

/**
 * 获取普通商品订单
 */
export function getOrderList(params: ParamsRule) {
  return request({
    url: '/order/order',
    method: Method.GET,
    needToken: true,
    params
  });
}
/**
 * 获取售后订单列表
 */
export function afterSaleOrderPage(params: ParamsRule) {
  return request({
    url: '/order/afterSale/page',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 * 获取交易投诉信息
 */
export function getComplainPage(params: ParamsRule) {
  return request({
    url: '/order/complain',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 * 平台收款记录
 */
export function paymentLog(params: ParamsRule) {
  return request({
    url: '/payment/paymentLog',
    method: Method.GET,
    needToken: true,
    params,
  });
}
/**
 * 付款单对账
 */
export function paymentReconciliation() {
  return request({
    url: '/payment/paymentLog',
    method: Method.PUT,
    needToken: true,
  });
}
/**
 * 平台退款记录
 */
export function refundLog(params: ParamsRule) {
  return request({
    url: '/payment/refundLog',
    method: Method.GET,
    needToken: true,
    params,
  });
}
/**
 * 退款单对账
 */
export function refundReconciliation() {
  return request({
    url: '/payment/refundLog',
    method: Method.PUT,
    needToken: true,
  });
}

export function getReceiptPage(params: ParamsRule) {
  return request({
    url: '/trade/receipt',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 * 获取售后原因分页列表
 */
export function getAfterSaleReasonPage(params: ParamsRule) {
  return request({
    url: '/order/afterSaleReason/getByPage',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 * 删除售后原因
 */
export function delAfterSaleReason(id: string | number) {
  return request({
    url: `/order/afterSaleReason/delByIds/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}
//  订单付款
export function orderDetail(sn: any) {
  return request({
    url: `/order/order/${sn}`,
    method: Method.GET,
    needToken: true,
  });
}
// 修改收货地址
export function editOrderConsignee(sn: any, params: any) {
  return request({
    url: `/order/order/update/${sn}/consignee`,
    method: Method.POST,
    needToken: true,
    params
  })
}
/**
 * 添加售后原因
 */
export function addAfterSaleReason(params: any) {
  return request({
    url: `/order/afterSaleReason`,
    method: Method.POST,
    needToken: true,
    params,
  });
}

/**
 * 修改售后原因
 */
export function editAfterSaleReason(id: string | number, params: any) {
  return request({
    url: `/order/afterSaleReason/update/${id}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
// 订单重新创建第三方订单
export function orderReorderAgain(sn: any) {
  return request({
    url: `/order/order/reorderAgain/${sn}`,
    method: Method.PUT,
    needToken: true
  });
}

// 订单取消
export function ordersCancel(sn: any, params: any) {
  return request({
    url: `/order/order/${sn}/cancel`,
    method: Method.POST,
    needToken: true,
    params,
  });
}

//  修改订单价格
export function updateOrderPrice(sn: any, params: any) {
  return request({
    url: `/order/order/update/${sn}/price`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
//  订单付款
export function orderPay(sn: string | number) {
  return request({
    url: `/order/order/${sn}/pay`,
    method: Method.POST,
    needToken: true,
  });
}
//  获取售后订单详情
export function getAfterSaleOrderDetail(sn:string) {
  return request({
    url: `/order/afterSale/get/${sn}`,
    method: Method.GET,
    needToken: true,
  });
}
// 平台退款
export function refundPrice(afterSaleSn: string | number, params: any) {
  return request({
    url: `/order/afterSale/refund/${afterSaleSn}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
// 售后单商家收货信息
export function storeAddress(sn: number | string) {
  return request({
    url: `/order/afterSale/getStoreAfterSaleAddress/${sn}`,
    method: Method.GET,
    needToken: true,
  });
}
// 售后单查询物流
export function getAfterSaleTraces(sn: number | string) {
  return request({
    url: `/order/afterSale/getDeliveryTraces/${sn}`,
    method: Method.GET,
    needToken: true,
  });
}
// 商家审核
export function afterSaleSellerReview(sn: string, params: any) {
  return request({
    url: `/order/afterSale/review/${sn}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
//  获取交易投诉详细信息
export function getOrderComplainDetail(id: any) {
  return request({
    url: `/order/complain/${id}`,
    method: Method.GET,
    needToken: true,
  });
}
//  交易投诉对话
export function addOrderCommunication(params: ParamsRule) {
  return request({
    url: `/order/complain/communication`,
    method: Method.POST,
    needToken: true,
    params
  });
}
//  交由商家申诉
export function storeComplain(params: any) {
  return request({
    url: `/order/complain/status`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
//  平台仲裁
export function orderComplete(id: any, params: any) {
  return request({
    url: `/order/complain/complete/${id}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
//  获取分账记录分页
export function withdrawLog(params: any) {
  return request({
    url: `/payment/withdrawLog`,
    method: Method.GET,
    needToken: true,
    params
  });
}

//  补差记录分页
export function subsidiesLog(params:any) {
  return request({
    url: `/payment/subsidiesLog`,
    method: Method.GET,
    needToken: true,
    params
  });
}

//  进行补差
export function subsidies(id:string | number) {
  return request({
    url: `/payment/subsidiesLog/subsidies/${id}`,
    method: Method.PUT,
    needToken: true,
  });
}

// 获取提现记录分页
export function outOrderLog (params:ParamsRule) {
  return request({
    url: `/payment/outOrderLog`,
    method: Method.GET,
    needToken: true,
    params
  });
}

// 通过分账单号获取分账详情列表
export function detailLog (outOrderNo: any) {
  return request({
    url: `/payment/outOrderLog/getDetailLog/${outOrderNo}`,
    method: Method.GET,
    needToken: true,
  });
}

// 交易详情
export function tradeDetail (sn: any) {
  return request({
    url: `/order/order/getTrade/${sn}`,
    method: Method.GET,
    needToken: true,
  });
}
