<template>
  <a-card class="general-card" title="拼团活动" :bordered="false">
    <a-tabs @change="(val)=>{apiParams.promotionStatus = val}" :default-active-key="pintuanStatus">
      <a-tab-pane key="NEW" title="未开始"></a-tab-pane>
      <a-tab-pane key="START" title="已开始"></a-tab-pane>
      <a-tab-pane key="END" title="已结束"></a-tab-pane>
      <a-tab-pane key="CLOSE" title="已关闭"></a-tab-pane>
    </a-tabs>
    <searchTable :columns="columnsSearch" time-type="timestamp" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <tablePage ref="tablePageRef" :columns="columnsTable" :methods="sortMethods" :api="getPintuanList"
      :api-params="apiParams" :bordered="true">
      <template #time="{ data }">{{ data.startTime }}<span style="margin: 0 10px">-</span>{{ data.endTime }}</template>
      <template #btnList="{ data }">
        <a-space>
          <a-button 
           @click="view(data)" type="text" status="success">查看</a-button>
          <a-button type="text" status="danger" v-if="data?.promotionStatus == 'START' || data?.promotionStatus == 'NEW'" 
            @click="close(data)">
            关闭</a-button>
        </a-space>
      </template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
import { getPintuanList } from '@/api/promotion';
import searchTable from '@/components/search-column/index.vue';
import tablePage from '@/components/table-pages/index.vue';
import { ColumnsDataRule, MethodsRule, SearchRule } from '@/types/global';
import { promotionStatus, promotionStatusSelect } from '@/utils/tools';
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const tablePageRef = ref('');
const pintuanStatus = ref<string>('START');
const apiParams = ref<any>({ sort: 'startTime',promotionStatus:pintuanStatus.value });
const columnsSearch: Array<SearchRule> = [
  {
    label: '活动名称',
    model: 'promotionName',
    disabled: false,
    input: true,
  },
  {
    label: '活动时间',
    model: 'selectDate',
    disabled: false,
    datePicker: {
      type: 'range',
    },
  },
];

const columnsTable: ColumnsDataRule[] = [
  {
    title: '活动名称',
    dataIndex: 'promotionName',
  },
  {
    title: '活动时间',
    dataIndex: 'time',
    width: 400,
    slot: true,
    slotTemplate: 'time',
  },

  {
    title: '状态',
    dataIndex: 'promotionStatus',
    slot: true,
    slotData: {
      badge: promotionStatus,
    },
  },
];

const sortMethods: MethodsRule = {
  title: '操作',
  width: 250,
  methods: [
    {
      slot: true,
      slotTemplate: 'btnList',
    },
  ],
};
// 查看
const view = (val: any) => {
  router.push({
    name: 'pintuan-goods', query: {
      id: val.id
    }
  })
  console.log(val);
}
// 关闭
const close = (val: any) => {
  console.log(val);
}
</script>
