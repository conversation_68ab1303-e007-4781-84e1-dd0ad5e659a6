<template>
  <div>
    <a-card :style="{ width: '100%' }">
      <a-button class="mb_10" type="primary" status="danger" @click="auditHandler"
        v-if="shopForm.storeDisable === 'APPLYING'">审核</a-button>
      <a-tabs v-model:activeKey="tabName">
        <a-tab-pane key="base" title="基本信息" disabled>
          <a-form ref="baseFormRef" :model="shopForm">
            <a-divider orientation="left">基本信息</a-divider>
            <div v-if="isRead" class="mask">只读不可修改</div>
            <div>
              <a-form-item field="memberName" label="会员名称" :rules="[REQUIRED]">
                <div class="item">
                  <a-input disabled v-model="shopForm.memberName"></a-input>
                  <a-button v-if="!route.query.id" @click="addVip">
                    选择会员
                  </a-button>
                </div>
              </a-form-item>
              <a-form-item field="storeName" label="店铺名称" :rules="[REQUIRED, VARCHAR2TO200]">
                <a-input v-model="shopForm.storeName" allow-clear :style="{ width: '350px' }"></a-input>
              </a-form-item>
              <a-form-item field="selfOperated" label="是否自营" :rules="[REQUIRED]">
                <a-radio-group type="button" v-model="shopForm.selfOperated">
                  <a-radio :value="true">自营</a-radio>
                  <a-radio :value="false">非自营</a-radio>
                </a-radio-group>
              </a-form-item>
              <!--<a-form-item field="shopCenter" label="店铺定位">-->
                <!--<a-button type="primary" status="danger">点击获取店铺定位</a-button>-->
              <!--</a-form-item>-->
              <a-form-item field="storeAddressIdPath" label="店铺所在地" :rules="[REQUIRED]">
                <city @callback="storeAddressPathCallback" :ids="shopForm.storeAddressIdPath" :address="shopForm.storeAddressPath" :key="isShowCity" />
                <!--<a-input disabled v-model="shopForm.storeAddressPath" :style="{ width: '350px' }"></a-input>-->
              </a-form-item>
              <a-form-item field="storeAddressDetail" label="店铺详细地址" :rules="[REQUIRED]">
                <a-input v-model="shopForm.storeAddressDetail" allow-clear :style="{ width: '350px' }"></a-input>
              </a-form-item>
              <a-form-item field="storeLogo" label="店铺logo">
                <img :src="shopForm.storeLogo" style="width: 100px;height: 100px;" v-if="shopForm.storeLogo">
                <div>
                  <a-button type="primary" @click="handleClickImg('storeLogo')" style="margin-left: 20px;">选择图片</a-button>
                </div>
              </a-form-item>
              <a-form-item field="storeDesc" label="店铺简介" :rules="[REQUIRED, VARCHAR6TO200]">
                <a-textarea :max-length="200" allow-clear show-word-limit style="width: 350px" v-model="shopForm.storeDesc" auto-size />
              </a-form-item>
              <br>
              <a-divider orientation="left">退货收件地址</a-divider>
              <a-form-item label="收件人姓名">
                <a-input v-model="shopForm.salesConsigneeName" :style="{ width: '350px' }"></a-input>
              </a-form-item>
              <a-form-item label="收件人手机">
                <a-input v-model="shopForm.salesConsigneeMobile" :style="{ width: '350px' }"></a-input>
              </a-form-item>
              <a-form-item field="salesConsigneeAddressId" label="地址信息">
                <city @callback="salesConsigneeAddressPathCallback" :ids="shopForm.salesConsigneeAddressId" :address="shopForm.salesConsigneeAddressPath" :key="isShowCity" />
              </a-form-item>
              <a-form-item label="详细地址">
                <a-input v-model="shopForm.salesConsigneeDetail" :style="{ width: '350px' }"></a-input>
              </a-form-item>
            </div>
          </a-form>
        </a-tab-pane>
        <a-tab-pane key="entry" title="入驻信息" disabled>
          <a-form ref="entryFormRef" :model="shopForm">
            <div v-if="isRead" class="mask">只读不可修改</div>
            <a-divider orientation="left">公司信息</a-divider>
            <div>
              <a-form-item field="companyName" label="公司名称" :rules="[REQUIRED, VARCHAR2TO200]">
                <a-input v-model="shopForm.companyName" allow-clear :style="{ width: '350px' }"></a-input>
              </a-form-item>
              <a-form-item field="companyPhone" label="公司电话" :rules="[REQUIRED, TELEPHONE]">
                <a-input v-model="shopForm.companyPhone" allow-clear :style="{ width: '350px' }"></a-input>
              </a-form-item>
              <a-form-item field="companyAddressIdPath" label="公司所在地" :rules="[REQUIRED]">
                <city @callback="companyAddressPathCallback" :ids="shopForm.companyAddressIdPath" :address="shopForm.companyAddressPath" :key="isShowCity" />
              </a-form-item>
              <a-form-item field="companyAddress" label="公司详细地址" :rules="[REQUIRED]">
                <a-input v-model="shopForm.companyAddress" allow-clear :style="{ width: '350px' }"></a-input>
              </a-form-item>
              <a-form-item field="employeeNum" label="员工总数" :rules="[REQUIRED]">
                <a-input-number v-model="shopForm.employeeNum" :style="{ width: '350px' }" placeholder="Please Enter" class="input-demo" :min="1" :max="9999999" />
              </a-form-item>
              <a-form-item field="registeredCapital" label="注册资金" :rules="[REQUIRED]">
                <a-input-number v-model="shopForm.registeredCapital" :style="{ width: '350px' }" placeholder="Please Enter" class="input-demo" :min="1" :max="9999999" />
                <span style="margin-left: 10px">万</span>
              </a-form-item>
              <a-form-item field="linkName" label="联系人姓名" :rules="[REQUIRED, VARCHAR2TO20]">
                <a-input v-model="shopForm.linkName" allow-clear :style="{ width: '350px' }"></a-input>
              </a-form-item>
              <a-form-item field="linkPhone" label="联系人手机" :rules="[REQUIRED, MOBILE]">
                <a-input v-model="shopForm.linkPhone" allow-clear :style="{ width: '350px' }" :max-length="11"></a-input>
              </a-form-item>
              <a-form-item field="companyEmail" label="电子邮箱" :rules="[REQUIRED, EMAIL]">
                <a-input v-model="shopForm.companyEmail" allow-clear :style="{ width: '350px' }"></a-input>
              </a-form-item>
              <a-divider orientation="left">营业执照信息</a-divider>
              <a-form-item field="licenseNum" label="营业执照号" :rules="[REQUIRED, LICENSENUM]">
                <a-input v-model="shopForm.licenseNum" :max-length="18" allow-clear :style="{ width: '350px' }"></a-input>
              </a-form-item>
              <a-form-item field="scope" label="法定经营范围" :rules="[REQUIRED]">
                <!--<a-input v-model="shopForm.scope" allow-clear :style="{ width: '350px' }"></a-input>-->
                <a-textarea allow-clear show-word-limit style="width: 350px;" v-model="shopForm.scope" auto-size />
                <!--<a-textarea :max-length="200" allow-clear show-word-limit style="width: 350px" v-model="shopForm.storeDesc" auto-size />-->
              </a-form-item>
              <a-form-item field="licencePhoto" label="营业执照电子版" :rules="[REQUIRED]">
                <!--<a-avatar :size="100" shape="square" style="margin-right: 20px;height: 100px; width: 100px" v-if="shopForm.licencePhoto">-->
                <!--<icon-plus v-if="!shopForm.licencePhoto" />-->
                <!--<img alt="avatar" :src="shopForm.licencePhoto" /></a-avatar>-->
                <!--<div>-->
                <!--<a-button type="primary" status="danger" @click="handleClickImg('licencePhoto')">选择图片</a-button>-->
                <!--</div>-->
                <a-avatar :size="100" shape="square" @click="handleClickImg('licencePhoto')" style="margin-right: 20px;">
                  <icon-plus v-if="!shopForm.licencePhoto" />
                  <img alt="avatar" v-if="shopForm.licencePhoto" :src="shopForm.licencePhoto" />
                </a-avatar>
              </a-form-item>
              <a-divider orientation="left">法人信息</a-divider>
              <a-form-item field="legalName" label="法人姓名" :rules="[REQUIRED, VARCHAR2TO20]">
                <a-input v-model="shopForm.legalName" allow-clear :style="{ width: '350px' }"></a-input>
              </a-form-item>
              <a-form-item field="legalId" label="法人证件号" :rules="[REQUIRED, IDCARD]">
                <a-input v-model="shopForm.legalId" :max-length="18" allow-clear :style="{ width: '350px' }"></a-input>
              </a-form-item>
              <a-form-item field="legalPhoto" label="法人身份证照片">
                <a-avatar :size="100" shape="square" @click="handleClickImg('legalPhoto', 0)"
                  style="margin-right: 20px;"><icon-plus v-if="!shopForm.legalPhoto[0]" /> <img alt="avatar"
                    v-if="shopForm.legalPhoto[0]" :src="shopForm.legalPhoto[0]" /></a-avatar>
                <a-avatar :size="100" shape="square" @click="handleClickImg('legalPhoto', 1)"
                  style="margin-right: 20px;"><icon-plus v-if="!shopForm.legalPhoto[1]" /> <img alt="avatar"
                    v-if="shopForm.legalPhoto[1]" :src="shopForm.legalPhoto[1]" /></a-avatar>
                <span>点击图片上传身份证正反面，要求身份证清晰，四角无缺漏</span>
              </a-form-item>
              <a-divider orientation="left">结算银行信息</a-divider>
              <a-form-item field="settlementBankAccountName" label="银行开户名" :rules="[REQUIRED]">
                <a-input v-model="shopForm.settlementBankAccountName" allow-clear :style="{ width: '350px' }"></a-input>
              </a-form-item>
              <a-form-item field="settlementBankAccountNum" label="银行账号" :rules="[REQUIRED]">
                <a-input v-model="shopForm.settlementBankAccountNum" allow-clear :style="{ width: '350px' }"></a-input>
              </a-form-item>
              <a-form-item field="settlementBankBranchName" label="银行支行名称" :rules="[REQUIRED]">
                <a-input v-model="shopForm.settlementBankBranchName" allow-clear :style="{ width: '350px' }"></a-input>
              </a-form-item>
              <a-form-item field="settlementBankJointName" label="支行联行号" :rules="[REQUIRED]">
                <a-input v-model="shopForm.settlementBankJointName" allow-clear :style="{ width: '350px' }"></a-input>
              </a-form-item>
            </div>
          </a-form>
        </a-tab-pane>
        <a-tab-pane key="category" title="经营范围" disabled>
          <a-form ref="categoryFormRef" :model="shopForm">
            <div v-if="isRead" class="mask">只读不可修改</div>
            <a-form-item label="经营类目" field="goodsManagementCategory">
              <div>
                <a-checkbox :model-value="checkedAll" :indeterminate="indeterminate" @change="handleCheckAll">全选
                </a-checkbox>
              </div>
            </a-form-item>
            <a-form-item>
              <a-checkbox-group v-model="checkAllGroup" @change='checkAllGroupChange'>
                <a-checkbox v-for="(item, index) in categories " :key="index + 1" :value="item.id">{{ item.name
                }}</a-checkbox>
              </a-checkbox-group>
            </a-form-item>
          </a-form>
        </a-tab-pane>
        <a-tab-pane key="settlement" title="结算信息" disabled>
          <a-alert type="warning">
            已添加<span class="theme_color">{{ settlementCycle.length }}</span>个结算日，最多可添加5个结算日，当月不包含所设日期时，将会顺延到下一个结算日
          </a-alert>
          <a-form ref="settlementFormRef" :model="shopForm">
            <div v-if="isRead" class="mask">只读不可修改</div>
            <div style="height: 20px;"></div>
            <a-form-item label="结算日期">
              <a-tag closable v-for="item in settlementCycle" :key="item" style="margin-left: 10px"
                @close="removesettlementCycle(item)">
                {{ item }}</a-tag>
              <a-input-number v-model="day" :style="{ width: '70px' }" placeholder="Please Enter" class="input-demo" :min="1" :max="32" v-show="settlementShow" style="margin-left: 20px;height: 24px;" />
              <a-button @click="addsettlementCycle" v-if="addSettlementBtn && settlementCycle.length < 5"
                style="margin-left: 8px" size="mini">添加结算日期</a-button>
              <a-button v-if="addSettlementConfirmBtn" @click="addsettlementCycleConfirm" style="margin-left: 8px"
                size="mini">确认</a-button>
            </a-form-item>
            <a-form-item field="subMchid" label="微信二级商户ID">
              <div>
                <a-input v-model="shopForm.subMchid" allow-clear :style="{ width: '350px' }"></a-input>
                <div style="color: #a0a0a0;margin-top: 6px;">微信进件后成功后返回的唯一标识</div>
              </div>
            </a-form-item>
            <a-form-item field="subAlipayMchid" label="支付宝二级商户ID">
              <div>
                <a-input v-model="shopForm.subAlipayMchid" allow-clear :style="{ width: '350px' }"></a-input>
                <div style="color: #a0a0a0;margin-top: 6px;">支付宝进件后成功后返回的唯一标识</div>
              </div>
            </a-form-item>
          </a-form>
        </a-tab-pane>
      </a-tabs>
      <div align="center">
        <a-button type="primary"  @click="prev" v-show="tabNameList.indexOf(tabName) > 0">上一步</a-button>
        <a-button type="primary"  style="margin: 20px;" @click="next"
          v-show="tabNameList.indexOf(tabName) < tabNameList.length - 1">下一步</a-button>
        <a-button type="primary" status="danger" style="margin-left: 20px;"
          v-show="tabNameList.indexOf(tabName) === tabNameList.length - 1" @click="save" v-if="!isRead">
          {{ route.query.id ? "修改" : "保存" }}
        </a-button>
      </div>
    </a-card>
    <!--<a-modal v-model:visible="ossvisible" :width="1100">-->
      <!--<ossManage :close-model="handleOssOk" @changOssImage="changOssImage"></ossManage>-->
    <!--</a-modal>-->
    <a-modal v-model:visible="checkUserList" width="1200px">
      <userList ref="memberLayout" @callback="callbackSelectUser"></userList>
    </a-modal>
    <a-modal v-model:visible="auditModel" width="360px">
      <template #title> 审核店铺 </template>
      <div style="text-align: center">
        <p>您确认要审核通过该店铺</p>
      </div>
      <template #footer>
        <div style="text-align: right">
          <a-button type="text" :loading="auditModalLoading" @click="audit('REFUSED')">驳回</a-button>
          <a-button type="primary" :loading="auditModalLoading" status="danger" @click="audit('PASS')">通过</a-button>
        </div>
      </template>
    </a-modal>

    <a-modal v-model:visible="showOssManager" :width="1100"  @ok="handleOss" title="oss资源管理" @cancel="showOssManager = false">
      <ossManages :initialize="showOssManager" @selected="changOssImage" :isSingle="true"></ossManages>
    </a-modal>

  </div>
</template>

<script setup lang='ts'>
import { onMounted, ref } from "vue";
import { useRoute, useRouter } from 'vue-router';
// import ossManage from '@/views/setting/oss-manage/index.vue';
import { getCategoryTree } from '@/api/goods';
import { getShopByMemberId, shopAdd, shopAudit, shopDetail, shopEdit } from '@/api/shops';
import city from '@/components/m-city/index.vue';
import ossManages from '@/components/oss-manage/index.vue';
import { EMAIL, IDCARD, LICENSENUM, MOBILE, REQUIRED, TELEPHONE, VARCHAR2TO20, VARCHAR2TO200, VARCHAR6TO200 } from '@/utils/validator';
import userList from '@/views/member/member-list/list.vue';
import { Message } from '@arco-design/web-vue';
import { FormInstance } from '@arco-design/web-vue/es/form';

const baseFormRef = ref<FormInstance>();  // 基本信息
const entryFormRef = ref<FormInstance>(); // 入驻信息
const categoryFormRef = ref<FormInstance>();  // 经营范围
const settlementFormRef = ref<FormInstance>();  // 结算信息
const formRef = ref<FormInstance>();
const router = useRouter()
const route = useRoute()
const shopForm = ref<any>({
  // 店铺数据
  settlementCycle: "",
  selfOperated: false,
  memberName: "",
  companyName: "",
  addressPath: "",
  addressIdPath: "",
  companyAddressPath: "",
  companyAddressIdPath: "",
  companyAddress: "",
  companyEmail: "",
  employeeNum: 1,
  registeredCapital: 1,
  linkName: "",
  linkPhone: "",
  licenseNum: "",
  scope: "",
  licencePhoto: "",
  legalName: "",
  legalId: "",
  legalPhoto: ["", ""],
  companyPhone: "",
  settlementBankAccountName: "",
  settlementBankAccountNum: "",
  settlementBankBranchName: "",
  settlementBankJointName: "",
  businesses: "",
  storeName: "",
  storeLogo: "",
  storeDesc: "",
  ddCode: "",
  storeAddressPath: '',
  storeAddressIdPath: '',
  storeAddressDetail: '',
  memberId: '',
  salesConsigneeName: '',
  salesConsigneeMobile: '',
  salesConsigneeDetail: '',
  storeDisable: '',
  salesConsigneeAddressId: '',
  salesConsigneeAddressPath: '',
  subAlipayMchid:''
})
const isRead = ref<boolean>(false)  // 是否只读，只有在店铺通过审核才可修改
const ossvisible = ref<boolean>(false); // oss弹框
const memberLayout = ref(null) as any // 选择会员
const checkUserList = ref<boolean>(false) // 选择会员弹框是否显示
const selectedFormBtnName = ref<string>('') // 点击图片绑定form
const picIndex = ref<any>(0) // 存储身份证图片下标，方便赋值
const tabNameList = ref<Array<string>>(["base", "entry", "category", "settlement"]) // tab栏name值数组
const tabName = ref<string>('base')
const subscript = ref<number>(0)
const categories = ref<any>([]) // 分类数据
const checkAllGroup = ref<any>([]) // 全选数组
const checkedAll = ref<boolean>(false) // 全选
const indeterminate = ref<boolean>(false)
const checkAllDate = ref<any>([])
const settlementCycle = ref<any>([]) // 结算周期
const day = ref<number>(1) // 结算日
const settlementShow = ref<boolean>(false) // 是否展示结算日输入框
const addSettlementConfirmBtn = ref<boolean>(false) // 添加结算日确认按钮
const addSettlementBtn = ref<boolean>(true) // 添加结算日按钮
const auditModel = ref<boolean>(false) // 审核
const auditModalLoading = ref<boolean>(false)

const isShowCity = ref(1);

const showOssManager = ref<boolean>(false); // oss弹框
const selectedSku = ref(); // 选择的sku

// 审核
const auditHandler = () => {
  auditModel.value = true
}

// oss资源确定
const handleOss = () => {
  showOssManager.value = false;
  if (picIndex.value == 0 || picIndex.value == 1) {
    shopForm.value[selectedFormBtnName.value][picIndex.value] = selectedSku.value[selectedSku.value.length -1].url
  } else {
    shopForm.value[selectedFormBtnName.value] = selectedSku.value[selectedSku.value.length -1].url
  }
  picIndex.value = 0.1
};
// oss资源改变
const changOssImage = (val:any) => {
  selectedSku.value = [];
  val.forEach((item:any)=>{
    selectedSku.value.push({url:item.url})
  })
}
// 选择图片
const handleClickImg = (val: string, index?: number) => {
  console.log('选择图片', val, index);
  selectedFormBtnName.value = val;
  picIndex.value = index;
  // ossvisible.value = true;
  showOssManager.value = true;
};




// 选择会员
const addVip = () => {
  memberLayout.value.selectedMember = false
  checkUserList.value = true
}
// 选择会员的回调
const callbackSelectUser = (val: any) => {
  if (val) {
    getShopByMemberId(val.id).then((res) => {
      if (res.data.success) {
        if (res.data.result != null) {
          Message.error('当前会员已经拥有店铺');
        } else {
          shopForm.value.memberId = val.id
          shopForm.value.memberName = val.username
        }
        checkUserList.value = false
      }
    })
  }
}

// 选择城市回调
const storeAddressPathCallback = (val: any) => {
  shopForm.value.storeAddressIdPath = val.ids.join(',');
  shopForm.value.storeAddressPath = val.cities.join(',');
};
const companyAddressPathCallback = (val: any) => {
  shopForm.value.companyAddressIdPath = val.ids.join(',');
  shopForm.value.companyAddressPath = val.cities.join(',');
}
const salesConsigneeAddressPathCallback = (val: any) => {
  shopForm.value.salesConsigneeAddressId = val.ids.join(',');
  shopForm.value.salesConsigneeAddressPath = val.cities.join(',');
}
// 下一步
const next = async () => {
  let auth;
  if (tabName.value === 'base') {
    auth = await baseFormRef.value?.validate();
  }
  if (tabName.value === 'entry') {
    auth = await entryFormRef.value?.validate();
  }
  if (tabName.value === 'category') {
    auth = await categoryFormRef.value?.validate();
  }
  if (!auth) {
    subscript.value += 1;
    tabName.value = tabNameList.value[subscript.value]
  }
};
// 上一步
const prev = () => {
  subscript.value -= 1
  tabName.value = tabNameList.value[subscript.value]
}
// 保存
const save = async () => {
  const auth = await formRef.value?.validate();
  if (!auth) {
    if (settlementShow.value) {
      Message.error('请确认当前所填结算日信息');
      tabName.value = 'settlement'
      subscript.value = 3
      return
    }
    if (checkAllGroup.value.length <= 0) {
      Message.error('请选择店铺经营类目');
      subscript.value = 2
      tabName.value = 'category'
      return
    }
    const params = JSON.parse(JSON.stringify(shopForm.value));
    params.goodsManagementCategory = checkAllGroup.value.join(',');
    params.settlementCycle = settlementCycle.value
    const pam = {
      ...params,
      // storeAddressIdPath: '1401797451706269749,1401797451706270007,1401797451706270104,1401797451706270108',
      // storeAddressPath: '河北省 廊坊市 广阳区 银河北路街道',
      // storeCenter: '116.704441,39.523927',
      legalPhoto: shopForm.value.legalPhoto.join(',')
    }
    if (route.query.id) {
      delete pam.memberId;
      if (!pam.subMchid) delete pam.memberId;
      shopEdit(route.query.id, pam).then((res) => {
        if (res.data.success) {
          Message.success("编辑成功");
          router.push({ name: "shop-list" });
        }
      })
    } else {
      if (params.memberName == '') {
        Message.error('请选择开店的会员');
        return
      }
      shopAdd(pam).then((res: any) => {
        if (res.data.success) {
          Message.success('添加成功');
          shopForm.value = {};
          router.push({ name: 'shop-list' })
        }
      })
    }
  }

}
// 经营类目的选择
const checkAllGroupChange = (data: any) => {
  if (data.length == categories.value.length) {
    indeterminate.value = false;
    checkedAll.value = true
  } else if (data.length > 0) {
    indeterminate.value = true;
    checkedAll.value = false
  } else {
    indeterminate.value = false;
    checkedAll.value = false
  }
}
// 全选
const handleCheckAll = (val: any) => {
  indeterminate.value = false
  if (val) {
    checkedAll.value = true
    categories.value.forEach((item:any) => checkAllDate.value.push(item.id))
    checkAllGroup.value = checkAllDate.value
  } else {
    checkedAll.value = false
    checkAllGroup.value = []
  }
}
// 添加结算日
const addsettlementCycleConfirm = () => {
  if (!day.value) {
    Message.warning('请输入正确的结算周期，1-31的整数');
    return;
  }
  if (settlementCycle.value.includes(day.value)) {
    Message.warning('已有该结算周期，不能重复输入');
    return
  }
  settlementCycle.value.push(day.value)
  addSettlementConfirmBtn.value = false
  addSettlementBtn.value = true;
  settlementShow.value = false
  day.value = 1
}
// 确认添加方法
const addsettlementCycle = () => {
  settlementShow.value = true
  addSettlementConfirmBtn.value = true
  addSettlementBtn.value = false
}
// 关闭tag标签
const removesettlementCycle = (val: any) => {
  settlementCycle.value = settlementCycle.value.filter((item: any) => {
    return item != val
  })
}
// 审核店铺
const reviewStore = (id: any, key: any) => {
  shopAudit(id, key).then((res: any) => {
    auditModel.value = false
    if (res.data.success) {
      Message.success("操作成功");
      router.push({ name: 'shop-audit' })
    }
  })
}
// 审核店铺
const audit = (operation: string) => {
  if (operation == 'PASS') {
    reviewStore(route.query.id, 0)
  } else {
    reviewStore(route.query.id, 1)
  }
}

// 获取所有分类
const getCategories = () => {
  getCategoryTree().then((res) => {
    if (res.data.success) {
      categories.value = res.data.result
    }
  })
}
// 获取店铺详情
const getShopDetail = async () => {
  await shopDetail(route.query.id).then((res: any) => {
    if (res.data.success) {
      isShowCity.value += 1;
      shopForm.value = res.data.result;
      shopForm.value.selfOperated ? shopForm.value == 'true' : shopForm.value == 'false'
      checkAllGroup.value = shopForm.value.goodsManagementCategory.split(',')
      if (shopForm.value.settlementCycle) {
        settlementCycle.value = shopForm.value.settlementCycle.split(",");
      }
      shopForm.value.legalPhoto = shopForm.value.legalPhoto.split(",");
    }
  })
}
onMounted(() => {
  getCategories()
  if (route.query.id) {
    getShopDetail()
  }
})
</script>

<style lang="less" scoped>
.selectedMember {
  width: 100%;
}

.mask {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(105, 105, 105, 0.1);
  z-index: 9;
}

.tab {
  padding: 16px;
  position: relative;
}

.categories-checkbox {
  display: flex;
  align-items: center;
}

.img {
  margin-right: 10px;
  width: 100px;
  height: 100px;
}

.item {
  width: 350px !important;
  display: flex;

  >* {
    margin: 0 4px;
  }
}

.legal-photo {
  width: 100px;
  height: 100px;
  cursor: pointer;
}

.theme_color {
  color: #FF5C58 !important;
}

:deep(.arco-tag-size-medium) {
  margin-left: 20px;
}
</style>
