import request, { Method } from '@/utils/axios';

import qs from "query-string";

// 分页获取日志
export function getLogListData(params: ParamsRule) {
  return request({
    url: '/setting/log/getAllByPage',
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 分页获取文件数据
export function getFileListData(params: ParamsRule) {
  return request({
    url: '/common/file',
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 分页获取敏感词
export function getSensitiveWordsPage(params: ParamsRule) {
  return request({
    url: '/other/sensitiveWords',
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 添加敏感词
export function insertSensitiveWords(params: any) {
  return request({
    url: '/other/sensitiveWords',
    method: Method.POST,
    needToken: true,
    params,
  });
}
// 修改敏感词
export function updateSensitiveWords(id: string | number, params: any) {
  return request({
    url: `/other/sensitiveWords/${id}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
// 删除敏感词
export function delSensitive(id: string | number) {
  return request({
    url: `/other/sensitiveWords/delByIds/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}

// 分页查询app版本信息
export function appVersionPage(params: ParamsRule) {
  return request({
    url: '/other/appVersion',
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 添加app版本信息
export function addVersion(params: any) {
  return request({
    url: '/other/appVersion',
    method: Method.POST,
    needToken: true,
    params,
  });
}
// 修改app版本信息
export function editVersion(id: string | number, params: any) {
  return request({
    url: `/other/appVersion/${id}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
// 删除app版本信息
export function deleteVersion(id: string | number) {
  return request({
    url: `/other/appVersion/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}

// 验证码源文件列表
export function verificationPage(params: ParamsRule) {
  return request({
    url: '/other/verificationSource',
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 验证码源文件删除
export function delVerification(id: string | number) {
  return request({
    url: `/other/verificationSource/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}
// 添加验证码源文件
export function addVerification(params: any) {
  return request({
    url: '/other/verificationSource',
    method: Method.POST,
    needToken: true,
    params,
  });
}
// 修改验证码源文件
export function editVerification(id: string | number, params: any) {
  return request({
    url: `/other/verificationSource/${id}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
// 分页获取角色数据
export function getRoleList(params: ParamsRule) {
  return request({
    url: '/permission/role',
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 添加角色
export function addRole(params: any) {
  return request({
    url: `/permission/role`,
    method: Method.POST,
    needToken: true,
    params,
  });
}
// 编辑角色
export function editRole(id: string | number, params: any) {
  return request({
    url: `/permission/role/${id}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
// 删除角色
export function deleteRole(ids: string | number, params?: ParamsRule) {
  return request({
    url: `/permission/role/${ids}`,
    method: Method.DELETE,
    needToken: true,
    params,
  });
}

// 获取用户数据 多条件
export function getUserListData(params: ParamsRule) {
  return request({
    url: `/passport/user`,
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 添加用户
export function addUser(params: ParamsRule) {
  return request({
    url: `/passport/user`,
    method: Method.POST,
    needToken: true,
    data: params,
  });
}
// 编辑用户
export function editOtherUser(params:any) {
  return request({
    url: `/passport/user/admin/edit`,
    method: Method.PUT,
    needToken: true,
    data: params,
  });
}
// 启用用户
export function enableUser(id: string | number, params:any) {
  return request({
    url: `/passport/user/enable/${id}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}

// 删除用户
export function deleteUser(ids: string | number) {
  return request({
    url: `/passport/user/${ids}`,
    method: Method.DELETE,
    needToken: true,
  });
}
// 重置用户密码
export function resetPassword(params: any) {
  return request({
    url: `/passport/user/resetPassword/${params}`,
    method: Method.POST,
    needToken: true,
  });
}

// 微信消息同步
export function wechatMessageSync(params?: ParamsRule) {
  return request({
    url: '/wechat/wechatMessage/init',
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 分页获取微信消息
export function getWechatMessagePage(params: ParamsRule) {
  return request({
    url: '/wechat/wechatMessage',
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 编辑微信消息模版
export function editWechatMessageTemplate(id: string | number, params: any) {
  return request({
    url: `/wechat/wechatMessage/${id}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
// 删除微信消息模版
export function delWechatMessageTemplate(id: string | number) {
  return request({
    url: `/wechat/wechatMessage/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}

// 微信消息订阅同步
export function wechatMPMessageSync(params?: ParamsRule) {
  return request({
    url: `/wechat/wechatMPMessage/init`,
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 分页获取微信消息订阅
export function getWechatMPMessagePage(params: ParamsRule) {
  return request({
    url: `/wechat/wechatMPMessage`,
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 编辑微信消息订阅模版
export function editWechatMPMessageTemplate(id: number | string, params: any) {
  return request({
    url: `/wechat/wechatMPMessage/${id}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}

// 删除微信消息订阅模版
export function delWechatMPMessageTemplate(id: number | string,) {
  return request({
    url: `/wechat/wechatMPMessage/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}

// 通过id获取子地区
export function getChildRegion(id: number | string) {
  return request({
    url: `/setting/region/item/${id}`,
    method: Method.GET,
    needToken: true,
  });
}

// 更新地区
export function updateRegion(id: string | number, params: ParamsRule) {
  return request({
    url: `/setting/region/${id}`,
    method: Method.PUT,
    needToken: true,
    data: qs.stringify(params)
  })
}

// 添加地区
export function addRegion(params: ParamsRule) {
  return request({
    url: `/setting/region`,
    method: Method.POST,
    needToken: true,
    data: qs.stringify(params)
  })
}

// 同步高德行政地区数据
export function asyncRegion() {
  return request({
    url: `/setting/region/sync`,
    method: Method.POST,
    needToken: true,
  })
}

// 批量id删除
export function delRegion(ids: string | number) {
  return request({
    url: `/setting/region/${ids}`,
    method: Method.DELETE,
    needToken: true,
  })
}
// 获取支付支持设置开关
export function getPaymentSupportForm() {
  return request({
    url: 'setting/settingx/paymentSupport',
    method: Method.GET,
    needToken: true,
  })
}

// 查询签名详情
export function smsSignDetail(id: string | number) {
  return request({
    url: `sms/sign/${id}`,
    method: Method.GET,
    needToken: true
  })
}

// 删除短信签名
export function deleteSign(id: string | number) {
  return request({
    url: `sms/sign/${id}`,
    method: Method.DELETE,
    needToken: true,
  })
}

// 新增短信签名
export function addSmsSign(params: any) {
  return request({
    url: `sms/sign`,
    method: Method.POST,
    needToken: true,
    params
  })
}

// 编辑短信签名
export function editSmsSign(params: any) {
  return request({
    url: `sms/sign/modifySmsSign`,
    method: Method.PUT,
    needToken: true,
    params
  })
}

// 短信签名同步
export function syncSign() {
  return request({
    url: `sms/sign/querySmsSign`,
    method: Method.PUT,
    needToken: true
  })
}

// 添加短信模板
export function addSmsTemplate(params: ParamsRule) {
  return request({
    url: `sms/template`,
    method: Method.POST,
    needToken: true,
    data: qs.stringify(params),
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
  })
}

// 修改短信模板
export function editSmsTemplate(params: ParamsRule) {
  return request({
    url: `sms/template/modifySmsTemplate`,
    method: Method.PUT,
    needToken: true,
    data: qs.stringify(params),
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 删除短信模板
export function deleteSmsTemplate(params: any) {
  return request({
    url: `sms/template`,
    method: Method.DELETE,
    needToken: true,
    params
  })
}

// 短信模板同步
export function syncSmsTemplate() {
  return request({
    url: `sms/template/querySmsSign`,
    method: Method.PUT,
    needToken: true
  })
}

// 查询短信发送记录
export function getSmsPage(params: ParamsRule) {
  return request({
    url: `sms/sms`,
    method: Method.GET,
    needToken: true,
    params
  })
}

// 发送短信
export function sendSms(params: any) {
  return request({
    url: `sms/sms`,
    method: Method.POST,
    needToken: true,
    data: qs.stringify(params),
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
  })
}
// 通过部门获取全部角色数据
export function getUserByDepartmentId(id: string | number, params?: ParamsRule) {
  return request({
    url: `/permission/departmentRole/${id}`,
    method: Method.GET,
    needToken: true,
    params
  })
}
// 通过部门修改绑定角色
export function updateDepartmentRole(id: string | number, params: ParamsRule) {
  return request({
    url: `/permission/departmentRole/${id}`,
    method: Method.PUT,
    needToken: true,
    data: params
  })
}
// 获取一级部门
export function initDepartment() {
  return request({
    url: `/permission/department`,
    method: Method.GET,
    needToken: true,
  })
}
// 加载部门子级数据
export function loadDepartment(id: string | number) {
  return request({
    url: `/permission/department/${id}`,
    method: Method.GET,
    needToken: true,
  })
}
// 添加部门
export function addDepartment(params: ParamsRule) {
  return request({
    url: `/permission/department`,
    method: Method.POST,
    needToken: true,
    params,
  })
}
// 编辑部门
export function editDepartment(ids: string | number, params: ParamsRule) {
  return request({
    url: `/permission/department/${ids}`,
    method: Method.PUT,
    needToken: true,
    params,
  })
}
// 删除部门
export function deleteDepartment(ids: string | number, params?: ParamsRule) {
  return request({
    url: `/permission/department/${ids}`,
    method: Method.DELETE,
    needToken: true,
    params,
  })
}
// 搜索部门
export function searchDepartment(params: ParamsRule) {
  return request({
    url: `/permission/department/search`,
    method: Method.GET,
    needToken: true,
    params,
  })
}

// 获取全部权限数据
export function getAllPermissionList() {
  return request({
    url: `/permission/menu/tree?version=v3`,
    method: Method.GET,
    needToken: true,
  })
}

// 获取全部权限数据
export function getStoreAllPermissionList() {
  return request({
    url: `/store/storeMenu/tree?version=v3`,
    method: Method.GET,
    needToken: true,
  })
}

// 保存角色菜单
export function saveRoleMenu(id: number | string, params: any) {
  return request({
    url: `/permission/roleMenu/${id}`,
    method: Method.POST,
    needToken: true,
    data: params
  })
}
// 查看某角色拥有的菜单
export function selectRoleMenu(params: ParamsRule) {
  return request({
    url: `/permission/roleMenu/${params}`,
    method: Method.GET,
    needToken: true,
    data: params
  })
}
// 删除权限
export function deletePermission(ids: string | number, params?: ParamsRule) {
  return request({
    url: `/permission/menu/${ids}`,
    method: Method.DELETE,
    needToken: true,
    params
  })
}

export function deleteStorePermission(ids: string | number, params?: ParamsRule) {
  return request({
    url: `/store/storeMenu/${ids}`,
    method: Method.DELETE,
    needToken: true,
    params
  })
}

// 搜索权限
export function searchPermission(params: ParamsRule) {
  params.version = 'v3'
  return request({
    url: `/permission/menu`,
    method: Method.GET,
    needToken: true,
    data: params
  })
}
// 编辑权限
export function editPermission(params: ParamsRule) {
  params.mVersion = 'v3'
  return request({
    url: `/permission/menu/${params.id}`,
    method: Method.PUT,
    needToken: true,
    data: qs.stringify(params),
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    }
  })
}

export function editStorePermission(params: ParamsRule) {
  params.mVersion = 'v3'
  return request({
    url: `/store/storeMenu/${params.id}`,
    method: Method.PUT,
    needToken: true,
    data: qs.stringify(params),
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    }
  })
}

// 添加权限
export function addPermission(params: ParamsRule) {
  params.mVersion = 'v3'
  return request({
    url: `/permission/menu`,
    method: Method.POST,
    needToken: true,
    data: qs.stringify(params),
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    }
  })
}

// 添加权限
export function addStorePermission(params: ParamsRule) {
  params.mVersion = 'v3'
  return request({
    url: `/store/storeMenu`,
    method: Method.POST,
    needToken: true,
    data: qs.stringify(params),
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    }
  })
}

// 获取全部权限数据
export function getCurrentPermissionList() {
  return request({
    url: `/permission/menu/memberMenu?version=v3`,
    method: "GET",
    needToken: true,
  })
}


// 获取首页楼层装数据
export function getHomePage(params: ParamsRule) {
  return request({
    url: `/other/pageData/pageDataList`,
    method: Method.GET,
    needToken: true,
    params
  })
}


// 修改首页页面开启状态
export function changeHomePage(id: string | number) {
  return request({
    url: `/other/pageData/release/${id}`,
    method: Method.PUT,
    needToken: true,

  })
}

// 添加楼层装修
export function opeartionHomePage(params: any, type = 'add') {
  let url = `/other/pageData/${type}`
  if (type !== 'add') {
    url = `/other/pageData/${type}/${params.id}`
  }
  return request({
    url: url,
    method: type === 'add' ? Method.POST : Method.PUT,
    needToken: true,

    data: qs.stringify(params),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  })
}




export function removeHomePage(id: string | number) {
  return request({
    url: `/other/pageData/remove/${id}`,
    method: Method.DELETE,
    needToken: true,
  })
}

export function getHomePageDetail(id: string | number) {
  return request({
    url: `/other/pageData/${id}`,
    method: Method.GET,
    needToken: true,
  })
}

