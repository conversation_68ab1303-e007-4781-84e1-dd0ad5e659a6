<template>
  <a-card class="general-card" title="包月管理" :bordered="false">
    <searchTable
      :columns="columnsSearch"
      :row-span="isNormal ? 0 : 12"
      @reset="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
      @search="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
    ></searchTable>
    <a-row v-if="isNormal" style="margin-bottom: 16px">
      <a-col :span="16">
        <a-button type="primary" @click="addGoods"> 新建包月商品 </a-button>
      </a-col>
    </a-row>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="tableMethods"
      :api="getSubGoodsList"
      :page-size="isNormal ? 10 : 6"
      :radio="!isNormal"
      :bordered="true"
      :api-params="apiParams"
    >
      <template #btnList="{ data }">
        <div v-if="data.status === 0">
          <a-space cursor-pointer>
            <a-spin :loading="data.id === addGoodsForm.id && detailLoading">
              <a-typography-text
                style="font-size: 12px"
                type="primary"
                @click="handleEdit(data)"
              >
                编辑
              </a-typography-text>
            </a-spin>
          </a-space>
        </div>
        <div>
          <a-space cursor-pointer>
            <a-typography-text
              v-if="data?.status === 0"
              style="font-size: 12px"
              type="primary"
              @click="upper(data)"
            >
              上架
            </a-typography-text>
            <a-typography-text
              v-else
              style="font-size: 12px"
              type="primary"
              @click="lower(data)"
            >
              下架
            </a-typography-text>
          </a-space>
        </div>
      </template>
      <template #platform="{ data }">
        <span>{{ getPlatformText(data) }}</span>
      </template>
    </tablePage>
  </a-card>
  <a-modal
    v-model:visible="showAddGoodsModal"
    :title="`${addGoodsForm.id ? '编辑' : '创建'}包月商品`"
    title-align="start"
    :mask-closable="false"
    :esc-to-close="false"
    :ok-loading="addGoodsLoading"
    :on-before-ok="handleConfirmAddGoods"
    :ok-text="addGoodsForm.id ? '确认创建' : '确认保存'"
    width="650px"
  >
    <a-form ref="addSubGoodsFormRef" :model="addGoodsForm">
      <a-form-item label="包月商品编号" :rules="[REQUIRED]">
        <a-input
          v-model="addGoodsForm.subGoodsSn"
          placeholder="请输入包月商品编号"
        />
      </a-form-item>
      <a-form-item label="包月商品名称" :rules="[REQUIRED]">
        <a-input
          v-model="addGoodsForm.subGoodsName"
          placeholder="请输入包月商品名称"
        />
      </a-form-item>
      <a-form-item label="商品销售价格" :rules="[REQUIRED]">
        <a-input-number
          v-model="addGoodsForm.price"
          :precision="2"
          hide-button
          placeholder="请输入包月商品价格"
        />
      </a-form-item>
      <a-form-item label="扣款平台">
        <a-select v-model="addGoodsForm.platform" placeholder="请选择扣款平台">
          <a-option label="微信" value="WEIXIN" />
          <a-option label="支付宝" value="ALIPAY" />
          <a-option label="电信" value="DIANXIN" />
        </a-select>
      </a-form-item>
      <a-form-item label="扣款编号">
        <a-input
          v-model="addGoodsForm.deductionNumber"
          placeholder="请输入扣款编号"
        />
      </a-form-item>
      <a-form-item label="底层商品">
        <a-tag
          v-if="addGoodsForm.goodsName"
          closable
          @close="handleRemoveGoods"
        >
          {{ addGoodsForm.goodsName }}
        </a-tag>
        <a-button v-else size="mini" type="primary" @click="handleSelectGoods">
          选择商品
        </a-button>
      </a-form-item>
      <a-form-item label="商品头图" class="form-item-images">
        <a-button @click="handleClickOssManages('goods')">选择图片</a-button>
        <div class="image-list">
          <a-image
            v-for="img in addGoodsForm.goodsGalleryList"
            :key="img"
            :src="img"
            style="margin: 0 5px 5px 0"
            width="100"
            height="100"
          />
        </div>
      </a-form-item>
      <a-form-item label="商品详情图" class="form-item-images">
        <a-button @click="handleClickOssManages('intro')">选择详情图</a-button>
        <div class="image-list">
          <a-image
            v-for="img in addGoodsForm.intro"
            :key="img"
            :src="img"
            style="margin: 0 5px 5px 0"
            width="100"
            height="100"
          />
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
  <skuselect
    ref="skuSelectRef"
    :api-params="{
      marketEnable: 'UPPER',
      authFlag: 'PASS',
    }"
    @change="handleGoodsChange"
  />
  <a-modal
    v-model:visible="showOssManages"
    :width="966"
    title="选择图片"
    :body-style="{ paddingTop: '0px', paddingBottom: '0px' }"
    @ok="ossManagesOk"
    @cancel="showOssManages = false"
  >
    <ossManages :initialize="showOssManages" @selected="handleOssSelected" />
  </a-modal>
</template>

<script setup lang="ts">
  import { getSubGoodsList, addSubGoods, updateSubGoods, upperSubGoods, underSubGoods, getSubGoodsDetail } from '@/api/goods';
  import { ref } from 'vue';
  import ossManages from '@/components/oss-manages/index.vue';
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import skuselect from '@/components/goods-sku-selector/index.vue';
  import useCurrentInstance from '@/hooks/useCurrentInstance';
  import { ColumnsDataRule, SearchRule } from '@/types/global';
  import { Message } from '@arco-design/web-vue';
  import { REQUIRED } from '@/utils/validator';

  const props = defineProps({
    templateModel: {
      type: String,
      default: 'normal',
    },
  });
  const isNormal: boolean = props.templateModel === 'normal';
  const goodsStatus = ref<string>('UPPER');
  const tablePageRef = ref<any>('');
  const apiParams = ref<any>({
    goodsStatus: goodsStatus.value,
  });
  // 获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;
  const platformOptions = [
    { label: '微信', value: 'WEIXIN' },
    { label: '支付宝', value: 'ALIPAY' },
    { label: '电信', value: 'DIANXIN' },
  ];
  const columnsSearch: Array<SearchRule> = [
    {
      label: '包月名称',
      model: 'subGoodsName',
      disabled: false,
      input: true,
    },
    {
      label: '包月商品编号',
      model: 'subGoodsSn',
      disabled: false,
      input: true,
    },
    {
      label: '商品状态',
      model: 'status',
      disabled: false,
      select: {
        options: [
          { value: 1, label: '上架中' },
          { value: 0, label: '已下架' },
        ],
      },
    },
    {
      label: '签约平台',
      model: 'platform',
      disabled: false,
      select: { options: platformOptions },
    },
    {
      label: '创建时间',
      disabled: false,
      datePicker: {
        type: 'range',
      },
    },
    {
      label: '底层商品编号',
      model: 'goodsId',
      disabled: false,
      input: true,
    },
    {
      label: '底层商品名称',
      model: 'goodsName',
      disabled: false,
      input: true,
    },
  ];

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '包月商品编号',
      dataIndex: 'subGoodsSn',
      width: 120,
    },
    {
      title: '包月商品名称',
      dataIndex: 'subGoodsName',
      width: 200,
      ellipsis: false,
    },
    {
      title: '底层商品编号',
      width: 120,
      dataIndex: 'goodsId',
    },
    {
      title: '底层商品名称',
      dataIndex: 'goodsName',
      width: 200,
      ellipsis: false,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '签约平台',
      dataIndex: 'platform',
      width: 100,
      slot: true,
      slotTemplate: 'platform',
    },
    {
      title: '扣款编号',
      dataIndex: 'deductionNumber',
      width: 100,
    },
    {
      title: '商品状态',
      dataIndex: 'status',
      width: 100,
      slot: true,
      slotData: {
        badge: [
          { value: 0, label: '已下架', color: 'red' },
          { value: 1, label: '上架中', color: 'green' },
        ],
      },
    },
    {
      title: '上架时间',
      dataIndex: 'listingTime',
      width: 100,
    },
  ];
  const tableMethods = {
    title: '操作',
    width: 100,
    fixed: 'right',
    methods: [
      {
        slot: true,
        slotTemplate: 'btnList',
      },
    ],
  };
  const getPlatformText = (data: any) => {
    const plat = platformOptions.find((item) => item.value === data.platform);
    return plat?.label || '';
  };

  // 下架
  const lower = (v: any) => {
    modal.confirm({
      title: '确认下架',
      content: `您确认要下架${v.subGoodsName}?`,
      alignCenter: false,
      onOk: async () => {
        const res = await underSubGoods(v.id);
        if (res.data.success) {
          Message.success('下架成功');
          tablePageRef.value.init();
        }
      },
    });
  };
  // 上架
  const upper = (v: any) => {
    modal.confirm({
      title: '确认上架',
      content: `您确认要上架${v.subGoodsName}?`,
      alignCenter: false,
      onOk: async () => {
        const res = await upperSubGoods(v.id);
        if (res.data.success) {
          Message.success('上架成功');
          tablePageRef.value.init();
        }
      },
    });
  };
  // 添加商品
  const showAddGoodsModal = ref(false);
  const addGoodsForm = ref<Record<string, any>>({});
  const addSubGoodsFormRef = ref();
  const addGoods = () => {
    addGoodsForm.value = {};
    showAddGoodsModal.value = true;
  };
  const detailLoading = ref(false);
  const handleEdit = async (item: Record<string, any>) => {
    detailLoading.value = true;
    addGoodsForm.value.id = item.id;
    try {
      const res = await getSubGoodsDetail(item.id);
      const data = JSON.parse(JSON.stringify(res.data.result));
      if (data.intro) {
        data.intro = data.intro.split(',');
      }
      addGoodsForm.value = data;
      showAddGoodsModal.value = true;
    } finally {
      detailLoading.value = false;
    }
  };
  const addGoodsLoading = ref(false);
  const handleConfirmAddGoods = async () => {
    addGoodsLoading.value = true;
    await addSubGoodsFormRef.value?.validate();
    const data = JSON.parse(JSON.stringify(addGoodsForm.value));
    if (Array.isArray(data.intro)) {
      data.intro = data.intro.join(',');
    }
    try {
      if (data.id) {
        await updateSubGoods(data.id, data);
      } else {
        await addSubGoods(data);
      }
      tablePageRef.value.init();
      addGoodsLoading.value = false;
      return true;
    } catch (e) {
      return false;
    }
  };
  const skuSelectRef = ref();
  const handleSelectGoods = () => {
    skuSelectRef.value.modalData.visible = true;
  };
  const handleGoodsChange = (goodsList: any) => {
    const goods = goodsList[0];
    if (!goods) {
      addGoodsForm.value.goodsId = '';
      addGoodsForm.value.goodsName = '';
    } else {
      addGoodsForm.value.goodsId = goods.id;
      addGoodsForm.value.goodsName = goods.goodsName;
    }
  };
  const handleRemoveGoods = () => {
    addGoodsForm.value.goodsId = '';
    addGoodsForm.value.goodsName = '';
    skuSelectRef.value.modalData.goodsSelectedList = [];
    skuSelectRef.value.modalData.selectedKeys = [];
  };
  const showOssManages = ref(false);
  const ossManagesType = ref('');
  const selectedOssList = ref<any[]>([]);
  const handleClickOssManages = (type: string) => {
    showOssManages.value = true;
    ossManagesType.value = type;
    selectedOssList.value = [];
  };
  const handleOssSelected = (ossList: any[]) => {
    selectedOssList.value = ossList;
  };
  const ossManagesOk = () => {
    const images = selectedOssList.value.map((item) => item.url);
    if (ossManagesType.value === 'goods') {
      addGoodsForm.value.goodsGalleryList = images;
    } else if (ossManagesType.value === 'intro') {
      addGoodsForm.value.intro = images;
    }
    showOssManages.value = false;
  };
</script>

<style lang="less" scoped>
  .form-item-images {
    :deep(.arco-form-item-content) {
      flex-direction: column;
      align-items: flex-start;
    }
    .image-list {
      margin-top: 10px;
      :deep(.arco-image-img) {
        cursor: pointer;
      }
    }
  }
</style>
