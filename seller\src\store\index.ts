import { createPinia } from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';
import useAppStore from './modules/app';
import useDesign from './modules/design';
import useTabBarStore from './modules/tab-bar';
import useUserStore from './modules/user';

const pinia = createPinia();
// 数据持久化
pinia.use(piniaPluginPersistedstate);
export { useAppStore, useDesign, useTabBarStore, useUserStore };
export default pinia;
