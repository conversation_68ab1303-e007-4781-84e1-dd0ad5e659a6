import { createPinia } from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';
import useAppStore from './modules/app';
import useUserStore from './modules/user';
import useTabBarStore from './modules/tab-bar';
import usePathJumpStore from './modules/global-jump/index';
import useDesign from './modules/design';

const pinia = createPinia();
// 数据持久化
pinia.use(piniaPluginPersistedstate);
export { useAppStore, useUserStore, useTabBarStore, usePathJumpStore,useDesign };
export default pinia;
