<template>
  <div class="phone" flex flex-a-c flex-j-c justify-center>
    <div class="model" v-auto-animate>
      <a-alert v-if="isDrop" class="tips" :type="isOver ? 'success' : 'info'">
        {{ isOver ? '干的不错👏' : '拖拽进此处👇' }}
      </a-alert>
      <div :ref="parent">
        <div :ref="drop" v-auto-animate px-13px py-50px h-800px role="phone">
          <div class="div-box">
            <div h-full class="phone-model" flex flex-col>
              <!-- 普通组件区域 -->
              <div class="content-area" flex-1>
                <div v-for="(element, block) in normalModels " :key="block">
                  <item @dragenter="componetDragIndex = block" @drop="componentDownIndex = block"
                    :id="item.type + new Date().getTime()" :move-card="moveCard" :index="block" class="model-item"
                    :class="{ 'active': active === block }" @click.native="handleClickComponent(element, block)" absolute
                    v-if="normalModels.length">
                    <a-tag v-if="active === block" class="current" absolute color="arcoblue">当前</a-tag>
                    <div v-if="active === block" left--10px z-99 class="border-left" w-1px left-0px absolute></div>
                    <component class="component" :is="template[element.type]" :res="element">
                    </component>
                    <div absolute top-0px z-99 v-if="active === block" class="border-right" w-1px right-0px></div>
                    <a-button @click="handleClickDelBtn(element, block)" v-if="active === block" status="danger" absolute
                      class="btn" right--55px size="mini">
                      删除
                    </a-button>
                  </item>
                </div>
              </div>

              <!-- 底部导航区域 -->
              <div class="bottom-nav-area" v-if="bottomNavModels.length > 0">
                <div v-for="(element, block) in bottomNavModels " :key="'bottom-' + block">
                  <item @dragenter="componetDragIndex = getBottomNavIndex(block)" @drop="componentDownIndex = getBottomNavIndex(block)"
                    :id="item.type + new Date().getTime()" :move-card="moveCard" :index="getBottomNavIndex(block)" class="model-item"
                    :class="{ 'active': active === getBottomNavIndex(block) }" @click.native="handleClickComponent(element, getBottomNavIndex(block))" absolute>
                    <a-tag v-if="active === getBottomNavIndex(block)" class="current" absolute color="arcoblue">当前</a-tag>
                    <div v-if="active === getBottomNavIndex(block)" left--10px z-99 class="border-left" w-1px left-0px absolute></div>
                    <component class="component" :is="template[element.type]" :res="element">
                    </component>
                    <div absolute top-0px z-99 v-if="active === getBottomNavIndex(block)" class="border-right" w-1px right-0px"></div>
                    <a-button @click="handleClickDelBtn(element, getBottomNavIndex(block))" v-if="active === getBottomNavIndex(block)" status="danger" absolute
                      class="btn" right--55px size="mini">
                      删除
                    </a-button>
                  </item>
                </div>
              </div>
            </div>
          </div>

          <!-- <div v-if="isShallowOver && !isDragging" :class="['indicator', { first: props.index === 0 }]"></div> -->
        </div>

      </div>
    </div>

    <a-modal v-model:visible="visible" :onOk="delCurrentModel" unmountOnClose>
      <template #title>
        提示
      </template>
      <div>确认要删除模块 {{ dragData.data.name }} 吗？
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { getHomePageDetail } from '@/api/setting';
import { useDesign } from '@/store';
import { DragRule } from '@/views/operation/decoration/app/models/types';
import { Message } from '@arco-design/web-vue';
import { useAutoAnimate } from '@formkit/auto-animate/vue';
import { toRefs } from '@vueuse/core';
import { computed, onMounted, ref, unref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useDrop } from 'vue3-dnd';
import templates from '../template/exports';
import item from './item.vue';

const componentDownIndex = ref<any>(0)
const componetDragIndex = ref<any>(0)
const route = useRoute()
const [parent]: any = useAutoAnimate()
const visible = ref<boolean>(false)
const template: any = templates
const models: any | Array<DragRule> = ref([])
const active = ref<number | string>('')


const [collect, drop] = useDrop(() => ({
  accept: 'box',
  // 此处接受传过来的值
  drop: (val, monitor) => handleDrop(val, monitor),
  collect: (monitor) => ({
    isOver: monitor.isOver(),
    canDrop: monitor.canDrop(),
    getItemType: monitor.getItemType(),
    isShallowOver: monitor.isOver({ shallow: true })

  }),

}));

onMounted(() => {
  active.value = models.value.length == 0 ? 0 : ''
  userDesign.setAppActiveIndex(Number(active.value));
  userDesign.setAppDesign(models.value)
})


const userDesign = useDesign();
const { canDrop, isOver } = toRefs(collect);

const isDrop = computed(() => (unref(canDrop) ? true : false));

// 分离普通组件和底部导航组件
const normalModels = computed(() => {
  return models.value.filter((model: any) => model.type !== 'bottomNav');
});

const bottomNavModels = computed(() => {
  return models.value.filter((model: any) => model.type === 'bottomNav');
});

// 获取底部导航组件在原数组中的索引
const getBottomNavIndex = (bottomNavIndex: number) => {
  const bottomNavModel = bottomNavModels.value[bottomNavIndex];
  return models.value.findIndex((model: any) => model === bottomNavModel);
};
// 监听装修组件active index的变化
watch(() => userDesign.indexOfApp, (val) => {
  active.value = val
})
// 点击删除组件数据
const dragData = ref({
  data: {
    name: ""
  },
  index: 0
})


// 定义一个函数 moveCard，接收两个参数：dragIndex（拖动项的索引）和hoverIndex（悬停项的索引）
const moveCard = (dragIndex: number, hoverIndex: number) => {
  // 从 models 数组中获取拖动项（dragIndex 索引对应的项）
  const item = models.value[dragIndex]

  // 从 models 数组中移除拖动项（dragIndex 索引对应的项）
  models.value.splice(dragIndex, 1)

  // 将拖动项插入到悬停项（hoverIndex 索引对应的项）之前
  models.value.splice(hoverIndex, 0, item)

  // 将active的值设置为hoverIndex
  active.value = hoverIndex

  userDesign.setAppActiveIndex(hoverIndex);
}

// 删除当前模块
function delCurrentModel() {
  models.value.splice(dragData.value.index, 1)
}
// 删除组件
function handleClickDelBtn(val: DragRule, index: number) {
  visible.value = true
  dragData.value = {
    data: val,
    index
  }

}

// 对组件进行点击处理
function handleClickComponent(val: DragRule, index: number) {
  active.value = index

  // 将点击的内容传入到pinia中
  userDesign.setAppActiveIndex(index);
}
// 将传过来的值进行处理
function handleDrop(res: any, val: any) {
  const current = { ...JSON.parse(JSON.stringify(res)), }
  if (!models.value.length) {
    models.value.push(current)
  }
  else {
    if (componetDragIndex.value === models.value.length - 1) {
      models.value.push(current)
    } else {
      // hoverIndex
      models.value.splice(componentDownIndex.value, 0, current)
    }
  }
}

// 实例化代码
async function init() {
  if (!route.query.id) {
    return false
  }
  const id: string = (route.query.id as string)
  const res = await getHomePageDetail(id)
  if (res.data.success) {
    // 判断当前楼层装修的值
    const fetchData = res.data.result.pageData ? JSON.parse(res.data.result.pageData) : ''
    if (fetchData) {
      if (fetchData?.version === 'v3') {
        models.value = fetchData.data
        userDesign.setAppDesign(fetchData.data)
      } else {
        Message.error({
          content: "当前编辑内容版本不支持",
          duration: 10000,
          closable: true
        })
      }

    }
  }
}

onMounted(() => {
  init()
})


</script>

<style scoped lang="less">
.div-box {
  height: 100%;
  width: 140%;
  overflow-y: scroll;
  overflow-x: hidden;
  padding: 0 20%;
  margin-left: -20%;
}

.border-left,
.border-right {
  height: 100%;
  background: rgb(var(--arcoblue-5));
}

.btn {
  top: 50%;
  margin-top: -12px;
}

.model-item {
  user-select: none;
  position: relative;
}

.phone,
.phone-model {
  background: #f7f7f7;
}

.content-area {
  overflow-y: auto;
}

.bottom-nav-area {
  flex-shrink: 0;
  margin-top: auto;
}

.current {
  top: 50%;
  margin-top: -12px;
  left: -50px;
}

.model {
  background: url('@/assets/iPhoneX_model.png') no-repeat;
  width: 390px;
  height: 800px;
  background-size: 390px;
}

.tips {
  position: absolute;
  width: 200px;
  left: 50%;
  top: -50px;
  margin-left: -100px;
}
</style>
