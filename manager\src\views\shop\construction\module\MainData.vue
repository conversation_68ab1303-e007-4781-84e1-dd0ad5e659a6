<template>
  <div>
    <div align="right">
      <a-button type="primary" status="warning" @click="nextStep('1')" size="small">上一步</a-button>
      <a-button type="primary" style="margin-left: 20px;" @click="nextStep('3')" size="small">下一步</a-button>
    </div>
    <a-form ref="mainDataFormRef" :model="mainForm">
      <a-form-item field="organization_type" label="主体类型" :rules="[REQUIRED]">
        <a-select :style="{ width: '280px' }" v-model="mainForm.organization_type" placeholder="请选择主体类型" @change="() => { mainForm.certificate_info.cert_type = '' }">
          <a-option v-for="item in subjectType" :key="item.value" :value="item.value">{{ item.title }}</a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="merchant_shortname" label="代理商名称">
        <a-input style="width: 400px;" v-model="mainForm.merchant_shortname" placeholder="请输入代理商名称"/>
      </a-form-item>
      <a-form-item field="finance_institution" label="是否是金融机构">
        <a-switch v-model="mainForm.finance_institution">
          <template #checked> 是 </template>
          <template #unchecked> 否 </template>
        </a-switch>
      </a-form-item>
      <!--
         1、主体为“小微/个人卖家”时，不填。
         2、主体为“个体工商户/企业”时，请上传营业执照。
         3、主体为“政府机关/事业单位/社会组织”时，请上传登记证书。
       -->
      <div v-if="mainForm.organization_type !== '2401' &&mainForm.organization_type !== '2500'">
        <a-divider orientation="left">营业执照/登记证书信息</a-divider>
        <a-form-item v-if="mainForm.organization_type !== '2' && mainForm.organization_type !== '4'" field="certificate_info.cert_type" label="登记证书类型" :rules="[REQUIRED]">
          <a-select :style="{ width: '280px' }" v-model="mainForm.certificate_info.cert_type" placeholder="请选择登记证书类型" :rules="[REQUIRED]">
            <a-option v-for="item in certTypeList" :key="item.value" :value="item.value">{{ item.label }}</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="business_license_info.business_license_copy" label="营业执照照片/登记证书照片" :rules="[REQUIRED]">
          <div>
            <a-avatar :size="100" shape="square" @click="handleClickImg('business_license_copy')" style="margin-right: 20px;">
              <icon-plus v-if="!mainForm.business_license_info.business_license_copy" />
              <img alt="avatar" v-if="mainForm.business_license_info && mainForm.business_license_info.business_license_copy" :src="mainForm.business_license_info.business_license_copy" />
            </a-avatar>
          </div>
          <template #extra>
            <div>
              <p>1、主体为“个体工商户/企业”时，请上传营业执照的证件图片。</p>
              <p>2、主体为“政府机关/事业单位/社会组织”时，请上传登记证书的证件图片。</p>
              <p>（1）请上传证件的彩色扫描件或彩色数码拍摄件，黑白复印件需加盖公章（公章信息需完整） 。</p>
              <p>（2）不得添加无关水印（非微信支付商户申请用途的其他水印）。</p>
              <p>（3）需提供证件的正面拍摄件，完整、照面信息清晰可见。信息不清晰、扭曲、压缩变形、反光、不完整均不接受。</p>
              <p>（4）不接受二次剪裁、翻拍、PS的证件照片。</p>
            </div>
          </template>
        </a-form-item>
        <a-form-item field="business_license_info.business_license_number" label="注册号/统一社会信用代码" :rules="[REQUIRED]">
          <a-input style="width: 400px;" v-model="mainForm.business_license_info.business_license_number" placeholder="请输入注册号/统一社会信用代码"/>
          <template #extra>
            <p>1、主体为“个体工商户/企业”时，请填写营业执照上的注册号/统一社会信用代码，须为18位数字|大写字母。</p>
            <p>2、主体为“政府机关/事业单位/社会组织”时，请填写登记证书的证书编号。</p>
          </template>
        </a-form-item>
        <a-form-item field="business_license_info.merchant_name" label="商户名称" :rules="[REQUIRED]">
          <a-input style="width: 400px;" v-model="mainForm.business_license_info.merchant_name" placeholder="请输入商户名称"/>
          <template #extra>
            <p>1、请填写营业执照/登记证书的商家名称，2~110个字符，支持括号 。</p>
            <p>2、个体工商户/政府机关/事业单位/社会组织，不能以“公司”结尾。</p>
            <p>3、个体工商户，若营业执照上商户名称为空或为“无”，请填写"个体户+经营者姓名"，如“个体户张三”。</p>
          </template>
        </a-form-item>
        <a-form-item field="business_license_info.legal_person" label="个体户经营者/法人姓名" :rules="[REQUIRED]">
          <a-input style="width: 400px;" v-model="mainForm.business_license_info.legal_person" placeholder="请输入个体户经营者/法人姓名"/>
          <template #extra>
            <div><p>请填写营业执照的经营者/法定代表人姓名</p></div>
          </template>
        </a-form-item>
        <a-form-item field="business_license_info.company_address" label="注册地址" :rules="[REQUIRED]">
          <a-input style="width: 400px;" v-model="mainForm.business_license_info.company_address" placeholder="请输入注册地址"/>
          <template #extra>
            <div><p>请填写登记证书的注册地址</p></div>
          </template>
        </a-form-item>
        <a-form-item field="business_license_info.business_time_startTime" label="有效期限开始日期">
          <a-date-picker
              v-model="mainForm.business_license_info.business_time_startTime"
              style="width: 200px"
              placeholder="选择有效期开始日期"
              @change="(event) => { mainForm.business_license_info.business_time_startTime = event }"
          ></a-date-picker>
        </a-form-item>
        <a-form-item field="business_license_info.business_time_endTime" label="有效期限结束日期">
          <a-date-picker
              v-model="mainForm.business_license_info.business_time_endTime2"
              style="width: 200px"
              placeholder="选择有效期结束日期"
              @change="(event) => { mainForm.business_license_info.business_time_endTime2 = event; mainForm.business_license_info.business_time_endTime = ''}"
          ></a-date-picker>
          <a-button @click="longTimeEffect('business_time_endTime')" :status="mainForm.business_license_info.business_time_endTime === '长期'?'danger':'primary'"
                    style="margin-left: 10px;">长期有效</a-button>
        </a-form-item>
      </div>
      <!-- 是否是金融机构 -->
      <div v-if="mainForm.finance_institution">
        <a-divider orientation="left">金融机构许可证信息</a-divider>
        <a-form-item field="finance_institution_info.finance_type" label="金融机构类型" :rules="[REQUIRED]">
          <a-select :style="{ width: '280px' }" v-model="mainForm.finance_institution_info.finance_type" placeholder="请选择金融机构类型">
            <a-option value="BANK_AGENT">银行业, 适用于商业银行、政策性银行、农村合作银行、村镇银行、开发性金融机构等</a-option>
            <a-option value="PAYMENT_AGENT">支付机构, 适用于非银行类支付机构</a-option>
            <a-option value="INSURANCE">保险业, 适用于保险、保险中介、保险代理、保险经纪等保险类业务</a-option>
            <a-option value="TRADE_AND_SETTLE">交易及结算类金融机构, 适用于交易所、登记结算类机构、银行卡清算机构、资金清算中心等</a-option>
            <a-option value="OTHER">其他金融机构</a-option>
          </a-select>
          <template #extra>
            <div><p>金融机构类型需与营业执照/登记证书上一致</p></div>
          </template>
        </a-form-item>
        <a-form-item field="finance_institution_info.finance_license_pics" label="金融机构许可证图片" :rules="[REQUIRED]">
          <div>
            <a-avatar :size="100" shape="square" @click="handleClickImg('finance_license_pics')" style="margin-right: 20px;">
              <icon-plus v-if="!mainForm.finance_institution_info.finance_license_pics" />
              <img alt="avatar" v-if="mainForm.finance_institution_info && mainForm.finance_institution_info.finance_license_pics" :src="mainForm.finance_institution_info.finance_license_pics" />
            </a-avatar>
          </div>
          <template #extra>
            <div>
              <p>1、根据所属金融机构类型的许可证要求提供,详情查看 <a-link @click="Examplediagramfinancial()" :hoverable="false" style="font-size: 12px;">示例图</a-link></p>
              <p>2、请提供为“申请商家主体”所属的许可证,可授权使用总公司/分公司的特殊资质。</p>
            </div>
          </template>
        </a-form-item>
      </div>
      <!-- 经营者/法人身份证件 -->
      <div>
        <a-divider orientation="left">经营者/法人身份证件</a-divider>
        <a-form-item field="id_holder_type" label="证件持有人类型" :rules="[REQUIRED]">
          <a-select :style="{ width: '280px' }" v-model="mainForm.id_holder_type" placeholder="请选择证件持有人类型">
            <a-option value="LEGAL">法人</a-option>
            <a-option value="SUPER">经办人</a-option>
          </a-select>
          <template #extra>
            若因特殊情况，无法提供法人证件时，可上传经办人。（经办人：经商户授权办理微信支付业务的人员，授权范围包括但不限于签约，入驻过程需完成账户验证）。
          </template>
        </a-form-item>
        <a-form-item field="id_doc_type" label="证件类型" :rules="[REQUIRED]">
          <a-select :style="{ width: '280px' }" v-model="mainForm.id_doc_type" placeholder="请选择证件类型">
            <a-option v-for="item in documentType" :key="item.value" :value="item.value">{{ item.title }}</a-option>
          </a-select>
        </a-form-item>
        <a-form-item v-if="mainForm.id_holder_type === 'LEGAL'" field="authorize_letter_copy" label="法定代表人说明函">
          <div>
            <a-avatar :size="100" shape="square" @click="handleClickImg('authorize_letter_copy')" style="margin-right: 20px;">
              <icon-plus v-if="!mainForm.authorize_letter_copy" />
              <img alt="avatar" v-if="mainForm.authorize_letter_copy" :src="mainForm.authorize_letter_copy" />
            </a-avatar>
          </div>
          <template #extra>
            <div>
              <p>若因特殊情况，无法提供法定代表人证件时，请参照示例图打印法定代表人说明函，全部信息需打印，不支持手写商户信息，并加盖公章
                <a-link @click="RepresentativeExamplediagram()" :hoverable="false" style="font-size: 12px;">示例图</a-link>
              </p>
            </div>
          </template>
        </a-form-item>
        <!-- 中国大陆居民-身份证 -->
        <div v-if="mainForm.id_doc_type === 'IDENTIFICATION_TYPE_MAINLAND_IDCARD'">
          <a-form-item field="id_card_info.id_card_copy" label="身份证人像面照片" :rules="[REQUIRED]">
            <div>
              <a-avatar :size="100" shape="square" @click="handleClickImg('id_card_copy')" style="margin-right: 20px;">
                <icon-plus v-if="!mainForm.id_card_info.id_card_copy" />
                <img alt="avatar" v-if="mainForm.id_card_info && mainForm.id_card_info.id_card_copy" :src="mainForm.id_card_info.id_card_copy" />
              </a-avatar>
            </div>
            <template #extra><div><p>请上传个体户经营者/法人的身份证人像面照片</p></div></template>
          </a-form-item>
          <a-form-item field="id_card_info.id_card_national" label="身份证国徽面照片" :rules="[REQUIRED]">
            <div>
              <a-avatar :size="100" shape="square" @click="handleClickImg('id_card_national')" style="margin-right: 20px;">
                <icon-plus v-if="!mainForm.id_card_info.id_card_national" />
                <img alt="avatar" v-if="mainForm.id_card_info && mainForm.id_card_info.id_card_national" :src="mainForm.id_card_info.id_card_national" />
              </a-avatar>
            </div>
            <template #extra><div><p>请上传个体户经营者/法人的身份证国徽面照片</p></div></template>
          </a-form-item>
          <a-form-item field="id_card_info.id_card_name" label="身份证姓名" :rules="[REQUIRED]">
            <a-input style="width: 400px;" v-model="mainForm.id_card_info.id_card_name" placeholder="请输入证件姓名"/>
            <template #extra><div><p>请填写个体户经营者/法定代表人对应身份证的姓名，2~30个中文字符、英文字符、符号</p></div></template>
          </a-form-item>
          <a-form-item field="id_card_info.id_card_number" label="身份证号码" :rules="[REQUIRED]">
            <a-input style="width: 400px;" v-model="mainForm.id_card_info.id_card_number" placeholder="请输入证件号码"/>
          </a-form-item>
          <a-form-item v-if="mainForm.organization_type === '2'" field="id_card_info.id_card_address" label="身份证居住地址" :rules="[REQUIRED]">
            <a-input style="width: 400px;" v-model="mainForm.id_card_info.id_card_address" placeholder="请输入证件居住地址"/>
          </a-form-item>
          <a-form-item field="id_card_info.id_card_valid_time_begin" label="身份证有效期开始时间" :rules="[REQUIRED]">
            <a-date-picker
                v-model="mainForm.id_card_info.id_card_valid_time_begin"
                style="width: 200px"
                placeholder="选择身份证有效期开始日期"
                @change="(event) => { mainForm.id_card_info.id_card_valid_time_begin = event }"
            ></a-date-picker>
          </a-form-item>
          <a-form-item field="id_card_info.id_card_valid_time" label="身份证有效期结束时间" :rules="[REQUIRED]">
            <a-date-picker
                v-model="mainForm.id_card_info.id_card_valid_time2"
                style="width: 200px"
                placeholder="选择身份证有效期结束日期"
                @change="(event) => { mainForm.id_card_info.id_card_valid_time2 = event; mainForm.id_card_info.id_card_valid_time = '' }"
            ></a-date-picker>
            <a-button @click="longTimeEffect('id_card_valid_time')" :status="mainForm.id_card_info.id_card_valid_time === '长期'?'danger':'primary'" style="margin-left: 10px;">长期有效</a-button>
          </a-form-item>
        </div>
        <!-- 非中国大陆居民-身份证 -->
        <div v-if="mainForm.id_doc_type !== 'IDENTIFICATION_TYPE_MAINLAND_IDCARD'">
          <a-form-item field="id_doc_info.id_doc_copy" label="证件正面照片" :rules="[REQUIRED]">
            <div>
              <a-avatar :size="100" shape="square" @click="handleClickImg('id_doc_copy')" style="margin-right: 20px;">
                <icon-plus v-if="!mainForm.id_doc_info.id_doc_copy" />
                <img alt="avatar" v-if="mainForm.id_doc_info && mainForm.id_doc_info.id_doc_copy" :src="mainForm.id_doc_info.id_doc_copy" />
              </a-avatar>
            </div>
            <template #extra>
              <div><p>请上传彩色照片or彩色扫描件or复印件（需加盖公章鲜章），可添加“微信支付”相关水印（如微信支付认证）。</p></div>
            </template>
          </a-form-item>
          <a-form-item field="id_doc_info.id_doc_copy_back" label="证件反面照片" :rules="[REQUIRED]">
            <div>
              <a-avatar :size="100" shape="square" @click="handleClickImg('id_doc_copy_back')" style="margin-right: 20px;">
                <icon-plus v-if="!mainForm.id_doc_info.id_doc_copy_back" />
                <img alt="avatar" v-if="mainForm.id_doc_info && mainForm.id_doc_info.id_doc_copy_back" :src="mainForm.id_doc_info.id_doc_copy_back" />
              </a-avatar>
            </div>
            <template #extra>
              <div><p>请上传彩色照片or彩色扫描件or复印件（需加盖公章鲜章），可添加“微信支付”相关水印（如微信支付认证）。</p></div>
            </template>
          </a-form-item>
          <a-form-item field="id_doc_info.ubo_id_doc_name" label="证件姓名" :rules="[REQUIRED]">
            <a-input style="width: 400px;" v-model="mainForm.id_doc_info.ubo_id_doc_name" placeholder="请输入证件姓名"/>
          </a-form-item>
          <a-form-item field="id_doc_info.ubo_id_doc_number" label="证件号码" :rules="[REQUIRED]">
            <a-input style="width: 400px;" v-model="mainForm.id_doc_info.ubo_id_doc_number" placeholder="请输入证件号码"/>
          </a-form-item>
          <a-form-item field="id_doc_info.ubo_id_doc_address" label="证件居住地址" :rules="[REQUIRED]">
            <a-input style="width: 400px;" v-model="mainForm.id_doc_info.ubo_id_doc_address" placeholder="请输入证件居住地址"/>
            <template #extra>
              <div><p>请按照证件上住址填写，若证件上无住址则按照实际住址填写，如广东省深圳市南山区xx路xx号xx室。</p></div>
            </template>
          </a-form-item>
          <a-form-item field="id_doc_info.ubo_id_doc_period_begin" label="证件有效期开始日期" :rules="[REQUIRED]">
            <a-date-picker
                v-model="mainForm.id_doc_info.ubo_id_doc_period_begin"
                style="width: 200px"
                placeholder="选择证件有效期开始日期"
                @change="(event) => { mainForm.id_doc_info.ubo_id_doc_period_begin = event }"
            ></a-date-picker>
          </a-form-item>
          <a-form-item field="id_doc_info.ubo_id_doc_period_end" label="证件有效期结束日期" :rules="[REQUIRED]">
            <a-date-picker
                v-model="mainForm.id_doc_info.ubo_id_doc_period_end"
                style="width: 200px"
                placeholder="选择证件有效期结束日期"
                @change="(event) => { mainForm.id_doc_info.ubo_id_doc_period_end = event }"
            ></a-date-picker>
          </a-form-item>
        </div>
      </div>
      <!-- 最终受益人信息列表(UBO) -->
      <div v-if="mainForm.organization_type === '2'">
        <a-divider orientation="left">最终受益人信息列表(UBO)</a-divider>
        <a-form-item field="owner" label="经营者/法人是否为受益人" :rules="[REQUIRED]">
          <a-switch v-model="mainForm.owner">
            <template #checked> 是 </template>
            <template #unchecked> 否 </template>
          </a-switch>
        </a-form-item>
        <div v-if="mainForm.owner">
          <a-form-item field="ubo_info_list[0].ubo_id_doc_type" label="证件类型" :rules="[REQUIRED]">
            <a-select :style="{ width: '280px' }" v-model="mainForm.ubo_info_list[0].ubo_id_doc_type" placeholder="请选择证件类型" :rules="[REQUIRED]">
              <a-option v-for="item in documentType" :key="item.value" :value="item.value">{{ item.title }}</a-option>
            </a-select>
          </a-form-item>
          <a-form-item field="ubo_info_list[0].ubo_id_doc_copy" label="证件正面照片" :rules="[REQUIRED]">
            <div>
              <a-avatar :size="100" shape="square" @click="handleClickImg('ubo_id_doc_copy')" style="margin-right: 20px;">
                <icon-plus v-if="!mainForm.ubo_info_list[0].ubo_id_doc_copy" />
                <img alt="avatar" v-if="mainForm.ubo_info_list && mainForm.ubo_info_list[0].ubo_id_doc_copy" :src="mainForm.ubo_info_list[0].ubo_id_doc_copy" />
              </a-avatar>
            </div>
            <template #extra>
              <div><p>请上传彩色照片or彩色扫描件or复印件（需加盖公章鲜章），可添加“微信支付”相关水印（如微信支付认证）。</p></div>
            </template>
          </a-form-item>
          <a-form-item field="ubo_info_list[0].ubo_id_doc_copy_back" label="证件反面照片" :rules="[REQUIRED]">
            <div>
              <a-avatar :size="100" shape="square" @click="handleClickImg('ubo_id_doc_copy_back')" style="margin-right: 20px;">
                <icon-plus v-if="!mainForm.ubo_info_list[0].ubo_id_doc_copy_back" />
                <img alt="avatar" v-if="mainForm.ubo_info_list && mainForm.ubo_info_list[0].ubo_id_doc_copy_back" :src="mainForm.ubo_info_list[0].ubo_id_doc_copy_back" />
              </a-avatar>
            </div>
            <template #extra>
              <div><p>请上传彩色照片or彩色扫描件or复印件（需加盖公章鲜章），可添加“微信支付”相关水印（如微信支付认证）。</p></div>
            </template>
          </a-form-item>
          <a-form-item field="ubo_info_list[0].ubo_id_doc_name" label="证件姓名" :rules="[REQUIRED]">
            <a-input style="width: 400px;" v-model="mainForm.ubo_info_list[0].ubo_id_doc_name" placeholder="请输入证件姓名"/>
          </a-form-item>
          <a-form-item field="ubo_info_list[0].ubo_id_doc_number" label="证件号码" :rules="[REQUIRED]">
            <a-input style="width: 400px;" v-model="mainForm.ubo_info_list[0].ubo_id_doc_number" placeholder="请输入证件号码"/>
          </a-form-item>
          <a-form-item field="ubo_info_list[0].ubo_id_doc_address" label="证件居住地址" :rules="[REQUIRED]">
            <a-input style="width: 400px;" v-model="mainForm.ubo_info_list[0].ubo_id_doc_address" placeholder="请输入证件居住地址"/>
            <template #extra>
              <div><p>请按照证件上住址填写，若证件上无住址则按照实际住址填写，如广东省深圳市南山区xx路xx号xx室。</p></div>
            </template>
          </a-form-item>
          <a-form-item field="ubo_info_list[0].ubo_id_doc_period_begin" label="证件有效期开始日期">
            <a-date-picker
                v-model="mainForm.ubo_info_list[0].ubo_id_doc_period_begin"
                style="width: 200px"
                placeholder="选择证件有效期开始日期"
                @change="(event) => { mainForm.ubo_info_list[0].ubo_id_doc_period_begin = event }"
            ></a-date-picker>
          </a-form-item>
          <a-form-item field="ubo_info_list[0].ubo_id_doc_period_end" label="证件有效期结束日期">
            <a-date-picker
                v-model="mainForm.ubo_info_list[0].ubo_id_doc_period_end"
                style="width: 200px"
                placeholder="选择证件有效期结束日期"
                @change="(event) => { mainForm.ubo_info_list[0].ubo_id_doc_period_end = event }"
            ></a-date-picker>
          </a-form-item>
        </div>
      </div>
    </a-form>

    <a-modal v-model:visible="showOssManager" :width="1100"  @ok="handleOss" title="oss资源管理" @cancel="showOssManager = false">
      <ossManages :initialize="showOssManager" @selected="changOssImage" :isSingle="true"></ossManages>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, watch, computed, ref } from 'vue';
  import { REQUIRED, VARCHAR20 } from '@/utils/validator';
  import ossManages from '@/components/oss-manage/index.vue';
  import { useAppStore } from '@/store';

  const appStore = useAppStore();
  const emit = defineEmits(['callback', 'callbackTab']);
  const props = defineProps({
    mainType: {
      type: String,
      default: "",
    },
    submitFrom: {
      type: Object,
      default: () => {},
    }
  });

  const mainDataFormRef = ref<any>('');
  const mainForm = ref<any>({});  // 表单主体内容
  const certTypeList = ref<Array<any>>([]);
  const selectedFormBtnName = ref<string>(''); // 点击图片绑定form
  const picIndex = ref<any>(0); // 存储身份证图片下标，方便赋值
  const selectedSku = ref(); // 选择的sku
  const showOssManager = ref<boolean>(false); // oss弹框

  // 主体类型
  const subjectType = computed(() => {
    return appStore.weChatApplyList;
  });
  const documentType = computed(() => {
    return appStore.weChatApplyDocTypeList;
  });

  // 选择图片
  const handleClickImg = (val: string, index?: number) => {
    selectedFormBtnName.value = val;
    picIndex.value = index;
    showOssManager.value = true;
  };
  // oss资源改变
  const changOssImage = (val:any) => {
    selectedSku.value = [];
    val.forEach((item:any)=>{
      selectedSku.value.push({url:item.url})
    })
  };
  // oss资源确定
  const handleOss = () => {
    showOssManager.value = false;
    let currentUrl = selectedSku.value[selectedSku.value.length -1].url;  // 当前选择的图片
    if (selectedFormBtnName.value === 'business_license_copy') {
      // 营业执照照片/登记证书照片
      mainForm.value.business_license_info.business_license_copy = currentUrl;
    } else if (selectedFormBtnName.value === 'finance_license_pics') {
      // 金融机构许可证图片
      mainForm.value.finance_institution_info.finance_license_pics = currentUrl;
    } else if (selectedFormBtnName.value === 'authorize_letter_copy') {
      // 法定代表人说明函
      mainForm.value.authorize_letter_copy = currentUrl;
    } else if (selectedFormBtnName.value === 'id_card_copy') {
      // 身份证人像面照片（中国大陆居民-身份证）
      mainForm.value.id_card_info.id_card_copy = currentUrl;
    } else if (selectedFormBtnName.value === 'id_card_national') {
      // 身份证国徽面照片（中国大陆居民-身份证）
      mainForm.value.id_card_info.id_card_national = currentUrl;
    } else if (selectedFormBtnName.value === 'id_doc_copy') {
      // 证件正面照片（非中国大陆居民-身份证）
      mainForm.value.id_doc_info.id_doc_copy = currentUrl;
    } else if (selectedFormBtnName.value === 'id_doc_copy_back') {
      // 证件反面照片（非中国大陆居民-身份证）
      mainForm.value.id_doc_info.id_doc_copy_back = currentUrl;
    } else if (selectedFormBtnName.value === 'ubo_id_doc_copy') {
      // 证件正面照片（最终受益人）
      mainForm.value.ubo_info_list[0].ubo_id_doc_copy = currentUrl;
    } else if (selectedFormBtnName.value === 'ubo_id_doc_copy_back') {
      // 证件反面照片（最终受益人）
      mainForm.value.ubo_info_list[0].ubo_id_doc_copy_back = currentUrl;
    }
    picIndex.value = 0.1
  };
  // 设置为长期有效
  const longTimeEffect = (type?: any) => {
    if (type === 'id_card_valid_time') {
     // 身份证有效期结束时间
      mainForm.value.id_card_info.id_card_valid_time = '长期';
      mainForm.value.id_card_info.id_card_valid_time2 = '';
    } else if (type === 'business_time_endTime') {
      // 营业执照等级证书有效期结束时间
      mainForm.value.business_license_info.business_time_endTime = '长期';
      mainForm.value.business_license_info.business_time_endTime2 = '';
    }
  };
  const RepresentativeExamplediagram = () => {
    window.open("https://kf.qq.com/faq/220127aUzAju220127UfiuQr.html");
  };
  const Examplediagramfinancial = () => {
    window.open("https://kf.qq.com/faq/220215IrMRZ3220215n6buiU.html");
  };

  // 上一步/下一步
  const nextStep = (name) => {
    emit("callbackTab", name);
  };
  const checkoutForm = async () => {
    const auth = await mainDataFormRef.value?.validate();
    if (!auth) {
      return true;
    } else {
      return false;
    }
  };
  // 组件暴露自己的属性
  defineExpose({
    checkoutForm
  });
  onMounted(() => {

  });
  watch(() => props.submitFrom, (val) => {
    mainForm.value = val;
    // 身份证有效期结束时间
    if (val.id_card_info.id_card_valid_time && val.id_card_info.id_card_valid_time !== '长期') {
      mainForm.value.id_card_info.id_card_valid_time2 = val.id_card_info.id_card_valid_time;
    }
    // 营业执照等级证书有效期结束时间
    if (val.business_license_info.business_time_endTime && val.business_license_info.business_time_endTime !== '长期') {
      mainForm.value.business_license_info.business_time_endTime2 = val.business_license_info.business_time_endTime;
    }

    if (!val.ubo_info_list) {
      mainForm.value.ubo_info_list = [{
        ubo_id_doc_type: "",
        ubo_id_doc_copy: "",
        ubo_id_doc_copy_back: "",
        ubo_id_doc_name: "",
        ubo_id_doc_number: "",
        ubo_id_doc_address: "",
        ubo_period_begin: "",
        ubo_period_end: "",
      }];
    }
  }, { immediate: true, deep: true });
  watch(() => props.mainType, (val) => {
    mainForm.value.organization_type = val;
  }, { immediate: true, deep: true });
  // 是否是金融机构
  watch(() => mainForm.value.finance_institution, (val) => {
    if (val) {
      if (!mainForm.value.finance_institution_info) {
        mainForm.value.finance_institution_info = {
          cert_copy: "",
          finance_type: "",
          finance_license_pics: "",
        }
      }
    }
  }, { immediate: true, deep: true });
  // 监听主体类型改变
  watch(() => mainForm.value.organization_type, (val) => {
    // console.log('监听主体类型改变', val);
    // 证书类型赋值
    let certType = [] as any;
    if (val === '3') {
      certType = [
        {label: "事业单位法人证书", value: "CERTIFICATE_TYPE_2388",}
      ]
    } else if (val === '2502') {
      certType = [
        {label: "统一社会信用代码证书", value: "CERTIFICATE_TYPE_2389",}
      ]
    } else if (val === '1708') {
      certType = [
        {label: "统一社会信用代码证书", value: "CERTIFICATE_TYPE_2389"},
        {label: "社会团体法人登记证书", value: "CERTIFICATE_TYPE_2394",},
        {label: "民办非企业单位登记证书", value: "CERTIFICATE_TYPE_2395",},
        {label: "基金会法人登记证书", value: "CERTIFICATE_TYPE_2396",},
        {label: "宗教活动场所登记证", value: "CERTIFICATE_TYPE_2399",},
        {label: "政府部门下发的其他有效证明文件", value: "CERTIFICATE_TYPE_2400",},
        {label: "执业许可证/执业证", value: "CERTIFICATE_TYPE_2520",},
        {label: "基层群众性自治组织特别法人统一社会信用代码证", value: "CERTIFICATE_TYPE_2521",},
        {label: "农村集体经济组织登记证", value: "CERTIFICATE_TYPE_2522",},
      ];
    }
    certTypeList.value = certType;
  }, { immediate: true, deep: true });
</script>

<style scoped lang="less">

</style>