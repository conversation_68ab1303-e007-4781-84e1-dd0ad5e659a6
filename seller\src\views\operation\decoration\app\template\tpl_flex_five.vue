<template>
  <div>
    <div flex flex-a-c>
      <div>
        <img h-72px w-72px :src="props.res.data.list[0].img" />
      </div>
      <div>
        <img h-72px w-72px :src="props.res.data.list[1].img" />
      </div>
      <div>
        <img h-72px w-72px :src="props.res.data.list[2].img" />
      </div>
      <div>
        <img h-72px w-72px :src="props.res.data.list[3].img" />
      </div>
      <div>
        <img h-72px w-72px :src="props.res.data.list[4].img" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  res: any
}>()
</script>

<style scoped>
img{
  display: block;
}
</style>
