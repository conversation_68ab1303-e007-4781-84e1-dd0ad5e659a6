<template>
  <div class="oss-wrapper">
    <a-card v-for="(item, index) in ossFileList" :key="index" class="oss-card">
      <div class="content">
        <img
          v-if="item.selected"
          class="active"
          src="@/assets/images/selected.png"
        />
        <img class="img" :src="item.url" @click="selectedParams(item)" />

        <div class="actions">
          <div class="btn">
            <a-tooltip content="下载">
              <icon-cloud-download @click="download(item)" />
            </a-tooltip>
          </div>
          <div class="btn">
            <a-tooltip content="预览">
              <icon-eye @click="look(item)" />
            </a-tooltip>
          </div>
          <div class="btn">
            <a-tooltip content="删除">
              <icon-delete @click="remove(item)" />
            </a-tooltip>
          </div>
        </div>
      </div>
    </a-card>
    <a-row
      style="
        display: flex;
        justify-content: end;
        width: 100%;
        margin-right: 25px;
      "
    >
      <a-pagination
        :current="apiParams.current"
        :total="apiParams.total"
        :page-size="apiParams.pageSize"
        @change="change"
      />
    </a-row>
    <a-modal v-model:visible="picVisible" title="查看图片" draggable>
      <a-image :src="file.url" width="100%" alt="无效的图片链接"></a-image>
      <template #footer>
        <span>文件类型：{{ file.fileType }} 文件大小：{{ file.msize }}</span>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, reactive } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { getFileListData, deleteFile } from '@/api/common';

  import store from '@/utils/storage';
  import useCurrentInstance from '@/hooks/useCurrentInstance';

  // 携带toekn
  const ossFileList = ref<any>([]); //
  //  获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;
  const type = ref<string>('multiple'); // 单选single；多选multiple
  const selectedWay = ref<any>([]); // 选择数据
  const picVisible = ref<boolean>(false); // 图片的modal
  const file = ref<any>({}); // 文件数据
  // 传递的参数
  const apiParams = reactive({
    // 搜索框对应data对象
    fileType: 'image',
    pageSize: 12, // 页面大小
    sort: 'createTime', // 默认排序字段
    order: 'desc', // 默认排序方式
    startDate: '', // 起始时间
    endDate: '', // 终止时间
    title: '',
    total: 0,
    current: 1,
  });
  const accessToken = ref(store.getAccessToken());
  const emit = defineEmits<{
    (e: 'selected', val: any, type: string): void;
  }>();
  interface PropsRule {
    isSingle?:boolean
  }
  const props = withDefaults(defineProps<PropsRule>(), {
    isSingle: false
  })


  // 选择
  const selectedParams = (item:any) => {
    if (type.value != 'multiple') {
      ossFileList.value.forEach((e:any) => {
        e.selected = false;
      });
      item.selected = true;
    
      selectedWay.value = item;
    }
    else{
      if (item.selected == false) {
        item.selected = true;
        selectedWay.value.push(item);
      } else {
        item.selected = false;
        for (let i = 0; i < selectedWay.value.length; i += 1) {
          if (selectedWay.value[i].id === item.id) {
            selectedWay.value.splice(i, 1);
            break;
          }
        }
      }
    }
    emit('selected', selectedWay.value, '');
  };
  // 下载
  const download = (v: any) => {
    window.open(
      `${v.url}?attname=&response-content-type=application/octet-stream`
    );
  };
  // 查看
  const look = (v: any) => {
    console.log(v);
    file.value = v;
    file.value.msize = `${((v.fileSize * 1.0) / (1024 * 1024)).toFixed(2)}MB`;
    picVisible.value = true;
  };

  // 缩略图请求接口
  const init = async (params = apiParams) => {
    type.value = props.isSingle ? 'single' : 'multiple';
    const res = await getFileListData(params);
    if (res.data.success) {
      res.data.result.records.forEach((item:any) => {
        item.selected = false;
      });
      ossFileList.value = res.data.result.records;
      const { current, size, total } = res.data.result;
      apiParams.current = current;
      apiParams.pageSize = size;
      apiParams.total = total;
    }
  };
  // 删除
  const remove = (v: any) => {
    console.log(v.id);
    modal.confirm({
      title: '确认删除',
      content: `您确认要删除文件${v.name}?`,
      alignCenter: false,
      onOk: async () => {
        const res = await deleteFile(v.id);
        if (res.data.success) {
          Message.success(`删除文件${v.name}成功`);
          // tablePageRef.value.init();
          init();
        }
      },
    });
  };
  // 分页改变事件
  const change = (v: any) => {
    const params = {
      ...apiParams,
      pageNumber: v,
    };
    init(params);
  };
  onMounted(() => {
    init();
  });
</script>

<style scoped lang="less">
  .none {
    display: none;
  }

  .oss-wrapper {
    display: flex;
    flex-wrap: wrap;
    position: relative;
    justify-content: center;
  }

  .oss-card {
    // display: flex;
    position: relative;
    margin: 10px 20px 10px 0;
    width: 200px;

    :hover {
      .content .other .name {
        color: #1890ff;
        transition: color 0.3s;
      }
    }

    cursor: pointer;

    .content {
      display: flex;
      flex-direction: column;

      :hover {
        .play {
          transition: opacity 0.3s;
          opacity: 1 !important;
        }
      }

      .img {
        height: 100px;
        object-fit: cover;
      }

      .actions {
        display: flex;
        align-items: center;
        height: 30px;
        background: #f7f9fa;
        border-top: 1px solid #e8e8e8;

        i:hover {
          color: #1890ff;
        }

        .btn {
          display: flex;
          justify-content: center;
          width: 33.33%;
          border-right: 1px solid #e8e8e8;
        }

        .btn-no {
          display: flex;
          justify-content: center;
          width: 33.33%;
        }
      }
    }
  }
  .active {
    width: 25px;
    position: absolute;
    top: 3px;
    right: 5px;
  }
</style>
