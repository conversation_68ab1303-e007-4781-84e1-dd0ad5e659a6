<template>
  <a-tabs @change="(val)=>{apiParams.grade = val}" :default-active-key="gradeStatusVar">
    <a-tab-pane key="GOOD" title="好评"></a-tab-pane>
    <a-tab-pane key="MODERATE" title="中评"></a-tab-pane>
    <a-tab-pane key="WORSE" title="差评"></a-tab-pane>
  </a-tabs>
  <a-card class="general-card" title="评价管理" :bordered="false">
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <tablePage ref="tablePageRef" :columns="columnsTable" :methods="sortMethods" :api="getMemberReview" :api-params="apiParams"
      :row-selection="checkbox" :bordered="true"/>
  </a-card>
</template>

<script setup lang="ts">
import searchTable from '@/components/search-column/index.vue';
import tablePage from '@/components/table-pages/index.vue';
import { getMemberReview } from '@/api/member';
import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
import { gradeList, commentStatus, replyStatus } from '@//utils/tools';
import { ref, reactive } from 'vue';
const gradeStatusVar = ref<string>('GOOD');
const apiParams = ref<any>({grade:gradeStatusVar.value});
const tablePageRef = ref('');
const checkbox = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});

const columnsSearch: Array<SearchRule> = [
  {
    label: '会员名称',
    model: 'memberName',
    disabled: false,
    input: true,
  },
  {
    label: '商品名称',
    model: 'goodsName',
    disabled: false,
    input: true,
  },
  {
    label: '评价',
    model: 'grade',
    disabled: false,
    select: {
      options: gradeList,
    },
  },
  {
    label: '下单时间',
    disabled: false,
    datePicker: {
      type: 'range',
    },
  },
];

const columnsTable: ColumnsDataRule[] = [
  {
    title: '会员名称',
    dataIndex: 'memberName',
  },
  {
    title: '商品名称',
    width: 300,
    dataIndex: 'goodsName',
  },
  {
    title: '评价内容',
    width: 200,
    dataIndex: 'content',
  },
  {
    title: '评论',
    dataIndex: 'grade',
    slot: true,
    slotData: {
      badge: gradeList,
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    slot: true,
    slotData: {
      badge: commentStatus,
    },
  },
  {
    title: '回复状态',
    dataIndex: 'replyStatus',
    slot: true,
    slotData: {
      badge: replyStatus,
    },
  },
  {
    title: '创建日期',
    width: 200,
    dataIndex: 'createTime',
  },
];

const sortMethods: MethodsRule = {
  title: '操作',
  width: 100,
  methods: [
    {
      title: '查看',
      callback: 'detail',
      type:"text" ,
      status:"success"
    },
  ],
};
</script>
