import BASE from '@/config/index';
import request, { commonUrl, Method } from '@/utils/axios';
import { ParamsRule } from '@/types/global';
import qs from 'query-string';

const uploadFile = `${commonUrl}/common/common/upload/file`;
export default uploadFile

// 删除文件
export function deleteFile(id: number | string) {
  return request({
    url: `/common/file/delete/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}
// 重命名文件
export function renameFile(params: any) {
  return request({
    url: `/common/file/rename`,
    method: Method.POST,
    needToken: true,
    params,
  });
}

// 获取热搜词
export function getHotWords() {
  return request({
      url: '/hotwords/hotwords',
      method: Method.GET,
      needToken: true
  })
}

// 设置热搜词
export function setHotWords(params: any) {
    return request({
        url: '/hotwords/hotwords',
        method: Method.POST,
        needToken: true,
        params
    })
}

// 删除热搜词
export function deleteHotWords(params: any) {
  return request({
      url: '/hotwords/hotwords',
      method: Method.DELETE,
      needToken: true,
      params
  })
}

// 获取历史热词
export function getHotWordsHistory(params: any) {
    return request ({
        url: '/hotwords/hotwords/history',
        method: Method.GET,
        needToken: true,
        params
    })
}

// 获取热词统计
export function getHotWordsStatistics(params: any) {
    return request({
        url: '/hotwords/hotwords/statistics',
        method: Method.GET,
        needToken: true,
        params
    })
}

// 系统设置
export function getSetting(key: string | number) {
    return request({
        url: `/setting/setting/get/${key}`,
        method: Method.GET,
        needToken: true,
    });
}
// 更新系统配置
export function setSetting(key: string | number, params?: any) {
    return request({
        url: `/setting/setting/put/${key}`,
        method: Method.PUT,
        needToken: true,
        data: params,
        headers: { 'Content-Type': 'application/json' },
    })
}

// 个人中心编辑
export function userInfoEdit(params:ParamsRule){
  return request({
    url:`/passport/user/edit`,
    method:Method.PUT,
    needToken:true,
    params,
  })
}

// 获取文件目录列表
export function getFileDirectory() {
  return request({
    url: `${commonUrl}/common/resource/fileDirectory`,
    method: Method.GET,
    needToken: true,
  })
}

// 添加文件目录
export function addFileDirectory(params: ParamsRule) {
  return request({
    url: `${commonUrl}/common/resource/fileDirectory`,
    method: Method.POST,
    needToken: true,
    data: params
  })
}

// 修改文件目录
export function updateFileDirectory(params: ParamsRule) {
  return request({
    url: `${commonUrl}/common/resource/fileDirectory`,
    method: Method.PUT,
    needToken: true,
    data: params
  })
}

// 删除文件目录
export function delFileDirectory(id: number | string) {
  return request({
    url: `${commonUrl}/common/resource/fileDirectory/${id}`,
    method: Method.DELETE,
    needToken: true
  })
}










