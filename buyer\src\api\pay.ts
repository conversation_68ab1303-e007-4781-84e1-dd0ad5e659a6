// eslint-disable-next-line import/no-cycle
import request, { Method, commonUrl } from '@/utils/axios';
import { ParamsRule } from '@/types/global';

/**
 * 获取支付详情
 * @param orderType 交易类型,可用值:TRADE,ORDER,RECHARGE
 * @param sn   订单编号
 * @param clientType  调起方式，PC
 */
export function tradeDetail (params: any) {
  return request({
    url: '/payment/cashier/tradeDetail',
    needToken: true,
    method: Method.GET,
    params
  });
}

/**
 * 支付
 * @param orderType 交易类型,可用值:TRADE,ORDER,RECHARGE
 * @param paymentMethod 支付方式 可用值:ALIPAY,WECHAT_PARTNER
 * @param payClient  调起方式 可用值：APP,NATIVE,JSAPI,H5
 * @param sn   订单编号
 */
export function pay (params: any) {
  return request({
    url: `/payment/cashier/pay/${params.paymentMethod}/${params.paymentClient}`,
    needToken: true,
    method: Method.GET,
    params
  });
}

/**
 * 支付结果查询
 * @param orderType 交易类型,可用值:TRADE,ORDER,RECHARGE
 * @param sn   订单编号
 */
export function payCallback (params: ParamsRule) {
  return request({
    url: `/payment/cashier/result`,
    needToken: true,
    method: Method.GET,
    params
  });
}

/**
 * 支付结果查询
 * @param orderType 交易类型,可用值:TRADE,ORDER,RECHARGE
 * @param sn   订单编号
 */
export function withdrawalSettingVO (params?: ParamsRule) {
  return request({
    url: `${commonUrl}/common/common/site/withdrawal`,
    needToken: true,
    method: Method.GET,
    params
  });
}
