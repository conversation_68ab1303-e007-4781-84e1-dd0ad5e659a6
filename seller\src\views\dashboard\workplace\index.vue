<template>
  <div class="box">
    <div class="left-side">
      <div class="panel">
        <Banner />
        <a-spin style="display: block">
          <DataPanel :res="result.data" />
          <todo-list :res="result.data" />
        </a-spin>
      </div>
      <a-grid :cols="24" :col-gap="16" :row-gap="16" style="margin-top: 16px">
        <a-grid-item
          :span="{ xs: 24, sm: 24, md: 24, lg: 12, xl: 12, xxl: 12 }"
        >
          <!-- <Preview :is-inline="true" /> -->
        </a-grid-item>
        <a-grid-item
          :span="{ xs: 24, sm: 24, md: 24, lg: 12, xl: 12, xxl: 12 }"
        >
          <!-- <OrderChart :is-inline="true" /> -->
        </a-grid-item>
        <a-grid-item
          :span="{ xs: 24, sm: 24, md: 24, lg: 24, xl: 24, xxl: 24 }"
        >
          <statisticalData v-if="!loading" :res="result.homeData" />
        </a-grid-item>
      </a-grid>
    </div>
    <!--<a-affix :offset-top="108">-->
      <div class="right-side">
        <a-grid :cols="24" :row-gap="16">
          <a-grid-item class="panel" :span="24">
            <Announcement />
          </a-grid-item>
          <a-grid-item class="cate" :span="24">
            <CategoriesPercent />
          </a-grid-item>
        </a-grid>
      </div>
    <!--</a-affix>-->
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, reactive } from 'vue';
  import { getDashboardData, getSellerHomeData } from '@/api/statisitics';
  import useLoading from '@/hooks/loading';
  import Banner from './components/banner.vue';
  import DataPanel from './components/data-panel.vue';
  import TodoList from './components/todo-list.vue';
  import CategoriesPercent from './components/categories-percent.vue';
  import Announcement from './components/announcement.vue';
  import statisticalData from './components/statistical-data.vue';

  const { loading, setLoading } = useLoading(false);
  const result = reactive({
    data: {},
    homeData: {},
  });
  const fetchData = async () => {
    setLoading(true);
    const res = await getDashboardData();
    if (res.data.success) {
      result.data = res.data.result;
    }
    setLoading(false);
  };
  const homeData = async () => {
    setLoading(true);
    const res = await getSellerHomeData();
    if (res.data.success) {
      result.homeData = res.data.result;
    }
    setLoading(false);
  };
  onMounted(() => {
    fetchData();
    homeData();
  });
</script>

<!--<script lang="ts">-->
<!--  export default {-->
<!--    name: 'Dashboard', // If you want the include property of keep-alive to take effect, you must name the component-->
<!--  };-->
<!--</script>-->

<style lang="less" scoped>
  .box {
    background-color: var(--color-fill-2);
    display: flex;
  }

  .left-side {
    flex: 1;
    overflow: auto;
  }

  .right-side {
    width: 280px;
    margin-left: 16px;
  }

  .panel {
    background-color: var(--color-bg-2);
    border-radius: 4px;
    overflow: auto;
  }
  :deep(.panel-border) {
    margin-bottom: 0;
    border-bottom: 1px solid rgb(var(--gray-2));
  }
  .moduler-wrap {
    border-radius: 4px;
    background-color: var(--color-bg-2);
    :deep(.text) {
      font-size: 12px;
      text-align: center;
      color: rgb(var(--gray-8));
    }

    :deep(.wrapper) {
      margin-bottom: 8px;
      text-align: center;
      cursor: pointer;

      &:last-child {
        .text {
          margin-bottom: 0;
        }
      }
      &:hover {
        .icon {
          color: rgb(var(--arcoblue-6));
          background-color: #e8f3ff;
        }
        .text {
          color: rgb(var(--arcoblue-6));
        }
      }
    }

    :deep(.icon) {
      display: inline-block;
      width: 32px;
      height: 32px;
      margin-bottom: 4px;
      color: rgb(var(--dark-gray-1));
      line-height: 32px;
      font-size: 16px;
      text-align: center;
      background-color: rgb(var(--gray-1));
      border-radius: 4px;
    }
  }
</style>

<style lang="less" scoped>
  // responsive
  .mobile {
    .container {
      display: block;
    }
    .right-side {
      // display: none;
      width: 100%;
      margin-left: 0;
      margin-top: 16px;
    }
  }
</style>
