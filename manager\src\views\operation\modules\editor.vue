<template>
  <div style="border: 1px solid #ccc">
    <Toolbar
      style="border-bottom: 1px solid #ccc"
      :editor="editorRef"
      :default-config="toolbarConfig"
      :mode="mode"
    />
    <Editor
      v-model="valueHtml"
      style="height: 500px; overflow-y: hidden"
      :default-config="editorConfig"
      :mode="mode"
      @onCreated="handleCreated"
    />
  </div>
</template>

<script setup lang="ts">
  import '@wangeditor/editor/dist/css/style.css';

  import { onBeforeUnmount, ref, shallowRef, onMounted } from 'vue';
  import { Editor, Toolbar } from '@wangeditor/editor-for-vue';

  const editorRef = shallowRef();
  const valueHtml = ref('<p>hello</p>');
  onMounted(() => {
    setTimeout(() => {
      valueHtml.value = '富文本编译器';
    }, 1500);
  });
  const mode = ref('default');
  const toolbarConfig = {};
  const editorConfig = { placeholder: '请输入内容...' };

  // 组件销毁时，也及时销毁编辑器
  onBeforeUnmount(() => {
    const editor = editorRef.value;
    if (editor == null) return;
    editor.destroy();
  });

  const handleCreated = (editor: any) => {
    editorRef.value = editor;
  };
  // export default {
  // components: { Editor, Toolbar },
  // setup() {

  //   return {
  //     editorRef,
  //     valueHtml,
  //     mode: 'default',
  //     mode: 'simple',
  //     toolbarConfig,
  //     editorConfig,
  //     handleCreated
  //   };
  // }
  // }
</script>
