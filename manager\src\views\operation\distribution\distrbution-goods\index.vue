<template>
  <a-card class="general-card" title="分销商品" :bordered="false">
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <a-row style="margin-bottom: 16px">
      <a-col :span="16">
        <a-space>
          <a-button  @click="delAll">
            批量下架
          </a-button>
        </a-space>
      </a-col>
    </a-row>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getDistributionGoods"
      :checkbox="true"
      @delete="handleDelete"
      @selectTableChange="selectTableChange"
      :api-params="apiParams"
      :bordered="true"
    >
      <template #thumbnail="{ data }">
        <a-image width="100" height="100" :src="data.thumbnail" />
      </template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { MethodsRule, ColumnsDataRule, SearchRule } from '@/types/global';
  import { getDistributionGoods, delDistributionGoods } from '@/api/operation';
  import { ref } from 'vue';
  import useCurrentInstance from '@/hooks/useCurrentInstance';
  import { Message } from '@arco-design/web-vue';

  const apiParams = ref({});
  const tablePageRef = ref<any>();
  // 获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;
  // 多选数据
  const selectList = ref([]); // 接受子组件传过来的值
  const ids = ref<string>(''); // 多选行id
  const columnsSearch: Array<SearchRule> = [
    {
      label: '商品名称',
      model: 'goodsName',
      disabled: false,
      input: true,
    },
  ];
  const columnsTable: ColumnsDataRule[] = [
    {
      title: '商品图片',
      dataIndex: 'thumbnail',
      slot: true,
      slotTemplate: 'thumbnail',
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
    },
    {
      title: '商品价格',
      dataIndex: 'price',
      currency: true,
    },
    {
      title: '添加时间',
      dataIndex: 'createTime',
    },
    {
      title: '店铺名称',
      dataIndex: 'storeName',
    },
    {
      title: '佣金金额',
      dataIndex: 'commission',
      currency: true,
    },
  ];

  const sortMethods: MethodsRule = {
    title: '操作',
    width: 250,
    fixed: 'right',
    methods: [
      {
        title: '下架',
        callback: 'delete',
        type:"text", 
        status:"danger"
      },
    ],
  };
  // 回调下架
  function handleDelete(data: any) {
    console.log(data.record.id, 'data.record.id');
    modal.confirm({
      title: '确认下架',
      content: `您确认要下架么?`,
      alignCenter: false,
      onOk: async () => {
        const res = await delDistributionGoods(data.record.id);
        if (res.data.success) {
          Message.success('删除下架');
          tablePageRef.value.init();
        }
      },
    });
  }
  // 选择的行
  const selectTableChange = (val: any) => {
    selectList.value = val;
  };
  // 批量下架
  const delAll = () => {
    if (selectList.value.length <= 0) {
      Message.error('您还未选择要下架的数据');
      return;
    }
    selectList.value.forEach((item: any) => {
      ids.value += `${item.id},`;
    });
    console.log(ids.value, 'ids.value');
    const joinid = ids.value.substring(0, ids.value.length - 1);
    modal.confirm({
      title: '确认下架',
      content: `您确认要下架所选的${selectList.value.length}条数据?`,
      alignCenter: false,
      onOk: async () => {
        const res = await delDistributionGoods(joinid);
        if (res.data.success) {
          Message.success('下架成功');
          tablePageRef.value.init();
        }
      },
    });
  };
</script>
