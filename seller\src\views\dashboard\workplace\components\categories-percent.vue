<template>
  <a-spin style="width: 100%">
    <a-card
      class="general-card"
      :header-style="{ paddingBottom: '0' }"
      :body-style="{
        padding: '20px',
      }"
    >
      <template #title> 店铺评分 </template>
      <div class="rate-box">
        <div>
          服务得分
          <a-rate readonly :default-value="userInfo.serviceScore" allow-half />
        </div>
        <div>
          交货得分
          <a-rate readonly :default-value="userInfo.deliveryScore" allow-half />
        </div>
        <div>
          评价得分
          <a-rate
            readonly
            :default-value="userInfo.descriptionScore"
            allow-half
          />
        </div>
      </div>
    </a-card>
  </a-spin>
</template>

<script lang="ts" setup>
  import { useUserStore } from '@/store';

  const { userInfo } = useUserStore();
</script>

<style scoped lang="less">
  .rate-box {
    > div {
      margin: 4px 0;
      display: flex;
      align-items: center;
      :deep(.arco-rate) {
        margin-left: 6px;
      }
    }
  }
</style>
