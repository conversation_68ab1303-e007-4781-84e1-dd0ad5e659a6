import request, { Method } from '@/utils/axios';

import { ParamsRule } from '@/types/global';
import qs from 'query-string';



/**
 *  获取优惠券列表
 */
export function getShopCouponList(params: ParamsRule) {
  return request({
    url: '/promotion/coupon',
    method: Method.GET,
    needToken: true,
    params,
  });
}
/**
 *  获取拼团列表
 */
export function getPintuanList(params: ParamsRule) {
  return request({
    url: '/promotion/pintuan',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 *  获取店铺直播间列表
 */
export function getLiveList(params: ParamsRule) {
  return request({
    url: '/broadcast/studio',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 * 获取店铺直播商品
 */
export function getLiveGoods(params: ParamsRule) {
  return request({
    url: '/broadcast/commodity',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 * 减免列表
 */
export function getFullDiscountList(params: ParamsRule) {
  return request({
    url: '/promotion/fullDiscount',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 * 获取分销商商品列表
 */
export function getDistributionGoods(params: ParamsRule) {
  return request({
    url: '/distribution/goods',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 * 获取秒杀活动数据
 */
export function getSeckillList(params: ParamsRule) {
  return request({
    url: '/promotion/seckill',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 * 获取分销订单列表
 */
export function getDistributionOrder(params: ParamsRule) {
  return request({
    url: '/distribution/order',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 * 获取平台优惠券活动
 */
export function getCouponActivityList(params: ParamsRule) {
  return request({
    url: '/promotion/couponActivity',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 * 获取砍价活动商品
 */
export function getKanJiaGoodsList(params: ParamsRule) {
  return request({
    url: '/promotion/kanJiaGoods',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 * 删除砍价活动商品
 */
export function delKanJiaGoods(id: number | string) {
  return request({
    url: `/promotion/kanJiaGoods/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}

/**
 * 积分商品列表
 */
export function getPointsGoodsList(params: ParamsRule) {
  return request({
    url: '/promotion/pointsGoods',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 * 删除积分商品
 */
export function deletePointsGoods(id: number | string) {
  return request({
    url: `/promotion/pointsGoods/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}

/**
 * 积分商品分类列表
 */
export function getPointsGoodsCategoryList() {
  return request({
    url: '/promotion/pointsGoodsCategory',
    method: Method.GET,
    needToken: true,
  });
}

// 删除积分商品分类
export function deletePointsGoodsCategory(id: number | string) {
  return request({
    url: `/promotion/pointsGoodsCategory/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}
// 添加积分商品分类
export function addPointsGoodsCategory(params: any) {
  return request({
    url: `/promotion/pointsGoodsCategory`,
    method: Method.POST,
    needToken: true,
    params,
  });
}
// 更新积分商品分类
export function updatePointsGoodsCategory(params: any) {
  return request({
    url: `/promotion/pointsGoodsCategory`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
//  更新优惠券状态
export function updatePlatformCouponStatus(params: any) {
  return request({
    url: `/promotion/coupon/status`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
//  作废优惠券
export function deletePlatformCoupon(id: number | string) {
  return request({
    url: `/promotion/coupon/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}
// 保存平台优惠券
export function saveActivityCoupon(params: ParamsRule) {
  return request({
    url: `/promotion/couponActivity`,
    method: Method.POST,
    needToken: true,
    data: params,
    headers: { 'Content-Type': 'application/json', },
  });
}
//  获取单个优惠券活动
export function getCouponActivity(id: any) {
  return request({
    url: `/promotion/couponActivity/${id}`,
    method: Method.GET,
    needToken: true,
  });
}
// 关闭优惠券活动
export function closeActivity(id: number | string) {
  return request({
    url: `/promotion/couponActivity/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}
// 获取平台优惠券
export function getPlatformCouponList(params: ParamsRule) {
  return request({
    url: `/promotion/coupon`,
    method: Method.GET,
    needToken: true,
    params
  });
}
// 满优惠列表
export function getFullDiscountById(id: any) {
  return request({
    url: `/promotion/fullDiscount/${id}`,
    method: Method.GET,
    needToken: true,
  });
}
// 开启、关闭满额活动
export function updateFullDiscount(id: string | number) {
  return request({
    url: `/promotion/fullDiscount/status/${id}`,
    method: Method.PUT,
    needToken: true,
  });
}
// 获取秒杀活动详情数据
export function seckillDetail(id: any, params?: ParamsRule) {
  return request({
    url: `/promotion/seckill/${id}`,
    method: Method.GET,
    needToken: true,
    params
  });
}
// 修改秒杀活动
export function updateSeckill(params: ParamsRule) {
  return request({
    url: `/promotion/seckill`,
    method: Method.PUT,
    needToken: true,
    data: params,
    headers: { 'Content-Type': 'application/json', },
  });
}
// 获取秒杀活动审核列表
export function seckillGoodsList(params: ParamsRule) {
  return request({
    url: `/promotion/seckill/apply`,
    method: Method.GET,
    needToken: true,
    params
  });
}
// 删除秒杀商品
export function delSeckillGoods(params: any) {
  return request({
    url: `/promotion/seckill/apply/${params.seckillId}/${params.id}`,
    method: Method.DELETE,
    needToken: true,
  });
}
// 关闭秒杀活动
export function updateSeckillStatus(id: string | number) {
  return request({
    url: `/promotion/seckill/status/${id}`,
    method: Method.PUT,
    needToken: true,
  });
}
// 删除秒杀活动
export function delSeckill(id: string | number) {
  return request({
    url: `/promotion/seckill/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}
// 系统设置
export function getSetting(key: string | number) {
  return request({
    url: `/setting/setting/get/${key}`,
    method: Method.GET,
    needToken: true,
  });
}
// 更新系统配置
export function setSetting(key: string | number, params?: any) {
  return request({
    url: `/setting/setting/put/${key}`,
    method: Method.PUT,
    needToken: true,
    data: params
  });
}
// 获取拼团详情
export function getPintuanDetail(id: any) {
  return request({
    url: `/promotion/pintuan/${id}`,
    method: Method.GET,
    needToken: true,
  });
}
// 获取拼团商品数据
export function getPintuanGoodsList(params: ParamsRule) {
  return request({
    url: `/promotion/pintuan/goods/${params.pintuanId}`,
    method: Method.GET,
    needToken: true,
  });
}
// 修改砍价活动商品
export function saveKanJiaActivityGoods(params: ParamsRule) {
  return request({
    url: `/promotion/kanJiaGoods`,
    method: Method.POST,
    needToken: true,
    data: params,
    headers: { 'Content-Type': 'application/json' },
  });
}
// 砍价活动商品详情
export function getKanJiaActivityGoodsById(id: any) {
  return request({
    url: `/promotion/kanJiaGoods/${id}`,
    method: Method.GET,
    needToken: true,
  });
}
// 修改砍价活动商品
export function editKanJiaActivityGoods(params: ParamsRule) {
  return request({
    url: `/promotion/kanJiaGoods`,
    method: Method.PUT,
    needToken: true,
    data: params,
    headers: { 'Content-Type': 'application/json', },
  });
}
// 是否推荐直播间
export function whetherStar(params: any) {
  return request({
    url: `/broadcast/studio/recommend/${params.id}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
// 获取直播间详情
export function getLiveInfo(id: any) {
  return request({
    url: `/broadcast/studio/${id}`,
    method: Method.GET,
    needToken: true,
  });
}
// 添加积分商品
export function addPointsGoods(params: ParamsRule) {
  return request({
    url: `/promotion/pointsGoods`,
    method: Method.POST,
    needToken: true,
    data: params,
    headers: { 'Content-Type': 'application/json', },
  });
}
// 修改积分商品状态
export function editPointsGoodsStatus(id: number | string) {
  return request({
    url: `/promotion/pointsGoods/status/${id}`,
    method: Method.PUT,
    needToken: true,
  });
}
// 修改积分商品
export function updatePointsGoods(params: ParamsRule) {
  return request({
    url: `/promotion/pointsGoods`,
    method: Method.PUT,
    needToken: true,
    data: params,
    headers: { 'Content-Type': 'application/json', },
  });
}
// 积分商品详情
export function getPointsGoodsById(id: any) {
  return request({
    url: `/promotion/pointsGoods/${id}`,
    method: Method.GET,
    needToken: true,
  });
}
// 获取优惠券领取记录
export function getCouponReceiveList(params?: ParamsRule) {
  return request({
    url: `/promotion/coupon/received`,
    method: Method.GET,
    needToken: true,
    params
  });
}