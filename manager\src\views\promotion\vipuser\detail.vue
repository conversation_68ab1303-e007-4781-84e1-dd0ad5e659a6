<template>
  <div>
    <a-card>
      <div class="member-header">
        <a-descriptions title="会员基本信息" :column="2">
          <a-descriptions-item label="用户昵称">{{ memberInfo.nickName }}</a-descriptions-item>
          <a-descriptions-item label="手机号">{{ memberInfo.mobile }}</a-descriptions-item>
          <a-descriptions-item label="是否有效">
            <a-tag
              v-for="item in vipEffectiveStatus"
              :key="item.value"
              v-show="memberInfo.vipEffective === item.value"
              :color="item.color"
            >
              {{ item.label }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="领取时间">{{ memberInfo.vipStartDate }}</a-descriptions-item>
          <a-descriptions-item label="结束时间">{{ memberInfo.vipEndDate }}</a-descriptions-item>
        </a-descriptions>
      </div>
    </a-card>
    
    <a-card class="mt-4">
      <a-tabs default-active-key="info">
        <a-tab-pane key="info" title="详细信息">
          <a-descriptions :column="1">
            <a-descriptions-item label="会员ID">{{ memberInfo.id }}</a-descriptions-item>
            <a-descriptions-item label="用户ID">{{ memberInfo.userId }}</a-descriptions-item>
            <a-descriptions-item label="会员等级">{{ memberInfo.vipLevel || '普通会员' }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ memberInfo.createTime }}</a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ memberInfo.updateTime }}</a-descriptions-item>
          </a-descriptions>
        </a-tab-pane>
        <a-tab-pane key="history" title="操作记录">
          <a-empty description="暂无操作记录" />
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { vipEffectiveStatus } from '@/utils/tools';
  import { Message } from '@arco-design/web-vue';

  const route = useRoute();
  const router = useRouter();
  const memberInfo = ref<any>({});

  // 获取会员详情
  const getMemberDetail = async () => {
    try {
      const memberId = route.query.id;
      if (!memberId) {
        Message.error('缺少会员ID参数');
        router.back();
        return;
      }
      
      // 这里应该调用API获取会员详情
      // const response = await getVipUserDetail(memberId);
      // memberInfo.value = response.data;
      
      // 临时使用路由传递的数据
      if (route.query.data) {
        memberInfo.value = JSON.parse(route.query.data as string);
      }
    } catch (error) {
      Message.error('获取会员详情失败');
      console.error('获取会员详情失败:', error);
    }
  };

  onMounted(() => {
    getMemberDetail();
  });
</script>

<style lang="less" scoped>
  .member-header {
    margin-bottom: 16px;
  }
  .mt-4 {
    margin-top: 16px;
  }
</style>