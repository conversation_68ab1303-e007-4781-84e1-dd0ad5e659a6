// eslint-disable-next-line import/no-cycle
import request, { Method, commonUrl } from '@/utils/axios';
import { ParamsRule } from '@/types/global';

// 设置密码
export function setPwd (params: ParamsRule) {
  return request({
    url: '/passport/member/wallet/set-password',
    method: Method.POST,
    needToken: true,
    data: params
  })
}

// 设置支付密码
export function setUpdatePwdOrdinary (params: ParamsRule) {
  return request({
    url: '/passport/member/wallet/update-password/ordinary',
    method: Method.GET,
    needToken: true,
    data: params
  })
}

// 修改会员资料
export function editMemberInfo (params: any) {
  return request({
    url: '/passport/member/editOwn',
    method: Method.PUT,
    needToken: true,
    data: params
  })
}

// 修改密码
export function editPwd (params: any) {
  return request({
    url: `/passport/member/modifyPass`,
    method: Method.PUT,
    needToken: true,
    data: params
  })
}
/**
 * 获取密码状态
 * */
export function logout() {
  return request({
    url: '/passport/member/logout',
    method: Method.POST,
    needToken: true
  });
}

// 设置支付密码
export function setPassword (params: any) {
  return request({
    url: `/wallet/wallet/set-password`,
    method: Method.POST,
    needToken: true,
    params,
  })
}

// 校验是否设置支付密码
export function checkPassword () {
  return request({
    url: `/wallet/wallet/check`,
    method: Method.GET,
    needToken: true,
  })
}

// 校验支付密码是否正确
export function isCheckPassword (params: any) {
  console.log('校验', params);
  return request({
    url: `/wallet/wallet/check/password`,
    method: Method.GET,
    needToken: true,
    params
  })
}
