import { ParamsRule } from '@/types/global';
import request, { Method } from '@/utils/axios';
import qs from 'query-string';



export function getMemberReview(params: ParamsRule) {
  return request({
    url: '/member/evaluation',
    method: Method.GET,
    needToken: true,
    params,
  });
}
//  获取会员列表
export function getMemberListData(params: ParamsRule) {
  return request({
    url: '/passport/member',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 *  会员状态修改
 */
export function updateMemberStatus(params:any) {
  return request({
    url: `/passport/member/updateMemberStatus`,
    method: Method.PUT,
    needToken: true,
    data:qs.stringify(params),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded', },
   
  });
}

/**
 *  添加会员基本信息
 */
export function addMember(params: ParamsRule) {
  return request({
    url: `/passport/member`,
    method: Method.POST,
    needToken: true,
    data: qs.stringify(params),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded', },
  });
}

/**
 *  修改会员基本信息
 */
export function updateMember(params: ParamsRule) {
  return request({
    url: `/passport/member`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
//  分页获取会员评价
export function memberReview(params: ParamsRule) {
  return request({
    url: '/member/evaluation/getByPage',
    method: Method.GET,
    needToken: true,
    params,
  });
}

//  删除评论
export function delMemberReview(id: string | number) {
  return request({
    url: `/member/evaluation/delete/${id}`,
    method: Method.PUT,
    needToken: true,
  });
}

//  查询会员历史积分
export function getHistoryPointData(params: ParamsRule) {
  return request({
    url: '/member/memberPointsHistory/getByPage',
    method: Method.GET,
    needToken: true,
    params,
  });
}

//  获取预存款明细列表数据
export function getUserWallet(params: ParamsRule) {
  return request({
    url: '/wallet/log',
    method: Method.GET,
    needToken: true,
    params,
  });
}

//  获取充值记录列表数据
export function getUserRecharge(params: ParamsRule) {
  return request({
    url: '/wallet/recharge',
    method: Method.GET,
    needToken: true,
    params,
  });
}

//  获取提现申请列表数据
export function getUserWithdrawApply(params: ParamsRule) {
  return request({
    url: '/wallet/withdrawApply',
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 获取会员详情
export function getMemberInfoData(id: any) {
  return request({
    url: `/passport/member/${id}`,
    method: Method.GET,
    needToken: true,
  });
}
// 收货地址
export function getMemberAddressData(params: ParamsRule) {
  return request({
    url: `/member/address/${localStorage.getItem('memberID')}`,
    method: Method.GET,
    needToken: true,
    params
  });
}
// 查询会员预存款
export const getMemberWallet = (params: any) => {
  return request({
    url: `/wallet/wallet`,
    method: Method.GET,
    needToken: true,
    params
  });
};
// 获取发票列表
export const getReceiptPage = (params: ParamsRule) => {
  return request({
    url: `/trade/receipt`,
    method: Method.GET,
    needToken: true,
    params
  });
};
// 添加会员收货地址
export const addMemberAddress = (params: ParamsRule) => {
  return request({
    url: `/member/address`,
    method: Method.POST,
    needToken: true,
    params,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};
// 修改会员收货地址
export const editMemberAddress = (params: ParamsRule) => {
  return request({
    url: `/member/address`,
    method: Method.PUT,
    needToken: true,
    params,
  });
};
// 删除会员地址
export const removeMemberAddress = (id: number | string) => {
  return request({
    url: `/member/address/delById/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
};
// 会员评价 查看详情
export const getMemberInfoReview = (id: number | string) => {
  return request({
    url: `/member/evaluation/get/${id}`,
    method: Method.GET,
    needToken: true,
  });
};
// 修改评价状态
export const updateMemberReview = (id: number | string, params: any) => {
  return request({
    url: `/member/evaluation/updateStatus/${id}`,
    method: Method.PUT,
    needToken: true,
    data: qs.stringify(params),
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
  });
};
// 审核提现申请
export const withdrawApply = (params: any) => {
  return request({
    url: `/wallet/withdrawApply`,
    method: Method.POST,
    needToken: true,
    params,
  });
};

// 查询会员数量
export const getMemberNum = (params: any) => {
  return request({
    url: `/passport/member/num`,
    method: Method.GET,
    needToken: true,
    params
  })
}


