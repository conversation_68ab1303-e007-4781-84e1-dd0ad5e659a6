<template>
  <div v-if="props.res" flex>
    <div class="nav-con"
      :style="{ borderRadius: props.res.data.round + 'px', backgroundColor: props.res.data.background, color: props.res.data.textColor }"
      @click="handleClickNav" w-914px h-46px>
      <div class="nav-item" v-for="(item, index) in props.res.data.list" :key="index">
        {{ item.title }}
        <div class="colum"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDesign } from '@/store'
const design = useDesign()
const props = defineProps<{
  res: any
}>()


function handleClickNav() {
  console.log(props.res)
  design.setCurrentPcDesign(props.res)
}
</script>

<style scoped>
.nav-con {
  width: 914px;
  height: 46px;
  border-radius: 10px;
  background: #fff;
  line-height: 46px;
  overflow: hidden;
  list-style: none;
  display: flex;
}

.nav-item {
  cursor: pointer;
  width: 103px;
  text-align: center;
  position: relative;
}

.colum {
  top: 15px;
  line-height: 46px;
  height: 14.7px;
  opacity: 1;
  border: 0.7px solid #cbc8c8;
  position: absolute;
  right: 0;
  width: 1px;
}
</style>
