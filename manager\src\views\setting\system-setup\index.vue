<template>
  <div>
    <!--<a-card>-->
      <!--<pageheader />-->
    <!--</a-card>-->
    <a-card class="mt_10">
      <a-tabs>
        <a-tab-pane
          v-for="item in tabPanes"
          :key="item.key"
          :title="item.title"
        >
          <component :is="item.component" />
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import base from './components/base.vue';
  import goods from './components/goods.vue';
  import order from './components/order.vue';
  import point from './components/point.vue';
  import oss from './components/oss.vue';
  import sms from './components/sms.vue';
  import logistics from './components/logistics.vue';
  import withdrawal from './components/withdrawal.vue';
  import im from './components/im.vue';
  import zhongzhi from './components/zhongzhi.vue';
  import new_point from './components/new_point.vue';

  const tabPanes = [
    {
      key: 'base',
      title: '基础设置',
      component: base,
    },
    {
      key: 'goods',
      title: '商品设置',
      component: goods,
    },
    {
      key: 'order',
      title: '订单设置',
      component: order,
    },
    {
      key: 'point',
      title: '积分设置',
      component: point,
    },
    {
      key: 'oss',
      title: '对象存储设置',
      component: oss,
    },
    {
      key: 'sms',
      title: '短信设置',
      component: sms,
    },
    {
      key: 'logistics',
      title: '快递查询设置',
      component: logistics,
    },
    {
      key: 'withdrawal',
      title: '提现设置',
      component: withdrawal,
    },
    {
      key: 'im',
      title: 'IM设置',
      component: im,
    },
    {
      key: 'zhongzhi',
      title: '中智分发平台',
      component: zhongzhi,
    },
    {
      key: 'new_point',
      title: '第三方积分',
      component: new_point,
    },
  ];
</script>

<style lang="less" scoped></style>
