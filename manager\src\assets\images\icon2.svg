<svg width="54" height="54" viewBox="0 0 54 54" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_1328_40591)">
<ellipse cx="26.5" cy="37.5" rx="11.5" ry="2.5" fill="#A667EB"/>
</g>
<g clip-path="url(#clip0_1328_40591)">
<rect x="15.0283" y="18.1052" width="12.9766" height="18.5206" rx="2.03077" fill="url(#paint0_linear_1328_40591)"/>
<mask id="mask0_1328_40591" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="18" y="15" width="21" height="24">
<path d="M20.4199 15.1194L30.1885 15.1194C30.8616 15.1194 31.5092 15.3765 31.9991 15.8381L37.4105 20.9378C37.9398 21.4366 38.2399 22.1317 38.2399 22.859L38.2399 36.8994C38.2399 37.9929 37.3534 38.8794 36.2599 38.8794L20.4199 38.8794C19.3264 38.8794 18.4399 37.9929 18.4399 36.8994L18.4399 17.0994C18.4399 16.0059 19.3264 15.1194 20.4199 15.1194Z" fill="#4D72D3"/>
</mask>
<g mask="url(#mask0_1328_40591)">
<g filter="url(#filter1_dii_1328_40591)">
<path d="M20.4199 15.1194L30.1885 15.1194C30.8616 15.1194 31.5092 15.3765 31.9991 15.8381L37.4105 20.9378C37.9398 21.4366 38.2399 22.1317 38.2399 22.859L38.2399 36.8994C38.2399 37.9929 37.3534 38.8794 36.2599 38.8794L20.4199 38.8794C19.3264 38.8794 18.4399 37.9929 18.4399 36.8994L18.4399 17.0994C18.4399 16.0059 19.3264 15.1194 20.4199 15.1194Z" fill="url(#paint1_linear_1328_40591)"/>
</g>
<g filter="url(#filter2_dii_1328_40591)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M34.2799 21.0594L38.2399 21.0594L38.2399 15.1195L32.2999 15.1195L32.2999 19.0794C32.2999 20.173 33.1864 21.0594 34.2799 21.0594Z" fill="white"/>
</g>
</g>
<g filter="url(#filter3_dii_1328_40591)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M26.2549 30.2324C26.2549 29.7277 26.664 29.3186 27.1687 29.3186L34.4422 29.3186C34.9469 29.3186 35.356 29.7277 35.356 30.2324C35.356 30.7372 34.9469 31.1463 34.4422 31.1463L27.1687 31.1463C26.664 31.1463 26.2549 30.7372 26.2549 30.2324Z" fill="white"/>
</g>
<g filter="url(#filter4_dii_1328_40591)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M26 24.9138C26 24.4091 26.4091 24 26.9138 24L34.1873 24C34.692 24 35.1011 24.4091 35.1011 24.9138C35.1011 25.4185 34.692 25.8277 34.1873 25.8277L26.9138 25.8277C26.4091 25.8277 26 25.4185 26 24.9138Z" fill="white"/>
</g>
<g filter="url(#filter5_dii_1328_40591)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M22.2137 30.2324C22.2137 29.7277 22.6055 29.3186 23.0889 29.3186L23.1663 29.3186C23.6496 29.3186 24.0414 29.7277 24.0414 30.2324C24.0414 30.7372 23.6496 31.1463 23.1663 31.1463L23.0889 31.1463C22.6055 31.1463 22.2137 30.7372 22.2137 30.2324Z" fill="white"/>
</g>
<g filter="url(#filter6_dii_1328_40591)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M22 24.9138C22 24.4091 22.3918 24 22.8751 24L22.9525 24C23.4359 24 23.8277 24.4091 23.8277 24.9138C23.8277 25.4185 23.4359 25.8277 22.9525 25.8277L22.8751 25.8277C22.3918 25.8277 22 25.4185 22 24.9138Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_f_1328_40591" x="2.99998" y="23" width="47" height="29" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="6" result="effect1_foregroundBlur_1328_40591"/>
</filter>
<filter id="filter1_dii_1328_40591" x="9.4399" y="10.1194" width="37.8" height="41.76" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.697798 0 0 0 0 0.346806 0 0 0 0 0.945833 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1328_40591"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1328_40591" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.131148"/>
<feGaussianBlur stdDeviation="0.0327869"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.968627 0 0 0 0 0.6 0 0 0 0 0.984314 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1328_40591"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.131148"/>
<feGaussianBlur stdDeviation="0.131148"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_1328_40591" result="effect3_innerShadow_1328_40591"/>
</filter>
<filter id="filter2_dii_1328_40591" x="26.2854" y="12.3856" width="17.9691" height="17.9691" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.28067"/>
<feGaussianBlur stdDeviation="3.00728"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.0450001 0 0 0 0 0.45 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1328_40591"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1328_40591" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.18712"/>
<feGaussianBlur stdDeviation="1.36695"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.690196 0 0 0 0 0.776941 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1328_40591"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.406154"/>
<feGaussianBlur stdDeviation="0.546779"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.75 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_1328_40591" result="effect3_innerShadow_1328_40591"/>
</filter>
<filter id="filter3_dii_1328_40591" x="20.2403" y="26.5847" width="21.1303" height="13.8568" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.28067"/>
<feGaussianBlur stdDeviation="3.00728"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00314236 0 0 0 0 0.0782449 0 0 0 0 0.754167 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1328_40591"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1328_40591" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.21846"/>
<feGaussianBlur stdDeviation="1.36695"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.57 0 0 0 0 0.6308 0 0 0 0 0.95 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1328_40591"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.820168"/>
<feGaussianBlur stdDeviation="0.546779"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.75 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_1328_40591" result="effect3_innerShadow_1328_40591"/>
</filter>
<filter id="filter4_dii_1328_40591" x="19.9854" y="21.2661" width="21.1303" height="13.8568" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.28067"/>
<feGaussianBlur stdDeviation="3.00728"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00314236 0 0 0 0 0.0782449 0 0 0 0 0.754167 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1328_40591"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1328_40591" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.21846"/>
<feGaussianBlur stdDeviation="1.36695"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.57 0 0 0 0 0.6308 0 0 0 0 0.95 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1328_40591"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.820168"/>
<feGaussianBlur stdDeviation="0.546779"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.75 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_1328_40591" result="effect3_innerShadow_1328_40591"/>
</filter>
<filter id="filter5_dii_1328_40591" x="18.1522" y="28.1001" width="9.95078" height="10.3883" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.28067"/>
<feGaussianBlur stdDeviation="2.03077"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.0450001 0 0 0 0 0.45 0 0 0 0.65 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1328_40591"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1328_40591" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.21846"/>
<feGaussianBlur stdDeviation="1.36695"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.57 0 0 0 0 0.6308 0 0 0 0 0.95 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1328_40591"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.820168"/>
<feGaussianBlur stdDeviation="0.546779"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.75 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_1328_40591" result="effect3_innerShadow_1328_40591"/>
</filter>
<filter id="filter6_dii_1328_40591" x="17.9384" y="22.7815" width="9.95078" height="10.3883" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.28067"/>
<feGaussianBlur stdDeviation="2.03077"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.0450001 0 0 0 0 0.45 0 0 0 0.65 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1328_40591"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1328_40591" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.21846"/>
<feGaussianBlur stdDeviation="1.36695"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.57 0 0 0 0 0.6308 0 0 0 0 0.95 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1328_40591"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.820168"/>
<feGaussianBlur stdDeviation="0.546779"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.75 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_1328_40591" result="effect3_innerShadow_1328_40591"/>
</filter>
<linearGradient id="paint0_linear_1328_40591" x1="16.9765" y1="18.1052" x2="16.9765" y2="31.0647" gradientUnits="userSpaceOnUse">
<stop stop-color="#E14BFE"/>
<stop offset="1" stop-color="#B84FD1"/>
</linearGradient>
<linearGradient id="paint1_linear_1328_40591" x1="18.4399" y1="15.1194" x2="18.4399" y2="38.8794" gradientUnits="userSpaceOnUse">
<stop stop-color="#E982FE"/>
<stop offset="1" stop-color="#B353FF"/>
</linearGradient>
<clipPath id="clip0_1328_40591">
<rect width="24" height="24" fill="white" transform="matrix(1 -8.74228e-08 -8.74228e-08 -1 15 39)"/>
</clipPath>
</defs>
</svg>
