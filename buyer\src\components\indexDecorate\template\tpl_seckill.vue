<script setup lang="ts">
import { useRouter } from 'vue-router'
import { unitPrice } from '@/utils/filters'
import { seckillByDay } from '@/api/promotion'

const router = useRouter()
const current = ref(0)
const list = ref([])
const goodsList = ref([])
const actStatus = ref(0)
const actName = ref('限时秒杀')
const currIndex = ref(0)
const currHour = ref('00')
const diffSeconds = ref(0)
const hours = ref(0)
const minutes = ref(0)
const seconds = ref(0)

let interval = null

function countDown(index) {
  clearInterval(interval)
  interval = null
  const zeroTime = new Date(new Date().toLocaleDateString()).getTime()
  const currTime = new Date().getTime()
  let actTime = 0
  const nowHour = new Date().getHours()

  if (list.value[index].timeLine > nowHour) {
    actStatus.value = 0
    actTime = zeroTime + list.value[index].timeLine * 3600 * 1000
  }
  else if (list.value[index].timeLine <= nowHour) {
    actStatus.value = 1
    actTime = index === list.value.length - 1
      ? zeroTime + 24 * 3600 * 1000
      : zeroTime + list.value[index + 1].timeLine * 3600 * 1000
  }

  currHour.value = list.value[index].timeLine
  diffSeconds.value = Math.floor((actTime - currTime) / 1000)
  interval = setInterval(() => {
    diffSeconds.value--
    updateTimerDisplay()
  }, 1000)
}

function updateTimerDisplay() {
  hours.value = padZero(Math.floor(diffSeconds.value / 3600))
  minutes.value = padZero(Math.floor((diffSeconds.value / 60) % 60))
  seconds.value = padZero(diffSeconds.value % 60)
  if (diffSeconds.value <= 0)
    clearInterval(interval)
}

function handleClickGoods(goods: any) {
  // const path = {
  //   path: '/goodsDetail',
  //   query: {
  //     skuId: goods.id,
  //     goodsId: goods.goodsId,
  //   },
  // }
  const path = { path: '/seckill' };
  const routeUrl = router.resolve(path);
  window.open(routeUrl.href, '_blank');
}

function padZero(num) { return num < 10 ? `0${num}` : num }

// watch(currIndex, countDown)

onMounted(() => {
  init()
})

function changeSwiper(e: any) {
  current.value = e - 1
}

const blockList: any = computed(() => {
  return Math.ceil(goodsList.value.length / 5)
})

async function init() {
  const res = await seckillByDay()
  if (res.data.success) {
    list.value = res.data.result
    goodsList.value = list.value[currIndex.value].seckillGoodsList
    countDown(currIndex.value)
  }
}

onBeforeUnmount(() => {
  clearInterval(interval)
})
</script>

<template>
  <div v-if="goodsList.length" class="seckill">
    <div w-190px class="aside hover-pointer">
      <div class="title">
        {{ actName }}
      </div>
      <div class="hour">
        <span>{{ currHour }}:00</span>点场 倒计时
      </div>
      <div v-if="actStatus === 1" class="count-down">
        <span>{{ hours }}</span>
        <span>{{ minutes }}</span>
        <span>{{ seconds }}</span>
      </div>
      <div v-else class="act-status">
        未开始
      </div>
    </div>

    <a-carousel v-model="current" arrow-class="arrow" indicator-type="slider" w-1010px :auto-play="{ interval: 10000 }"
      @change="changeSwiper">
      <a-carousel-item v-for="(block, i) in blockList" :key="i">
        <div h-full flex bg-white>
          <div v-for="(item, index) in goodsList.slice((current) * 5, (current + 1) * 5)" :key="index">
            <div class="content hover-pointer" @click.stop="handleClickGoods(item)">
              <img :src="item.goodsImage" width="140" height="140" :alt="item.goodsName">
              <div class="ellipsis">
                {{ item.goodsName }}
              </div>
              <div>
                <span>{{ unitPrice(item.price, '￥') }}</span>
                <span>{{ unitPrice(item.originalPrice, '￥') }}</span>
              </div>
            </div>
          </div>
        </div>
      </a-carousel-item>
    </a-carousel>
  </div>
</template>

<style scoped lang="less">
:deep(.arco-carousel-arrow-right) {
  background: #ededed !important;
}

:deep(.arco-carousel-arrow-left) {
  background: #ededed !important;
}

.seckill {
  width: 100%;
  height: 260px;
  display: flex;
  background-color: #eee;

  .aside {
    overflow: hidden;
    width: 190px;
    height: 100%;
    color: #fff;
    background-image: url('https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/hot.png');

    .title {
      width: 100%;
      text-align: center;
      font-size: 28px;
      margin-top: 31px;
    }

    .hour {
      margin-top: 90px;
      text-align: center;

      span {
        font-size: 18px;
      }
    }

    .count-down {
      margin: 10px 0 0 30px;

      >span {
        position: relative;
        float: left;
        width: 30px;
        height: 30px;
        text-align: center;
        background-color: #2f3430;
        margin-right: 20px;
        color: white;
        font-size: 20px;

        &::after {
          content: ':';
          display: block;
          position: absolute;
          right: -20px;
          font-weight: bolder;
          font-size: 18px;
          width: 20px;
          height: 100%;
          top: 0;
        }
      }

      >span:last-child::after {
        content: '';
      }
    }

    .act-status {
      margin: 10px 0 0 65px;
      font-size: 20px;
    }
  }

  .content {
    width: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    position: relative;

    &::after {
      content: '';
      display: block;
      position: absolute;
      top: 50%;
      right: 0;
      width: 1px;
      height: 200px;
      transform: translateY(-50%);
      background: linear-gradient(180deg, white, #eeeeee, white);
    }

    img {
      margin-top: 30px;
    }

    >div {
      width: 160px;
      margin-top: 10px;
      font-size: 12px;
      position: relative;
    }

    >div:nth-of-type(1):hover {
      color: @theme_color;
      cursor: pointer;
    }

    >div:nth-of-type(2) {
      border: 1px solid @theme_color;
      line-height: 24px;
      display: flex;
      text-align: center;

      span:nth-child(1) {
        color: #fff;
        font-size: 16px;
        width: 92px;
        background-color: @theme_color;
        position: relative;

        &::before {
          content: ' ';
          width: 0;
          height: 0;
          border-color: transparent white transparent transparent;
          border-style: solid;
          border-width: 24px 8px 0 0;
          position: absolute;
          top: 0;
          left: 84px;
        }
      }

      span:nth-child(2) {
        color: #999;
        width: 66px;
        text-decoration: line-through;
      }
    }
  }
}

.swiper-container {
  height: 260px;
  width: 1000px;
  margin-left: 10px;
  background-color: #fff;
}

.swiper-button-prev,
.swiper-button-next {
  background: #ccc;
  width: 25px;
  height: 35px;
  font-size: 16px;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.swiper-button-prev:hover,
.swiper-button-next:hover {
  background: #aaa;
}

.swiper-button-prev {
  left: 0;
  border-bottom-right-radius: 18px;
  border-top-right-radius: 18px;
  padding-right: 5px;
}

.swiper-button-next {
  right: 0;
  border-top-left-radius: 18px;
  border-bottom-left-radius: 18px;
  padding-left: 5px;
}

.ellipsis {
  /* 适用于webkit内核和移动端 */
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  height: 28px;
}
</style>
