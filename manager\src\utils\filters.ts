import dayjs from 'dayjs';
/* eslint-disable */
/**
 * 货币格式化
 * @param price
 */
function formatPrice(price: number) {
  return String(Number(price).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

// 返回格式化金额
export function unitPrice(val: number, unit?: string, location?: string) {
  if (!val) val = 0;
  const price = formatPrice(val);
  if (location === 'before') {
    return price.substr(0, price.length - 3);
  }
  if (location === 'after') {
    return price.substr(-2);
  }
  return (unit || '') + price;
}

/**
 * 处理unix时间戳，转换为可阅读时间格式
 * @param unix
 * @param format
 * @returns {*|string}
 */
export function unixToDate(unix: number, format?: string) {
  // eslint-disable-next-line no-underscore-dangle
  let _format = format || 'yyyy-MM-dd hh:mm:ss';
  const d = new Date(unix * 1000);
  const o: any = {
    'M+': d.getMonth() + 1,
    'd+': d.getDate(),
    'h+': d.getHours(),
    'm+': d.getMinutes(),
    's+': d.getSeconds(),
    'q+': Math.floor((d.getMonth() + 3) / 3),
    'S': d.getMilliseconds(),
  };
  if (/(y+)/.test(_format))
    _format = _format.replace(
      RegExp.$1,
      `${d.getFullYear()}`.substr(4 - RegExp.$1.length)
    );
  // eslint-disable-next-line no-restricted-syntax
  for (const k in o)
    if (new RegExp(`(${k})`).test(_format)) {
      _format = _format.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : `00${o[k]}`.substr(`${o[k]}`.length)
      );
    }
  return _format;
}

// 类型转换
export function clientTypeWay(val: any) {
  if (val == "ALL") {
    return "全品类";
  } else if (val == "PORTION_GOODS_CATEGORY") {
    return "商品分类";
  } else if (val == "PORTION_SHOP_CATEGORY") {
    return "店铺分类";
  } else if (val == "PORTION_GOODS") {
    return "指定商品";
  } else {
    return '未知';
  }
}

export function unixSellerBillStatus(val: any) {
  if (val == "OUT") {
    return "待对账";
  } else if (val == "CHECK") {
    return "待结算";
  } else {
    return '已完成';
  }
}

// 处理金钱格式
export const money = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/

//处理时间 00:00:00
export const dayFormatHHssMM = (day: string) => {
  return dayjs(new Date(day).getTime()).format('YYYY-MM-DD HH:mm:ss')
}

export const unitDatePickerEndTime = (v: any) => {
  const startSub = (v[1].split(" ")[1] = "00:00:00");
  const endSub = (v[1].split(" ")[1] = "23:59:59");
  const startTime = v[0].split(" ")[0] + " " + startSub;
  const endTime = v[1].split(" ")[0] + " " + endSub;
  return {
    startTime,
    endTime,
  };
};

