<template>
  <div w-584px h-343px rounded-10px flex bg-white class="box-shadow" v-if="props.res">
    <div class="left-side" w-254px>
      <img :src="props!.res!.data!.list![0].img">
    </div>
    <div w-330px px-16px>
      <div mt-33px flex justify-end>
        <icon-right-circle size="17px" />
      </div>
      <div flex flex-wrap>
        <div v-for="(item, index) in props.res.data.brandList" :key="index" text-center class="brand-item">
          <img :src="item.img" w-90px h-90px>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DragRule } from '@/views/operation/decoration/app/models/types';
const props = defineProps<{
  res: DragRule,

}>()
</script>

<style lang="less" scoped>
.left-side {
  overflow: hidden;
  width: 254px;
  height: 344px;
  border-radius: 9.8px 0 0 9.8px;
  opacity: 1;

  >img {
    width: 100%;
    height: 100%;
  }
}

.brand-item {
  width: 50%;
}
</style>
