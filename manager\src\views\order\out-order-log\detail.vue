<template>
    <a-card class="general-card" title="分账详情" :bordered="false">

      <!-- 表格 -->
      <tablePage
        ref="tablePageRef"
        :columns="columnsTable"
        :dataList="data"
        :enablePagination="false"
        :bordered="true"
      />
    </a-card>
  </template>
  
  <script setup lang="ts">
    import tablePage from '@/components/table-pages/index.vue';
    import { ColumnsDataRule } from '@/types/global';
    import { detailLog } from '@/api/order';
    import { outOrderResult } from '@/utils/tools';
    import { ref, onMounted } from 'vue';
    import { useRoute } from 'vue-router';
  
    const tablePageRef = ref('');
    const route = useRoute()
    const data = ref([]) // 表单数据
  
    // 表格列表
    const columnsTable: ColumnsDataRule[] = [
      {
        title: '二级商户号',
        dataIndex: 'subMchid',
      },
      
      {
        title: '二级商户号名称',
        dataIndex: 'subMchName',
      },
      {
        title: '商户分账单号',
        dataIndex: 'outOrderNo',
      },
      
      {
        title: '分账金额',
        dataIndex: 'amount',
        currency: true,
      },
      {
        title: '订单状态',
        dataIndex: 'result',
        slot: true,
        slotData: {
          badge: outOrderResult,
        },
      },
      {
        title: '分账明细单号',
        dataIndex: 'detailId',
      },
    ];

 // 初始化
const init = () => {
    detailLog(route.query.id).then((res) => {
        data.value = res.data.result
  })
}
// 初始化
onMounted(() => {
  init()
})
  </script>
  
  <style scoped></style>
  