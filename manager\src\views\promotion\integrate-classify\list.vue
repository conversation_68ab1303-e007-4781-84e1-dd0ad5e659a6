<template>
  <a-card class="general-card" title="积分分类" :bordered="false">
    <a-row style="margin-bottom: 16px">
      <a-col :span="16">
        <a-space>
          <a-button type="primary" @click="handleAdd">
            添加积分商品分类
          </a-button>
        </a-space>
      </a-col>
    </a-row>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getPointsGoodsCategoryList"
      :api-params="{ sort: 'startTime' }"
      @delete="handleDelete"
      @editor="handleEdit"
    >
    </tablePage>
    <!-- 添加/编辑modal -->
    <a-modal
      v-model:visible="pointsCategoryData.enableAddModal"
      :align-center="false"
      :footer="false"
    >
      <template #title> {{ title }} </template>
      <a-form
        ref="formRef"
        :model="pointsCategoryData.form"
        @submit="handleAddOk"
      >
        <a-form-item
          field="name"
          label="分类名称"
          :rules="[REQUIRED]"
          :validate-trigger="['change']"
        >
          <a-input v-model="pointsCategoryData.form.name" />
        </a-form-item>
        <a-form-item
          field="sortOrder"
          label="排序值"
          :rules="[REQUIRED]"
          :validate-trigger="['change']"
        >
          <!-- <a-input v-model="pointsCategoryData.form.sortOrder" /> -->
          <a-input-number
            v-model="pointsCategoryData.form.sortOrder"
            :min="0"
          />
        </a-form-item>
        <a-form-item label="操作">
          <a-button :loading="pointsCategoryData.formLoading" html-type="submit"
          type="primary" >保存</a-button
          >
        </a-form-item>
      </a-form>
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">
  import {
addPointsGoodsCategory,
deletePointsGoodsCategory,
getPointsGoodsCategoryList,
updatePointsGoodsCategory,
} from '@/api/promotion';
import tablePage from '@/components/table-pages/index.vue';
import useCurrentInstance from '@/hooks/useCurrentInstance';
import { ColumnsDataRule, MethodsRule } from '@/types/global';
import { REQUIRED, VARCHAR20 } from '@/utils/validator';
import { Message } from '@arco-design/web-vue';
import { FormInstance } from '@arco-design/web-vue/es/form';
import { reactive, ref } from 'vue';

  const tablePageRef = ref<any>();
  // 获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;
  const formRef = ref<FormInstance>();
  const title = ref<string>('');
  interface formInterface {
    enableAddModal: boolean;
    formLoading: boolean;
    fid: string | number;
    form: {
      name: string;
      sortOrder: number;
      parentId: number;
      level: number;
      id: string | number;
      [key: string]: any;
    };
    [key: string]: any;
  }

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '分类名称',
      dataIndex: 'name',
    },
  ];

  // todo 本页面操作列表为选择展示
  const sortMethods: MethodsRule = {
    title: '操作',
    width: 260,
    methods: [
      {
        title: '编辑',
        callback: 'editor',
        type:"text" ,
        status:"warning"
      },
      {
        title: '删除',
        callback: 'delete',
        type:"text" ,
        status:"danger"
      },
    ],
  };
  // 数据集
  const pointsCategoryData = reactive<formInterface>({
    enableAddModal: false,
    formLoading: false,
    fid: '', // 当前form的ids
    form: {
      name: '',
      sortOrder: 0,
      parentId: 0,
      level: 0,
      id: '',
    }, // 表单提交数据
  });
  // 点击添加
  function handleAdd() {
    pointsCategoryData.enableAddModal = true;
    title.value = '添加';
    pointsCategoryData.fid = '';
    pointsCategoryData.name = ''
    // Object.keys(pointsCategoryData.form).forEach((key) => {
    //   pointsCategoryData.form[key] = '';
    // });
  }
  // 添加/修改地址
  async function handleAddOk() {
    // pointsCategoryData.form.password = this.md5(pointsCategoryData.form.password);
    const auth = await formRef.value?.validate();
    if (!auth) {
      let res;
      !pointsCategoryData.fid
        ? (res = await addPointsGoodsCategory(pointsCategoryData.form))
        : (res = await updatePointsGoodsCategory(pointsCategoryData.form));

      if (res.data.success) {
        Message.success(`${pointsCategoryData.fid ? '修改' : '添加'}成功!`);
        pointsCategoryData.enableAddModal = false;
        tablePageRef.value.init();
      }
    }
  }
  // 点击修改地址
  function handleEdit(val: any) {
    title.value = '编辑';
    if (val) {
      pointsCategoryData.form.parentId = 0;
      pointsCategoryData.form.id = val.record.id;
      Object.keys(val.record).forEach((key) => {
        // eslint-disable-next-line no-unused-expressions,no-prototype-builtins
        pointsCategoryData.form.hasOwnProperty(key)? (pointsCategoryData.form[key] = val.record[key]): ''; });
      pointsCategoryData.fid = val.record.id;
      pointsCategoryData.enableAddModal = true;
    }
  }
  // 回调删除
  function handleDelete(data: any) {
    modal.confirm({
      title: '确认删除',
      content: `您确认要删除${data.record.name}?`,
      alignCenter: false,
      onOk: async () => {
        const res = await deletePointsGoodsCategory(data.record.id);
        if (res.data.success) {
          Message.success('删除成功');
          tablePageRef.value.init();
        }
      },
    });
  }
</script>
