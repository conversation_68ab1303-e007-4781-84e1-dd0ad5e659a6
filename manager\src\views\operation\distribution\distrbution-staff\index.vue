<template>
  <a-card class="general-card" title="分销员" :bordered="false">
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getDistributionListData"
      :api-params="apiParams"
      :bordered="true"
      ><template #btnList="{ data }">
        <a-space>
          <a-button type="text" status="warning" @click="memberList(data)">下级用户</a-button>
          <a-button type="text" @click="groupList(data)">团队列表</a-button>
          <a-button v-if="data?.distributionStatus != 'RETREAT'"
            type="text" status="danger"  @click="retreat(data)"> 清退</a-button>
          <a-button v-else type="text" status="success" @click="resume(data)">恢复</a-button>
        </a-space>
      </template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import {
    getDistributionListData,
    retreatDistribution,
    resumeDistribution,
  } from '@/api/operation';
  import { distributionStatus } from '@/utils/tools';
  import { ref } from 'vue';
  import useCurrentInstance from '@/hooks/useCurrentInstance';
  import { Message } from '@arco-design/web-vue';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const tablePageRef = ref<any>();
  //  获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;
  const columnsSearch: Array<SearchRule> = [
    {
      label: '会员名称',
      model: 'memberName',
      disabled: false,
      input: true,
    },
    {
      label: '状态',
      model: 'distributionStatus',
      disabled: false,
      select: {
        options: distributionStatus,
      },
    },
  ];

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '会员名称',
      dataIndex: 'memberName',
    },
    {
      title: '推广单数',
      dataIndex: 'distributionOrderCount',
    },
    {
      title: "推广人数",
      dataIndex: "peopleNum",
    },
    {
      title: '分销金额',
      dataIndex: 'rebateTotal',
      currency: true,
    },
    {
      title: '可提现金额',
      dataIndex: 'canRebate',
      currency: true,
    },
    {
      title: '冻结金额',
      dataIndex: 'commissionFrozen',
      currency: true,
    },
    {
      title: '状态',
      dataIndex: 'distributionStatus',
      slot: true,
      slotData: {
        badge: distributionStatus,
      },
    },
  ];

  const sortMethods: MethodsRule = {
    title: '操作',
    width: 300,
    methods: [
      {
        slot: true,
        slotTemplate: 'btnList',
      },
    ],
  };

  const apiParams = ref({
    status: 'PASS',
  });

  // 清退
  const retreat = (val: any) => {
    console.log(val, 'val');
    modal.confirm({
      title: '提示',
      content: `您确认要清退 ${val.memberName}?`,
      alignCenter: false,
      onOk: async () => {
        const res = await retreatDistribution(val.id);
        if (res.data.success) {
          Message.success('操作成功！');
          tablePageRef.value.init();
        }
      },
    });
  };
  // 恢复
  const resume = (val: any) => {
    modal.confirm({
      title: '提示',
      content: `您确认要恢复 ${val.memberName}?`,
      alignCenter: false,
      onOk: async () => {
        const res = await resumeDistribution(val.id);
        if (res.data.success) {
          Message.success('操作成功！');
          tablePageRef.value.init();
        }
      },
    });
  };
  // 下级列表
  const memberList = (val:any) => {
    router.push({ name: 'distribution-member', query: { id: val.id },});
  }
  // 团队列表
  const groupList = (val:any) => {
    router.push({ name: 'group-list', query: { id: val.id }, });
  }
</script>

<style scoped></style>
