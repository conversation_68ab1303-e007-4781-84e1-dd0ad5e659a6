<template>
  <div v-auto-animate overflow-y-auto>
    <!-- 单独给顶部广告以及banner做处理 -->
    <div v-if="currentDesignOfPc" v-auto-animate>
      <div p-16px border-b-1>
        <span>{{ currentDesignOfPc?.name }}</span>
      </div>
      <div px-16px v-if="defaultComponentType.includes(currentDesignOfPc.type)">
        <backgroundTpl :res="currentDesignOfPc" :text="currentDesignOfPc.models[0].label"
          :bind="currentDesignOfPc.models[0].bind" v-if="currentDesignOfPc.type === 'topAdvert'" />
        <div v-for="(item, index) in Object.keys(constComponent)" :key="index">
          <component :res="currentDesignOfPc" :text="currentDesignOfPc.name" :is="constComponent[item]"></component>
        </div>

        <div v-if="currentDesignOfPc.type !== 'topAdvert'">
          <div class="role" :key="index" v-for="(item, index) in currentDesignOfPc.models">
            <component :bind="item.bind" :delete="item.delete" :res="currentDesignOfPc" :text="item.label"
              :is="modelsTemplate[item.model]">
            </component>
          </div>
          <!-- 对于轮播图单独做处理 -->
          <a-button mt-20px h-40px type="outline" @click="pushBanner" long>添加</a-button>
        </div>
      </div>
    </div>
    <div v-if="pc[indexOfPc] && !currentDesignOfPc" v-auto-animate>
      <!-- 组件名称 -->
      <div p-16px border-b-1>
        <span v-if="pc[indexOfPc].name">{{ pc[indexOfPc].name }}</span>
      </div>
      <div px-16px>
        <!-- 拥有的组件权限 -->
        <div class="role" :key="index" v-for="(item, index) in pc[indexOfPc].roles">
          <component :res="pc[indexOfPc]" :is="roleTemplate[item]"></component>
        </div>
        <!-- 商品组件单独做处理 -->
        <mix :res="pc[indexOfPc]" v-if="pc[indexOfPc].type === 'mix'"></mix>

        <div v-if="pc[indexOfPc].models">
          <!-- 拥有的组件 -->
          <div class="role" :key="index" v-for="(item, index) in pc[indexOfPc].models">
            <component :res="pc[indexOfPc]" :max="item.max" :bind="item.bind" :text="item.label"
              :is="modelsTemplate[item.model]">
            </component>

          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import { useDesign } from '@/store'
import { storeToRefs } from 'pinia'
import heightTpl from '@/views/operation/decoration/components/height.vue'
import imgTpl from '@/views/operation/decoration/components/img.vue'
import linkTpl from '@/views/operation/decoration/components/link.vue'
import roundTpl from '@/views/operation/decoration/components/round.vue'
import backgroundTpl from '@/views/operation/decoration/components/background.vue'
import mix from '@/views/operation/decoration/pc/layout/modules/mix/mix.vue'
import { watch, ref } from 'vue'
import { defaultList, defaultTextList } from '@/views/operation/decoration/pc/models/goods';
import roles from '@/views/operation/decoration/pc/layout/modules/roles'
import models from '@/views/operation/decoration/pc/layout/modules/models'
const roleTemplate: any = roles
const modelsTemplate: any = models
const topAd = { heightTpl, imgTpl, linkTpl }

let constComponent: any = ref({ heightTpl, imgTpl, linkTpl, roundTpl })
const defaultComponentType = ['bannerAdvert', 'topAdvert', 'topNav']
const userDesign = useDesign();
const { indexOfPc, pc, currentDesignOfPc }: any = storeToRefs(userDesign)
watch(currentDesignOfPc, (val: any) => {
  // console.log('current', val)
  if (val.type === 'topAdvert') {
    userDesign.pinPc[0] = val
    constComponent = topAd
  } else if (val.type === 'topNav') {
    userDesign.pinPc[1] = val
    constComponent = { roundTpl }
  } else if (val.type === 'bannerAdvert') {
    userDesign.pinPc[2] = val
    constComponent = {}
  }
}, { deep: true, immediate: true })

// 添加banner
function pushBanner() {

  currentDesignOfPc.value.data.list.push(currentDesignOfPc.value.type === 'bannerAdvert' ? JSON.parse(JSON.stringify(defaultList)) : JSON.parse(JSON.stringify(defaultTextList)))
}

</script>

<style scoped>
</style>
