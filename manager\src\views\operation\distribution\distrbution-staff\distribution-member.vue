<template>
  <div class="search">
    <a-card :style="{ width: '100%' }">
      <a-button style="margin-bottom: 10px" @click="back()">返回</a-button>
      <tablePage ref="tablePageRef" :columns="columns" :dataList="memberList" 
        :bordered="true" />
    </a-card>
  </div>
</template>

<script setup lang='ts'>
import { getDistributionMember } from '@/api/operation';
import tablePage from '@/components/table-pages/index.vue';
import { ColumnsDataRule } from '@/types/global';
import { onMounted, ref } from "vue";
import { useRoute, useRouter } from 'vue-router';

const tablePageRef = ref<any>('');
const route = useRoute();
const memberList = ref<any>([]);
const columns: ColumnsDataRule[] = [
  {
    title: '客户昵称',
    dataIndex: 'nickName',
  },
  {
    title: '成交额',
    dataIndex: 'orderPrice',
    currency: true,
  },
  {
    title: '佣金总额',
    dataIndex: 'rebatePrice',
    currency: true,
  },
  {
    title: '订单数',
    dataIndex: 'orderNum',
  },
  {
    title: '最近下单时间',
    dataIndex: 'lastLoginDate',
  },
]
const router = useRouter()
// 返回
const back = () => {
  router.push({ name: 'distrbution-staff' })
}

async function init() {
  const res = await getDistributionMember(route.query.id || '');
  if(res.data.success){
    memberList.value = res.data.result.records
  }
}

onMounted(() => {
  init()
})
</script>

<style lang="less" scoped></style>
