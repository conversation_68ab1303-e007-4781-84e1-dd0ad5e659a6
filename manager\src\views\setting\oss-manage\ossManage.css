.search .oss-operation {
  margin-bottom: 2vh;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.search .oss-operation button {
  margin-right: 5px;
}
.none {
  display: none;
}
.oss-wrapper {
  display: flex;
  flex-wrap: wrap;
  position: relative;
}
.oss-card {
  margin: 10px 20px 10px 0;
  width: 290px;
  cursor: pointer;
}
.oss-card :hover .content .other .name {
  color: #1890ff;
  transition: color 0.3s;
}
.oss-card .content {
  display: flex;
  flex-direction: column;
}
.oss-card .content :hover .play {
  transition: opacity 0.3s;
  opacity: 1 !important;
}
.oss-card .content .img {
  height: 135px;
  object-fit: cover;
}
.oss-card .content .video {
  height: 135px;
  position: relative;
}
.oss-card .content .video .cover {
  height: 100%;
  width: 100%;
  object-fit: fill;
}
.oss-card .content .video .play {
  position: absolute;
  top: 43px;
  left: 117px;
  height: 50px;
  width: 50px;
  opacity: 0.8;
}
.oss-card .content .other {
  padding: 16px;
  height: 135px;
}
.oss-card .content .other .name {
  font-size: 16px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  margin-bottom: 4px;
}
.oss-card .content .other .key {
  overflow: hidden;
  text-overflow: ellipsis;
  height: 45px;
  word-break: break-all;
  color: rgba(0, 0, 0, 0.45);
}
.oss-card .content .other .info {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  overflow: hidden;
  text-overflow: ellipsis;
  height: 36px;
  word-break: break-all;
}
.oss-card .content .actions {
  display: flex;
  align-items: center;
  height: 50px;
  background: #f7f9fa;
  border-top: 1px solid #e8e8e8;
}
.oss-card .content .actions i:hover {
  color: #1890ff;
}
.oss-card .content .actions .btn {
  display: flex;
  justify-content: center;
  width: 33.33%;
  border-right: 1px solid #e8e8e8;
}
.oss-card .content .actions .btn-no {
  display: flex;
  justify-content: center;
  width: 33.33%;
}
.select-clear {
  color: #2D8cF0;
  cursor: pointer;
}
.aaa {
  color: red;
}
