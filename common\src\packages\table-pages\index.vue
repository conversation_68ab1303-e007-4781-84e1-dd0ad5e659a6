<template>
  <div>
    <a-table row-key="id" :loading="initData.data.loading" :data="initData.data.result.records || initData.data.result"
      :bordered="bordered ? false : { cell: true }" :pagination="false"
      :row-selection="checkbox ? initData.data.rowSelection : undefined" :scroll="{ x: '100%' }"
      @selection-change="selectTableChanges" v-model:selectedKeys="initData.data.selectedList">
      <template #columns>
        <!-- 循环表头 -->
        <a-table-column v-for="(item, index) in props.columns" :key="index" :title="item.title"
          :data-index="item.dataIndex" :tooltip="item.tooltip && item.tooltip === 'none' ? false : true"
          :width="Number(item.width)" :ellipsis="!item.hasOwnProperty('ellipsis') ? true : item.ellipsis">
          <!-- 自定义模版 -->
          <template v-if="item.slot || item.currency || item.empty || item.render" #cell="{ record, column, rowIndex }">
            <!-- 插槽 自定义-->
            <slot v-if="item.slot && item.slotTemplate" :name="item.slotTemplate" :record="record[item.dataIndex]"
              :column="column" :row-index="rowIndex" :data="record"></slot>
            <!-- 标签展示 -->
            <a-tag v-if="item.slot && item.slotData?.tag" :color="item.slotData.tag.find((tag: any) => {
              return tag.value === record[item.dataIndex];
            })?.color
              ">{{
                item.slotData.tag.find((tag: any) => {
                  return tag.value === record[item.dataIndex];
                })?.label
              }}</a-tag>
            <!-- 商品展示 -->
            <div v-if="item.slot && item.slotData?.goods" class="flex">
              <a-image width="50" height="50" :src="record[item.slotData.goods.goodsImage]" show-loader>
                <template #loader>
                  <div class="loader-animate" />
                </template>
              </a-image>
              <div style="margin-left: 5px">
                <a-typography-text copyable bold>
                  {{ record[item.slotData.goods.goodsName] }}
                </a-typography-text>

                <div class="flex">
                  <div v-if="record?.goodsUnit">
                    <a-typography-text style="font-size: 12px" type="secondary">
                      {{ record.goodsUnit }}
                    </a-typography-text>
                  </div>

                </div>
              </div>
            </div>
            <!-- 货币展示 -->
            <a-typography-text v-if="item.currency">
              {{ unitPrice(record[item.dataIndex], '¥') }}
            </a-typography-text>
            <!-- 空值展示 -->
            <a-typography-text v-if="item.empty">
              {{ record[item.dataIndex] || item.empty }}</a-typography-text>

            <!-- 徽标展示 -->
            <div v-if="item.slot && item.slotData?.badge">
              <a-badge style="margin-right: 5px" v-if="item.slotData.badge.find((tag: StatusRules) => {
                return tag.value === record[item.dataIndex];
              })" :color="item.slotData.badge.find((tag: StatusRules) => {
                return tag.value === record[item.dataIndex];
              })?.color
                "></a-badge>
              {{
                item.slotData.badge.find((tag: StatusRules) => {
                  return tag.value === record[item.dataIndex];
                })?.label || ''
              }}
            </div>
          </template>
        </a-table-column>
        <!-- 方法 -->
        <a-table-column v-if="props?.methods?.methods" :width="props.methods?.width" align="center"
          :title="props.methods?.title" :fixed="props.methods?.fixed">
          <template #cell="{ record, column, rowIndex }">
            <a-space>
              <div v-for="(btn, index) in props.methods.methods" :key="index">
                <!-- 插槽 自定义-->
                <slot v-if="btn.slot && btn.slotTemplate" :name="btn.slotTemplate" :record="record" :column="column"
                  :row-index="rowIndex" :data="record"></slot>
                <a-button v-else size="small" class="btn" :type="btn.type" :status="btn.status" @click="
                  handleClickBtn({
                    ...btn,
                    record: { ...record, ...column, ...rowIndex },
                  })
                  ">{{ btn.title }}
                </a-button>
              </div>
            </a-space>
          </template>
        </a-table-column>
      </template>
    </a-table>
    <div class="paginationBox" v-if="enablePagination">
      <a-pagination :total="initData.data.result.total || 0" :show-page-size="apiParams.pageSize ? false : true"
        :current="initData.data.params.pageNumber" :page-size="initData.data.params.pageSize"
        @change="(number: number) => { initData.data.params.pageNumber = number; }"
        @page-size-change="(number: number) => { initData.data.params.pageSize = number; }" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { unitPrice } from '../filters';
import { ColumnsDataRule, StatusRules, InitDataRule } from '../types'
import {
  onMounted,
  reactive,
  ref,
  toRefs,
  watch,
} from 'vue';
const table = ref<null>(null);


const props = defineProps({
  columns: {
    type: Array<ColumnsDataRule | any>,
    default: () => {
      return [];
    },
  },
  methods: {
    type: Object,
    default: () => {
      return {};
    },
  },
  // 最大显示内容 一般来说就是不分页的话才会使用到
  max: {
    type: null,
    default: '',
  },
  // 请求接口
  api: {
    type: null,
    default: '',
  },
  // 不请求接口 传数据
  dataList: {
    type: null,
    default: '',
  },
  // 附加请求参数
  apiParams: {
    type: null,
    default: '',
  },
  // 自定义请求参数
  customApiParams: {
    type: null,
    default: '',
  },
  // 是否展示分页
  enablePagination: {
    type: Boolean,
    default: true,
  },
  // 是否展示多选框
  checkbox: {
    type: Boolean,
    default: false,
  },
  radio: {
    type: Boolean,
    default: false,
  },


  // 表格行key的取值字段
  defaultRowKey: {
    type: null,
    default: 'id',
  },
  defaultSelectedKeys: {
    type: Array,
    default: () => {
      return []
    }
  },
  // 是否展示边框
  bordered: {
    type: Boolean,
    default: false,
  },
  pageSize: {
    type: Number,
    default: 10
  }
});
const initData: any = reactive<InitDataRule>({
  data: {
    result: {
      current: 1,
      total: 0,
      records: [],
    }, // 请求内容
    loading: false, // 加载动画
    rowSelection: {
      type: props.radio ? 'radio' : 'checkbox',
      showCheckedAll: true,
      onlyCurrent: false,
      selectedRowKeys: [],
    },
    params: {
      pageNumber: 1, // 当前页数
      pageSize: props.pageSize || 10, // 页面大小
      sort: 'createTime', // 默认排序字段
      order: 'desc', // 默认排序方式
    },
    recordsList: [], // 查询的数据总合
    selectedRowList: [],  // 选择的数据总合
    selected: [], // 已选择的商品
    ids: [],
    selectedList: []
  },
});

const defaultRow = ref<Array<any>>([]);
const emit = defineEmits(['selectTableChange', 'parentComponentData', 'selectionChanges']);
// 已选择的数据行发生改变时触发
const selectTableChanges = (keys: Array<string | number>) => {
  console.log("selectTableChanges", keys)
  emit('selectionChanges', keys);
  initData.data.ids = keys;
  initData.data.rowSelection.selectedRowKeys = keys;
  // 将id进行循环赋值给父级
  const exportTableList: any = [];
  // 如果有传值内容则判断
  if (initData.data.selectedRowList.length) {
    keys.forEach((id) => {
      exportTableList.push(
        // initData.data.recordsList.find((item) => {return item.id == id;})
        // initData.data.selectedRowList.find((item) => {return item.skuId == id})
        initData.data.selectedRowList.find((item: any) => {
          return item.id == id;
        })
      );
    });
  }

  //  将选择的内容抛给子级
  emit('selectTableChange', exportTableList.length ? exportTableList : keys);
};

// 初始化内容
async function init(val = {}, isRefresh = false) {
  initData.data.loading = true;
  const { apiParams } = toRefs(props);
  let submit;
  props.apiParams ? (submit = { ...val, ...apiParams.value }) : (submit = val);
  submit = { ...initData.data.params, ...submit };
  if (props.api) {
    const res = await props.api(props.customApiParams || submit); // 使用父级给的api地址
    if (res.data.success) {
      //  最后判断如果有 max的话 截取max位置的内容
      if (props.max) {
        res.data.result = res.data.result.splice(0, props.max);
      } else {
        const records = [] as any;
        records.push(...(res.data.result.records || res.data.result));
        initData.data.recordsList = records;
        // 获取当前已选择的数据
        const map = new Map();
        const myData = [...initData.data.selectedRowList, ...records, ...defaultRow.value];
        initData.data.selectedRowList = myData.filter(item => {
          return !map.has(item.id) && map.set(item.id, item.id)
        });
      }
      // 请求到的数据全部给赋值
      initData.data.result = res.data.result;
      if (initData.data.result) {
        emit('parentComponentData', initData.data.result);
      }
    }
  } else if (props.dataList.length >= 0) {
    initData.data.result = reactive<any>([...props.dataList]);
  }
  initData.data.loading = false;
}

// 点击按钮进行回调方法
function handleClickBtn(data: any) {
  const { callback } = data;
  emit(callback, data);
}

// 监听分页值的改变并进行查询
watch(
  () => [initData.data.params, props.dataList, props.defaultSelectedKeys],
  (val) => {
    val ? init() : '';
  },
  {
    deep: true,
  }
);
// 初始化监听
watch(
  () => [props.apiParams],
  (val) => {
    delete initData.data.params.pageNumber;
    initData.data.params.pageNumber = 1;
  }, { deep: true }
);

onMounted(() => {
  init();
  defaultRow.value = props.defaultSelectedKeys.map((item: any) => {
    item.id = item.skuId;
    return item;
  });
  // initData.data.selectedList = props.defaultSelectedKeys;
  initData.data.selectedList = props.defaultSelectedKeys.map((item: any) => {
    return item.skuId;
  });
});
defineExpose({
  init,
  table,
  initData,
});
</script>

<style lang="less" scoped>
.btn {
  margin-right: 10px;
}

.loader-animate {
  width: 100%;
  height: 100%;
  background: linear-gradient(-60deg,
      var(--color-fill-2) 25%,
      var(--color-neutral-3) 40%,
      var(--color-fill-3) 55%);
  background-size: 400% 100%;
  animation: loop-circle 1.5s cubic-bezier(0.34, 0.69, 0.1, 1) infinite;
}

@keyframes loop-circle {
  0% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0 50%;
  }
}

.qrcode {
  width: 15px;
}

.paginationBox {
  margin-top: 18px;
  display: flex;
  flex-direction: row-reverse;
}
</style>
