<template>
  <div class="sign-up">
    <div style="height: 50px;"></div>

    <div class="logo-box">
      <div class="logo">
        <img src="/src/assets/logo_transparent.png" :style="{width: '150px'}"  />
        <div>修改密码</div>
      </div>
      <div class="login"><span @click="toLogin()">前往登录</span></div>
    </div>
    <div class="register-box">
      <!--注册-->
      <a-form ref="retrieveRef" size="large" layout="vertical" v-if="data.step === 0"
              :style="{ width: '310px', margin: '0 auto' }" @submit-success="next" :model="data.retrieveForm">
        <a-form-item :hide-asterisk="true" field="mobile" :rules="[REQUIRED, MOBILE]">
          <a-input v-model="data.retrieveForm.mobile" allow-clear placeholder="请输入手机号">
            <template #prefix>
              <icon-lock />
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 256 256">
                <path fill="currentColor" d="M231.88 175.08A56.26 56.26 0 0 1 176 224C96.6 224 32 159.4 32 80a56.26 56.26 0 0 1 48.92-55.88a16 16 0 0 1 16.62 9.52l21.12 47.15v.12A16 16 0 0 1 117.39 96c-.18.27-.37.52-.57.77L96 121.45c7.49 15.22 23.41 31 38.83 38.51l24.34-20.71a8.12 8.12 0 0 1 .75-.56a16 16 0 0 1 15.17-1.4l.13.06l47.11 21.11a16 16 0 0 1 9.55 16.62"/>
              </svg>
            </template>
          </a-input>
        </a-form-item>
        <a-form-item :hide-asterisk="true" field="code" :rules="[REQUIRED]">
          <a-input v-model="data.retrieveForm.code" allow-clear placeholder="请输入手机验证码">
            <template #prefix>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24">
                <g fill="none">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M2 5a3 3 0 0 1 3-3h10a3 3 0 0 1 3 3v6a3 3 0 0 1-3 3h-3.586l-3.707 3.707A1 1 0 0 1 6 17v-3H5a3 3 0 0 1-3-3V5zm20 4v6c0 1-.6 3-3 3h-1v3c0 .333-.2 1-1 1c-.203 0-.368-.043-.5-.113L12.613 18H9l3-3h3c1.333 0 4-.8 4-4V6c1 0 3 .6 3 3z" fill="currentColor"/>
                </g>
              </svg>
            </template>
            <template #append><span class="code-msg" @click="sendCode">{{ data.codeMsg }}</span></template>
          </a-input>
        </a-form-item>
        <a-form-item no-style>
          <a-button @click.stop="verifyBtnClick" :status="data.verifyStatus?'success':'danger'" long :style="{marginBottom: '20px'}" :loading="loading">
            {{ data.verifyStatus?'验证通过': '点击完成安全验证' }}</a-button>
          <!-- 拼图验证码 -->
          <verify ref="verifyDom" class="verify-con" verify-type="FIND_USER" @on-change="verifyChange"></verify>
        </a-form-item>
        <a-form-item no-style>
          <a-button html-type="submit" type="primary" status="danger" long :style="{width: '310px'}" :loading="loading">下一步</a-button>
        </a-form-item>
      </a-form>

      <a-form ref="pwdRef" size="large" layout="vertical" v-if="data.step === 1"
              :style="{ width: '310px', margin: '0 auto' }" @submit-success="handleSubmit" :model="data.pwdForm">
        <a-form-item :hide-asterisk="true" field="password" :rules="[REQUIRED, VARCHAR20]">
          <a-input-password v-model="data.pwdForm.password" value="large" allow-clear placeholder="请输入至少六位密码">
            <template #prefix>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24">
                <path fill="currentColor" d="M19 10h1a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V11a1 1 0 0 1 1-1h1V9a7 7 0 0 1 14 0zm-2 0V9A5 5 0 0 0 7 9v1zm-6 4v4h2v-4z"/>
              </svg>
            </template>
          </a-input-password>
        </a-form-item>
        <a-form-item :hide-asterisk="true" field="oncePasd" :rules="[REQUIRED, VARCHAR20]">
          <a-input-password v-model="data.pwdForm.oncePasd" value="large" allow-clear placeholder="请输入再次输入密码">
            <template #prefix>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24">
                <path fill="currentColor" d="M19 10h1a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V11a1 1 0 0 1 1-1h1V9a7 7 0 0 1 14 0zm-2 0V9A5 5 0 0 0 7 9v1zm-6 4v4h2v-4z"/>
              </svg>
            </template>
          </a-input-password>
        </a-form-item>
        <a-form-item no-style>
          <a-button html-type="submit" type="primary" status="danger" long :style="{width: '310px'}" :loading="loading">提交</a-button>
        </a-form-item>
      </a-form>

    </div>

    <!--底部模块-->
    <div class="login-footer">
      <div flex justify-center class="help">
        <div>帮助<span class="line"></span></div>
        <div>隐私<span class="line"></span></div>
        <div>条款</div>
      </div>
      <div>
        Copyright © {{data.myData.year}} - Present
        <a href="https://pickmall.cn" target="_blank">{{data.myData.copyright.title}}</a>
        版权所有</div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import config from '@/config/index';
  import { REQUIRED, VARCHAR20, MOBILE } from '@/utils/validator';
  import { validateCode, resetPassword } from "@/api/login";
  import { sendSms } from "@/api/common";
  import verify from '@/components/verify/index.vue';
  import { md5 } from '@/utils/md5';
  import { Message } from '@arco-design/web-vue';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const loading = ref(false);
  const verifyDom = ref();
  interface formInterface {
    myData: {
      year: any,
      copyright: any
    },
    retrieveForm: {
      mobile: string,
      code: string
    },
    codeMsg: any,
    verifyStatus: boolean,
    interval: any,
    time: number,
    pwdForm: {
      password: string,
      oncePasd: string
    },
    step: any,
  }
  // 数据集
  const data = ref<formInterface>({
    myData: {
      year: new Date().getFullYear(),
      copyright: config
    },
    // 找回密码表单
    retrieveForm: {
      mobile: '',
      code: ''
    },
    // 验证码文字
    codeMsg: '发送验证码',
    //  是否图片验证通过
    verifyStatus: false,
    interval: null, // 定时器
    time: 60, // 倒计时
    // 密码表单
    pwdForm: {
      password: '',
      oncePasd: ''
    },
    step: 0
  });
  // 提交短信验证码表单
  const retrieveRef = ref();
  // 提交密码
  const pwdRef = ref();


  // 发送手机验证码
  const sendCode = async () => {
    if (data.value.time === 60) {
      if (data.value.retrieveForm.mobile === "") {
        Message.warning("请先填写手机号");
        return;
      }
      if (!data.value.verifyStatus) {
        Message.warning("请先完成安全验证");
        return;
      }
      let params = {
        mobile: data.value.retrieveForm.mobile,
        verificationEnums: "FIND_USER"
      };
      sendSms(params).then(res => {
        if (res.data.success) {
          Message.success("验证码发送成功");
          data.value.interval = setInterval(() => {
            data.value.time--;
            if (data.value.time === 0) {
              data.value.time = 60;
              data.value.codeMsg = "重新发送";
              data.value.verifyStatus = false;
              clearInterval(data.value.interval);
            } else {
              data.value.codeMsg = data.value.time;
            }
          }, 1000);
        } else {
          Message.warning(res.data.message);
        }
      })
    }
  };
  // 开启滑块验证
  const verifyBtnClick = async () => {
    if (!data.value.verifyStatus) {
      if (data.value.retrieveForm.mobile === "") {
        Message.warning("请先填写手机号");
        return;
      }
      verifyDom.value.init();
    }
  };
  // 验证是否正确
  async function verifyChange(callback:any) {
    if (!callback.status) return;
    try {
      Message.success('验证通过！');
      data.value.verifyStatus = true;
      verifyDom.value.verifyShow = false;
    } catch (error) {
      loading.value = false;
    }
    verifyDom.value.verifyShow = false;
  }

  // 提交短信验证码，修改密码
  const next = async () => {
    const auth = await retrieveRef.value?.validate();
    if (!auth) {
      let params = JSON.parse(JSON.stringify(data.value.retrieveForm));
      await validateCode(params).then(res => {
        if (res.data.success) {
          data.value.step = 1;
        } else {
          Message.error(res.data.message);
        }
      });
    }
  };
  // 提交密码
  const handleSubmit = async () => {
    const auth = await retrieveRef.value?.validate();
    if (!auth) {
      let params = JSON.parse(JSON.stringify(data.value.pwdForm));
      if (params.password !== params.oncePasd) {
        Message.warning('两次输入密码不一致');
        return;
      }
      params.mobile = data.value.retrieveForm.mobile;
      params.password = md5(params.password);
      delete params.oncePasd;
      loading.value = true;
      resetPassword(params).then(res => {
        loading.value = false;
        if (res.data.success) {
          Message.success('修改密码成功');
          toLogin();
        }
      })
    }
  };

  // 立即登录
  const toLogin = () => {
    router.push('/Login')
  }
</script>

<style scoped lang="less">
  .sign-up {
    background-color: @light_background_color;
    min-height: 100vh;
  }
  .logo-box {
    width: 600px;
    height: 80px;
    margin: 0 auto;
    border-bottom: 2px solid @theme_color;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 18px 10px 0;
    line-height: 50px;
    color: @text_color;
    .logo {
      width: 250px;
      height: 60px;
      position: relative;
      overflow: hidden;
      font-size: 20px;
      padding-left: 150px;
      img {
        position: absolute;
        top: -46px;
        left: 0;
      }
    }
    .login {
      font-size: 14px;
      span {
        color: @primary_color;
        cursor: pointer;
      }
    }
  }
  .register-box {
    width: 600px;
    margin: 0 auto;
    background-color: @light_white_background_color;
    padding: 30px 0;
    position: relative;
    .verify-con {
      position: absolute;
      right: 140px;
      top: 60px;
      z-index: 10;
    }
  }

  .login-footer {
    width: 100%;
    text-align: center;
    margin: 0 auto;
    color: #aaaaaa;
    position: fixed;
    bottom: 4vh;
    font-size: 14px;
    letter-spacing: 1px;
    .help {
      > div {
        width: 80px;
        height: 20px;
        line-height: 16px;
        margin-bottom: 10px;
        text-align: center;
        position: relative;
        .line {
          position: absolute;
          top: 2px;
          right: 0px;
          display: block;
          width: 1px;
          height: 12px;
          background-color: #aaaaaa;
        }
      }
    }
    a {
      color: @primary_color;
    }
  }
  .code-msg {
    cursor: pointer;
  }
</style>
