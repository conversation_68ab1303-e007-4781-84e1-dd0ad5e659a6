<template>
  <div>
    <a-carousel :style="{
      width: '100%',
      height: props.res.data.height + 'px',
      borderRadius: props.res.data.round + 'px',

    }" overflow-hidden>
      <a-carousel-item :key="index" v-for="(image, index) in props.res.data.list">
        <img :src="image.img" :style="{
          width: '100%',
          height: props.res.data.height + 'px',
        }" />
      </a-carousel-item>
    </a-carousel>
  </div>
</template>

<script setup lang="ts">

import { DragRule } from '@/views/operation/decoration/app/models/types';
const props = defineProps<{
  res: DragRule
}>()


</script>

<style scoped>
</style>
