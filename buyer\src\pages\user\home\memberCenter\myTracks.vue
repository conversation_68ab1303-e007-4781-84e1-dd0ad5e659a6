<template>
  <div>
    <Card _Title="我的足迹" :_Size="16"></Card>
    <a-button status="danger" size="small" type="primary" @click="clearAll">删除全部</a-button>
    <div v-if="tracksData.length && tracksData.length !== 0">
      <div class="track-list">
        <div v-for="(item, index) in tracksData" :key="index" @click="goodsDetail(item.id, item.goodsId)">
          <div><img :src="item.thumbnail" :alt="item.thumbnail" width="160" height="160" /></div>
          <p class="ellipsis ellipsis-1">{{ item.goodsName }}</p>
          <p>{{ unitPrice(item.price, "￥") }}</p>
          <span class="del-icon" @click.stop="clearById(item.goodsId)">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24">
              <path fill="none" stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6h18m-2 0v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6m3 0V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2m-6 5v6m4-6v6"/>
            </svg>
          </span>
        </div>
      </div>

    </div>
    <Empty v-else />
    <div class="paginationBox">
      <a-pagination :total="total" :current="apiParams.pageNumber" :page-size="apiParams.pageSize" show-page-size
                    @change="(number) => {apiParams.pageNumber = number;}" @page-size-change="(number) => {apiParams.pageSize = number; apiParams.pageNumber = 1;}" >
      </a-pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router';
  import { ref, onMounted, watch } from 'vue';
  import { unitPrice } from '@/utils/filters';
  import { tracksList, clearTracks, clearTracksById } from "@/api/member";
  import { Message, Modal } from '@arco-design/web-vue';

  const router = useRouter();
  const loading = ref(false);
  // 请求参数
  const apiParams = ref({
    pageNumber: 1,
    pageSize: 20,
    order: "desc",
    sort: "updateTime",
  });
  // 消息数据
  const tracksData = ref<any>([]);
  const total = ref(0);

  // 获取消息列表
  const getList = () => {
    let params = JSON.parse(JSON.stringify(apiParams.value));
    loading.value = true;
    tracksList(params).then(res => {
      loading.value = false;
      if (res.data.success) {
        tracksData.value = res.data.result.records;
        total.value = res.data.result.total;
      }
    });
  };

  // 跳转商品详情
  const goodsDetail = (skuId: any, goodsId: any) => {
    let routeUrl = router.resolve({path: "/goodsDetail", query: { skuId, goodsId },});
    window.open(routeUrl.href, "_blank");
  };
  // 删除足迹
  const clearById = (id: any) => {
    clearTracksById(id).then((res) => {
      if (res.data.success) {
        Message.success("删除成功！");
        getList();
      }
    });
  };
  // 删除全部
  const clearAll = () => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除全部足迹吗？`,
      okButtonProps: {
        type: "primary",
        status: "danger"
      },
      onOk: () => {
        clearTracks().then(res => {
          if (res.data.success) {
            Message.success('删除成功！');
            getList();
          }
        })
      }
    });
  };

  onMounted(() => {
    getList();
  });

  watch(() => [apiParams],
    (val) => {
      getList();
    }, { deep: true }
  );
</script>

<style scoped lang="less">
  .track-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    > div {
      width: 162px;
      height: 216px;
      overflow: hidden;
      margin: 10px;
      border: 1px solid #eee;
      position: relative;
      box-sizing: border-box;
      &:hover {
        cursor: pointer;
        box-shadow: 1px 1px 4px #dddddd;
        .del-icon {
          display: block;
        }
      }
      > div {
        width: 160px;
        height: 160px;
      }
      p {
        line-height: 24px;
        margin: 0;
        padding: 0 6px;
      }
      p:nth-child(2) {
        color: #999;
      }
      p:nth-child(3) {
        color: @theme_color;
      }
      .del-icon {
        display: none;
        font-size: 30px;
        background-color: rgba(0, 0, 0, 0.3);
        position: absolute;
        width: 30px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        right: 0;
        bottom: 0;
        cursor: pointer;
      }
    }
  }
</style>
