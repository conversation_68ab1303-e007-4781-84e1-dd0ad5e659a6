<template>
  <div>
    <a-form
      ref="formRef"
      :model="addressData.data"
      class="form"
      :label-col-props="{ span: 8 }"
      :wrapper-col-props="{ span: 16 }"
      layout="horizontal"
      auto-label-width
      @submit="handleAddOk"
    >
      <a-form-item
        field="salesConsignorName"
        label="发货人"
        :rules="[REQUIRED]"
      >
        <a-input v-model="addressData.data.salesConsignorName" />
      </a-form-item>
      <a-form-item
        field="salesConsignorMobile"
        label="发货人电话"
        :rules="[MOBILE, REQUIRED]"
        validate-phone
      >
        <a-input v-model="addressData.data.salesConsignorMobile" :max-length="11" />
      </a-form-item>
      <a-form-item field="salesConsignorAddressId" label="发货地址">
        <city
          :key="isShowCity"
          :ids="addressData.data.salesConsignorAddressId"
          :address="addressData.data.salesConsignorAddressPath"
          @callback="cityRes"
        />
      </a-form-item>
      <a-form-item
        field="salesConsignorDetail"
        label="详细地址"
        :rules="[REQUIRED, VARCHAR255]"
      >
        <a-textarea
          v-model="addressData.data.salesConsignorDetail"
          :max-length="500"
          show-word-limit
          allow-clear
        />
      </a-form-item>

      <a-form-item>
        <a-space>
          <a-button :loading="loading" type="primary" html-type="submit">
            保存
          </a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import city from '@/components/m-city/index.vue';
  import { getDeliverAddress, editDeliverAddress } from '@/api/shops';
  import { Message } from '@arco-design/web-vue';
  import { VARCHAR255, REQUIRED, VARCHAR20, MOBILE } from '@/utils/validator';

  const formRef = ref();
  const loading = ref();
  const isShowCity = ref(1);
  const addressData = reactive<any>({
    data: {
      // 退货地址
      salesConsignorName: '', // 收货人姓名
      salesConsignorMobile: '', // 收货人电话
      salesConsignorAddressId: '', // 售后地址id,逗号分割
      salesConsignorAddressPath: '', // 售后地址，逗号分割
      salesConsignorDetail: '', // 详细地址
    },
  });
  // 获取退货地址
  async function getStoreReturnAddress() {
    const res = await getDeliverAddress();
    if (res.data.success) {
      isShowCity.value += 1;
      if (res.data.result) {
        addressData.data = res.data.result;
      }
    }
  }

  // 级联子组件传过来的值
  const cityRes = (val: any) => {
    addressData.data.salesConsignorAddressId = val.ids.join(',');
    addressData.data.salesConsignorAddressPath = val.cities.join(',');
  };

  // 保存
  const handleAddOk = async () => {
    const auth = await formRef.value?.validate();
    if (!auth) {
      editDeliverAddress(addressData.data).then((res) => {
        if (res.data.success) {
          Message.success('保存成功');
          getStoreReturnAddress();
        }
      });
    }
  };

  // 加载方法
  onMounted(() => {
    getStoreReturnAddress();
  });
</script>

<style scoped lang="less">
  .form {
    width: 602px;
    /*margin: 0 auto;*/
  }
</style>
