<template>
  <a-card class="general-card" title="售后原因" :bordered="false">
    <a-tabs :default-active-key="type" @change="clickTabPane">
      <a-tab-pane key="RETURN_MONEY" title="退款"></a-tab-pane>
      <a-tab-pane key="CANCEL" title="取消"></a-tab-pane>
      <a-tab-pane key="RETURN_GOODS" title="退货"></a-tab-pane>
      <a-tab-pane key="COMPLAIN" title="投诉"></a-tab-pane>
    </a-tabs>
    <a-row style="margin-bottom: 16px">
      <a-col :span="16" style="margin-bottom: 10px">
        <a-space>
          <a-button type="primary" @click="handleAdd(type)">
            添加
          </a-button>
        </a-space>
      </a-col>
      <tablePage
        ref="tablePageRef"
        :columns="columnsTable"
        :methods="sortMethods"
        :api="getAfterSaleReasonPage"
        :api-params="params"
        @delete="handleDelete"
        @update="handleEdit"
        :bordered="true"
      >
      </tablePage>
    </a-row>
    <!-- 添加modal -->
    <a-modal
      v-model:visible="memberData.enableAddModal"
      :align-center="false"
      :footer="false"
    >
      <template #title> 售后原因 </template>
      <a-form ref="formRef" :model="memberData.form" @submit="handleAddOk">
        <a-form-item
          field="reason"
          label="售后原因"
          :rules="[REQUIRED, VARCHAR20]"
          :validate-trigger="['change']"
        >
          <a-input v-model="memberData.form.reason" />
        </a-form-item>
        <a-form-item label="操作">
          <a-button :loading="memberData.formLoading" html-type="submit"
            type="primary">保存</a-button
          >
        </a-form-item>
      </a-form>
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">
  import tablePage from '@/components/table-pages/index.vue';
  import { MethodsRule, ColumnsDataRule } from '@/types/global';
  import { REQUIRED, VARCHAR20 } from '@/utils/validator';
  import {
    getAfterSaleReasonPage,
    delAfterSaleReason,
    editAfterSaleReason,
    addAfterSaleReason,
  } from '@/api/order';
  import { ref, watch, reactive } from 'vue';
  import useCurrentInstance from '@/hooks/useCurrentInstance';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { Message } from '@arco-design/web-vue';

  const tablePageRef = ref<any>('');
  const type = ref<string>('RETURN_MONEY');
  const formRef = ref<FormInstance>();
  interface formInterface {
    enableAddModal: boolean;
    formLoading: boolean;
    fid: string | number;
    form: {
      reason: string;
      serviceType: string;
      [key: string]: any;
    };
    [key: string]: any;
  }
  // 获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;
  const columnsTable: ColumnsDataRule[] = [
    {
      title: '创建人',
      dataIndex: 'createBy',
    },
    {
      title: '原因',
      dataIndex: 'reason',
    },

    {
      title: '时间',
      dataIndex: 'createTime',
    },
  ];

  const sortMethods: MethodsRule = {
    title: '操作',
    width: 300,
    methods: [
      {
        title: '编辑',
        callback: 'update',
        type:"text" ,
        status:"warning"
      },
      {
        title: '删除',
        callback: 'delete',
        type:"text" ,
        status:"danger"
      },
    ],
  };
  // 数据集
  const memberData = reactive<formInterface>({
    enableAddModal: false,
    formLoading: false,
    fid: '', // 当前form的ids
    form: {
      reason: '',
      serviceType: '',
    }, // 表单提交数据
  });
  // 点击添加
  function handleAdd(type: string) {
    memberData.form.serviceType = type;
    memberData.enableAddModal = true;
    memberData.fid = '';
    memberData.form.reason = '';
    // Object.keys(memberData.form).forEach((key) => {
    //   memberData.form[key] = '';
    // });
  }
  // 点击修改
  function handleEdit(val:any) {
    if (val) {
      Object.keys(val.record).forEach((key) => {
        // eslint-disable-next-line no-unused-expressions,no-prototype-builtins
        memberData.form.hasOwnProperty(key)
          ? (memberData.form[key] = val.record[key])
          : '';
      });
      memberData.form.serviceType = val.record.serviceType;
      memberData.form.reason = val.record.reason;
      memberData.fid = val.record.id;
      memberData.enableAddModal = true;
    }
  }
  // 添加/修改地址
  async function handleAddOk() {
    const auth = await formRef.value?.validate();
    if (!auth) {
      let res;
      !memberData.fid
        ? (res = await addAfterSaleReason(memberData.form))
        : (res = await editAfterSaleReason(memberData.fid, memberData.form));

      if (res.data.success) {
        Message.success(`${memberData.fid ? '修改' : '添加'}成功!`);
        memberData.enableAddModal = false;
        tablePageRef.value.init();
      }
    }
  }
  // 回调删除
  function handleDelete(data: any) {
    modal.confirm({
      title: '确认删除',
      content: '确认要删除此售后原因',
      alignCenter: false,
      onOk: async () => {
        const res = await delAfterSaleReason(data.record.id);
        if (res.data.success) {
          Message.success('删除成功！');
          tablePageRef.value.init();
        }
      },
    });
  }
  const params = reactive({
    serviceType: type,
  });
  // 切换tabs
  const clickTabPane = (val: any) => {
    type.value = val;
  };
  watch(type, (val) => {
    params.serviceType = val;
    tablePageRef.value.init();
  });
</script>
