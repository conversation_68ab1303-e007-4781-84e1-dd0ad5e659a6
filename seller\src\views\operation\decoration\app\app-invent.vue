<template>
  <div class="invent">
    <navbar border-b-1 class="navbar"> </navbar>
    <div flex h-100vh class="invent-box">
      <div flex>
        <widgetMenu></widgetMenu>
        <widgetDrag w-400px border-r-1></widgetDrag>
      </div>
      <widgetPhone flex-1 />
      <widgetMiniMap w-160px />
      <widgetModule w-400px border-l-1 overflow-hidden />
    </div>
  </div>
</template>

<script setup lang="ts">
import navbar from './layout/widget-navbar.vue'
import widgetMenu from './layout/widget-menu.vue'
import widgetDrag from './layout/widget-drag.vue'
import widgetPhone from './layout/widget-phone.vue'
import widgetModule from './layout/widget-module.vue'
import widgetMiniMap from './layout/widget-minimap.vue'
</script>

<style lang="less" scoped>
.invent {
  width: 100%;
  height: 100%;

}

.navbar {
  width: 100%;
  position: sticky;
}

.invent-box {
  height: calc(100vh - 66px);
}
</style>
