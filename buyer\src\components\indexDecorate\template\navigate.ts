export function navigateTo(val: any) {
  const data = val?.url
  if (!data) return
  // if(data.___value){
  //   const path:any = {
  //     'goods':{
  //       path: '/goodsDetail',
  //       query: {
  //         skuId: data.id,
  //         goodsId: data.goodsId,
  //       },
  //     },
  //     'category':{
  //       path: '/goodsList',
  //       query: {
  //         category: data.id,
  //       },
  //     },
  //     'shops':{},
  //     'special':{},
  //   }
  //   return path[data.___value]
  // }
  switch (data.___value) {
    // 商品
    case 'goods':
      return {
        path: '/goodsDetail',
        query: {
          skuId: data.id,
          goodsId: data.goodsId,
        },
      }
    // 分类
    case 'category':
      // 针对于商家自己装修的分类单独做处理
      return {
        path: '/goodsList',
        query: {
          category: data.id,
        },
      }
    // 店铺
    case 'shops':
      return {
        path: '/Merchant',
        query: {
          id: data.id,
        },
      }
    // 微页面
    case 'special': {
      return {
        path: '/topic',
        query: {
          id: data.id,
        },
      }
    }
  }

  // 处理底部菜单栏导航
  if (data.___value === 'tabbar') {
    switch (data.title) {
      case "首页":
        uni.reLaunch({
          url: data.path || `/pages/tabbar/home/<USER>
        });
        break;
      case "分类":
        uni.reLaunch({
          url: data.path || `/pages/tabbar/category/category`,
        });
        break;
      case "充值中心":
        uni.reLaunch({
          url: data.path || `/pages/tabbar/voucher/voucher`,
        });
        break;
      case "优惠购":
        uni.reLaunch({
          url: data.path || `/pages/tabbar/special/tabbar`,
        });
        break;
      case "我的":
        uni.reLaunch({
          url: data.path || `/pages/tabbar/user/my`,
        });
        break;
      case "购物车":
        uni.reLaunch({
          url: data.path || `/pages/tabbar/cart/cartList`,
        });
        break;
      default:
        console.warn('未知的底部菜单栏导航:', data.title);
    }
    return null; // 已经处理了跳转，不需要返回路径
  }

  if (data.___value === 'other') {
    switch (data.title) {
      case '首页':
        return { path: '/' }
      case '分类':
        return { path: '/goodsList' }
      case '购物车':
        return { path: '/cart' }
      case '个人中心':
        return { path: '/user/home' }
      case '收藏商品':
        return { path: '/user/home/<USER>/myFavorites' }
      case '我的订单':
        return { path: '/user/home/<USER>/myOrder' }
      case '领券中心':
        return { path: '/coupon' }
      case '签到':
        return { path: '/user/home/<USER>/signIn' }
      case '秒杀频道':
        return { path: '/seckill' }
      case '拼团频道':
        return { path: '/pintuan' }
      case '砍价':
        return { path: '/bargain' }
      case '积分商城':
        return { path: '/point' }
      case '充值中心':
        return { path: '/user/home/<USER>/moneyManagement' }
      case '优惠购':
        return { path: '/promotion' }
      default:
        return { path: '/' }
    }
  }
}
