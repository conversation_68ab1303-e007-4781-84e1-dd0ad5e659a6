<template>
  <div class="home">
    <div class="home-container">
      <!--<a-row>-->
        <!--<a-col :span="5" class="side-bar"></a-col>-->
        <!--<a-col :span="19" class="main-body"></a-col>-->
      <!--</a-row>-->
      <div class="side-bar">
        <!-- 用户头像、昵称 -->
        <div class="user-info" v-if="userInfo">
          <a-avatar v-if="userInfo && userInfo.face" :size="96"><img alt="avatar" :src="userInfo.face"/></a-avatar>
          <a-avatar v-else :size="96"><img alt="avatar" src="/src/assets/images/default.png"/></a-avatar>
          <div>{{userInfo.nickName}}</div>
        </div>
        <!-- 循环导航栏 -->
        <a-menu :style="{ width: '200px' }" auto-open :default-selected-keys="defultSelectedKeys"
                @sub-menu-click="handleSubMenu" @menu-item-click="handleMenuItem" @collapse="onCollapse">
          <a-sub-menu v-for="(menu, index) in menuList" :key="index">
            <template #title>{{menu.title}}</template>
            <a-menu-item v-for="(children) in menu.menus" :key="children.path">{{children.title}}</a-menu-item>
          </a-sub-menu>
        </a-menu>
      </div>
      <div class="main-body">
        <div class="userBox" v-if="isHome">
          <div class="box">
            <!-- 我的订单组件 -->
            <MyOrder />
          </div>
          <div class="box">
            <!-- 近期收藏 -->
            <MyFavorites />
          </div>
        </div>
        <div v-else>
          <router-view></router-view>
        </div>

      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router';
  import { ref, onMounted, watch } from 'vue';
  import storage from '@/utils/storage';
  import menuList from "./menu";  // 会员中心左侧列表

  const router = useRouter();
  const userInfo = ref();
  const componentKey = ref(0);
  const isHome = ref(true);
  // 默认选中的菜单项key数组
  const defultSelectedKeys = ref([router.currentRoute.value.path]);

  // 菜单折叠状态改变时触发
  const onCollapse = () => {

  };
  // 点击菜单项时触发
  const handleSubMenu = () => {
    // router.push({ name: name });
  };
  // 点击子菜单时触发
  const handleMenuItem = (key: any) => {
    router.push({path: key});
  };


  onMounted(() => {
    if (storage.getUserInfo()) {
      userInfo.value = JSON.parse(storage.getUserInfo());
    }
  });

  watch(
    () => router.currentRoute.value.path,
    (newValue) => {
      // 判断跳转的页面是否为个人中心首页
      componentKey.value +=1; // 改变key的值来刷新组件
      if (/^\/user\/home$|^\/user\/home\?/.test(newValue)) {
        isHome.value = true;
      } else {
        isHome.value = false;
      }
    },
    { immediate: true }
  );
</script>

<style scoped lang="less">
  @import "./home.less";


</style>
