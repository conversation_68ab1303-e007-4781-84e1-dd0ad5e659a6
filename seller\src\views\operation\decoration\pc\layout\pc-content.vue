<template>
  <div class="pc" flex flex-a-c flex-j-c justify-center>
    <div class="model" h-full v-auto-animate>
      <div :ref="parent" pb-300px>
        <div w-1200px>

          <div w-1184px :ref="drop"  v-auto-animate min-h-400px role="pc">
            <div class="div-box" pb-400px min-h-400px>
              <div h-full class="pc-model" v-if="models.length">
                <div v-for="(element, block) in models" h-auto :key="block">
                  
                  <item w-1184px @dragenter="componetDragIndex = block" @drop="componentDownIndex = block"
                    :id="item.type + new Date().getTime()" :move-card="moveCard" :index="block" class="model-item"
                    :class="{ 'active': active === block }" @click.native="handleClickComponent(element, block)" absolute
                    v-if="models.length && (element.type !== 'topAdvert' && element.type !== 'bannerAdvert')">
                    <a-tag v-if="active === block" class="current" absolute color="arcoblue">当前</a-tag>
                    <div v-if="active === block" left--10px z-99 class="border-left" w-1px left-0px absolute></div>
                    <component class="component" :is="template[element.type]" :res="element">
                    </component>
                    <div absolute top-0px z-99 v-if="active === block" class="border-right" w-1px right-0px></div>
                    <a-button top-0px @click="handleClickDelBtn(element, block)" v-if="active === block" status="danger"
                      absolute class="btn" right--55px size="mini">
                      删除
                    </a-button>
                  </item>
                </div>
              </div>
              <div h-full class="pc-model" min-h-100vh  flex flex-a-c flex-j-c v-else>
                装修内容区域
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <a-modal v-model:visible="visible" :onOk="delCurrentModel" unmountOnClose>
      <template #title>
        提示
      </template>
      <div>确认要删除模块 {{ dragData.data.name }} 吗？
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { DragRule } from '@/views/operation/decoration/app/models/types';
import { onMounted, ref, watch } from 'vue';
import { useDrop } from 'vue3-dnd';
import templates from '../template/exports';

import { getHomePageDetail } from '@/api/setting';
import { useDesign } from '@/store';
import { Message } from '@arco-design/web-vue';
import { useAutoAnimate } from '@formkit/auto-animate/vue';
import { useRoute } from 'vue-router';
import item from './item.vue';

const componentDownIndex = ref<any>(0)
const componetDragIndex = ref<any>(0)
const route = useRoute()
const [parent]: any = useAutoAnimate()
const visible = ref<boolean>(false)
const template: any = templates
const models: any | Array<DragRule> = ref([])
const active = ref<number | string>('')




const [, drop] = useDrop(() => ({
  accept: 'box',
  // 此处接受传过来的值
  drop: (val, monitor) => handleDrop(val, monitor),
  collect: (monitor) => ({
    isOver: monitor.isOver(),
    canDrop: monitor.canDrop(),
    getItemType: monitor.getItemType(),
    isShallowOver: monitor.isOver({ shallow: true })
  }),

}));

onMounted(() => {
  active.value = models.value.length == 0 ? 0 : ''
  userDesign.setPcActiveIndex(Number(active.value));
  userDesign.setPcDesign(models.value)
})


const userDesign = useDesign();
// const { canDrop, isOver } = toRefs(collect);
// const isDrop = computed(() => (unref(canDrop) ? true : false));
// 监听装修组件active index的变化
watch([() => userDesign.indexOfPc], ([index]) => {
  active.value = index 
}, { deep: true })


// 点击删除组件数据
const dragData = ref({
  data: {
    name: ""
  },
  index: 0
})


// 定义一个函数 moveCard，接收两个参数：dragIndex（拖动项的索引）和hoverIndex（悬停项的索引）
const moveCard = (dragIndex: number, hoverIndex: number) => {
  // 从 models 数组中获取拖动项（dragIndex 索引对应的项）
  const item = models.value[dragIndex]

  // 从 models 数组中移除拖动项（dragIndex 索引对应的项）
  models.value.splice(dragIndex, 1)

  // 将拖动项插入到悬停项（hoverIndex 索引对应的项）之前
  models.value.splice(hoverIndex, 0, item)

  // 将active的值设置为hoverIndex
  active.value = hoverIndex

  userDesign.setPcActiveIndex(hoverIndex);
}

// 删除当前模块
function delCurrentModel() {
  models.value.splice(dragData.value.index, 1)
}
// 删除组件
function handleClickDelBtn(val: DragRule, index: number) {
  visible.value = true
  dragData.value = {
    data: val,
    index
  }

}

// 对组件进行点击处理
function handleClickComponent(val: DragRule, index: number) {
  userDesign.setCurrentPcDesign('')
  active.value = index
  // 将点击的内容传入到pinia中
  userDesign.setPcActiveIndex(index);
}
// 将传过来的值进行处理
function handleDrop(res: any, val: any) {

  const current = { ...JSON.parse(JSON.stringify(res)), }
  if (!models.value.length) {
    models.value.push(current)
  }
  else {
    if (componetDragIndex.value === models.value.length - 1) {
      models.value.push(current)
    } else {
      // hoverIndex
      models.value.splice(componentDownIndex.value, 0, current)
    }
  }
}

// 实例化代码
async function init() {
  
  const id: string = (route.query.id as string)
  const res = await getHomePageDetail(id)
  if (res.data.success) {
    // 判断当前楼层装修的值
    const fetchData = res.data.result.pageData ? JSON.parse(res.data.result.pageData) : []
    if (fetchData) {
      if (fetchData?.version === 'v3') {
        models.value = fetchData.data || []
        userDesign.setPcDesign(models.value)
      } else {
        Message.error({
          content: "当前编辑内容版本不支持",
          duration: 10000,
          closable: true
        })
      }

    }
  }
}

onMounted(() => {
  init()
})


</script>

<style scoped lang="less">
.pc-model {
  user-select: none;
  position: relative;
}

.div-box {
  // height: calc(100vh - 590px);
  height: auto;
}

.pc {
  min-height: calc(100vh - 66px);
  background: #f5f5f7;
  overflow-y: auto;
  padding-bottom: 200px;
  height: auto;
}

.body {
  background: #fff;
}

.border-left,
.border-right {
  height: 100%;
  background: rgb(var(--arcoblue-5));
}

.model-item {
  position: relative;
}
</style>
