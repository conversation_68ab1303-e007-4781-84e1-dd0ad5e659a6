<template>
  <a-card class="general-card" title="券活动" :bordered="false">
    <a-tabs @change="(val)=>{apiParams.promotionStatus = val}" :default-active-key="couponActivityStatus">
      <a-tab-pane key="NEW" title="未开始"></a-tab-pane>
      <a-tab-pane key="START" title="已开始"></a-tab-pane>
      <a-tab-pane key="END" title="已结束"></a-tab-pane>
      <a-tab-pane key="CLOSE" title="已关闭"></a-tab-pane>
    </a-tabs>
    <a-row style="margin-bottom: 16px">
      <a-col :span="16">
        <a-space>
          <a-button type="primary" @click="add">
            添加活动
          </a-button>
        </a-space>
      </a-col>
    </a-row>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getCouponActivityList"
      :api-params="apiParams"
      :bordered="true"
    >
      <template #price="{ data }">{{
        data.price
          ? unitPrice(data.price, '¥')
          : (data.couponDiscount || 0) + '折'
      }}</template>

      <template #time="{ data }"
        >{{ data.startTime }}<span style="margin: 0 10px">-</span
        >{{ data.endTime }}</template
      >
      <template #see="{ data }">
        <a-button @click="see(data)" type="text" status="success">查看</a-button>
      </template>
      <template #close="{ data }">
        <a-button
          v-if="
            data.promotionStatus == 'START' || data.promotionStatus == 'NEW'
          "
          @click="close(data)" type="text" status="danger"
          >关闭</a-button
        >
      </template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { MethodsRule, ColumnsDataRule } from '@/types/global';
  import { getCouponActivityList, closeActivity } from '@/api/promotion';
  import useCurrentInstance from '@/hooks/useCurrentInstance';
  import { Message } from '@arco-design/web-vue';

  import {
    couponActivityType,
    activityScope,
    promotionsStatusRender,
  } from '@/utils/tools';
  import { unitPrice } from '@/utils/filters';
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';

  const couponActivityStatus = ref<string>('START');
  const apiParams = ref<any>({ sort: 'startTime',promotionStatus:couponActivityStatus.value });
  const tablePageRef = ref<any>();
  const router = useRouter();
  // 获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;
  const columnsTable: ColumnsDataRule[] = [
    {
      title: '活动名称',
      dataIndex: 'promotionName',
    },
    {
      title: '活动类型',
      dataIndex: 'couponActivityType',
      slot: true,
      slotData: {
        badge: couponActivityType,
      },
    },

    {
      title: '活动范围',
      dataIndex: 'activityScope',
      slot: true,
      slotData: {
        badge: activityScope,
      },
    },
    {
      title: '活动时间',
      width: 400,
      ellipsis: false,
      dataIndex: 'startTime',
      slot: true,
      slotTemplate: 'time',
    },
    {
      title: '状态',
      dataIndex: 'promotionStatus',
      slot: true,
      slotData: {
        badge: promotionsStatusRender,
      },
    },
  ];

  // todo 本页面操作列表为选择展示
  const sortMethods: MethodsRule = {
    title: '操作',
    width: 200,
    methods: [
      {
        title: '查看',
        callback: 'see',
        slot: true,
        slotTemplate: 'see',
      },
      {
        title: '关闭',
        callback: 'close',
        slot: true,
        slotTemplate: 'close',
      },
    ],
  };
  // 添加活动
  const add = () => {
    router.push({ name: 'add-coupon-activity' });
  };
  // 查看
  const see = (val: any) => {
    router.push({
      name: 'coupon-info',
      query: {
        id: val.id,
      },
    });
  };
  // 关闭
  const close = (val: any) => {
    console.log(val);
    modal.confirm({
      title: '确认关闭',
      content: `确认要关闭此优惠券活动么?关闭活动只能重新创建`,
      alignCenter: false,
      onOk: async () => {
        const res = await closeActivity(val.id);
        if (res.data.success) {
          Message.success('优惠券活动已关闭');
          tablePageRef.value.init();
        }
      },
    });
  };
</script>
