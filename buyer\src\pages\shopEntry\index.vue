<template>
  <div style="overflow: hidden;">
    <div class="shop-entry">
      <h1>店铺入驻</h1>
      <a-steps :current="currentIndex">
        <a-step>企业资质信息</a-step>
        <a-step>财务资质信息</a-step>
        <a-step>其他信息</a-step>
        <a-step>提交审核</a-step>
      </a-steps>
      <FirstApply v-if="currentIndex === 1 && dataReview" :content="firstData" @handleChange="nextPage"></FirstApply>
      <SecondApply v-if="currentIndex === 2 && dataReview" :content="secondData" @handleChange="nextPage"></SecondApply>
      <ThirdApply v-if="currentIndex === 3 && dataReview" :content="thirdData" @handleChange="nextPage"></ThirdApply>
      <div class="success-page" v-if="currentIndex === 4">
        <span v-if="storeDisable === '' || storeDisable === 'APPLYING'">入驻申请提交成功，等待平台审核</span>
        <span v-if="storeDisable === 'OPEN'">申请已通过，请联系管理员</span>
        <span v-if="storeDisable === 'CLOSED'">店铺已关闭，重申请联系管理员</span>
        <span v-if="storeDisable === 'REFUSED'">审核未通过,请修改资质信息，如有疑问请联系管理员</span>
      </div>
      <a-button v-if="currentIndex === 4" @click="router.push('/')" class="mr_10">返回</a-button>
      <a-button type="primary" @click="currentIndex = 1"  v-if="storeDisable === 'REFUSED' && currentIndex === 4">重新申请</a-button>
    </div>

    <!-- 店铺入驻协议modal -->
    <a-modal v-model:visible="showAgreement" width="800px" :mask-closable="false" :closable="false">
      <template #title>店铺入驻协议</template>
      <div class="">
        <div class="agreeent-con" v-html="agreementCon"></div>
        <div class="flex mt_20" style="justify-content: center;">
          <a-checkbox v-model="checked">我已同意以上协议</a-checkbox>
        </div>
      </div>
      <template #footer>
        <div class="flex mt_20" style="justify-content: center;">
          <a-button :disabled="!checked" @click="showAgreement = false" status="danger" type="primary">同意协议填写资质信息</a-button>
        </div>
      </template>
    </a-modal>

  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, nextTick } from 'vue';
  import { useRouter } from 'vue-router';
  import { agreement, applyStatus } from "@/api/shopentry";

  const router = useRouter();
  const currentIndex = ref(1); // 当前步骤
  const showAgreement = ref(false); // 协议显示
  const agreementCon = ref(""); // 协议内容
  const checked = ref(false); // 选中协议
  const firstData = ref<any>({}); // 第一步数据
  const secondData = ref<any>({}); // 第二步数据
  const thirdData = ref<any>({}); // 第三步数据
  const storeDisable = ref(""); // APPLY OPEN 开店中 CLOSED 关闭 REFUSED 拒绝 APPLYING 申请中，审核
  const dataReview = ref(true); // 根据接口返回判断是否可展示数据


  // 入驻协议
  const getArticle = () => {
    agreement().then((res) => {
      if (res.data.success) {
        agreementCon.value = res.data.result.content;
      }
    });
  };
  // 获取已填写店铺信息
  const getData = (status: any) => {
    applyStatus().then((res) => {
      if (res.data.success) {
        if (!res.data.result) {
          showAgreement.value = true;
        } else {
          dataReview.value = false;
          let data = res.data.result;
          let first = [
            "companyAddressPath", "companyAddress", "companyAddressIdPath", "companyEmail", "companyName", "employeeNum",
            "companyPhone", "legalId", "legalName", "licencePhoto", "legalPhoto", "licenseNum", "linkName", "linkPhone", "registeredCapital", "scope",
          ];
          let second = ["settlementBankAccountName", "settlementBankAccountNum", "settlementBankBranchName", "settlementBankJointName",];
          let third = ["goodsManagementCategory", "storeCenter", "storeDesc", "storeLogo", "storeName", "storeAddressIdPath", "storeAddressPath", "storeAddressDetail",];
          storeDisable.value = data.storeDisable;
          first.forEach((e) => {firstData.value[e] = data[e];});
          second.forEach((e) => {secondData.value[e] = data[e];});
          third.forEach((e) => {thirdData.value[e] = data[e];});
          if (status === "init") {
            if (storeDisable.value === "APPLY") {
              currentIndex.value = 1;
            } else {
              currentIndex.value = 4;
            }
          }
          nextTick(() => {
            dataReview.value = true;
          });
        }
      }
    });
  };
  // 下一步
  const nextPage = (step: any) => {
    currentIndex.value = step;
    getData("next");
  };
  onMounted(() => {
    getData("init");
    getArticle();
  })
</script>

<style scoped lang="less">
  .shop-entry {
    width: 1200px;
    margin: 50px auto;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: 500px;
    border-radius: 20px;
    background: #fff;
    padding: 10px 20px;
    h1 {
      margin-top: 20px;
    }
  }

  .success-page {
    height: 500px;
    width: 100%;
    line-height: 500px;
    text-align: center;
    font-size: 20px;
  }
</style>
