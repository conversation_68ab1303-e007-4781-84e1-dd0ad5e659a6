// import { RouteLocationNormalized, RouteRecordRaw } from 'vue-router';
// import { useUserStore } from '@/store';

import { getCurrentPermissionList } from '@/api/setting'
import result from './router.json'
import { appRoutes } from '@/router/routes/index.ts';
import { Message } from '@arco-design/web-vue';
export default async function usePermission() {
  const permissions = await getCurrentPermissionList()
  // const permissions = result
  if(!permissions.data.success){
    Message.error("获取菜单失败")
  }
  else{
    console.log("appRoutes",appRoutes)
  }
  console.log("获取菜单",permissions)
}
