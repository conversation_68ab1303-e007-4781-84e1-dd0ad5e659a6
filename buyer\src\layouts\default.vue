<template>
  <div>
    <main mx-auto my-0>
      <TheHeader v-if="isNavigation" :key="componentKey" />
      <RouterView />
      <TheFooter v-if="isFooter" :key="componentKey" />
    </main>
  </div>

</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import storage from "@/utils/storage";

  const router = useRouter();
  const isNavigation = ref(true);
  const isFooter = ref(true);
  const componentKey = ref(0);

  // router.beforeEach((to: any, from: any, next: any) => {
  //   const title = to.name;
  //   const token: any = storage.getAccessToken();
  //   console.log('跳转的页面', title, token);
  //   if (!token && !storage.getRefreshToken() && title != '/Login') {
  //     // 判断未登录且前往的页面不是登录页
  //     next({ name: '/Login' })
  //   } else if (token && storage.getRefreshToken() && title == '/Login') {
  //     // 判断已登录且前往的页面是登录页
  //     next({ name: '/' })
  //   } else {
  //     next();
  //   }
  // });

  watch(
    () => router.currentRoute.value.path,
    (newValue) => {
      componentKey.value +=1; // 改变key的值来刷新组件
      console.log('跳转页面', router.currentRoute.value.path);
      // 是否展示顶部导航栏
      if (/^\/Login|\/signUp|\/forgetPassword|^\/$|\/shopEntry$|\/topic/.test(newValue)) {
        isNavigation.value = false;
      } else {
        isNavigation.value = true;
      }
      // 是否展示底部导航栏
      if (/^\/Login|\/signUp|\/forgetPassword|^\/$|\/user\/home|^\/payDone|^\/shopEntry|^\/article/.test(newValue)) {
        isFooter.value = false;
      } else {
        isFooter.value = true;
      }
    },
    { immediate: true }
  );
</script>
