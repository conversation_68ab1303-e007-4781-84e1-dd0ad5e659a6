<script setup lang="ts">
import { useStorage } from '@vueuse/core'
import { beautifyTime } from '~/utils/tools'
import { textReplaceEmoji } from '~/utils/emoji'

const props = defineProps<{
  res: any
}>()
const isShow = ref<boolean>(false)
// 左侧展示用户
const leftUser = ref<any>('')
// 右侧展示用户自己
const rightUser = ref<any>('')
onMounted(() => {
  const talkUser = useStorage('talk_detail', '')
  if (talkUser.value)
    leftUser.value = JSON.parse(talkUser.value)

  const myself = useStorage('user', '')
  if (myself.value)
    rightUser.value = JSON.parse(myself.value)

  isShow.value = true
})

const parseTextToEmoji = (text: string): string => {
  // 检查输入是否为字符串，防止非字符串输入导致错误
  if (typeof text !== 'string') {
    console.warn('Invalid input: Expected a string.');
    return text; // 如果不是字符串，直接返回原始值
  }

  return textReplaceEmoji(text); // 调用替换函数
};

</script>

<template>
  <div v-if="isShow" py-16px>
    <!-- 左侧 -->
    <div v-if="res.fromUser === leftUser.userId " flex flex-a-c>
      <div>
        <a-avatar :image-url="leftUser.face" :size="32" />
      </div>
      <div ml-5px>
        <div mb-5px flex flex-a-c text-12px>
          <div color-warmgray>
            {{ beautifyTime(leftUser.lastTalkTime) }}
          </div>
        </div>
        <!-- <div rounded-5px bg-warmgray-200 px-6px py-5px text-center line-height-25px>
          {{ res.text }}
        </div> -->
        <div v-html="parseTextToEmoji(res.text)"></div>
      </div>
    </div>
    <!-- 右侧 -->
    <div v-else flex justify-end flex-a-c py-16px>
      <div mr-5px>
        <div mb-5px flex flex-a-c text-12px>
          <div color-gray>
            {{ beautifyTime(leftUser.lastTalkTime) }}
          </div>
        </div>
        <div min-h-35px rounded-5px bg-warmgray-200 px-6px py-5px text-center line-height-25px>
          {{ props.res.text }}
        </div>
      </div>
      <div>
        <a-avatar v-if="rightUser.___role === 'STORE'" :image-url="rightUser.storeLogo" :size="32" />
        <a-avatar v-if="rightUser.___role === 'BUYER'" :image-url="rightUser.face" :size="32" />
      </div>
    </div>
  </div>
</template>

<style scoped>

</style>
