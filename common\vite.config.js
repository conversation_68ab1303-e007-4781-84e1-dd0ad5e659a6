import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from "path";

export default defineConfig({
  server: {
    port: 11000,
  },
  build: {
    outDir: "lib/dist",  //输出目录名称
    lib: {
      entry: resolve(__dirname, "./src/packages/index.js"),
      name: "dist", 
      fileName: 'dist',
    },
    rollupOptions: {
      external: ['vue'],
      output: {
        // 在 UMD 构建模式下为这些外部化的依赖提供一个全局变量，为了确保在构建后的库文件中能够正确地访问到 Vue 库的 API
        globals: {
          vue: 'Vue'
        }
      }
    }
  },
  plugins: [vue()],
})
