<template>
  <a-card class="general-card" title="售后管理" :bordered="false">
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <tablePage ref="tablePageRef" :columns="columnsTable" :methods="sortMethods" :api-params="apiParams"
      :api="afterSaleOrderPage" :bordered="true">
      <template #btnList="{ data }">
        <a-space>
          <a-button type="text" status="success" @click="()=>$openWindow({ name: 'after-order-detail',query: {id: data.sn}})">查看</a-button>
        </a-space>
      </template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
import { afterSaleOrderPage } from '@/api/order';
import searchTable from '@/components/search-column/index.vue';
import tablePage from '@/components/table-pages/index.vue';
import { ColumnsDataRule, MethodsRule, SearchRule } from '@/types/global';
import { afterSaleType, serviceStatus } from '@/utils/tools';
import { ref,onMounted } from 'vue';
import { useRoute } from 'vue-router';

const apiParams = ref<any>({
  serviceStatus:useRoute().query.serviceStatus
});

const tablePageRef = ref('');
const columnsSearch: Array<SearchRule> = [
  {
    label: '订单编号',
    model: 'orderSn',
    disabled: false,
    input: true,
  },
  {
    label: '售后单号',
    model: 'sn',
    disabled: false,
    input: true,
  },
  {
    label: '售后状态',
    model: 'serviceStatus',
    disabled: false,
    select: {
      options: serviceStatus,
    },
  },
  {
    label: '售后类型',
    model: 'serviceType',
    disabled: false,
    select: {
      options: afterSaleType,
    },
  },
  {
    label: '商家名称',
    model: 'storeName',
    disabled: false,
    input: true,
  },
  {
    label: '会员名称',
    model: 'memberName',
    disabled: false,
    input: true,
  },
  {
    label: '申请时间',
    disabled: false,
    datePicker: {
      type: 'range',
    },
  },
];

const columnsTable: ColumnsDataRule[] = [
  {
    title: '售后服务单号',
    dataIndex: 'sn',
    width: 180,
  },
  {
    title: '订单编号',
    dataIndex: 'orderSn',
    width: 180,
  },

  {
    title: '商品名称',
    dataIndex: 'goodsName',
    width: 300,
    slot: true,
    ellipsis: false,
    slotData: {
      goods: {
        goodsImage: 'goodsImage',
        goodsName: 'goodsName',
      },
    },
  },
  {
    title: '会员名称',
    dataIndex: 'memberName',
    width: 120,
  },
  {
    title: '商家名称',
    dataIndex: 'storeName',
    width: 120,
  },
  {
    title: '售后金额',
    dataIndex: 'applyRefundPrice',
    currency: true,
    width: 110,
  },

  {
    title: '售后类型',
    dataIndex: 'serviceType',
    slot: true,
    width: 80,
    slotData: {
      badge: afterSaleType,
    },
  },
  {
    title: '售后状态',
    dataIndex: 'serviceStatus',
    slot: true,
    width: 190,
    slotData: {
      badge: serviceStatus,
    },
  },
  {
    title: '申请时间',
    width: 180,
    dataIndex: 'createTime',
  },
];

const sortMethods: MethodsRule = {
  title: '操作',
  width: 100,
  fixed: 'right',
  methods: [
    {
      slot: true,
      slotTemplate: 'btnList',
    },
  ],
};


onMounted(() => {
 
});
</script>
