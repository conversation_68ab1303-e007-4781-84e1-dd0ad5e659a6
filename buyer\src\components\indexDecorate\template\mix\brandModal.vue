<script setup lang="ts">
import { useRouter } from 'vue-router'
import { navigateTo } from '../navigate.ts'
import type { DragRule } from '@/views/operation/decoration/app/models/types'

const props = defineProps<{
  res: DragRule

}>()
const router = useRouter()
function handleClickItem(item: any) {
  const path = navigateTo(item)

  window.open(router.resolve(path).href, '_blank')
}
</script>

<template>
  <div v-if="props.res" h-343px w-584px flex rounded-10px bg-white class="box-shadow">
    <div class="left-side" w-254px @click="handleClickItem(props!.res!.data!.list![0])">
      <img :src="props!.res!.data!.list![0].img">
    </div>
    <div w-330px px-16px>
      <div mt-33px flex justify-end>
        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24">
          <path fill="currentColor" d="M6.23 20.23L8 22l10-10L8 2L6.23 3.77L14.46 12z" />
        </svg>
      </div>
      <div flex flex-wrap>
        <div v-for="(item, index) in props.res.data.brandList" :key="index" text-center class="brand-item"
          @click="handleClickItem(item)">
          <img :src="item.img" h-90px w-90px>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.left-side {
  overflow: hidden;
  width: 254px;
  height: 344px;
  border-radius: 9.8px 0 0 9.8px;
  opacity: 1;

  >img {
    width: 100%;
    height: 100%;
  }
}

.brand-item {
  width: 50%;
}
</style>
