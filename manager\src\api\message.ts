import request, { Method } from '@/utils/axios';
import { ParamsRule } from '@/types/global';

/**
 *  分页获取消息数据(未读消息/已读消息/回收站）
 *  @param params
 */
export function getStoreMessage(params: ParamsRule) {
  return request({
    url: `/message/storeMessage`,
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 * 已读消息
 * @param id
 */
export function readMessage(id: string | number) {
  return request({
    url: `/message/storeMessage/${id}/read`,
    method: Method.PUT,
    needToken: true,
  });
}

/**
 * 已读消息放入回收站
 * @param ids
 */
export function deleteMessage(ids: string | number) {
  return request({
    url: `/message/storeMessage/${ids}/delete`,
    method: Method.DELETE,
    needToken: true,
  });
}

/**
 * 回收站还原消息
 * @param ids
 */
export function resetMessage(ids: string | number) {
  return request({
    url: `/message/storeMessage/${ids}/reduction`,
    method: Method.PUT,
    needToken: true,
  });
}
/**
 * 彻底删除消息
 * @param ids
 */
export function clearMessage(ids: string | number) {
  return request({
    url: `/message/storeMessage/${ids}`,
    method: Method.DELETE,
    needToken: true,
  });
}
