import { setupLayouts } from 'virtual:generated-layouts'
import { ViteSSG } from 'vite-ssg'

// import Previewer from 'virtual:vue-component-preview'
import '@/assets/style/global.less'
import 'uno.css'
import { routes } from 'vue-router/auto-routes'
import App from './App.vue'

import './styles/main.css'
import type { UserModule } from './types'
// 引入arco-design样式
import '@arco-design/web-vue/dist/arco.css'

// const routes = setupLayouts(generatedRoutes)

// https://github.com/antfu/vite-ssg
export const createApp = ViteSSG(
  App,
  {
    routes: setupLayouts(routes),
    base: import.meta.env.BASE_URL,
  },
  (ctx) => {
    // install all modules under `modules/`
    Object.values(import.meta.glob<{ install: UserModule }>('./modules/*.ts', { eager: true }))
      .forEach(i => i.install?.(ctx))
    // ctx.app.use(Previewer)

  },
)
