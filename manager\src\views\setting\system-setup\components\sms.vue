<template>
  <div style="background-color: #fff">
    <a-form ref="formRef" style="padding: 30px" :model="form" layout="horizontal" auto-label-width>
      <a-divider orientation="left">短信平台</a-divider>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item field="type" label="平台" :validate-trigger="['change']">
            <a-radio-group type="button" v-model="form.type">
              <a-radio value="ALI">阿里云</a-radio>
              <a-radio value="HUAWEI">华为云</a-radio>
              <a-radio value="TENCENT">腾讯云</a-radio>
              <a-radio value="SHUMI">数米</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>
      <template v-if="form.type === 'ALI'">
        <a-divider orientation="left">阿里云短信</a-divider>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item field="accessKeyId" label="accessKeyId" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.accessKeyId" allow-clear/>
            </a-form-item>
            <a-form-item field="accessSecret" label="accessSecret" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.accessSecret" allow-clear/>
            </a-form-item>
            <a-form-item field="signName" label="短信签名" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.signName" allow-clear/>
            </a-form-item>
          </a-col>
        </a-row>
      </template>
      <template v-if="form.type === 'HUAWEI'">
        <a-divider orientation="left">华为云短信</a-divider>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item field="huaweiAppKey" label="APP_Key" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.huaweiAppKey" allow-clear/>
            </a-form-item>
            <a-form-item field="huaweiAppSecret" label="APP_Secret" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.huaweiAppSecret" allow-clear/>
            </a-form-item>
            <a-form-item field="huaweiSender" label="短信签名通道号" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.huaweiSender" allow-clear/>
            </a-form-item>
            <a-form-item field="huaweiSignature" label="短信签名" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.huaweiSignature" allow-clear/>
            </a-form-item>
          </a-col>
        </a-row>
      </template>
      <template v-if="form.type === 'TENCENT'">
        <a-divider orientation="left">腾讯云短信</a-divider>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item field="tencentSecretId" label="SecretId" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.tencentSecretId" allow-clear/>
            </a-form-item>
            <a-form-item field="tencentSecretKey" label="SecretKey" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.tencentSecretKey" allow-clear/>
            </a-form-item>
            <a-form-item field="tencentSdkAppId" label="短信应用ID" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.tencentSdkAppId" allow-clear/>
            </a-form-item>
            <a-form-item field="tencentSignName" label="短信签名" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.tencentSignName" allow-clear/>
            </a-form-item>
          </a-col>
        </a-row>
      </template>
      <template v-if="form.type === 'SHUMI'">
        <a-divider orientation="left">数米云短信</a-divider>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item field="shumiAccount" label="用户账号" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.shumiAccount" allow-clear/>
            </a-form-item>
            <a-form-item field="shumiPassword" label="用户密码" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.shumiPassword" allow-clear/>
            </a-form-item>
           
            <a-form-item field="shumiUrl" label="Url" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.shumiUrl" allow-clear/>
            </a-form-item>
            <a-form-item field="shumiName" label="短信签名" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.shumiName" allow-clear/>
            </a-form-item>
          </a-col>
        </a-row>
      </template>
      <a-form-item>
        <a-button type="primary"  @click="handleSubmit">保存</a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import {getSetting, setSetting} from '@/api/operation';
import {onMounted, ref} from 'vue';
import useCurrentInstance from '@/hooks/useCurrentInstance';
import {Message} from '@arco-design/web-vue';
import {FormInstance} from '@arco-design/web-vue/es/form';
import { VARCHAR255, REQUIRED, VARCHAR20 } from '@/utils/validator';

const formRef = ref<FormInstance>();
// 获取modal
const modal = useCurrentInstance().globalProperties?.$modal;

interface formInterface {
  type: string;
  accessKeyId: string;
  regionId: string;
  picLocation: string;
  accessSecret: string;
  signName: string;
  tencentSecretId: string;
  tencentSecretKey: string;
  tencentSdkAppId: string;
  tencentSignName: string;
  huaweiAppKey: string;
  huaweiAppSecret: string;
  huaweiSender: string;
  huaweiSignature: string;
  shumiAccount:string;
  shumiPassword:string;
  shumiUrl:string;
  shumiName:string;
}

// 数据集
const form = ref<formInterface>({
  type: "",
  accessKeyId: "",
  regionId: "",
  picLocation: "",
  accessSecret: "",
  signName:  "",
  tencentSecretId: "",
  tencentSecretKey: "",
  tencentSdkAppId: "",
  tencentSignName: "",
  huaweiAppKey: "",
  huaweiAppSecret: "",
  huaweiSender: "",
  huaweiSignature: "",
  shumiAccount:"",
  shumiPassword:"",
  shumiUrl:"",
  shumiName:"",
});

async function init() {
  const res = await getSetting('SMS_SETTING');
  form.value = res.data.result;
}

const handleSubmit = async () => {
  const auth = await formRef.value?.validate();
  if (!auth) {
    // console.log(form.value, 'form.value');
    const result = await setSetting('SMS_SETTING', form.value);
    if (result.data.success) {
      Message.success('设置成功!');
      init();
    }
  }
};

onMounted(() => {
  init();
});
</script>
