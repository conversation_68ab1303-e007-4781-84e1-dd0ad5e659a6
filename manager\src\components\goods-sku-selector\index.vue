<template>
  <a-modal v-model:visible="modalData.visible" :align-center="false" title-align="start" :width="800" draggable
    @ok="confirm" @cancel="close">
    <template #title> 商品选择器 </template>
    <div class="modal-wrapper">
      <!--<searchTable :row-span="12" :columns="columnsSearch" @reset="reset" @search="-->
        <!--(val) => {-->
          <!--tablePageRef.init(val);-->
        <!--}-->
      <!--" @category-list="changeCategory" />-->
      <searchTable :row-span="12" :columns="columnsSearch" @reset="reset"
                   @search="(val) => {modalData.apiParams = {...modalData.apiParams, ...val}}" @category-list="changeCategory">
      </searchTable>
      <a-radio-group v-model:model-value="modalData.queryType" style="margin-bottom: 10px" type="button">
        <a-radio value="goodsList">商品库</a-radio>
        <!--<a-badge :count="modalData.goodsCount || 0">-->
          <!--<a-radio value="selectedGoods">已选择</a-radio>-->
        <!--</a-badge>-->
      </a-radio-group>
      <span style="margin-left: 20px;">已选择({{modalData.goodsCount}})</span>
      <!--<tablePage v-if="modalData.visible" ref="tablePageRef" :checkbox="true" :defaultSelectedKeys="modalData.goodsSelectedList" :columns="columnsTable"-->
        <!--:api-params="modalData.apiParams" :api="props.goodsOrSku == true ? getGoodsSkuData : getGoodsListData"-->
        <!--@select-table-change="selectTableChange" />-->

      <tablePage v-if="modalData.visible" ref="tablePageRef" :checkbox="true" :defaultSelectedKeys="modalData.goodsSelectedList" :columns="columnsTable"
                 :api-params="modalData.apiParams" :api="props.goodsOrSku == true ? getGoodsSkuData : getGoodsListData"
                 @select-table-change="selectTableChange" />

    </div>
  </a-modal>
</template>

<script setup lang="ts">
import searchTable from '@/components/search-column/index.vue';
import tablePage from '@/components/table-pages/index.vue';
import { getGoodsListData, getGoodsSkuData } from '@/api/goods';
import { reactive, ref, watch, onMounted } from 'vue';
import { ColumnsDataRule, SearchRule } from '@/types/global';
import { goodsType, salesModel } from '@/utils/tools';

interface ModalRule {
  visible: boolean;
  categoryPath: string | number;
  queryType: string;
  goodsCount:number
  goodsSelectedList: any[];
  apiParams: {
    pageSize: number;
    categoryPath: string | number;
    [key: string]: unknown;
  };
  selectedKeys: any[];
}
const tablePageRef = ref();
const props = defineProps({
  apiParams: {
    type: null,
    default: {},
  },
  goodsOrSku: {
    type: null,
    default: ''
  },
  defaultGoodsSelectedList: {
    type: Array,
    default: () => {
      return []
    }
  }
});

const columnsSearch: Array<SearchRule> = [
  {
    label: '商品名称',
    model: 'goodsName',
    disabled: false,
    input: true,
  },
  {
    label: '商品分类',
    model: 'goodsName',
    disabled: false,
    category: true,
  },
];

const columnsTable: ColumnsDataRule[] = [
  {
    title: '商品',
    dataIndex: 'goods',
    width: 300,
    slot: true,
    slotData: {
      goods: {
        goodsImage: 'thumbnail',
        goodsName: 'goodsName',
      },
    },
  },
  {
    title: '价格',
    dataIndex: 'price',
    currency: true,
  },

  {
    title: '销售模式',
    dataIndex: 'salesModel',
    slot: true,
    slotData: {
      tag: salesModel,
    },
  },
  {
    title: '商品类型',
    dataIndex: 'goodsType',
    slot: true,
    slotData: {
      tag: goodsType,
    },
  },

  {
    title: '库存',
    dataIndex: 'quantity',
  },
];

const modalData: ModalRule = reactive({
  visible: false, // 是否可见
  categoryPath: '',
  queryType: 'goodsList',
  goodsSelectedList: [], // 选择的商品列表
  apiParams: { pageSize: 6, categoryPath: '', ...props.apiParams },
  goodsCount:0,
  selectedKeys: []
});
const emit = defineEmits<{
  (e: 'change', val: any): void;
}>();
const selectTableChange = (goodsList: any) => {
  console.log('=== 商品选择器表格选择变化 ===');
  console.log('表格选择的商品列表:', goodsList);
  console.log('表格选择的商品数量:', goodsList.length);

  modalData.goodsSelectedList = goodsList;
  modalData.goodsCount =  modalData.goodsSelectedList.length;

  console.log('更新后的modalData.goodsSelectedList:', modalData.goodsSelectedList);
  console.log('更新后的商品数量:', modalData.goodsCount);
};

// 点击分类回调内容
const changeCategory: any = (id: string | number) => {
  modalData.apiParams.categoryPath = id;
};

// 重制搜索条件
const reset = (val: any) => {
  changeCategory('');
  // tablePageRef.value.init(val);
  modalData.apiParams = {...modalData.apiParams, ...val};
};

// 初始化内容
const init = () => {
  modalData.visible = true;
};

// 确认
const confirm = () => {
  console.log('=== 商品选择器确认选择 ===');
  console.log('选择的商品列表:', modalData.goodsSelectedList);
  console.log('选择的商品数量:', modalData.goodsSelectedList.length);
  modalData.goodsSelectedList.forEach((item: any, index: number) => {
    console.log(`选择的第${index + 1}个商品详细信息:`, item);
  });

  emit('change', modalData.goodsSelectedList);
};

// 取消
const close = () => {
  modalData.visible = false;
};

watch(() => props.defaultGoodsSelectedList, (newValue, oldValue) => {
  modalData.goodsSelectedList = newValue;
  modalData.goodsCount = newValue.length;
  modalData.selectedKeys = newValue.map((item: any) => {
    return item.skuId;
  });
}, {deep: true,},);

onMounted(() => {
  modalData.goodsSelectedList = props.defaultGoodsSelectedList;
  modalData.goodsCount = props.defaultGoodsSelectedList.length;
  modalData.selectedKeys = props.defaultGoodsSelectedList.map((item: any) => {
    return item.skuId;
  });
});

defineExpose({
  init,
  close,
  modalData
});
</script>

<style lang="less" scoped>
.modal-wrapper {
  height: 650px;
}
</style>
