<template>
  <div class="home-bgcolor">
    <div class="pay-done-box">
      <img src="../../assets/images/pay-success.png">
    </div>
    <div class="pay-btn">
      <a-button status="danger" type="primary" @click="router.push('/')">继续逛逛</a-button>
      <a-button status="warning" type="primary" v-if="route.query.orderType ==='RECHARGE'" @click="router.push('/user/home/<USER>/moneyManagement')">查看余额</a-button>
      <a-button status="warning" type="primary" v-else @click="router.push('/user/home/<USER>/myOrder')">查看订单</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useRouter, useRoute } from 'vue-router';

  const router = useRouter();
  const route = useRoute();
</script>

<style scoped lang="less">
  .home-bgcolor {
    overflow: hidden;
  }
  .pay-done-box {
    margin: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .pay-btn{
    width: 300px;
    margin: 0 auto;
    >*{
      margin:0 4px;
    }
  }
</style>
