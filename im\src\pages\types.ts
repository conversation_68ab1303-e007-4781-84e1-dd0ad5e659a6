// 输入聊天内容约束
export interface Editor {
  text: string // 当前编辑的内容
  imageViewer: {
    show: boolean
    url: string
  }
  sendtime: number // 上次发送消息的时间
  interval: number // 发送间隔
  [key: string]: any
}

// socket状态约束
export type SocketStatus = 'connect' | 'close' | 'loading'
export type MessageType = 'MESSAGE'

// ws发送消息约束
export interface SendMessage {
  context: string // 聊天内容
  from: string // 发出消息ID
  messageType: MessageType // 消息类型
  operationType: MessageType // 操作类型
  talkId: string // 聊天id
  to: string // 聊天对象ID
}

// 消息约束
export interface InsertMessage {
  createTime: any
  fromUser: string
  isRead: boolean
  messageType: MessageType
  text: string
  toUser: string
}
