<script setup>
import { useWindowScroll } from '@vueuse/core'
import templates from '@/components/indexDecorate/template/exports'
import tpl_category from '@/components/indexDecorate/template/tpl_category.vue'
import tpl_banner from '@/components/indexDecorate/template/tpl_pin_banner.vue'
import tpl_pin_nav from '@/components/indexDecorate/template/tpl_pin_nav.vue'
import tpl_user_panel from '@/components/indexDecorate/template/tpl_user_panel.vue'
import tpl_ad from '@/components/indexDecorate/template/tpl_top_ad.vue'
import { indexData, getTopicData } from '@/api/index'
import { useRoute } from 'vue-router'
const route = useRoute()
const models = ref([])
const template = templates
const topSearchShow = ref(false)
async function init() {
  let res
  if (route.query.id) {
    res = await getTopicData(route.query.id)
  } else {
    res = await indexData({ clientType: 'PC' })

  }
  if (res.data.success) {
    const fetchData = res.data.result.pageData ? JSON.parse(res.data.result.pageData) : ''
    if (fetchData) {
      if (fetchData?.version === 'v3') {
        models.value = fetchData.data

      }
      else {
        Message.error({
          content: '当前编辑内容版本不支持',
          duration: 10000,
          closable: true,
        })
      }
    }
  }
}

onMounted(() => {

  init()
  const { y } = useWindowScroll()
  if (y.value > 300)
    topSearchShow.value = true

  else
    topSearchShow.value = false
})
</script>

<template>
  <!-- ad -->
  <tpl_ad :res="models[0]" />
  <!-- 固定头部 -->
  <HoverSearch class="hover-search" :class="{ show: topSearchShow }" />

  <!-- 头部 包括登录，我的订单等 -->
  <TheHeader />
  <!-- 搜索框、logo -->
  <Search />

  <!-- 分类/轮播图/个人中心 -->
  <div mx-auto w-1200px>
    <div my-14px flex flex-a-c>
      <div h-375px w-262px class="category">
        <tpl_category />
      </div>
      <div ml-10px w-914px>
        <div>
          <tpl_pin_nav :res="models[1]" />
        </div>
        <div mt-10px flex>
          <tpl_banner :res="models[2]" />
          <div ml-10px>
            <tpl_user_panel />
          </div>
        </div>
      </div>
    </div>
    <!-- 楼层装修内容 -->
    <div v-for="(element, block) in models " :key="block">
      <component :is="template[element.type]" class="component" :res="element" />
    </div>
  </div>
</template>

<style lang="less" scoped>
.category {
  border-radius: 10px;
  background: #ffffff;
  width: 263.2px;
  padding-top: 15.4px;
  line-height: 20px;
  border-bottom: none;
  font-size: 16.8px;
  font-weight: normal;
  color: #333333;
  letter-spacing: 0px;
}

/*固定头部*/
.hover-search {
  width: 100%;
  height: 60px;
  transform: translateY(-200px);
  background-color: #fff;
  position: fixed;
  top: 0;
  z-index: 9999;
  box-shadow: 0 0 10px 2px rgb(90 90 90 / 60%);
  transition: 0.35s;
}

.show {
  transform: translateY(0);
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0);
  top: 0;
}
</style>
