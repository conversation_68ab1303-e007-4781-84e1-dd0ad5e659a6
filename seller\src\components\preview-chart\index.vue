<template>
  <div>
    <a-spin style="display: block" :loading="loading">
      <div id="previewChart"></div>
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
  import { defineProps, onMounted, reactive, watch } from 'vue';
  import { Chart } from '@antv/g2';
  import useLoading from '@/hooks/loading';

  import { getPreviewChart } from '@/api/statisitics';
  import { useUserStore } from '@/store';
  import { PreViewParamsRule } from '@/types/global';

  interface PreviewNumber {
    date: string;
    uvNum?: number;
    title: string;
    pv?: number;
    pvNum?: number;
  }
  interface ResponseRule {
    chartList: Array<any>;
    previewChart: any;
    uvs: number;
    pvs: number;
  }

  const { loading, setLoading } = useLoading();
  const props = defineProps({
    dateType: {
      type: Object,
      default: () => {
        return {
          recent: 'LAST_SEVEN',
          month: '',
        };
      },
    },
  });
  const responseResult = reactive<ResponseRule>({
    chartList: [],
    previewChart: '',
    uvs: 0, // 访客数
    pvs: 0, // 浏览量
  });

  // 订单请求参数
  const previewParams = reactive<PreViewParamsRule>({
    searchType: props.dateType.recent, // TODAY ,  YESTERDAY , LAST_SEVEN , LAST_THIRTY
    year: props.dateType.month || new Date().getFullYear(),
    storeId: useUserStore().userInfo.id,
  });

  // 加载图表
  const chart = () => {
    const uv: Array<PreviewNumber> = [];
    const pv: Array<PreviewNumber> = [];
    const chartData = responseResult.chartList;

    chartData.forEach((item: any) => {
      uv.push({
        date: item.date,
        uvNum: item.uvNum,
        title: '访客数UV',
        pv: item.uvNum,
      });
      pv.push({
        date: item.date,
        pvNum: item.pvNum,
        pv: item.pvNum,
        title: '浏览量PV',
      });
    });

    const data = [...uv, ...pv];

    responseResult.previewChart.data(data);
    responseResult.previewChart.scale({
      activeQuantity: {
        range: [0, 1],
        nice: true,
      },
    });
    responseResult.previewChart.tooltip({
      showCrosshairs: true,
      shared: true,
    });

    responseResult.previewChart
      .line()
      .position('date*pv')
      .color('title')
      .label('pv')
      .shape('smooth');

    responseResult.previewChart
      .point()
      .position('date*pv')
      .color('title')
      .label('pv')
      .shape('circle')
      .style({
        stroke: '#fff',
        lineWidth: 1,
      });
    responseResult.previewChart
      .area()
      .position('date*pv')
      .color('title')
      .shape('smooth');

    responseResult.previewChart.render();
  };
  // 初始化流量的图表
  const initOrderChart = async () => {
    setLoading(true);
    const res = await getPreviewChart(previewParams);
    if (res.data.success) {
      responseResult.chartList = res.data.result;
      res.data.result.forEach((item: any) => {
        responseResult.uvs += item.uvNum;
        responseResult.pvs += item.pvNum;
      });
      if (!responseResult.previewChart) {
        responseResult.previewChart = new Chart({
          container: 'previewChart',
          autoFit: true,
          height: 500,
          padding: [70, 70, 70, 70],
        });
      }
    }
    chart();
    setLoading(false);
  };
  onMounted(() => {
    initOrderChart();
  });
  // 监听值的改变 父级值改变
  watch(
    () => props.dateType,
    (val) => {
      previewParams.searchType = val.recent;
      if (val.month) {
        // eslint-disable-next-line prefer-destructuring,no-nested-ternary
        previewParams.month = val.month.split('-')[1]
          ? val.month.split('-')[1].indexOf('0') != -1
            ? val.month.split('-')[1].substr(1)
            : ''
          : '';

        // eslint-disable-next-line prefer-destructuring
        previewParams.year = val.month.split('-')[0];
      }

      initOrderChart();
    },
    {
      deep: true,
      immediate: true,
    }
  );
</script>

<style scoped lang="less">
  .previewChart {
    width: 100%;
  }
</style>
