<template>
  <a-card class="general-card"  title="会员评价"  :bordered="false">
    <a-tabs @change="(val)=>{apiParams.grade = val}" :default-active-key="gradeStatusVar">
      <a-tab-pane key="GOOD" title="好评"></a-tab-pane>
      <a-tab-pane key="MODERATE" title="中评"></a-tab-pane>
      <a-tab-pane key="WORSE" title="差评"></a-tab-pane>
    </a-tabs>
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <tablePage ref="tablePageRef" :columns="columnsTable" :methods="sortMethods" :api="memberReview"
      @delete="handleDelete" @info="handleInfoModal" :api-params="apiParams" :bordered="true">
      <template #grade="{ data }">
        <span v-if="data.grade === 'GOOD'">好评</span>
        <span v-if="data.grade === 'MODERATE'">中评</span>
        <span v-if="data.grade === 'WORSE'">差评</span>
      </template>
      <template #shopDisable="{ data }">
        <!-- <a-switch v-model="data.status" /> -->
        <a-switch v-model="data.status" checked-value="OPEN" unchecked-value="CLOSE" @change="changeSwitchView(data)"
           >
          <template #checked>
            展示
          </template>
          <template #unchecked>
            隐藏
          </template>
        </a-switch>
      </template>
    </tablePage>
    <a-modal v-model:visible="infoFlag" :align-center="false" :footer="false" width="800px">
      <template #title>{{ infoData.data.memberName }}的评价详情 </template>
      <div class="info-list" style="overflow: hidden;display: flex;padding-bottom: 20px;">
        <div class="left-container">
          <div class="product">
            <img class="img" :src=infoData.data.goodsImage>
          </div>
          <div class="show">
            <label>页面展示：</label>
            <a-switch v-model="infoData.data.status" checked-value="OPEN" unchecked-value="CLOSE" @change="changeSwitch"
               >
              <template #checked>
                展示
              </template>
              <template #unchecked>
                隐藏
              </template>
            </a-switch>
          </div>
        </div>
        <div class="right-container">
          <div class="border-b" style="margin-top: 0;">{{ infoData.data.goodsName }}</div>
          <div class="border-b">
            <div class="div-height"> 店铺名称：{{ infoData.data.storeName }}</div>
            <div class="div-height"> 订单号：{{ infoData.data.orderNo }}</div>
            <div class="border-b" style="display: flex;align-items: flex-start;">
              <div class="content">
                <a-avatar>
                  <img :src="infoData.data.memberProfile" alt="">
                </a-avatar>
              </div>
              <div style="margin-left: 20px;">
                <div>{{ infoData.data.memberName }}</div>
                <div style="color: #aaaaaa;margin: 8px 0;">{{ infoData.data.content }}</div>
              </div>
            </div>
            <div class="score-content">
              <span>物流评分：{{ infoData.data.deliveryScore }}</span>
              <span>服务评分：{{ infoData.data.serviceScore }}</span>
              <span>描述评分：{{ infoData.data.descriptionScore }}</span>
            </div>
            <div class="" v-if="infoData.data.haveImage">
              评价图
              <div style="margin-left: 40px">
                <template v-if="infoData.data.images && infoData.data.images.length">
                  <img style="width: 100px;height: 110px;margin-left: 2px"
                    v-for="(img, index) in infoData.data.images.split(',')" :src="img" alt="" :key="index" />
                </template>
              </div>
            </div>
            <div class="border-b" v-if="infoData.data.reply" style="border-bottom: none;">
              <div>
                <div>
                  <div style="float: left"> 商家回复：</div>
                  <div style="margin-left: 60px">{{ infoData.data.reply }}</div>
                </div>
                <div v-if="infoData.data.haveReplyImage">
                  <div style="margin-left: 60px">
                    <template v-if="infoData.data.replyImage && infoData.data.replyImage.length">
                      <img style="width: 100px;height: 110px"
                        v-for="(img, index) in infoData.data.replyImage.split(',')" :key="index" :src="img" alt="" />
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">
import searchTable from '@/components/search-column/index.vue';
import tablePage from '@/components/table-pages/index.vue';
import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
import { gradeList } from '@/utils/tools';
import { memberReview, delMemberReview } from '@/api/member';
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import useCurrentInstance from '@/hooks/useCurrentInstance';
import { Message } from '@arco-design/web-vue';
import {
  getMemberInfoReview,
  updateMemberReview
} from '@/api/member';

const gradeStatusVar = ref<string>('GOOD');
const apiParams = ref<any>({grade:gradeStatusVar.value});
const tablePageRef = ref<any>();
const router = useRouter();
const infoFlag = ref<boolean>(false) // 弹框隐藏与显示
const infoData = reactive({
  data: {
    memberName: '',
    goodsImage: '',
    goodsName: '',
    storeName: '',
    orderNo: '',
    deliveryScore: '',
    serviceScore: '',
    descriptionScore: '',
    content: '',
    haveImage: '',
    images: '',
    reply: '',
    haveReplyImage: '',
    replyImage: '',
    memberProfile: '',
    status: '',
    id: ''
  }
})
//  获取modal
const modal = useCurrentInstance().globalProperties?.$modal;
const columnsSearch: Array<SearchRule> = [
  {
    label: '会员名称',
    model: 'memberName',
    disabled: false,
    input: true,
  },
];

const columnsTable: ColumnsDataRule[] = [
  {
    title: '商品名称',
    dataIndex: 'goodsName',
  },
  {
    title: '会员名称',
    dataIndex: 'memberName',
  },

  {
    title: '评价',
    dataIndex: 'grade',
    slot: true,
    slotTemplate: 'grade',
    // slotData: {
    //   tag: gradeList,
    // },
  },
  {
    title: '物流评分',
    dataIndex: 'deliveryScore',
  },
  {
    title: '服务评分',
    dataIndex: 'deliveryScore',
  },
  {
    title: '评价时间',
    dataIndex: 'createTime',
  },
  {
    title: '页面展示',
    dataIndex: 'status',
    slot: true,
    slotTemplate: 'shopDisable',
  },
];

const sortMethods: MethodsRule = {
  title: '操作',
  width: 300,
  fixed: 'right',
  methods: [
    {
      title: '查看',
      callback: 'info',
      type:"text" ,
      status:"success"
    },
    {
      title: '删除',
      callback: 'delete',
      type:"text" ,
      status:"danger"
    },
  ],
};

// 回调删除
function handleDelete(data: any) {
  modal.confirm({
    title: '确认删除',
    content: `您确认要删除会员${data.record.memberName}的评论`,
    alignCenter: false,
    onOk: async () => {
      const res = await delMemberReview(data.record.id);
      if (res.data.success) {
        Message.success('删除成功');
        tablePageRef.value.init();
      }
    },
  });
}
// 查看
const handleInfoModal = (v: any) => {
  // getMemberInfoReview
  infoFlag.value = true
  getMemberInfoReview(v.record.id).then((res: any) => {
    if (res.data.code == 200) {
      infoData.data = res.data.result
    }
  })
}
// 是否展示
const changeSwitchView = (v: any) => {
  updateMemberReview(v.id, {status:v.status}).then((res: any) => {
    if (res.data.code == 200) {
      Message.success('修改成功');
      tablePageRef.value.init();
    }
  })
}

// 修改评价
const changeSwitch = (v: any) => {
  updateMemberReview(infoData.data.id, v).then((res: any) => {
    if (res.data.code == 200) {
      Message.success('修改成功');
      tablePageRef.value.init();
    }
  })
}

</script>
<style lang="less" scoped>
.left-container {
  float: left;
}

.right-container {
  float: left;
  margin-left: 50px;
}

.img {
  width: 100%;
  height: 100%;
}

img {
  vertical-align: middle;
  border-style: none;
}

.product {
  width: 140px;
  height: 160px;
  border: 1px solid #d9d9d9;
  border-radius: 3px;
}

.show {
  label {
    font-size: 14px;
  }

  margin-top: 15px;
}

label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  display: block;
  float: left;
  margin-right: 2px;
}

.border-b {
  border-bottom: 1px solid #e9e9e9;
  width: 500px;
  overflow: hidden;
  position: relative;
  margin-top: 12px;
  line-height: 24px;
}

.div-height {
  line-height: 25px;
}

.score-content {
  margin: 5px 0;

  span {
    margin-right: 20px;
  }
}

.content {
  color: rgba(0, 0, 0, 0.45);
  margin: 5px 0 10px 0;
}
</style>
