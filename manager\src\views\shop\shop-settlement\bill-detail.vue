<template>
  <div>
    <a-card :style="{ width: '100%' }">
      <p>商家信息</p>
      <div class="flex_align_item flex">
        <p>店铺名称：{{ bill.storeName }}</p>
        <p>银行开户名：{{ bill.bankAccountName }}</p>
        <p>银行账号：{{ bill.bankAccountNumber }}</p>
        <p>开户行支行名称：{{ bill.bankName }}</p>
        <p>支行联行号：{{ bill.bankCode }}</p>
      </div>
    </a-card>
    <a-card class="mt_10 mb_10" :style="{ width: '100%' }">
      <p>账单详细</p>
      <div class="tips-status">
        <span>账单状态 ：</span>
        <span class="theme_color">{{
          unixSellerBillStatus(bill.billStatus)
        }}</span>
        <a-button
          v-if="bill.billStatus == 'CHECK'"
          size="mini"
          type="primary"
          @click="pass()"
          >付款</a-button
        >
      </div>
      <table>
        <tbody>
          <tr v-for="(item, index) in data" :key="index">
            <td>{{ item.name }}：</td>
            <td>{{ item.value }}</td>
          </tr>
        </tbody>
      </table>
      <div>
        <h3 class="ml_10" style="padding: 10px">结算详细</h3>
        <div class="bill-detail-price">
          <span>
            <p>积分结算金额</p>
            <p class="increase-color">
              +{{
                unitPrice(
                  bill.pointSettlementPrice ? bill.pointSettlementPrice : 0,
                  '￥'
                )
              }}
            </p>
          </span>
          <span>
            <p>平台优惠券补贴</p>
            <p class="increase-color">
              +{{
                unitPrice(
                  bill.siteCouponCommission ? bill.siteCouponCommission : 0,
                  '￥'
                )
              }}
            </p>
          </span>
          <span>
            <p>退单分销返现返还</p>
            <p class="increase-color">
              +{{
                unitPrice(
                  bill.kanjiaSettlementPrice ? bill.kanjiaSettlementPrice : 0,
                  '￥'
                )
              }}
            </p>
          </span>
          <span>
            <p>退单产生退还佣金金额</p>
            <p class="increase-color">
              +{{
                unitPrice(
                  bill.refundCommissionPrice ? bill.refundCommissionPrice : 0,
                  '￥'
                )
              }}
            </p>
          </span>
          <span>
            <p>退单金额</p>
            <p class="theme_color">
              -{{ unitPrice(bill.refundPrice ? bill.refundPrice : 0, '￥') }}
            </p>
          </span>
          <span>
            <p>平台收取佣金</p>
            <p class="theme_color">
              -{{
                unitPrice(bill.commissionPrice ? bill.commissionPrice : 0, '￥')
              }}
            </p>
          </span>
          <span>
            <p>分销返现支出</p>
            <p class="theme_color">
              -{{
                unitPrice(
                  bill.distributionCommission ? bill.distributionCommission : 0,
                  '￥'
                )
              }}
            </p>
          </span>
          <span>
            <p>退单平台优惠券补贴返还</p>
            <p class="theme_color">
              -{{
                unitPrice(
                  bill.siteCouponRefundCommission
                    ? bill.siteCouponRefundCommission
                    : 0,
                  '￥'
                )
              }}
            </p>
          </span>
        </div>
      </div>
    </a-card>
    <a-tabs default-active-key="1" @change="clickTabs">
      <a-tab-pane key="1" title="入账流水">
        <a-card :style="{ width: '100%' }">
          <tablePage
            v-if="show"
            ref="tablePageRefOwn"
            :columns="orderColumns"
            :api="APIShop.getStoreFlow"
            :api-params="apiParams"
            :bordered="true"
          />
        </a-card>
      </a-tab-pane>
      <a-tab-pane key="2" title="退款流水">
        <a-card :style="{ width: '100%' }">
          <tablePage
            v-if="drawback"
            ref="tablePageRefTwo"
            :columns="refundColumns"
            :api="APIShop.getStoreFlow"
            :api-params="apiParams"
            :bordered="true"
          />
        </a-card>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
  import * as APIShop from '@/api/shops';
  import { ref, reactive, onMounted } from 'vue';
  import { useRoute } from 'vue-router';
  import { unixSellerBillStatus, unitPrice } from '@/utils/filters';
  import tablePage from '@/components/table-pages/index.vue';
  import { ColumnsDataRule } from '@/types/global';
  import { Message } from '@arco-design/web-vue';

  const drawback = ref<boolean>(false);
  const show = ref<boolean>(false);
  const tablePageRefOwn = ref<any>();
  const tablePageRefTwo = ref<any>();
  // 传递的参数
  const apiParams = {
    flowType: 'PAY',
    startTime: '',
    endTime: '',
  };
  const route = useRoute();
  const bill = ref({
    storeName: '',
    bankAccountName: '',
    bankAccountNumber: '',
    bankName: '',
    bankCode: '',
    billStatus: '',
    sn: '',
    pointSettlementPrice: 0,
    siteCouponCommission: 0,
    kanjiaSettlementPrice: 0,
    refundCommissionPrice: 0,
    refundPrice: 0,
    commissionPrice: 0,
    distributionCommission: 0,
    siteCouponRefundCommission: 0,
  }); // 账单详情
  const orderColumns: ColumnsDataRule[] = [
    {
      title: '入账时间',
      dataIndex: 'createTime',
      width: 120,
    },
    {
      title: '订单编号',
      dataIndex: 'orderSn',
      width: 120,
    },
    {
      title: '订单金额',
      dataIndex: 'finalPrice',
      width: 120,
      currency: true,
    },
    {
      title: '平台分佣',
      dataIndex: 'commissionPrice',
      width: 120,
      currency: true,
    },
    {
      title: '平台优惠券',
      dataIndex: 'siteCouponPrice',
      width: 120,
      currency: true,
    },
    {
      title: '平台优惠券补贴金额',
      dataIndex: 'siteCouponCommission',
      width: 120,
      currency: true,
    },
    {
      title: '分销金额',
      dataIndex: 'distributionRebate',
      width: 120,
      currency: true,
    },
    {
      title: '应结金额',
      dataIndex: 'billPrice',
      width: 120,
      currency: true,
    },
  ];
  const refundColumns: ColumnsDataRule[] = [
    {
      title: '退款时间',
      dataIndex: 'createTime',
      width: 120,
    },
    {
      title: '退款流水编号',
      dataIndex: 'sn',
      width: 120,
    },
    {
      title: '订单编号',
      dataIndex: 'orderSn',
      width: 120,
    },
    {
      title: '售后编号',
      dataIndex: 'refundSn',
      width: 120,
    },
    {
      title: '退款金额',
      dataIndex: 'finalPrice',
      width: 120,
      currency: true,
    },
    {
      title: '退还佣金',
      dataIndex: 'commissionPrice',
      width: 120,
      currency: true,
    },
    {
      title: '退还平台优惠券',
      dataIndex: 'siteCouponCommission',
      width: 120,
      currency: true,
    },
    {
      title: '退还分销',
      dataIndex: 'distributionRebate',
      width: 120,
      currency: true,
    },
    {
      title: '合计金额',
      dataIndex: 'billPrice',
      width: 120,
      currency: true,
    },
  ]; // 退款单表头
  const data = ref([
    {
      name: '计算中',
      value: 0,
    },
    {
      name: '计算中',
      value: 0,
    },
    {
      name: '计算中',
      value: 0,
    },
    {
      name: '计算中',
      value: 0,
    },
    {
      name: '计算中',
      value: 0,
    },
    {
      name: '计算中',
      value: 0,
    },
    {
      name: '计算中',
      value: 0,
    },
    {
      name: '计算中',
      value: 0,
    },
  ]) as any; // 数据

  const initTable = () => {
    const list: any = bill.value;
    data.value[0].name = '结算单号';
    data.value[0].value = list.sn;
    data.value[1].name = '起止日期';
    data.value[1].value = `${list.startTime}~${list.endTime}`;

    data.value[2].name = '出帐日期';
    data.value[2].value = list.createTime;

    data.value[3].name = '当前状态';
    data.value[3].value = unixSellerBillStatus(list.billStatus);

    data.value[4].name = '当前店铺';
    data.value[4].value = list.storeName;

    data.value[5].name = '平台打款时间';
    data.value[5].value = list.payTime === null ? '未付款' : list.payTime;

    data.value[6].name = '订单付款总金额';
    data.value[6].value = unitPrice(
      list.orderPrice ? list.orderPrice : 0,
      ' ¥'
    );

    data.value[7].name = '结算金额';
    data.value[7].value = unitPrice(list.billPrice ? list.billPrice : 0, ' ¥');
  };

  // 切换tab
  const clickTabs = (v: any) => {
    console.log(v);
    if (v == 1) {
      show.value = true;
      apiParams.flowType = 'PAY';
      tablePageRefOwn.value.init();
    } else {
      drawback.value = true;
      apiParams.flowType = 'REFUND';
      tablePageRefTwo.value.init();
    }
  };
  const getDetail = async () => {
    const res = await APIShop.getBuyBillDetail(route.query.id);
    if (res.data.success) {
      bill.value = res.data.result;
      apiParams.startTime = res.data.result.startTime;
      apiParams.endTime = res.data.result.endTime;
      console.log(apiParams, ' apiParams');

      show.value = true;
      initTable();
    }
  };
  const init = () => {
    getDetail();
  };
  onMounted(async () => {
    init();
  });
  // 付款
  const pass = async () => {
    console.log('付款');
    const res = await APIShop.pay(route.query.id);
    if (res.data.success) {
      Message.success(res.data.message);
      init();
    }
  };
</script>

<style lang="less" scoped>
  .flex {
    justify-content: space-between;
    flex-wrap: wrap;

    > p {
      width: 50%;
      margin: 15px 0;
    }
  }

  .tips-status {
    padding: 10px;
    font-size: 14px;

    > span {
      font-weight: bold;
      margin-right: 8px;
    }

    > span:nth-of-type(2) {
      color: #e4393c;
    }
  }

  table {
    font-size: 14px;
    margin-left: 40px;

    tr {
      font-size: 12px;
      height: 40px;
      padding: 10px;

      td:nth-child(1) {
        width: 120px;
      }
    }
  }

  .bill-detail-price {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 10px;

    > span {
      font-size: 14px;
      text-align: center;
      width: 200px;
      margin-bottom: 20px;
    }

    .increase-color {
      color: green;
      margin-top: 5px;
    }

    .theme_color {
      margin-top: 5px;
    }
  }
</style>
