<template>
  <div class="sign-up">
    <div style="height: 50px;"></div>

    <div class="logo-box">
      <div class="logo">
        <img src="/src/assets/logo_transparent.png" :style="{width: '150px'}"  />
        <div>注册</div>
      </div>
      <div class="login">已有账号？<span @click="toLogin()">立即登录</span></div>
    </div>
    <div class="register-box">
      <!--注册-->
      <a-form ref="registerRef" size="large" layout="vertical"
              :style="{ width: '310px', margin: '0 auto' }" @submit-success="handleSubmit" :model="data.registerForm">
        <a-form-item :hide-asterisk="true" field="username" :rules="[REQUIRED, VARCHAR20]">
          <a-input v-model="data.registerForm.username" size="large" allow-clear placeholder="请输入用户名">
            <template #prefix>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 1024 1024">
                <path fill="currentColor" d="M628.736 528.896A416 416 0 0 1 928 928H96a415.872 415.872 0 0 1 299.264-399.104L512 704zM720 304a208 208 0 1 1-416 0a208 208 0 0 1 416 0"/>
              </svg>
            </template>
          </a-input>
        </a-form-item>
        <a-form-item :hide-asterisk="true" field="password" :rules="[REQUIRED, VARCHAR20]">
          <a-input-password v-model="data.registerForm.password" value="large" allow-clear placeholder="请输入密码">
            <template #prefix>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24">
                <path fill="currentColor" d="M19 10h1a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V11a1 1 0 0 1 1-1h1V9a7 7 0 0 1 14 0zm-2 0V9A5 5 0 0 0 7 9v1zm-6 4v4h2v-4z"/>
              </svg>
            </template>
          </a-input-password>
        </a-form-item>
        <a-form-item :hide-asterisk="true" field="mobilePhone" :rules="[REQUIRED, MOBILE]">
          <a-input v-model="data.registerForm.mobilePhone" allow-clear placeholder="请输入手机号">
            <template #prefix>
              <icon-lock />
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 256 256">
                <path fill="currentColor" d="M231.88 175.08A56.26 56.26 0 0 1 176 224C96.6 224 32 159.4 32 80a56.26 56.26 0 0 1 48.92-55.88a16 16 0 0 1 16.62 9.52l21.12 47.15v.12A16 16 0 0 1 117.39 96c-.18.27-.37.52-.57.77L96 121.45c7.49 15.22 23.41 31 38.83 38.51l24.34-20.71a8.12 8.12 0 0 1 .75-.56a16 16 0 0 1 15.17-1.4l.13.06l47.11 21.11a16 16 0 0 1 9.55 16.62"/>
              </svg>
            </template>
          </a-input>
        </a-form-item>
        <a-form-item :hide-asterisk="true" field="code" :rules="[REQUIRED]">
          <a-input v-model="data.registerForm.code" allow-clear placeholder="请输入手机验证码">
            <template #prefix>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24">
                <g fill="none">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M2 5a3 3 0 0 1 3-3h10a3 3 0 0 1 3 3v6a3 3 0 0 1-3 3h-3.586l-3.707 3.707A1 1 0 0 1 6 17v-3H5a3 3 0 0 1-3-3V5zm20 4v6c0 1-.6 3-3 3h-1v3c0 .333-.2 1-1 1c-.203 0-.368-.043-.5-.113L12.613 18H9l3-3h3c1.333 0 4-.8 4-4V6c1 0 3 .6 3 3z" fill="currentColor"/>
                </g>
              </svg>
            </template>
            <template #append><span class="code-msg" @click="sendCode">{{ data.codeMsg }}</span></template>
          </a-input>
        </a-form-item>
        <a-form-item no-style>
          <a-button @click.stop="verifyBtnClick" :status="data.verifyStatus?'success':'danger'" long :style="{marginBottom: '20px'}" :loading="loading">
            {{ data.verifyStatus?'验证通过': '点击完成安全验证' }}</a-button>
          <!-- 拼图验证码 -->
          <verify ref="verifyDom" class="verify-con" verify-type="REGISTER" @on-change="verifyChange"></verify>
        </a-form-item>
        <a-form-item no-style>
          <a-button html-type="submit" type="primary" status="danger" long :style="{width: '310px'}" :loading="loading">注册</a-button>
        </a-form-item>
      </a-form>
      <div class="user-agreement">点击注册，表示您同意《<span @click="toArticle('1371992704333905920')">商城用户协议</span>》</div>
    </div>

    <!--底部模块-->
    <div class="login-footer">
      <div flex justify-center class="help">
        <div class="hover-color hover-pointer" @click="toArticle()">帮助<span class="line"></span></div>
        <div class="hover-color hover-pointer" @click="toArticle('1371779927900160000')">隐私<span class="line"></span></div>
        <div class="hover-color hover-pointer" @click="toArticle('1371992704333905920')">条款</div>
      </div>
      <div>
        Copyright © {{data.myData.year}} - Present
        <a href="https://m.cizinst.cn" target="_blank">{{data.myData.copyright.title}}</a>
        版权所有</div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import config from '@/config/index';
  import { REQUIRED, VARCHAR20, MOBILE } from '@/utils/validator';
  import { regist } from "@/api/login";
  import { sendSms } from "@/api/common";
  import verify from '@/components/verify/index.vue';
  import { md5 } from '@/utils/md5';
  import { Message } from '@arco-design/web-vue';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const loading = ref(false);
  const verifyDom = ref();
  interface formInterface {
    myData: {
      year: any,
      copyright: any
    },
    registerForm: {
      username: string,
      password: string,
      mobilePhone: string,
      code: string
    },
    codeMsg: any,
    verifyStatus: boolean,
    interval: any,
    time: number,
  }
  // 数据集
  const data = ref<formInterface>({
    myData: {
      year: new Date().getFullYear(),
      copyright: config
    },
    // 注册表单
    registerForm: {
      username: '',
      password: '',
      mobilePhone: '',
      code: ''
    },
    // 验证码文字
    codeMsg: '发送验证码',
    //  是否图片验证通过
    verifyStatus: false,
    interval: null, // 定时器
    time: 60, // 倒计时
  });
  // 注册表单
  const registerRef = ref();


  // 发送手机验证码
  const sendCode = async () => {
    if (data.value.time === 60) {
      if (data.value.registerForm.mobilePhone === "") {
        Message.warning("请先填写手机号");
        return;
      }
      if (!data.value.verifyStatus) {
        Message.warning("请先完成安全验证");
        return;
      }
      let params = {
        mobile: data.value.registerForm.mobilePhone,
        verificationEnums: "REGISTER"
      };
      sendSms(params).then(res => {
        if (res.data.success) {
          Message.success("验证码发送成功");
          data.value.interval = setInterval(() => {
            data.value.time--;
            if (data.value.time === 0) {
              data.value.time = 60;
              data.value.codeMsg = "重新发送";
              data.value.verifyStatus = false;
              clearInterval(data.value.interval);
            } else {
              data.value.codeMsg = data.value.time;
            }
          }, 1000);
        } else {
          Message.warning(res.data.message);
        }
      })
    }
  };
  // 开启滑块验证
  const verifyBtnClick = async () => {
    if (!data.value.verifyStatus) {
      if (data.value.registerForm.mobilePhone === "") {
        Message.warning("请先填写手机号");
        return;
      }
      verifyDom.value.init();
    }
  };
  // 验证是否正确
  async function verifyChange(callback:any) {
    if (!callback.status) return;
    try {
      Message.success('验证通过！');
      data.value.verifyStatus = true;
      verifyDom.value.verifyShow = false;
    } catch (error) {
      loading.value = false;
    }
    verifyDom.value.verifyShow = false;
  }

  // 注册
  const handleSubmit = async () => {
    const auth = await registerRef.value?.validate();
    if (!auth) {
      let params = JSON.parse(JSON.stringify(data.value.registerForm));
      params.password = md5(params.password);
      await regist(params).then(res => {
        if (res.data.success) {
          Message.loading('注册成功！');
          router.push('/Login');
        } else {
          Message.loading(res.data.message);
        }
      });
    }
  };

  // 立即登录
  const toLogin = () => {
    router.push('/Login')
  };

  // 跳转文章详情
  const toArticle = (id?: any) => {
    let routeUrl = router.resolve({path: "/article", query: { id }});
    window.open(routeUrl.href, "_blank");
  };
</script>

<style scoped lang="less">
  .sign-up {
    background-color: @light_background_color;
    min-height: 100vh;
  }
  .logo-box {
    width: 600px;
    height: 80px;
    margin: 0 auto;
    border-bottom: 2px solid @theme_color;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 18px 10px 0;
    line-height: 50px;
    color: @text_color;
    .logo {
      width: 250px;
      height: 60px;
      position: relative;
      overflow: hidden;
      font-size: 20px;
      padding-left: 150px;
      img {
        position: absolute;
        top: -46px;
        left: 0;
      }
    }
    .login {
      font-size: 14px;
      span {
        color: @primary_color;
        cursor: pointer;
      }
    }
  }
  .register-box {
    width: 600px;
    margin: 0 auto;
    background-color: @light_white_background_color;
    padding: 30px 0;
    position: relative;
    .verify-con {
      position: absolute;
      right: 140px;
      top: 60px;
      z-index: 10;
    }
  }
  .user-agreement {
    text-align: center;
    margin-top: 16px;
    color: #333333;
    span {
      color: @primary_color;
      cursor: pointer;
    }
  }

  .login-footer {
    width: 100%;
    text-align: center;
    margin: 0 auto;
    color: #aaaaaa;
    position: fixed;
    bottom: 4vh;
    font-size: 14px;
    letter-spacing: 1px;
    .help {
      > div {
        width: 80px;
        height: 20px;
        line-height: 16px;
        margin-bottom: 10px;
        text-align: center;
        position: relative;
        .line {
          position: absolute;
          top: 2px;
          right: 0px;
          display: block;
          width: 1px;
          height: 12px;
          background-color: #aaaaaa;
        }
      }
    }
    a {
      color: @primary_color;
    }
  }
  .code-msg {
    cursor: pointer;
  }
</style>
