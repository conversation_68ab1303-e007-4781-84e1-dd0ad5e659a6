
import { getBaseSite } from '@/api/common';
import { useTitle, useFavicon, } from '@vueuse/core';


export function initSiteInfo() {
  const now = Number(new Date());

  // 尝试从localStorage中获取所需的各项数据
  const sellerSiteName = localStorage.getItem("seller_site_name");
  const sellerLogo = localStorage.getItem("seller_logo");
  const sellerIconImg = localStorage.getItem("seller_icon");
  const seller_expiration_time = localStorage.getItem("seller_expiration_time");
  // 检查数据是否存在且未过期
  if (!sellerSiteName || !sellerLogo || !sellerIconImg || !seller_expiration_time || now > Number(seller_expiration_time)) {
    fetchSite();
  } else {
    const title = useTitle()
    const icon = useFavicon()
    title.value = sellerSiteName + " - 商家后台";
    icon.value = sellerIconImg;
  }
}


/**
 * 获取站点信息
 */
export function fetchSite() {
  getBaseSite().then((res) => {
    if (res.data.success && res.data.result.settingValue) {
      try {
        let data = JSON.parse(res.data.result.settingValue);

        // 计算过期时间并存储
        var expirationTime = JSON.stringify(new Date().setHours(new Date().getHours() + 1));
        localStorage.setItem("seller_expiration_time", expirationTime);

        // 存储站点信息
        localStorage.setItem("seller_site_name", data.siteName);
        localStorage.setItem("seller_logo", data.storeSideLogo);
        localStorage.setItem("seller_icon", data.storeSideIcon);


        const title = useTitle()
        const icon = useFavicon()
        title.value = data.storeSideIcon + " - 商家后台";
        icon.value = data.storeSideIcon;
      } catch (error) {
        console.error("Failed", error);
      }
    }
  });
}
