<template>
  <a-card class="general-card" title="权益订单" :bordered="false">
    <!-- 搜索 -->
    <searchTable
      :columns="columnsSearch"
      @reset="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
      @search="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
    ></searchTable>
    <!-- <a-row style="margin-bottom: 16px">
      <a-col :span="16">
        <a-popover title="核验码" trigger="click" position="bl">
          <a-button type="primary">核验订单</a-button>
          <template #content>
            <div class="popover-content">
              <a-input v-model="qrCode" :style="{ width: '320px' }"></a-input>
              <a-button
                :style="{ marginLeft: '10px' }"
                type="primary"
                @click="orderVerification"
                >查询</a-button
              >
            </div>
          </template>
        </a-popover>
      </a-col>
    </a-row> -->
    <!-- 表格 -->
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getOrderList"
      :api-params="apiParams"
      :bordered="true"
      @orderDetail="handleDetails"
    />
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { getOrderList } from '@/api/order';
  import {
    orderStatusList,
    orderClientType,
    orderClientStatus,
  } from '@/utils/tools';
  import { useRouter } from 'vue-router';
  import { ref } from 'vue';

  const tablePageRef = ref('');
  const qrCode = ref('');
  const router = useRouter();
  // 查询列表
  const columnsSearch: Array<SearchRule> = [
    {
      label: '订单编号',
      model: 'orderSn',
      disabled: false,
      input: true,
    },
    {
      label: '会员名称',
      model: 'buyerName',
      disabled: false,
      input: true,
    },
    {
      label: '订单状态',
      model: 'orderStatus',
      disabled: false,
      select: {
        options: orderStatusList.filter((item) => item.value !== 'UNDELIVERED' && item.value !== 'DELIVERED' && item.value !== 'STAY_PICKED_UP'),
      },
    },
    {
      label: '下单时间',
      disabled: false,
      datePicker: {
        type: 'range',
      },
    },
  ];

  // 表格搜索列表
  const columnsTable: ColumnsDataRule[] = [
    {
      title: '订单号',
      dataIndex: 'sn',
    },
    {
      title: '订单来源',
      dataIndex: 'clientType',
      slot: true,
      slotData: {
        badge: orderClientType,
      },
    },
    {
      title: '买家名称',
      dataIndex: 'memberName',
    },
    {
      title: '订单金额',
      dataIndex: 'flowPrice',
      currency: true,
    },
    {
      title: '订单状态',
      dataIndex: 'orderStatus',
      slot: true,
      slotData: {
        badge: orderClientStatus,
      },
    },
    {
      title: '下单时间',
      width: 200,
      dataIndex: 'createTime',
    },
  ];
  // 操作列表
  const sortMethods: MethodsRule = {
    title: '操作',
    width: 100,
    methods: [
      {
        title: '查看',
        callback: 'orderDetail',
        type: 'text',
        status: 'success',
      },
    ],
  };

  // 查看
  const handleDetails = (val: any) => {
    router.push({
      name: 'order-detail',
      query: {
        id: val.record.sn,
      },
    });
  };
  
  // 传递的参数
  const apiParams = ref({
    orderType: 'EQUITY',
  });
</script>

<style scoped>
  .popover-content {
    display: flex;
  }
</style>
