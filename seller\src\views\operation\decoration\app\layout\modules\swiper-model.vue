<template>
  <div py-16px border-b-1>
    <div w-80px>{{ props.text }}</div>
    <div mt-20px>
      <a-radio-group v-model="props.res.data.swiper">
        <a-radio v-for="(item, index) in swiper" :key="index" :value="item.value">{{ item.label }}</a-radio>
      </a-radio-group>
    </div>

  </div>
</template>

<script setup lang="ts">
import { DragRule } from '@/views/operation/decoration/app/models/types';
const props = defineProps<{
  res: DragRule,
  text: string
}>()

const swiper = [
  {
    label: '固定',
    value: 'default',
  },
  {
    label: '横向滑动',
    value: 'swiper',
  },
]
</script>

<style scoped>
</style>
