import request, { Method } from '@/utils/axios';
import { ParamsRule } from '@/types/global';
import qs from 'query-string';
/**
 * 修改im商户id
 */
export function setMerchantId(params: any) {
  return request({
    url: '/settings/storeSettings/merchantEuid',
    method: Method.PUT,
    data: qs.stringify(params),
    needToken: true,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 * 修改保存库存预警数
 */
export function setStockWarning(params: any) {
  return request({
    url: '/settings/updateStockWarning',
    method: Method.PUT,
    data: qs.stringify(params),
    needToken: true,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 * 获取发货地址
 * */
export function getDeliverAddress() {
  return request({
    url: `/settings/storeSettings/storeDeliverGoodsAddress`,
    method: Method.GET,
    needToken: true,
  });
}

/**
 * 修改发货地址
 * */
export function editDeliverAddress(params: ParamsRule) {
  return request({
    url: `/settings/storeSettings/storeDeliverGoodsAddress`,
    method: Method.PUT,
    data: qs.stringify(params),
    needToken: true,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 * 获取商家退货地址
 */
export function getRefundGoodsAddress() {
  return request({
    url: '/settings/storeSettings/storeAfterSaleAddress',
    method: Method.GET,
    needToken: true,
  });
}

/**
 * 修改商家退货地址
 * */
export function saveRefundGoodsAddress(params: any) {
  return request({
    url: `/settings/storeSettings/storeAfterSaleAddress`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}

/**
 * 获取商家自提点
 */
export function getShopAddress(params: ParamsRule) {
  return request({
    url: '/member/storeAddress',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 * 保存商家自提点
 */
export function addShopAddress(params: any) {
  return request({
    url: '/member/storeAddress',
    method: Method.POST,
    needToken: true,
    params,
  });
}

// 修改商家自提点
export function editShopAddress(id: string | number, params: any) {
  return request({
    url: `/member/storeAddress/${id}`,
    method: Method.PUT,
    needToken: true,
    data: qs.stringify(params),
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 *  删除商家自提点
 */
export function delShopAddress(id: string | number) {
  return request({
    url: `/member/storeAddress/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}

/**
 *  获取商家公告列表数据
 */
export function getSellerArticleList() {
  return request({
    url: `/other/article/getByPage?type=STORE_ARTICLE&pageSize=15`,
    method: Method.GET,
    needToken: true,
  });
}

/**
 *  获取商家公告列表数据
 */
export function getSellerArticleDetail(id: string | number) {
  return request({
    url: `/other/article/${id}`,
    method: Method.GET,
    needToken: true,
  });
}

/**
 *  获取一级部门
 */
export function initDepartment() {
  return request({
    url: `/department`,
    method: Method.GET,
    needToken: true,
    // params,
  });
}
/**
 *  添加部门
 */
export function addDepartment(params: any) {
  return request({
    url: `/department`,
    method: Method.POST,
    needToken: true,
    params,
  });
}
/**
 *  删除部门
 */
export function deleteDepartment(ids: string | number, params?: ParamsRule) {
  return request({
    url: `/department/${ids}`,
    method: Method.DELETE,
    needToken: true,
    params,
  });
}
/**
 *  编辑部门
 */
export function editDepartment(ids: string | number, params: ParamsRule) {
  return request({
    url: `/department/${ids}`,
    method: Method.PUT,
    needToken: true,
    data: qs.stringify(params),
  });
}
/**
 *  加载部门子级数据
 */
export function loadDepartment(id: string | number, params: ParamsRule) {
  return request({
    url: `/department/${id}`,
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 分页获取角色数据
export function getRoleList(params: ParamsRule) {
  return request({
    url: '/role',
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 通过部门获取全部角色数据
export function getUserByDepartmentId(id: string | number) {
  return request({
    url: `/departmentRole/${id}`,
    method: Method.GET,
    needToken: true,
  });
}
// 通过部门修改绑定角色
export function updateDepartmentRole(id: string | number, params: any) {
  return request({
    url: `/departmentRole/${id}`,
    method: Method.PUT,
    needToken: true,
    data: params,
  });
}
// 获取用户数据 多条件
export function getUserListData(params: ParamsRule) {
  return request({
    url: `/clerk`,
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 校验店员手机号
export function checkClerk(mobile: string | number) {
  return request({
    url: `/clerk/${mobile}/check`,
    method: Method.POST,
    needToken: true,
  });
}

// 添加用户
export function addUser(params: ParamsRule) {
  return request({
    url: `/clerk`,
    method: Method.POST,
    needToken: true,
    data: qs.stringify(params),
  });
}
// 编辑用户
export function editOtherUser(id: string | number, params: any) {
  return request({
    url: `/clerk/${id}`,
    method: Method.PUT,
    needToken: true,
    data: qs.stringify(params),
  });
}

// 禁用用户
export function disableUser(id: string | number, params: any) {
  return request({
    url: `/clerk/enable/${id}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
// 删除用户
export function deleteUser(ids: string | number) {
  return request({
    url: `/clerk/delByIds/${ids}`,
    method: Method.DELETE,
    needToken: true,
  });
}
// 重置用户密码
export function resetUserPass(params: any) {
  return request({
    url: `/clerk/resetPassword/${params}`,
    method: Method.POST,
    needToken: true,
  });
}
// 获取店员详细
export function getClerk(id: string | number) {
  return request({
    url: `/clerk/${id}`,
    method: Method.GET,
    needToken: true,
  });
}

// 获取全部权限数据
export function getAllPermissionList(params?: ParamsRule) {
  return request({
    url: `/menu/tree`,
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 添加角色
export function addRole(params: any) {
  return request({
    url: `/role`,
    method: Method.POST,
    needToken: true,
    params,
  });
}
// 删除角色
export function deleteRole(ids: string | number, params?: ParamsRule) {
  return request({
    url: `/role/${ids}`,
    method: Method.DELETE,
    needToken: true,
    params,
  });
}
// 编辑角色
export function editRole(id: string | number, params: any) {
  return request({
    url: `/role/${id}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
// 查看某角色拥有的菜单
export function selectRoleMenu(params: ParamsRule) {
  return request({
    url: `/roleMenu/${params}`,
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 保存角色菜单
export function saveRoleMenu(id: string | number, params: ParamsRule) {
  return request({
    url: `/roleMenu/${id}`,
    method: Method.POST,
    needToken: true,
    data: params,
  });
}
// 查询运费模板
export function getShipTemplate() {
  return request({
    url: `/setting/freightTemplate`,
    method: Method.GET,
    needToken: true,
  });
}

// 删除运费模板
export function deleteShipTemplate(id: string | number) {
  return request({
    url: `/setting/freightTemplate/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}

// 新增运费模板
export function addShipTemplate(params: ParamsRule) {
  return request({
    url: `/setting/freightTemplate`,
    method: Method.POST,
    needToken: true,
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=utf-8',
    },
  });
}

// 编辑运费模板
export function editShipTemplate(id: number | string, params: ParamsRule) {
  return request({
    url: `/setting/freightTemplate/${id}`,
    method: Method.PUT,
    needToken: true,
    data: params,
    headers: {
      'Content-type': 'application/json;charset=utf-8',
    },
  });
}
