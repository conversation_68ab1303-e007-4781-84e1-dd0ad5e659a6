import request, { Method } from '@/utils/axios';
import { ParamsRule } from '@/types/global';

/**
 * 获取普通商品订单
 */
export function getOrderList(params: ParamsRule) {
  return request({
    url: '/order/order',
    method: Method.GET,
    needToken: true,
    params,
  });
}
/**
 * 获取商品订单详情
 */
export function orderDetail(sn: any) {
  return request({
    url: `/order/order/${sn}`,
    method: Method.GET,
    needToken: true,
  });
}
// 订单取消
export function ordersCancel(sn: string | number, params: ParamsRule) {
  return request({
    url: `/order/order/${sn}/cancel`,
    method: Method.POST,
    needToken: true,
    params,
  });
}
//  订单付款
export function orderPay(sn: string | number) {
  return request({
    url: `/order/order/${sn}/pay`,
    method: Method.POST,
    needToken: true,
  });
}
//  订单核销
export function orderTake(sn: any, verificationCode: string | number) {
  return request({
    url: `/order/order/take/${sn}/${verificationCode}`,
    method: Method.PUT,
    needToken: true,
  });
}
// 订单发货
export function orderDelivery(sn: any, params: ParamsRule) {
  return request({
    url: `/order/order/${sn}/delivery`,
    method: Method.POST,
    needToken: true,
    params,
  });
}
//  修改订单价格
export function updateOrderPrice(sn: any, params: any) {
  return request({
    url: `/order/order/update/${sn}/price`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
// 修改收货地址
export function editOrderConsignee(sn: any, params: any) {
  return request({
    url: `/order/order/update/${sn}/consignee`,
    method: Method.POST,
    needToken: true,
    params,
  });
}
export function afterSaleOrderPage(params: ParamsRule) {
  return request({
    url: '/order/afterSale/page',
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 获取投诉列表
export function getComplainPage(params: ParamsRule) {
  return request({
    url: '/order/complain',
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 获取投诉详情
export function getComplainDetail(id: any) {
  return request({
    url: `/order/complain/${id}`,
    method: Method.GET,
    needToken: true,
  });
}

// 添加交易投诉对话
export function addOrderComplaint(params: ParamsRule) {
  return request({
    url: `/order/complain/communication`,
    method: Method.POST,
    needToken: true,
    params,
  });
}

// 添加交易投诉对话
export function complainAppeal(params: ParamsRule) {
  return request({
    url: `/order/complain/appeal`,
    method: Method.PUT,
    needToken: true,
    params,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  });
}

// 分页获取发票列表
export function getReceiptPage(params: ParamsRule) {
  return request({
    url: '/trade/receipt',
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 开票
export function invoicing(id: string | number) {
  return request({
    url: `/trade/receipt/${id}/invoicing`,
    method: Method.POST,
    needToken: true,
  });
}

// 售后服务单详情
export function afterSaleOrderDetail(sn: any) {
  return request({
    url: `/order/afterSale/${sn}`,
    method: Method.GET,
    needToken: true,
  });
}

// 商家审核
export function afterSaleSellerReview(sn: any, params: any) {
  return request({
    url: `/order/afterSale/review/${sn}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}

// 商家确认收货
export function afterSaleSellerConfirm(sn: any, params: any) {
  return request({
    url: `/order/afterSale/confirm/${sn}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
// 售后单查询物流
export function getAfterSaleTraces(sn: any) {
  return request({
    url: `/order/afterSale/getDeliveryTraces/${sn}`,
    method: Method.GET,
    needToken: true,
  });
}
// 获取商家选中的物流公司
export function getLogisticsChecked() {
  return request({
    url: `/other/logistics/getChecked`,
    method: Method.GET,
    needToken: true,
  });
}
// 商家换货业务发货
export function afterSaleSellerDelivery( sn: any, params: any) {
  return request({
    url: `/order/afterSale/${sn}/delivery`,
    method: Method.POST,
    needToken: true,
    params,
  });
}

// 虚拟订单核验
export function verificationCode(verificationCode: string | number) {
  return request({
    url: `/order/order/getOrderByVerificationCode/${verificationCode}`,
    method: Method.GET,
    needToken: true,
  });
}

// 导出待发货订单
export function queryExportOrder(params: ParamsRule) {
  return request({
    url: `/order/order/queryExportOrder`,
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 下载代发货订单模板
export function downLoadDeliverExcel() {
  return request({
    url: `/order/order/downLoadDeliverExcel`,
    method: Method.GET,
    needToken: true,
    responseType: 'blob',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  });
}

// 上传代发货订单列表
export function uploadDeliverExcel(params: any) {
  return request({
    url: `/order/order/batchDeliver`,
    method: Method.POST,
    needToken: true,
    params,
    // headers: { 'Content-Type': 'application/json', },
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 查询包裹列表
export function getPackage(orderSn: any) {
  return request({
    url: `/order/order/getPackage/${orderSn}`,
    method: Method.GET,
    needToken: true,
  });
}

//查询物流
export function getTraces(sn: any, params?: ParamsRule) {
  return request({
    url: `/order/order/getTraces/${sn}`,
    method: Method.GET,
    needToken: true,
    data: params
  });
}

// 分包裹发货
export function partDelivery(orderSn: any, params: ParamsRule) {
  return request({
    url: `/order/order/${orderSn}/partDelivery`,
    method: Method.POST,
    needToken: true,
    data: params,
  });
}

export function getOrderFaceSheet(orderSn: any, params: ParamsRule){
  return request({
    url: `/order/order/${orderSn}/createElectronicsFaceSheet`,
    method: Method.POST,
    needToken: true,
    data: params,
  });
}

export function getOrderBenefitsInfo(orderSn: any){
  return request({
    url: `/order/benefits/${orderSn}`,
    method: Method.GET,
    needToken: true,
  });
}

/**
 * 获取订阅订单
 */
export function getSubscribeOrderList(params: ParamsRule) {
  return request({
    url: '/order/subscribe',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 * 获取订阅订单
 */
export function getSubscribeOrderDetail(sn: any) {
  return request({
    url: `/order/subscribe/${sn}`,
    method: Method.GET,
    needToken: true
  });
}
