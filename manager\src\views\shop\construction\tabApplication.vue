<template>
     <a-card class="general-card" title="微信进件" :bordered="false">
        <a-spin style="display: block" :loading="loading">
          <a-tabs :active-key="currentType" @change="(val)=>{currentType = val}">
              <a-tab-pane key="1" title="主体类型">
                <Main @callback="callbackMain" :submitFrom="submitFrom"></Main>
              </a-tab-pane>
              <a-tab-pane key="2" title="主体资料">
                <MainData ref="zhutiRef" :mainType="mainType" :submitFrom="submitFrom" @callbackTab="callbackTab"></MainData>
              </a-tab-pane>
              <a-tab-pane key="3" title="超级管理员信息">
                <Superinformation ref="SuperinformationRef" :submitFrom="submitFrom" @callbackTab="callbackTab"></Superinformation>
              </a-tab-pane>
              <a-tab-pane key="4" title="店铺信息">
                <Businessdata ref="BusinessdataRef" :submitFrom="submitFrom" @callbackTab="callbackTab"></Businessdata>
              </a-tab-pane>
              <a-tab-pane key="5" title="结算账户信息">
                <Settlementbankaccount ref="SettlementbankaccountRef" :submitFrom="submitFrom" @callbackTab="callbackTab"></Settlementbankaccount>
              </a-tab-pane>
              <a-tab-pane key="6" title="补充材料">
                <Supplementarymaterials ref="SupplementarymaterialsRef" :submitFrom="submitFrom" @callbackTab="callbackTab"></Supplementarymaterials>
              </a-tab-pane>
           </a-tabs>
         </a-spin>
         <div class="btn flex">
            <div flex>
              <a-button mr-10px @click="handleSubmit" type="primary">提交</a-button>
              <a-button @click="handleDraftSave">暂存</a-button>
            </div>
         </div>
     </a-card>
</template>
<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import Main from "./module/Main.vue"; // 主体类型
  import MainData from "./module/MainData.vue"; // 主体资料
  import Superinformation from "./module/Superinformation.vue"; //  超级管理员信息
  import Businessdata from "./module/Businessdata.vue"; // 店铺信息
  import Settlementbankaccount from "./module/Settlementbankaccount.vue"; // 结算账户信息
  import Supplementarymaterials from  "./module/Supplementarymaterials.vue";  // 补充材料
  import { draftSave, editConstruction, editputConstruction, postConstruction } from "@/api/shops";
  import { useRoute, useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';

  // 表单ref
  const zhutiRef = ref<any>('');
  const SuperinformationRef = ref<any>('');
  const BusinessdataRef = ref<any>('');
  const SettlementbankaccountRef = ref<any>('');
  const SupplementarymaterialsRef = ref<any>('');
  const loading = ref<any>(false);
  const route = useRoute();
  const router = useRouter();
  const currentType = ref<any>("1");
  const mainType = ref<any>(); // 主体类型
  // 主体资料表单
  const submitFrom =  ref<any>({
    organization_type: "",
    finance_institution: false,
    business_license_info: {
      business_license_copy: "",
      business_license_number: "",
      merchant_name: "",
      legal_person: "",
      company_address: "",
      business_time: "",
      business_time_startTime: "",
      business_time_endTime: ""
    },
    legal_person_commitment: "",
    legal_person_video: "",
    business_addition_msg: "",
    business_addition_pics: "",
    qualifications: "",
    sales_scene_info: {
      merchant_shortname: "",
      store_url: "",
      store_qr_code: "",
    },
    account_info: {
      bank_account_type: "",
      merchant_shortname: "",
      account_bank: "",
      bank_address_code: "",
      bank_branch_id: "",
      bank_name: "",
      account_number: "",
      account_name: "",
      account_cert_info: {
        settlement_cert_pic: "",
        relation_cert_pic: "",
        other_cert_pics: [],
      },
    },
    contact_info: {
      contact_type: "",
      contact_name: "",
      contact_id_doc_type: "",
      contact_id_card_number: "",
      contact_id_doc_copy: "",
      contact_id_doc_copy_back: "",
      contact_id_doc_period_begin: "",
      contact_id_doc_period_end: "",
      business_authorization_letter: "",
      openid: "",
      mobile_phone: "",
      contact_email: "",
    },
    certificate_info: {
      cert_copy: "",
      cert_type: "",
      cert_number: "",
      merchant_name: "",
      company_address: "",
      legal_person: "",
      period_begin: "",
      period_end: "",
    },
    certificate_letter_copy: "",
    finance_institution_info: {
      cert_copy: "",
      finance_type: "",
      finance_license_pics: "",
    },
    id_holder_type: "",
    authorize_letter_copy: null,
    owner: true,
    id_doc_type: "",
    id_card_info: {
      id_card_copy: "",
      id_card_national: "",
      id_card_name: "",
      id_card_number: "",
      id_card_address: "",
      card_period_begin: "",
      card_period_end: "",
    },
    id_doc_info: {
      id_doc_copy: "",
      id_doc_copy_back: "",
      id_doc_name: "",
      id_doc_number: "",
      id_doc_address: "",
      doc_period_begin: "",
      doc_period_end: "",
    },
    ubo_info_list: [
      {
        ubo_id_doc_type: "",
        ubo_id_doc_copy: "",
        ubo_id_doc_copy_back: "",
        ubo_id_doc_name: "",
        ubo_id_doc_number: "",
        ubo_id_doc_address: "",
        ubo_period_begin: "",
        ubo_period_end: "",
      },
    ],
  });


  // 选择主体类型回调
  const callbackMain = (val) => {
    mainType.value = val;
    currentType.value = "2";
  };
  // 步骤
  const callbackTab = (val) => {
    currentType.value = val;
  };

  // 提交
  const handleSubmit = async () => {
    // 身份证有效期结束时间
    if (!submitFrom.value.id_card_info.id_card_valid_time && submitFrom.value.id_card_info.id_card_valid_time2) {
      submitFrom.value.id_card_info.id_card_valid_time = submitFrom.value.id_card_info.id_card_valid_time2;
    }
    delete submitFrom.value.id_card_info.id_card_valid_time2;
    // 营业执照等级证书有效期结束时间
    if (!submitFrom.value.business_license_info.business_time_endTime && submitFrom.value.business_license_info.business_time_endTime2) {
      submitFrom.value.business_license_info.business_time_endTime = submitFrom.value.business_license_info.business_time_endTime2;
    }
    delete submitFrom.value.business_license_info.business_time_endTime2;
    // 超级管理员证件有效结束时间
    if (!submitFrom.value.contact_info.contact_id_doc_period_end && submitFrom.value.contact_info.contact_id_doc_period_end2) {
      submitFrom.value.contact_info.contact_id_doc_period_end = submitFrom.value.contact_info.contact_id_doc_period_end2;
    }
    delete submitFrom.value.contact_info.contact_id_doc_period_end2;

    if (submitFrom.value.business_license_info && submitFrom.value.business_license_info.business_time) {
      delete submitFrom.value.business_license_info.business_time;
    }


    if (!(await zhutiRef.value?.checkoutForm())) {
      Message.error("请填写主体资料必填项");
      return false;
    }else if (!(await SuperinformationRef.value?.checkoutForm())) {
      Message.error("请填写超级管理员信息必填项");
      return false;
    }else if (!(await BusinessdataRef.value?.checkoutForm())) {
      Message.error("请填写店铺信息必填项");
      return false;
    }else if (!(await SettlementbankaccountRef.value?.checkoutForm())) {
      Message.error("请填写结算账户信息必填项");
      return false;
    }
    const params = { ...submitFrom.value };
    !params.finance_institution ? delete params.finance_institution_info : '';
    params.owner ? delete params.ubo_info_list : '';
    params.id_doc_type == 'IDENTIFICATION_TYPE_MAINLAND_IDCARD' ? delete params.id_doc_info : delete params.id_card_info;
    // 当超级管理员类型是不是经办人 删除 contact_id_doc_copy contact_id_doc_copy_back
    // contact_id_doc_type
    if (params.contact_info.contact_type == '65') {
      delete params.contact_info.contact_id_doc_copy;
      delete params.contact_info.contact_id_doc_copy_back;
      delete params.contact_info.contact_id_doc_period_begin;
      delete params.contact_info.contact_id_doc_period_end;
      delete params.contact_info.business_authorization_letter;
      delete params.contact_info.contact_id_doc_type;
    }
    // 如果是小微商户需删除
    if(params.organization_type == '2401'||params.organization_type == '2500'){
      delete params.business_license_info;
      delete params.sales_scene_info.mini_program_sub_appid;
    }
    // 如果不是企业删除
    if(params.organization_type != '2'){
      delete params.id_card_info.id_card_address;
    }
    // 营业执照等级证书有效期时间
    if (params.business_license_info && params.business_license_info.business_time) {
      delete params.business_license_info.business_time;
    }

    loading.value = true;
    if (route.query.id) {
      editputConstruction(route.query.id, params).then((res) => {
            if (res.data.success) {
              Message.success('编辑成功！');
              router.push({ name: 'progress-form-list' });
            }
          }
      );
      loading.value = false;
    } else {
      postConstruction(params).then((res) => {
        if (res.data.success) {
          Message.success('保存成功！');
          router.push({ name: 'progress-form-list' });
        }
      });
      loading.value = false;
    }
  };
  // 暂存
  const handleDraftSave = () => {
    // 身份证有效期结束时间
    if (!submitFrom.value.id_card_info.id_card_valid_time && submitFrom.value.id_card_info.id_card_valid_time2) {
      submitFrom.value.id_card_info.id_card_valid_time = submitFrom.value.id_card_info.id_card_valid_time2;
    }
    delete submitFrom.value.id_card_info.id_card_valid_time2;
    // 营业执照等级证书有效期结束时间
    if (!submitFrom.value.business_license_info.business_time_endTime && submitFrom.value.business_license_info.business_time_endTime2) {
      submitFrom.value.business_license_info.business_time_endTime = submitFrom.value.business_license_info.business_time_endTime2;
    }
    delete submitFrom.value.business_license_info.business_time_endTime2;
    // 超级管理员证件有效结束时间
    if (!submitFrom.value.contact_info.contact_id_doc_period_end && submitFrom.value.contact_info.contact_id_doc_period_end2) {
      submitFrom.value.contact_info.contact_id_doc_period_end = submitFrom.value.contact_info.contact_id_doc_period_end2;
    }
    delete submitFrom.value.contact_info.contact_id_doc_period_end2;

    if (submitFrom.value.business_license_info && submitFrom.value.business_license_info.business_time) {
      delete submitFrom.value.business_license_info.business_time;
    }

    // if (submitFrom.value.business_license_info && submitFrom.value.business_license_info.business_time) {
    //   submitFrom.value.business_license_info.business_time = JSON.stringify(submitFrom.value.business_license_info.business_time.join(','))
    // }
    draftSave(submitFrom.value).then((res) => {
      if (res.data.success) {
        Message.success('暂存成功！');
        router.push({ name: 'progress-form-list' });
      }
    });
  };

  onMounted(() => {
    if (route.query.id) {
      editConstruction(route.query.id).then((res) => {
        if (res.data.result) {
          submitFrom.value = res.data.result;
          if (!submitFrom.value.business_license_info.business_time) {
            submitFrom.value.business_license_info.business_time = ["", ""]
          }
          else {
            submitFrom.value.business_license_info.business_time = submitFrom.value.business_license_info.business_time.split(',');
            submitFrom.value.business_license_info.business_time_startTime = submitFrom.value.business_license_info.business_time[0];
            submitFrom.value.business_license_info.business_time_endTime = submitFrom.value.business_license_info.business_time[1];
          }
        }
      });
    }
  })
</script>
<style lang="less" scoped>
.btn {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 60px;
  background-color: cornsilk;
  align-items: center;
  justify-content: center;
}
</style>
