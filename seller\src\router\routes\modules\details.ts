import { DEFAULT_LAYOUT } from '@/router/constans';

export default {
  path: '/detail',
  name: 'detail',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: '详情',
    requiresAuth: true,
    icon: 'icon-user',
    order: 0,
  },
  children: [
    {
      path: 'order-detail',
      name: 'order-detail',
      component: () => import('@/views/order/order-detail/orderDetail.vue'),
      meta: {
        locale: '订单详情',
        requiresAuth: true,
        hideInMenu: true,
      },
    },
    {
      path: 'bill-detail',
      name: 'bill-detail',
      component: () => import('@/views/order/bill/bill-detail.vue'),
      meta: {
        locale: '订单详情',
        requiresAuth: true,
        hideInMenu: true,
      },
    },
    {
      path: 'return-goods-detail',
      name: 'return-goods-detail',
      component: () => import('@/views/order/return-goods/detail.vue'),
      meta: {
        locale: '退货管理详情',
        requiresAuth: true,
        hideInMenu: true,
      },
    },
    {
      path: 'order-complaint-detail',
      name: 'order-complaint-detail',
      component: () => import('@/views/order/order-complaint/detail.vue'),
      meta: {
        locale: '投诉管理',
        requiresAuth: true,
        hideInMenu: true,
      },
    },
    {
      path: 'store-bill-detail',
      name: 'store-bill-detail',
      component: () => import('@/views/finance/store-bill/detail.vue'),
      meta: {
        locale: '店铺结算详情',
        requiresAuth: true,
        hideInMenu: true,
      },
    },
    {
      path: 'change-password',
      name: 'change-password',
      component: () => import('@/views/store/change-password/index.vue'),
      meta: {
        locale: '修改密码',
        requiresAuth: true,
        hideInMenu: true,
      },
    },
    {
      path: 'pintuan-edit',
      name: 'pintuan-edit',
      component: () => import('@/views/promotion/pintuan/pintuan-edit.vue'),
      meta: {
        locale: '新增拼团',
        requiresAuth: true,
        hideInMenu: true,
      },
    },
    {
      path: 'pintuan-goods',
      name: 'pintuan-goods',
      component: () => import('@/views/promotion/pintuan/pintuan-goods.vue'),
      meta: {
        locale: '拼团商品',
        requiresAuth: true,
        hideInMenu: true,
      },
    },
    {
      path: 'seckill-goods',
      name: 'seckill-goods',
      component: () => import('@/views/promotion/seckill/seckill-goods.vue'),
      meta: {
        locale: '秒杀活动管理',
        requiresAuth: true,
        hideInMenu: true,
      },
    },
{
      path: 'add-live',
      name: 'add-live',
      component: () => import('@/views/promotion/live/addLive.vue'),
      meta: {
        locale: '直播管理详情',
        requiresAuth: true,
        hideInMenu: true,
      },
    },
{
      path: 'full-discount-detail',
      name: 'full-discount-detail',
      component: () =>
        import('@/views/promotion/full-discount/full-discount-detail.vue'),
      meta: {
        locale: '满额活动详情',
        requiresAuth: true,
        hideInMenu: true,
      },
    },
    {
      path: 'add-coupon',
      name: 'add-coupon',
      component: () => import('@/views/promotion/coupon/coupon-publish.vue'),
      meta: {
        locale: '编辑平台优惠券',
        requiresAuth: true,
        hideInMenu: true,
      },
    },
    {
      path: 'coupon-receive',
      name: 'coupon-receive',
      component: () => import('@/views/promotion/coupon/coupon-receive.vue'),
      meta: {
        locale: '优惠券领取记录',
        requiresAuth: true,
        hideInMenu: true,
      },
    },
  ]
}
