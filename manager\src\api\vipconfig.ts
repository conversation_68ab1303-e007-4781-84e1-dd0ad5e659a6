import request, { Method } from '@/utils/axios';
import { ParamsRule } from '@/types/global';

/**
 *  获取vip商品列表
 */
export function getVipConfingList(params: ParamsRule) {
  return request({
    url: '/goods/vipConfig/list',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 *  保存商品信息
 */
export function saveVipConfing(params: any) {
  return request({
    url: '/goods/vipConfig/add',
    method: Method.POST,
    needToken: true,
    data: params,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 *  获取vip配置详情
 */
export function getVipConfigById(id: string) {
  return request({
    url: `/goods/vipConfig/${id}`,
    method: Method.GET,
    needToken: true,
  });
}

/**
 *  编辑vip配置信息
 */
export function updateVipConfig(id: string, params: any) {
  return request({
    url: `/goods/vipConfig/${id}`,
    method: Method.PUT,
    needToken: true,
    data: params,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 *  删除vip配置
 */
export function deleteVipConfig(id: string) {
  return request({
    url: `/goods/vipConfig/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}

/**
 *  批量删除vip配置
 */
export function batchDeleteVipConfig(ids: string[]) {
  return request({
    url: '/goods/vipConfig/batch',
    method: Method.DELETE,
    needToken: true,
    data: ids,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

