/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'unplugin-vue-router/types'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/': RouteRecordInfo<'/', '/', Record<never, never>, Record<never, never>>,
    '/[...all]': RouteRecordInfo<'/[...all]', '/:all(.*)', { all: ParamValue<true> }, { all: ParamValue<false> }>,
    '/article/': RouteRecordInfo<'/article/', '/article', Record<never, never>, Record<never, never>>,
    '/cart': RouteRecordInfo<'/cart', '/cart', Record<never, never>, Record<never, never>>,
    '/coupon': RouteRecordInfo<'/coupon', '/coupon', Record<never, never>, Record<never, never>>,
    '/forgetPassword': RouteRecordInfo<'/forgetPassword', '/forgetPassword', Record<never, never>, Record<never, never>>,
    '/goodsDetail': RouteRecordInfo<'/goodsDetail', '/goodsDetail', Record<never, never>, Record<never, never>>,
    '/goodsList': RouteRecordInfo<'/goodsList', '/goodsList', Record<never, never>, Record<never, never>>,
    '/Login': RouteRecordInfo<'/Login', '/Login', Record<never, never>, Record<never, never>>,
    '/merchant': RouteRecordInfo<'/merchant', '/merchant', Record<never, never>, Record<never, never>>,
    '/payment/pay': RouteRecordInfo<'/payment/pay', '/payment/pay', Record<never, never>, Record<never, never>>,
    '/payment/payDone': RouteRecordInfo<'/payment/payDone', '/payment/payDone', Record<never, never>, Record<never, never>>,
    '/payment/payment': RouteRecordInfo<'/payment/payment', '/payment/payment', Record<never, never>, Record<never, never>>,
    '/payment/thirdPay': RouteRecordInfo<'/payment/thirdPay', '/payment/thirdPay', Record<never, never>, Record<never, never>>,
    '/seckill': RouteRecordInfo<'/seckill', '/seckill', Record<never, never>, Record<never, never>>,
    '/shopEntry/': RouteRecordInfo<'/shopEntry/', '/shopEntry', Record<never, never>, Record<never, never>>,
    '/shoppingCart': RouteRecordInfo<'/shoppingCart', '/shoppingCart', Record<never, never>, Record<never, never>>,
    '/signUp': RouteRecordInfo<'/signUp', '/signUp', Record<never, never>, Record<never, never>>,
    '/topic': RouteRecordInfo<'/topic', '/topic', Record<never, never>, Record<never, never>>,
    '/user/home': RouteRecordInfo<'/user/home', '/user/home', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/accountSafe': RouteRecordInfo<'/user/home/<USER>/accountSafe', '/user/home/<USER>/accountSafe', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/addComment': RouteRecordInfo<'/user/home/<USER>/addComment', '/user/home/<USER>/addComment', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/commentDetail': RouteRecordInfo<'/user/home/<USER>/commentDetail', '/user/home/<USER>/commentDetail', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/commentList': RouteRecordInfo<'/user/home/<USER>/commentList', '/user/home/<USER>/commentList', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/complain': RouteRecordInfo<'/user/home/<USER>/complain', '/user/home/<USER>/complain', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/complainDetail': RouteRecordInfo<'/user/home/<USER>/complainDetail', '/user/home/<USER>/complainDetail', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/complainList': RouteRecordInfo<'/user/home/<USER>/complainList', '/user/home/<USER>/complainList', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/distribution': RouteRecordInfo<'/user/home/<USER>/distribution', '/user/home/<USER>/distribution', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/loginPwd': RouteRecordInfo<'/user/home/<USER>/loginPwd', '/user/home/<USER>/loginPwd', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/messageList': RouteRecordInfo<'/user/home/<USER>/messageList', '/user/home/<USER>/messageList', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/mobilePwd': RouteRecordInfo<'/user/home/<USER>/mobilePwd', '/user/home/<USER>/mobilePwd', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/myFavorites': RouteRecordInfo<'/user/home/<USER>/myFavorites', '/user/home/<USER>/myFavorites', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/myPoint': RouteRecordInfo<'/user/home/<USER>/myPoint', '/user/home/<USER>/myPoint', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/myTracks': RouteRecordInfo<'/user/home/<USER>/myTracks', '/user/home/<USER>/myTracks', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/paymentPwd': RouteRecordInfo<'/user/home/<USER>/paymentPwd', '/user/home/<USER>/paymentPwd', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/updatePwdTab': RouteRecordInfo<'/user/home/<USER>/updatePwdTab', '/user/home/<USER>/updatePwdTab', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/userInformation': RouteRecordInfo<'/user/home/<USER>/userInformation', '/user/home/<USER>/userInformation', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/addAddress': RouteRecordInfo<'/user/home/<USER>/addAddress', '/user/home/<USER>/addAddress', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/afterSale': RouteRecordInfo<'/user/home/<USER>/afterSale', '/user/home/<USER>/afterSale', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/afterSaleDetail': RouteRecordInfo<'/user/home/<USER>/afterSaleDetail', '/user/home/<USER>/afterSaleDetail', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/applyAfterSale': RouteRecordInfo<'/user/home/<USER>/applyAfterSale', '/user/home/<USER>/applyAfterSale', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/myAddress': RouteRecordInfo<'/user/home/<USER>/myAddress', '/user/home/<USER>/myAddress', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/myOrder': RouteRecordInfo<'/user/home/<USER>/myOrder', '/user/home/<USER>/myOrder', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/orderDetail': RouteRecordInfo<'/user/home/<USER>/orderDetail', '/user/home/<USER>/orderDetail', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/coupons': RouteRecordInfo<'/user/home/<USER>/coupons', '/user/home/<USER>/coupons', Record<never, never>, Record<never, never>>,
    '/user/home/<USER>/moneyManagement': RouteRecordInfo<'/user/home/<USER>/moneyManagement', '/user/home/<USER>/moneyManagement', Record<never, never>, Record<never, never>>,
  }
}
