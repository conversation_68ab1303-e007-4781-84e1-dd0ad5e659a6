<template>
  <a-spin style="width: 100%">
    <a-card
      class="general-card"
      :header-style="{ paddingBottom: '0' }"
      :body-style="{ padding: '17px 20px 21px 20px' }"
    >
      <template #title> 商品统计 </template>
      <template #extra>
        <a-link @click="$router.push('/statistic/goods')">更多</a-link>
      </template>
      <hot-goods-order />
    </a-card>
  </a-spin>
</template>

<script lang="ts" setup>
  import hotGoodsOrder from '@/components/hot-goods-order/index.vue';
</script>

<style scoped lang="less">
  .general-card {
    height: 642px;
  }
  :deep(.arco-table-tr) {
    height: 44px;
    .arco-typography {
      margin-bottom: 0;
    }
  }
  .increases-cell {
    display: flex;
    align-items: center;
    span {
      margin-right: 4px;
    }
  }
</style>
