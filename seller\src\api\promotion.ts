import request, { Method } from '@/utils/axios';
import qs from 'query-string';
import { ParamsRule } from '@/types/global';

/**
 *  获取优惠券列表
 */
export function getShopCouponList(params: ParamsRule) {
  return request({
    url: '/promotion/coupon',
    method: Method.GET,
    needToken: true,
    params,
  });
}
/**
 *  更新优惠券状态
 */
export function updateCouponStatus(params: any) {
  return request({
    url: '/promotion/coupon/status',
    method: Method.PUT,
    needToken: true,
    params,
  });
}
/**
 *  新增优惠券
 */
export function saveShopCoupon(params: ParamsRule) {
  return request({
    url: '/promotion/coupon',
    method: Method.POST,
    needToken: true,
    data: params,
    headers: { 'Content-Type': 'application/json' },
  });
}
/**
 *  修改优惠券
 */
export function editShopCoupon(params: ParamsRule) {
  return request({
    url: '/promotion/coupon',
    method: Method.PUT,
    needToken: true,
    data: params,
    headers: { 'Content-Type': 'application/json' },
  });
}

/**
 *  获取单个优惠券
 */
export function getShopCoupon(id: any) {
  return request({
    url: `/promotion/coupon/${id}`,
    method: Method.GET,
    needToken: true,
  });
}

/**
 *  获取优惠券领取记录
 */
export function getCouponReceiveList(params: ParamsRule) {
  return request({
    url: `/promotion/coupon/received`,
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 *  获取拼团列表
 */
export function getPintuanList(params: ParamsRule) {
  return request({
    url: '/promotion/pintuan',
    method: Method.GET,
    needToken: true,
    params,
  });
}
/**
 *  新建拼团
 */
export function savePintuan(params: ParamsRule) {
  return request({
    url: '/promotion/pintuan',
    method: Method.POST,
    needToken: true,
    data: params,
    headers: { 'Content-Type': 'application/json' },
  });
}
/**
 *  编辑拼团
 */
export function editPintuan(params: ParamsRule) {
  return request({
    url: '/promotion/pintuan',
    method: Method.PUT,
    needToken: true,
    data: params,
    headers: { 'Content-Type': 'application/json' },
  });
}
/**
 *  手动开启拼团活动
 */
export function editPintuanStatus(pintuanId: string | number, params?: any) {
  return request({
    url: `/promotion/pintuan/status/${pintuanId}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
/**
 *  删除拼团活动
 */
export function deletePintuan(pintuanId: string | number) {
  return request({
    url: `/promotion/pintuan/${pintuanId}`,
    method: Method.DELETE,
    needToken: true,
  });
}
/**
 *  根据id获取拼团信息
 */
export function getPintuanDetail(pintuanId: any) {
  return request({
    url: `/promotion/pintuan/${pintuanId}`,
    method: Method.GET,
    needToken: true,
  });
}
/**
 *  获取店铺直播间列表
 */
export function getLiveList(params: ParamsRule) {
  return request({
    url: '/broadcast/studio',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 * 获取店铺直播商品
 */
export function getLiveGoods(params: ParamsRule) {
  return request({
    url: '/broadcast/commodity',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 * 获取直播间详情
 */
export function getLiveInfo(studioId: any) {
  return request({
    url: `/broadcast/studio/studioInfo/${studioId}`,
    method: Method.GET,
    needToken: true,
  });
}

/**
 * 修改直播间
 */
export function editLive(params: ParamsRule) {
  return request({
    url: `/broadcast/studio`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
/**
 * 添加店铺直播商品
 */
export function addLiveStoreGoods(params: any) {
  return request({
    url: `/broadcast/commodity`,
    method: Method.POST,
    needToken: true,
    data: params,
    headers: { 'Content-Type': 'application/json' },
  });
}
/**
 * 店铺直播间添加
 */
export function addLiveGoods(params: any) {
  return request({
    url: `/broadcast/studio/${params.roomId}/${params.liveGoodsId}`,
    method: Method.PUT,
    needToken: true,
    data: { goodsId: params.goodsId },
  });
}
/**
 * 添加直播间
 */
export function addLive(params: ParamsRule) {
  return request({
    url: '/broadcast/studio',
    method: Method.POST,
    needToken: true,
    data: qs.stringify(params),
  });
}
/**
 * 直播间删除商品
 */
export function delRoomLiveGoods(
  roomId: string | number,
  liveGoodsId: string | number
) {
  return request({
    url: `/broadcast/studio/deleteInRoom/${roomId}/${liveGoodsId}`,
    method: Method.DELETE,
    needToken: true,
  });
}
/**
 * 减免列表
 */
export function getFullDiscountList(params: ParamsRule) {
  return request({
    url: '/promotion/fullDiscount',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 * 新增满减活动
 */
export function newFullDiscount(params: ParamsRule) {
  return request({
    url: '/promotion/fullDiscount',
    method: Method.POST,
    needToken: true,
    data: params,
    headers: { 'Content-Type': 'application/json' },
  });
}

/**
 * 编辑满减活动
 */
export function editFullDiscount(params: ParamsRule) {
  return request({
    url: '/promotion/fullDiscount',
    method: Method.PUT,
    needToken: true,
    data: params,
    headers: { 'Content-Type': 'application/json' },
  });
}

/**
 * 通过id获取满减活动
 */
export function getFullDiscountById(id: any) {
  return request({
    url: `/promotion/fullDiscount/${id}`,
    method: Method.GET,
    needToken: true,
  });
}

/**
 * 删除满减活动
 */
export function delFullDiscount(id: string | number) {
  return request({
    url: `/promotion/fullDiscount/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}

/**
 * 开启、关闭满减活动
 */
export function updateFullDiscount(id: any, params?: any) {
  return request({
    url: `/promotion/fullDiscount/status/${id}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}

/**
 * 获取分销商商品列表
 */
export function getDistributionGoods(params: ParamsRule) {
  return request({
    url: '/distribution/goods',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 * 取消分销商品
 */
export function distributionGoodsCancel(id: string | number) {
  return request({
    url: `/distribution/goods/delByIds/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}
/**
 * 获取分销设置
 */
export function getDistributionSetting() {
  return request({
    url: `/distribution/setting`,
    method: Method.GET,
    needToken: true,
  });
}
/**
 * 保存更新分销设置
 */
export function saveDistributionSetting(params: any) {
  return request({
    url: `/distribution/setting`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
/**
 * 获取秒杀活动数据
 */
export function getSeckillList(params: ParamsRule) {
  return request({
    url: '/promotion/seckill',
    method: Method.GET,
    needToken: true,
    params,
  });
}
/**
 * 限时秒杀活动商品
 */
export function seckillGoodsList(params: any) {
  return request({
    url: '/promotion/seckill/apply',
    method: Method.GET,
    needToken: true,
    params,
  });
}
/**
 * 限时秒杀活动详情
 */
export function seckillDetail(seckillId: any) {
  return request({
    url: `/promotion/seckill/${seckillId}`,
    method: Method.GET,
    needToken: true,
  });
}
/**
 * 添加限时抢购 商品
 */
export function setSeckillGoods(params: ParamsRule) {
  return request({
    url: `/promotion/seckill/apply/${params.seckillId}`,
    method: Method.POST,
    needToken: true,
    data: params.applyVos,
    headers: { 'Content-Type': 'application/json' },
  });
}
/**
 * 删除秒杀商品
 */
export function delSeckillGoods(params: any) {
  return request({
    url: `/promotion/seckill/apply/${params.seckillId}/${params.id}`,
    method: Method.DELETE,
    needToken: true,
  });
}

/**
 * 获取分销订单列表
 */
export function getDistributionOrder(params: ParamsRule) {
  return request({
    url: '/distribution/order',
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 选择分销商品
export function distributionGoodsCheck(params: any) {
  return request({
    url: `/distribution/goods/add`,
    method: Method.POST,
    needToken: true,
    data: params,
  });
}
