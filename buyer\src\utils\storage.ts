/**
 * 存储localStorage
 */
const setStorage = (label: any, value: any) => {
  if (label) {
    if (typeof value !== 'string') {
      value = JSON.stringify(value);
    }
    window.localStorage.setItem(label, value);
  }
};

/**
 * 获取localStorage
 */
const getStorage = (label: any) => {
  return label ? window.localStorage.getItem(label) : '';
};

export default {
  // 写入用户信息
  setUserInfo(val: any) {
    setStorage('userInfo', val);
  },
  // 获取用户信息
  getUserInfo() {
    return getStorage('userInfo');
  },
  // 写入购物车数量
  setCartNum(val: any) {
    setStorage('cartNum', val);
  },
  // 获取购物车数量
  getCartNum() {
    return getStorage('cartNum');
  },
  // 写入uuid
  setUuid(val: any) {
    setStorage('uuid', val);
  },
  // 获取uuid
  getUuid() {
    return getStorage('uuid');
  },
  // 写入accessToken
  setAccessToken(val: any) {
    setStorage('accessToken', val);
  },
  // 获取accessToken
  getAccessToken() {
    return getStorage('accessToken');
  },
// 写入getTimes
  setGetTimes(val: any) {
    setStorage('getTimes', val);
  },
  // 获取getTimes
  getGetTimes() {
    return getStorage('getTimes');
  },
  // 写入刷新token
  setRefreshToken(val: any) {
    setStorage('refreshToken', val);
  },
  // 获取刷新token
  getRefreshToken() {
    return getStorage('refreshToken');
  },
  // 清除token
  clearToken() {
    setStorage('accessToken', '');
    setStorage('refreshToken', '');
    setStorage('userInfo', '');
  },
  // 清除用户信息
  clearUserInfo() {
    setStorage('userInfo', '');
  },
  // 写入 siteName
  setSiteName(val: any) {
    setStorage('siteName', val);
  },
  // 获取 siteName
  getSiteName() {
    return getStorage('siteName');
  },
  // 写入 logoImg
  setLogoImg(val: any) {
    setStorage('logoImg', val);
  },
  // 获取 logoImg
  getLogoImg() {
    return getStorage('logoImg');
  },
  // 写入 sitelogoExpirationTime
  setSitelogoExpirationTime(val: any) {
    setStorage('sitelogoExpirationTime', val);
  },
  // 获取 sitelogoExpirationTime
  getSitelogoExpirationTime() {
    return getStorage('sitelogoExpirationTime');
  },
  // 写入 siteIcon
  setSiteIcon(val: any) {
    setStorage('siteIcon', val);
  },
  // 获取 siteIcon
  getSiteIcon() {
    return getStorage('siteIcon');
  },
  // 写入 hotWordsList
  setHotWordsList(val: any) {
    setStorage('hotWordsList', val);
  },
  // 获取 hotWordsList
  getHotWordsList() {
    return getStorage('hotWordsList');
  },
// 写入 hotWordsReloadTime
  setHotWordsReloadTime(val: any) {
    setStorage('hotWordsReloadTime', val);
  },
  // 获取 hotWordsList
  getHotWordsReloadTime() {
    return getStorage('hotWordsReloadTime');
  },
  // 写入 category
  setCategory(val: any) {
    setStorage('category', val);
  },
  // 获取 logoImg
  getCategory() {
    return getStorage('category');
  },
  // 写入 navList
  setNavList(val: any) {
    setStorage('navList', val);
  },
  // 获取 navList
  getNavList() {
    return getStorage('navList');
  },
  // 写入 categoryExpirationTime
  setCategoryExpirationTime (val: any) {
    setStorage('categoryExpirationTime', val);
  },
  // 获取 navList
  getCategoryExpirationTime () {
    return getStorage('categoryExpirationTime');
  }
};


