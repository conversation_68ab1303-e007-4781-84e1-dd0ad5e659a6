// eslint-disable-next-line import/no-cycle
import request, { Method, commonUrl } from '@/utils/axios';
import { ParamsRule } from '@/types/global';

// 查询会员订单列表
export function getOrderList (params: ParamsRule) {
  return request({
    url: `/order/order`,
    method: Method.GET,
    needToken: true,
    params
  });
}

/**
 * 订单明细
 * @param {orderSn} 订单编号
 */
export function orderDetail (orderSn: any) {
  return request({
    url: `/order/order/${orderSn}`,
    method: Method.GET,
    needToken: true
  });
}

/**
 * 取消订单
 * @param {orderSn} 订单编号
 * @param reason 取消订单原因
 */
export function cancelOrder (params: any) {
  return request({
    url: `/order/order/${params.tradeSn}/cancel`,
    method: Method.POST,
    needToken: true,
    params
  });
}

/**
 * 删除订单
 * @param {orderSn} 订单编号
 */
export function delOrder (orderSn: ParamsRule) {
  return request({
    url: `/order/order/${orderSn}`,
    method: Method.DELETE,
    needToken: true
  });
}

/**
 * 确认收货
 * @param {orderSn} 订单编号
 */
export function sureReceived (orderSn: number | string) {
  return request({
    url: `/order/order/${orderSn}/receiving`,
    method: Method.POST,
    needToken: true
  });
}

/**
 * 查询物流
 * @param {orderSn} 订单编号
 */
export function getTraces (orderSn: any) {
  return request({
    url: `/order/order/getTraces/${orderSn}`,
    method: Method.POST,
    needToken: true
  });
}

/**
 * 评价列表
 *
 */
export function evaluationList (params: ParamsRule) {
  return request({
    url: `/member/evaluation`,
    method: Method.GET,
    needToken: true,
    params
  });
}

// 添加交易投诉对话
export function communication (params: ParamsRule) {
  return request({
    url: `/order/complain/communication`,
    method: Method.POST,
    needToken: true,
    params
  });
}

// 退换货服务 提交物流
export function afterSaleDelivery (params: any) {
  return request({
    url: `/order/afterSale/delivery/${params.afterSaleSn}`,
    method: Method.POST,
    needToken: true,
    params
  });
}
// 获取退货可选物流公司
export function getLogisticsCompany () {
  return request({
    url: `/other/logistics`,
    method: Method.GET,
    needToken: true,
    params: { pageNumber: 1, pageSize: 200, disabled: 'OPEN' }
  });
}

//查询包裹列表
export const getPackage = (sn: number | string) => {
  return request({
    url: `/order/order/getPackage/${sn}`,
    method: Method.GET,
    needToken: true,
  })
}

//查询物流
export const getTracesList = (sn: number | string, params: ParamsRule) => {
  return request({
    url: `/order/order/getTracesList/${sn}`,
    method: Method.GET,
    needToken: true,
  })
};

// 获取商家售后收件地址
export function getStoreAfterSaleAddress(sn: number | string) {
  return request({
    url: `/order/afterSale/getStoreAfterSaleAddress/${sn}`,
    method: Method.GET,
    needToken: true,
  });
}
