<template>
  <card>
    <a-steps :current="activestep">
      <a-step>选择商品品类</a-step>
      <a-step>填写商品详情</a-step>
      <a-step>商品发布成功</a-step>
    </a-steps>
    <a-divider />
    <first-step v-show="activestep == 1" ref="first" @change="getFirstData" :categoryData="categoryData"></first-step>
    <second-step v-if="activestep === 2" ref="second" :first-data="firstData" @preChange="preChange"></second-step>
    <third-step v-if="activestep === 3" ref="third"></third-step>
  </card>
</template>

<script lang="ts" setup>
  import { onMounted, ref, provide } from 'vue';
  import { useRoute } from 'vue-router';
  import firstStep from './goodsOperationFirst.vue';
  import secondStep from './goodsOperationSec.vue';
  import thirdStep from './goodsOperationThird.vue';

  const route = useRoute();
  const activestep = ref(1);
  provide('activestep', activestep);
  const firstData = ref({});
  const first = ref();
  const categoryData = ref<Array<any>>([]);
  // 选择商品分类回调
  const getFirstData = (item:any) => {
    firstData.value = item;
    activestep.value = 2;
  };

  // 第二步点击上一步回调，返回商品分类
  const preChange = (val: any) => {
    categoryData.value = [];
    const ids = val.categoryPath.split(',');
    ids.map((item: any, index: number) => {
      categoryData.value.push({id: item, name: val.categoryName[index]});
    });
  };
  onMounted(() => {
    if (route.query.id || route.query.draftId || route.query.copyId) {
      activestep.value = 2;
    } else {
      activestep.value = 1;
      first.value.selectGoodsType = true;
    }
  });
</script>
