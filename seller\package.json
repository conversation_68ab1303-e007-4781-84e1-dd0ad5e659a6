{"name": "Lili-shop", "description": "Lili-shop Pro for Vue", "version": "1.0.0", "author": "LiLi-shop ui", "scripts": {"dev": "vite --config ./config/vite.config.dev.ts", "build": "vite build --config ./config/vite.config.prod.ts", "report": "cross-env REPORT=true npm run build", "preview": "npm run build && vite preview --host", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint-fix": "eslint --fix --ext .ts --ext .vue src"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["prettier --write", "eslint --fix"], "*.vue": ["stylelint --fix", "prettier --write", "eslint --fix"], "*.{less,css}": ["stylelint --fix", "prettier --write"]}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@antv/g2": "^4.2.4", "@arco-design/web-vue": "^2.52.1", "@chenfengyuan/vue-qrcode": "^2.0.0", "@formkit/auto-animate": "^0.8.1", "@types/mockjs": "^1.0.4", "@vueuse/core": "^7.7.1", "arco-design-pro-vue": "^2.5.2", "axios": "^0.24.0", "dayjs": "^1.10.7", "js-cookie": "^2.2.1", "lodash": "^4.17.21", "mitt": "^3.0.0", "nprogress": "^0.2.0", "pinia": "^2.0.9", "pinia-plugin-persistedstate": "^1.6.1", "qrcode": "^1.5.0", "query-string": "^7.0.1", "react-dnd-html5-backend": "^16.0.1", "rimraf": "^5.0.5", "tdesign-vue-next": "^1.6.7", "uuid": "^8.3.2", "vue": "^3.2.37", "vue-i18n": "^9.2.0-beta.17", "vue-router": "^4.0.14", "vue3-dnd": "^2.0.4", "vue3-print-nb": "^0.1.4", "xlsx": "^0.18.5"}, "devDependencies": {"@antfu/eslint-config": "^0.39.8", "@arco-design/web-vue": "^2.52.1", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^12.0.1", "@types/lodash": "^4.14.177", "@types/node": "^20.5.7", "@types/nprogress": "^0.2.0", "@typescript-eslint/eslint-plugin": "^5.10.0", "@typescript-eslint/parser": "^5.10.0", "@unocss/eslint-config": "^0.53.0", "@vitejs/plugin-vue": "^4.4.0", "@vitejs/plugin-vue-jsx": "^1.2.0", "@vue/babel-plugin-jsx": "^1.1.1", "cross-env": "^7.0.3", "eslint": "^8.7.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-prettier": "^8.3.0", "eslint-import-resolver-typescript": "^2.4.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^8.3.0", "less": "^4.1.2", "lint-staged": "^11.2.6", "mockjs": "^1.1.0", "prettier": "^2.2.1", "rollup-plugin-visualizer": "^5.6.0", "stylelint": "^13.8.0", "stylelint-config-prettier": "^8.0.2", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^20.0.0", "stylelint-order": "^4.1.0", "typescript": "^4.5.5", "unocss": "^0.53.0", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.19.3", "vite": "^3.2.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.3.0", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-style-import": "1.4.1", "vite-plugin-turbo-console": "0.2.3-beta.0", "vite-svg-loader": "^3.1.0", "vue-tsc": "^0.34.15", "webpack-dev-server": "^3.0.0"}, "engines": {"node": ">=14.0.0"}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china", "rollup": "^2.56.3", "gifsicle": "5.2.0"}, "peerDependencies": {"@arco-design/web-vue": ">=2.0.0-beta.7"}, "eslintConfig": {"extends": ["@antfu", "@unocss"]}}