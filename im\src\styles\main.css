html,
body,
#app {
  height: 100%;
  margin: 0;
  padding: 0;
}

html.dark {
  background: #121212;
}

/* 设置滚动条宽度 */
::-webkit-scrollbar {
  width: 6px;
  --scrollbar-width: 10px;
  --scrollbar-track-color: #f1f1f1;
  --scrollbar-radius: 8px; /* 这里设置圆角，单位可以是 px、em 等 */
}

/* 设置滚动条轨道颜色 */
::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

/* 设置滑块（ thumb ）颜色 */
::-webkit-scrollbar-thumb {
  background-color: #92908a;
}

/* 滚动条滑块:hover时的颜色 */
::-webkit-scrollbar-thumb:hover {
  background-color: #888;
}

/* 高亮显示部分的背景色 */
::-webkit-scrollbar-thumb:active {
  background-color: #888;
}

/* 设置滚动条轨道的圆角（仅 WebKit 内核浏览器支持） */
::-webkit-scrollbar-track-corner {
  background-color: #f1f1f1;
}
