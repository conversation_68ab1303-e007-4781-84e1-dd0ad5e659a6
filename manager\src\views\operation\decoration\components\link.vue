<template>
  <div py-30px border-b-1>
    <a-space>

      <div w-90px>跳转链接</div>
      <div>
        <div>
          <div v-if="isRes">
            <!-- 如果是res传值 -->
            <a-input :style="{ width: '240px' }" v-model="props!.res!.data.list![0]!.url!.___key" readonly
              @click="handleClickHref" placeholder="请添加跳转链接">
              <template v-if="props!.res!.data.list![0]!.url.___type" #prepend>
                {{ props!.res!.data.list![0]!.url.___type }}
              </template>
              <template #append>
                <a-link @click="handleClickHref()" :hoverable="false">
                  添加链接
                </a-link>
              </template>
            </a-input>
          </div>
          <!-- 如果是item传值则为组件的形式 -->
          <div v-if="props.item">
            <a-input :style="{ width: '240px' }" v-model="props.item!.url.___key" readonly @click="handleClickHref"
              placeholder="请添加跳转链接">
              <!-- 修改第24行附近的代码 -->
              <template v-if="props.item?.url?.___type" #prepend>
                {{ props.item?.url?.___type }}
              </template>
              <template #append>
                <a-link @click="handleClickHref()" :hoverable="false">
                  添加链接
                </a-link>
              </template>
            </a-input>
          </div>
          <!-- (isRes && props!.res!.data.list![0]!.url.___type) || (!isRes && props.item?.url.___type) -->
        </div>
        <div v-if="(isRes && props!.res!.data.list![0]!.url.___key) || (!isRes && props.item?.url.___key)" pt-10px
          text-right>
          <div @click="clear" color-gray cursor-pointer>
            清空
          </div>
        </div>
      </div>
    </a-space>
    <choose ref="decide" :res="props.res" @callback="receive" />
  </div>
</template>

<script setup lang="ts">

import choose from '@/views/operation/decoration/components/choose.vue'
import { tabList, TabList } from '@/views/operation/decoration/components/receive'
import { ref, computed } from 'vue'
import { DragRule } from '@/views/operation/decoration/app/models/types';
import { onMounted } from 'vue';

const isRes = computed<boolean>(() => !!props.res)

const props = defineProps<{
  res?: DragRule,
  item?: {
    img: string,
    url: any,
    [key: string]: any
  }

}>()
const decide = ref<any>(null)

// 添加链接
function handleClickHref() {
  decide.value?.open()
}
// 清空clear
function clear() {
  if (!props.item) {
    props!.res!.data.list![0]!.url.___type = ''
    props!.res!.data.list![0]!.url.___key = ''
    props!.res!.data.list![0]!.url.___value = ''
  } else {
    props.item.url.___type = ''
    props.item.url.___key = ''
    props.item.url.___value = ''
  }

}

onMounted(() => {
  if (props.res) {

    // !props.res.data.list![0]!.url.___key ? props.res.data.list![0]!.url.___key = '' :''
  }
})

// 过滤返回___key值
function filterKey(): void {
  
  // 判断当前组件传值返回
  return isRes.value ? props!.res!.data.list![0]!.url.___key : props.item?.url.___key
}
// 过滤返回___value值
function filterValue(): void {

  // 判断当前组件传值返回
  return isRes.value ? props!.res!.data.list![0]!.url.___value : props.item?.url.___value
}
/**
 * 过滤一下一些没有用的信息
 * 
 */
function filterPath(type: string, url: any): any {
  switch (type) {
    case 'goods':
      return {
        id: url.id,
        goodsId: url.goodsId,
        ___type: type,
        ___key: filterKey(),
        ___value: filterValue()
      }
    case 'category':
      return {
        id: url.id,
        ___type: type,
        ___key: filterKey(),
        ___value: filterValue()
      }
    case 'shops':
      return {
        id: url.id,
        ___type: type,
        ___key: filterKey(),
        ___value: filterValue()
      }
    case 'pages':
      return {
        id: url.skuId,
        goodsId: url.goodsId,
        ___type: type,
        ___key: filterKey(),
        ___value: filterValue()
      }
    case 'special':
      return {
        id: url.id,
        ___type: type,
        ___key: filterKey(),
        ___value: filterValue()
      }
    case 'other':
      return {
        title: url.label,
        ___type: type,
        ___key: filterKey(),
        ___value: filterValue()
      }
    case 'url':
      return {
        title: url.value,
        ___type: type,
        ___key: filterKey(),
        ___value: filterValue()
      }
  }
}


// 确认链接
function receive(val: any) {
  console.log(val, "值值")
  const type = val.___type
  if (!props.item) {
    
    props!.res!.data.list![0]!.url = filterPath(type, val)
    props!.res!.data.list![0]!.url.___value = type
    props!.res!.data.list![0]!.url.___type = tabList.find((item: TabList) => {
      props!.res!.data.list![0]!.url.___key = val[item._bind];
      return item.value === type
    })?.label
    

  } else {
    props!.item!.url = filterPath(type, val)
    props!.item!.url.___value = type
    props!.item!.url.___type = tabList.find((item: TabList) => {
      props!.item!.url.___key = val[item._bind];
      return item.value === type
    })?.label
    
  }

}

</script>

<style scoped>
</style>
