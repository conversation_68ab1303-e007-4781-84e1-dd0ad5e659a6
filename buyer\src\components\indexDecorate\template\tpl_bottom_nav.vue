<template>
  <div class="bottom-nav-container" v-if="res?.data?.list && res.data.list.length > 0">
    <div
      class="bottom-nav-wrapper"
      :style="{
        backgroundColor: res.data.background || '#ffffff',
        height: (res.data.height || 60) + 'px'
      }"
    >
      <div
        class="nav-item"
        v-for="(item, index) in res.data.list"
        :key="index"
        @click="handleNavClick(item, index)"
        :class="{ 'active': currentIndex === index }"
      >
        <div class="nav-icon">
          <img
            v-if="item.img"
            :style="{
              width: (res.data.iconSize || 24) + 'px',
              height: (res.data.iconSize || 24) + 'px'
            }"
            :src="item.img"
            alt=""
          />
          <div
            v-else
            class="default-icon"
            :style="{
              width: (res.data.iconSize || 24) + 'px',
              height: (res.data.iconSize || 24) + 'px'
            }"
          ></div>
        </div>
        <div
          class="nav-text"
          :style="{
            color: currentIndex === index ? (res.data.activeColor || '#1890ff') : (res.data.textColor || '#666'),
            fontSize: (res.data.fontSize || 12) + 'px'
          }"
        >
          {{ item.title }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { modelNavigateTo } from './tpl-v3'

const props = defineProps<{
  res: any
}>()

const router = useRouter()
const currentIndex = ref(0)

onMounted(() => {
  setCurrentIndex()
})

/**
 * 设置当前激活的tab索引
 */
function setCurrentIndex() {
  const currentPath = router.currentRoute.value.path

  // 根据当前路由匹配对应的tab
  props.res.data.list.forEach((item: any, index: number) => {
    if (item.url && item.url.___value === 'other') {
      const targetPath = getTargetPath(item.url.title)
      if (targetPath === currentPath) {
        currentIndex.value = index
      }
    }
  })
}

/**
 * 获取目标路径
 */
function getTargetPath(title: string): string {
  const pathMap: Record<string, string> = {
    '首页': '/',
    '分类': '/goodsList',
    '购物车': '/cart',
    '个人中心': '/user/home',
    '收藏商品': '/user/home/<USER>/myFavorites',
    '我的订单': '/user/home/<USER>/myOrder',
    '领券中心': '/coupon',
    '签到': '/user/home/<USER>/signIn',
    '秒杀频道': '/seckill',
    '拼团频道': '/pintuan',
    '砍价': '/bargain',
    '积分商城': '/point',
    '充值中心': '/user/home/<USER>/moneyManagement',
    '优惠购': '/promotion',
  }

  return pathMap[title] || '/'
}

/**
 * 处理导航点击事件
 */
function handleNavClick(item: any, index: number) {
  currentIndex.value = index

  // 如果配置了url，使用模板导航逻辑
  if (item.url) {
    const path = modelNavigateTo(item)
    if (path) {
      // 对于tab页面使用switchTab，对于普通页面使用navigateTo
      if (isTabPage(path.path)) {
        // 这里可以根据实际需求处理tab页面跳转
        router.push(path)
      } else {
        router.push(path)
      }
    }
  }
}

/**
 * 判断是否为tab页面
 */
function isTabPage(path: string): boolean {
  const tabPages = ['/', '/goodsList', '/cart', '/user/home']
  return tabPages.includes(path)
}
</script>

<style scoped lang="less">
.bottom-nav-container {
  position: relative;
  width: 100%;
}

.bottom-nav-wrapper {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 999;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 0;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 60px;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  &.active {
    .nav-icon {
      transform: scale(1.1);
    }
  }
}

.nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
  transition: transform 0.3s ease;

  img {
    border-radius: 4px;
    object-fit: cover;
  }
}

.default-icon {
  background-color: #f0f0f0;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-text {
  font-weight: 500;
  line-height: 1.2;
  text-align: center;
  transition: color 0.3s ease;
  word-break: break-all;
}
</style>
