<template>
  <div>
    <a-form :model="goodsList.data">
      <a-card>
        <div class="base-info-item">
          <h4>基本信息</h4>

          <div class="form-item-view">
            <a-form-item field="name" label="商品分类">
              <span v-for="(item, index) in goodsList.data.categoryName" :key="index">
                {{ item }}
                <i v-if="index !== goodsList.data.categoryName.length - 1">&gt;</i>
              </span>
            </a-form-item>
            <a-form-item field="goodsName" label="商品名称">
              {{ goodsList.data.goodsName }}
            </a-form-item>
            <a-form-item field="sellingPoint" label="商品卖点">
              {{ goodsList.data.sellingPoint }}
            </a-form-item>
            <a-form-item label="商品品牌">
              {{ goodsList.data.brandName }}
            </a-form-item>
            <a-form-item label="商品参数">
              <div>
                <div v-if="goodsList.data.goodsParamsDTOList && goodsList.data.goodsParamsDTOList.length" v-for="(item,index) in goodsList.data.goodsParamsDTOList" :key="index" style="margin-bottom: 10px;">
                  {{ item.groupName }} : <a-tag v-for="(child,i) in item.goodsParamsItemDTOList" :key="i" style="margin-right: 10px;">{{ child.paramName }} - {{ child.paramValue }}</a-tag>
                </div>
              </div>
            </a-form-item>
            <a-form-item label="虚拟商品类型" v-if="goodsList.data.goodsType == 'VIRTUAL_GOODS'">
              {{ getVirtualGoodsTypeText(goodsList.data.virtualGoodsType) }}
            </a-form-item>
          </div>
          <h4>商品交易信息</h4>
          <div class="form-item-view">
            <a-form-item field="goodsUnit" label="计量单位">
              {{ goodsList.data.goodsUnit }}
            </a-form-item>
            <a-form-item field="salesModel" label="销售模式">
              {{ goodsList.data.salesModel === 'RETAIL' ? '零售型' : '批发型' }}
            </a-form-item>
            <a-form-item field="name" label="销售规则" v-if="goodsList.data.salesModel !== 'RETAIL'">
              <Table border :columns="wholesalePreviewColumns" :data="wholesaleData"></Table>
            </a-form-item>
          </div>
          <h4>商品规格及图片</h4>
          <div class="form-item-view">
            <a-form-item field="id" label="商品编号">
              {{ goodsList.data.id }}</a-form-item>
            <a-form-item field="price" label="商品价格">
              {{ unitPrice(goodsList.data.price, '￥') }}</a-form-item>
            <a-form-item field="name" label="商品图片">
              <div class="demo-upload-list" v-for="(item, __index) in goodsList.data.goodsGalleryList" :key="__index">
                <a-image :src="item" width="100%"/>
                <!--<div class="demo-upload-list-cover">-->
                  <!--<icon-eye :size="32" type="ios-eye-outline" @click="handleViewGoodsPicture(item)"></icon-eye>-->
                <!--</div>-->
                <!--<a-modal v-model:visible="visible" @ok="handleOk" @cancel="handleCancel">-->
                  <!--<div>-->
                    <!--<a-image :src="previewGoodsPicture" class="imgSty" width="100%"/>-->
                  <!--</div>-->
                <!--</a-modal>-->
              </div>
            </a-form-item>
            <a-form-item field="goodsVideo" label="商品视频">
              <div class="video-box">
                <video v-if="goodsList.data.goodsVideo" controls class="player" :src="goodsList.data.goodsVideo"/>
              </div>
            </a-form-item>
            <a-form-item field="name" label="商品规格">
              <tablePage :columns="columnsTable" :dataList="skuDateList" :enablePagination="false" v-if="diaLogKey">
                <template #original="{ rowIndex }">
                  <a-input v-model="skuDateList[rowIndex].name" />
                </template>
                <template>
                  <a-input v-if="wholesaleData[0]" clearable disabled v-model="wholesaleData[0].price">
                  <!-- <span slot="append">元</span> -->
                  </a-input>
                </template>
                <template>
                  <a-input v-if="wholesaleData[1]" clearable disabled v-model="wholesaleData[1].price">
                  <!-- <span slot="append">元</span> -->
                  </a-input>
                </template>
                <template>
                  <a-input v-if="wholesaleData[2]" clearable disabled v-model="wholesaleData[2].price">
                  <!-- <span slot="append">元</span> -->
                  </a-input>
                </template>
                <template #children="{ data }">
                  <a-image v-for="(item,index) in data?.goodsGalleryList" :key="index" :src="item" width="60" height="60" style="margin-right: 6px;" />
                </template>
              </tablePage>
            </a-form-item>
          </div>
          <h4>商品详情描述</h4>
          <div class="form-item-view">
            <a-form-item label="PC端商品描述" v-if="goodsList.data.intro">
                <div v-for="(item, __index) in goodsList.data.intro.split(',')" :key="__index">
                  <a-image :src="item" class="intro-image" width="150" height='150'/>
                </div>
            </a-form-item>
            <a-form-item label="移动端描述" v-if="goodsList.data.mobileIntro">
                <div v-for="(item, __index) in goodsList.data.mobileIntro.split(',')" :key="__index">
                  <a-image :src="item" class="intro-image" width="150" height='150'/>
                </div>             
            </a-form-item>
          </div>
        </div>
      </a-card>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import tablePage from '@/components/table-pages/index.vue';
import { onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { DetailRule, skuColumnRule, skuListRule } from '@/types/seedetails';
import { getGoodsDetail } from '@/api/goods';
import { unitPrice } from '@/utils/filters';

const route = useRoute();

// 详情页数据
const goodsList = reactive<DetailRule>({
  data: {
    goodsUnit: '',
    categoryName: [],
    goodsName: '',
    sellingPoint: '',
    salesModel: '',
    id: '',
    price: 0,
    goodsGalleryList: [],
    wholesaleList: null,
    intro: '',
    mobileIntro: '',
    tags: '',
  },
});
// 销售规则
const wholesalePreviewColumns: any = [
  {
    title: '销售规则',
    width: 300,
    render: (h: any, params: any) => {
      return h(
        'div',
        `当商品购买数量 ≥ +${params.row.num} 时，售价为 ￥${params.row.price}/${goodsList.data.goodsUnit}`
      );
    },
  },
];
// sku表格
const columnsTable: skuColumnRule[] = [
  {
    title: '规格',
    // width: 150,
    dataIndex: 'specs',
  },
  {
    title: '编号',
    width: 150,
    dataIndex: 'sn',
  },
  {
    title: '重量(kg)',
    width: 100,
    dataIndex: 'weight',
  },
];
// 弹窗是否显示
const visible = ref<boolean>(false);
// 弹窗关闭
const handleOk = () => {
  visible.value = false;
};
// 弹窗关闭
const handleCancel = () => {
  visible.value = false;
};
let wholesaleData: any = [];
// 表格 传到封装的tablePage
const skuDateList: skuListRule[] = [];
// 父组件获取到表格数据再传给子组件
const diaLogKey = ref<boolean>(false);
// 弹窗图片
const previewGoodsPicture = ref<string>('');

// 浏览图片
const handleViewGoodsPicture = (url: any) => {
  visible.value = true;
  previewGoodsPicture.value = url;
  console.log(previewGoodsPicture.value);
};

const getVirtualGoodsTypeText = (virtualGoodsType: string) => {
  switch (virtualGoodsType) {
    case 'API':
      return 'API型';
    case 'CAMI':
      return '卡密型';
    case 'EQUITY':
      return '权益型';
    default:
      return '';
  }
};

// 初始化数据
const init = async (): Promise<any> => {
  const res = await getGoodsDetail(route.query.id);

  const { skuList } = res.data.result;
  goodsList.data = res.data.result;
  if (skuList) {
    skuList.forEach((item: any) => {
      skuDateList.push({
        specs: item.goodsName,
        sn: item.sn,
        weight: item.weight,
        cost: unitPrice(item.cost, '￥'),
        price: unitPrice(item.price, '￥'),
        image: item.thumbnail,
        goodsGalleryList: item.goodsGalleryList,
      });
      diaLogKey.value = true;
    });
    if (
      res.data.result.salesModel === 'WHOLESALE' &&
      res.data.result.wholesaleList
    ) {
      res.data.result.wholesaleList.forEach((item: any, index: any) => {
        columnsTable.push({
          title: `购买量 ≥${item.num}`,
          slot: `wholePrice${index}`,
          dataIndex: 'num',
        });
      });
    } else {
      columnsTable.push(
        {
          title: '成本',
          key: 'cost',
          dataIndex: 'cost',
          width: 160,
        },
        {
          title: '价格',
          dataIndex: 'price',
          key: 'price',
          width: 160,
        }
      );
    }
    columnsTable.push({
      title: '图片',
      dataIndex: 'original',
      slot: true,
      slotTemplate: 'children',
      tooltip: 'none',
    });
    wholesaleData = res.data.result.wholesaleList;
  }
};
onMounted(() => {
  init();
});
</script>

<style lang="less" scoped>
@import './goodsDetail.less';
</style>
