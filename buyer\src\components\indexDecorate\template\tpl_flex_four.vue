<script setup lang="ts">
import { useRouter } from 'vue-router'
import { navigateTo } from './navigate.ts'
import type { DragRule } from '@/views/operation/decoration/app/models/types'

const props = defineProps<{
  res: DragRule
}>()
const router = useRouter()
function handleClickItem(item: any) {
  const path = navigateTo(item)

  window.open(router.resolve(path).href, '_blank')
}
</script>

<template>
  <div flex flex-j-sb flex-a-c>
    <div v-for="(item, index) in props.res.data.list" :key="index" class="goods-item" @click="handleClickItem(item)">
      <div>
        <div class="goods-name">
          {{ item.title }}
        </div>
        <div class="goods-desc">
          {{ item.desc }}
        </div>
      </div>
      <div class="goods-img">
        <img :src="item.img">
      </div>
    </div>
  </div>
</template>

<style scoped>
.goods-item {
  padding-top: 34.8px;
  margin-bottom: 14.3px;
  width: 287px;
  height: 343.7px;
  border-radius: 9.8px;
  opacity: 1;
  cursor: pointer;
  background: #fff;
  transition: 0.35s;
  box-shadow: 0 1px 13px 0 #e5e5e5;
}

.goods-name {
  margin-bottom: 11.9px;
  font-size: 25px;
  font-weight: normal;
  line-height: 30px;
  text-align: center;
  letter-spacing: 0px;

  color: #333333;

  -webkit-text-stroke: #979797 0.7px;
  /* 浏览器可能不支持 */
}

.goods-img {
  text-align: center;

  >img {
    width: auto;
    max-height: 183px;
  }
}

.goods-desc {
  margin-bottom: 30px;
  font-size: 16px;
  font-weight: normal;
  line-height: 19px;
  text-align: center;
  letter-spacing: 0px;

  color: #666666;

  -webkit-text-stroke: #979797 0.7px;
  /* 浏览器可能不支持 */
}
</style>
