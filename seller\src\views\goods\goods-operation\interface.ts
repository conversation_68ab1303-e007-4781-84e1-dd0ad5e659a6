// 发布商品基本参数
export interface baseInfoFormRule {
  goodsGalleryFiles: any; // 商品相册列表
  release: string | number; // 是否立即发布  true立即发布，false 放入仓库
  recommend: string | number; // 是否为推进商品
  storeCategoryPath: Array<any>; // 店铺分类
  brandId: any;
  goodsUnit: any; // 计量单位
  goodsType: string; // 商品类型
  categoryPath: string; // 分类路径
  sellingPoint: any; // 商品卖点
  intro: any; // 商品详情
  mobileIntro: any; // 移动端商品详情
  updateSku: true;
  regeneratorSkuFlag: boolean; // 是否重新生成sku
  templateId: any; // 物流模板id
  goodsParamsDTOList: any; // 参数组
  categoryName: any; // 商品分类中文名
  goodsVideo?: string; // 商品视频
  [key: string]: any;
}
