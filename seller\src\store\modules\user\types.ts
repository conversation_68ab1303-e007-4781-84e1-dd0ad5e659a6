export interface UserState {
  userInfo: {
    storeLogo?: string;
    collectionNum?: string | number;
    createBy?: string | number;
    createTime?: string;
    deleteFlag?: unknown;
    deliveryScore?: number;
    descriptionScore?: number;
    goodsNum?: number;
    id?: string;
    memberId?: string;
    memberName?: string;
    merchantEuid?: string | number;
    nickName?: string;
    selfOperated?: boolean;
    serviceScore?: number;
    stockWarning?: number | string;
    storeAddressDetail?: string;
    storeAddressIdPath?: string;
    storeAddressPath?: string;
    storeCenter?: string;
    storeDesc?: string;
    storeDisable?: string;
    storeEndTime?: unknown;
    storeName?: string;
    updateBy?: string;
    updateTime?: string;
    yzfMpSign?: string;
    yzfSign?: string;
    [key: string]: unknown;
  };
  goodsCategoryData: {
    timeStamp: number;
    list: any[];
  };
}
