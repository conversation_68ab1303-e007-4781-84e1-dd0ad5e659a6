<template>
  <div class="box">
    <div w-1200px class="nav">
      <div flex class="nav-left">
        <div v-if="route.path.includes('home')">
          <router-link to="/" class="nav-item">首页</router-link>
        </div>
        <div>Hi，欢迎来到{{ config.title }}</div>
        <div v-show="!userInfo">
          <router-link :to="`/signUp`" class="nav-item">
            <span>立即注册</span>
          </router-link>
        </div>
        <div v-show="!userInfo">
          <router-link
            class="nav-item"
            :to="`/Login?rePath=${route.path}&query=${JSON.stringify(
              route.query
            )}`"
          >
            <span>请登录</span>
          </router-link>
        </div>
      </div>
      <div flex class="nav-right">
        <div v-if="!!userInfo">
          <a-popover position="bottom" trigger="hover">
            <div>
              <a-avatar v-if="userInfo && userInfo.face" :size="26" :imageUrl="userInfo.face"></a-avatar>
              <a-avatar v-else :size="26" imageUrl="/src/assets/images/default.png"></a-avatar>
              <!-- <a-avatar v-if="userInfo && userInfo.face" :size="26"><img alt="avatar" :src="userInfo.face"/></a-avatar>
              <a-avatar v-else :size="26"><img alt="avatar" src="/src/assets/images/default.png"/></a-avatar> -->
              {{ userInfo.nickName }}
            </div>
            <template #content>
              <ul class="drop-items">
                <li @click="goUserCenter('/user/home')">我的主页</li>
                <li @click="goUserCenter('/user/home/<USER>/coupons')">
                  我的优惠券
                </li>
                <li
                  @click="goUserCenter('/user/home/<USER>/myFavorites')"
                >
                  我的收藏
                </li>
                <li @click="signOutFun">退出登录</li>
              </ul>
            </template>
          </a-popover>
        </div>
        <div @click="goUserCenter('/user/home/<USER>/myOrder')">
          我的订单
        </div>
        <div @click="goUserCenter('/user/home/<USER>/myTracks')">
          我的足迹
        </div>
        <div @click="goUserCenter('/user/home/<USER>/messageList')">
          我的消息
        </div>
        <a-dropdown trigger="hover">
          <div @click="goUserCenter('/cart')">
            <!--<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 24 24">-->
            <!--<path fill="#ffffff" d="M4.004 6.417L.762 3.174L2.176 1.76l3.243 3.243H20.66a1 1 0 0 1 .958 1.287l-2.4 8a1 1 0 0 1-.958.713H6.004v2h11v2h-12a1 1 0 0 1-1-1zm2 .586v6h11.512l1.8-6zm-.5 16a1.5 1.5 0 1 1 0-3a1.5 1.5 0 0 1 0 3m12 0a1.5 1.5 0 1 1 0-3a1.5 1.5 0 0 1 0 3"/>-->
            <!--</svg>-->
            购物车({{ cartNum < 100 ? cartNum : "99" }})
          </div>
          <template #content>
            <a-doption v-show="shoppingCart.length <= 0">
              <div style="text-align: center" @click="goToPay">
                <div>你的购物车没有宝贝哦</div>
                <div>赶快去添加商品吧~</div>
              </div>
            </a-doption>
            <a-doption v-show="shoppingCart.length > 0">
              <div
                class="shopping-cart-box"
                v-for="(item, index) in shoppingCart"
                @click="goToPay"
                :key="index"
              >
                <div class="shopping-cart-img mr_10">
                  <img
                    :src="item.goodsSku.thumbnail"
                    style="width: 60px; height: 60px"
                    class="hover-pointer"
                  />
                </div>
                <div class="shopping-cart-info">
                  <div class="ellipsis ellipsis-1">
                    {{ item.goodsSku.goodsName }}
                  </div>
                  <div class="light-text-color">
                    数量:<span class="">{{ item.num }}</span>
                  </div>
                  <div class="price-color">
                    {{ unitPrice(item.purchasePrice, "￥") }}
                  </div>
                </div>
              </div>
            </a-doption>
          </template>
        </a-dropdown>
        <div @click="goUserCenter('/shopEntry?id=1')">店铺入驻</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import config from "@/config/index";
import { useRouter, useRoute } from "vue-router";
import { ref, onMounted, computed } from "vue";
import { unitPrice } from "@/utils/filters";
import storage from "@/utils/storage";
import { logout } from "@/api/account";
// import useCurrentInstance from "@/hooks/useCurrentInstance";
import { Modal } from "@arco-design/web-vue";
import { cartGoodsAll } from "@/api/cart";

const router = useRouter();
const route = useRoute();
// const modal = useCurrentInstance().globalProperties?.$modal;
const userInfo = ref();
const shoppingCart = ref<Array<any>>([]); // 购物车

// 跳转我的订单，我的足迹、收藏等
const goUserCenter = (path: any) => {
  if (userInfo.value) {
    router.push({ path: path });
  } else {
    Modal.confirm({
      title: "请登录",
      content: `请登录后执行此操作`,
      okButtonProps: { type: "primary", status: "danger" },
      okText: "立即登录",
      cancelText: "继续浏览",
      onOk: () => {
        console.log(
          "立即登录",
          router.currentRoute.value.path,
          router.currentRoute.value
        );
        router.push({
          path: "/Login",
          query: {
            rePath: router.currentRoute.value.path,
            query: JSON.stringify(router.currentRoute.value.query),
          },
        });
      },
    });
  }
};
// 退出登录
const signOutFun = () => {
  logout().then((res: any) => {
    if (res.data.success) {
      storage.clearToken();
      storage.clearUserInfo();
      router.push("/Login");
    }
  });
};
// 获取购物车列表
const getCartList = () => {
  if (userInfo.value && userInfo.value.username) {
    cartGoodsAll().then((res) => {
      if (res.data && res.data.success) {
        shoppingCart.value = res.data.result.skuList;
        storage.setCartNum(shoppingCart.value.length);
      }
    });
  }
};
// 购物车商品数量
const cartNum = computed(() => {
  return storage.getCartNum() || 0;
}) as any;
// 跳转购物车
const goToPay = () => {
  let url = router.resolve({ path: "/cart" });
  window.open(url.href, "_blank");
};

onMounted(() => {
  if (storage.getUserInfo()) {
    userInfo.value = JSON.parse(storage.getUserInfo());
    getCartList();
  }
});
</script>

<style scoped lang="less">
.box {
  width: 100%;
  font-size: 13px !important;
  height: 36px;
  background: #333;
  color: #fff;
  line-height: 36px;
}
.nav {
  width: 1200px;
  height: 36px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .nav-left {
    > div {
      padding: 0 14px;
      cursor: pointer;
    }
  }
  .nav-right {
    > div {
      padding: 0 14px;
      cursor: pointer;
    }
  }
}

.nav-item {
  font-size: 13px;
  font-weight: normal;
  text-decoration: none;
  cursor: pointer;
  color: #fff;
}

.drop-items {
  margin: 0 auto;
  padding: 0;
  z-index: 20;
  li {
    list-style: none;
    color: rgb(107, 106, 106);
    width: 70px;
    border-bottom: 1px solid rgb(207, 206, 206);
    font-weight: normal;
    text-align: center;
    line-height: 34px;
    font-size: 12px;
    &:last-child {
      border: none;
    }
    &:hover {
      cursor: pointer;
      color: #f00;
    }
  }
}

.shopping-cart-box {
  margin: 0 10px;
  padding: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  border-bottom: 1px #ccc dotted;
  .shopping-cart-img {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
  }
  .shopping-cart-info {
    > div {
      max-width: 250px;
      height: 22px;
      line-height: 22px;
      font-size: 13px;
    }
  }
}
</style>
