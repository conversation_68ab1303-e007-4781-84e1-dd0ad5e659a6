<template>
  <div>
    <a-spin :loading="loading" style="width: 100%">
      <!--搜索栏-->
      <Search></Search>
      <!--侧边栏 右侧bar-->
      <Drawer></Drawer>
      <!--商品详情导航栏-->
      <div class="shop-item-path">
        <div class="shop-nav-container">
          <a-breadcrumb :routes="categoryBar">
            <template #item-render="{route}">
              <a-link :href="route.path" :target="route.id===0?'_self':'_blank'">{{route.name}}</a-link>
            </template>
          </a-breadcrumb>
          <div v-if="!takeDownSale" class="store-collect">
            <span v-if="goodsMsg.data">
              <router-link :to="'Merchant?id=' + goodsMsg.data.storeId" class="link">{{goodsMsg.data.storeName}}</router-link>
            </span>
            <span @click="collectStore">
              <template v-if="storeCollected">
                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 24 24">
                  <path fill="#ef4444" d="M12.001 4.529a5.998 5.998 0 0 1 8.242.228a6 6 0 0 1 .236 8.236l-8.48 8.492l-8.478-8.492a6 6 0 0 1 8.48-8.464"/>
                </svg>
                已收藏店铺
              </template>
              <template v-else>
                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 24 24">
                  <path fill="#999999" d="M12.001 4.529a5.998 5.998 0 0 1 8.242.228a6 6 0 0 1 .236 8.236l-8.48 8.492l-8.478-8.492a6 6 0 0 1 8.48-8.464m6.826 1.641a3.998 3.998 0 0 0-5.49-.153l-1.335 1.198l-1.336-1.197a4 4 0 0 0-5.686 5.605L12 18.654l7.02-7.03a4 4 0 0 0-.193-5.454"/>
                </svg>
                收藏店铺
              </template>
            </span>
            <span @click="IMService()">联系客服</span>
          </div>
        </div>
      </div>
      <!--商品信息展示-->
      <ShowGoods v-if="goodsMsg.data" :detail="goodsMsg" @handleClickSku="targetClickSku"></ShowGoods>
      <!--商品详细展示-->
      <ShowGoodsDetail v-if="goodsMsg.data" :detail="goodsMsg"></ShowGoodsDetail>
      <Empty v-if="takeDownSale" _Title="当前商品已下架"></Empty>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import storage from '@/utils/storage';
  import { useRoute } from 'vue-router';
  import { getGoodsDistribution } from "@/api/distribution";
  import { goodsSkuDetail } from "@/api/goods";
  import { cancelCollect, collectGoods, isCollection } from "@/api/member";
  import { getDetailById } from "@/api/shopentry";
  import { getIMDetail } from "@/api/common";
  import Cookies from 'js-cookie';

  const route = useRoute();
  const loading = ref(false); // 加载状态
  const categoryBar = ref<any>([]); // 分类
  const goodsMsg = ref<any>({}); // 商品信息
  const storeMsg = ref({}); // 店铺信息
  const storeCollected = ref(false); // 商品收藏
  const takeDownSale = ref(false); // 是否下架
  const IMLink = ref();


  // 点击规格
  const targetClickSku = (val: any) => {
    getGoodsDetail(val);
  };
  // 获取商品详情
  const getGoodsDetail = (val?: any) => {
    loading.value = true;
    const params = val || route.query;

    // 分销员id
    let distributionId = params && params.distributionId ? params.distributionId : Cookies.get("distributionId");
    // 如果有分销信息
    if (distributionId) {
      // 先存储
      Cookies.set("distributionId", params.distributionId);
      // 绑定关系
      getGoodsDistribution(params.distributionId).then((res) => {
        // 绑定成功，则清除关系
        if (res.data.success) {
          Cookies.remove("distributionId");
        }
      });
    }

    goodsSkuDetail(params).then((res) => {
      loading.value = false;
      if (res.data.success) {
        const cateName = res.data.result.categoryName;
        const cateId = res.data.result.data.categoryPath.split(",");
        const cateArr = [{id: 0, name: "首页", path: "/"}];
        categoryBar.value = cateArr;
        goodsMsg.value = res.data.result;
        // 插入分类id和name和path
        const arr = [] as any;
        cateId.forEach((e: any, index: any) => {
          arr.push(e);
          cateArr.push({id: e, name: cateName[index], path: `/goodsList?categoryId=${arr.toString()}`});
        });
        if (!goodsMsg.value.data.intro) {goodsMsg.value.data.intro = '';}
        // 判断是否收藏
        if (storage.getUserInfo()) {
          isCollection("STORE", goodsMsg.value.data.storeId).then((res) => {
            if (res.data.success && res.data.result) {
              storeCollected.value = true;
            }
          });
        }
        if (!storeMsg.value) {
          // 获取店铺信息
          getDetailById(goodsMsg.value.data.storeId).then((res) => {
            if (res.data.success) {
              storeMsg.value = res.data.result;
            }
          });
        }
      } else {
        Message.error(res.data.message);
        loading.value = false;
      }
    }).catch((e) => {
      loading.value = false;
      if (e.code === 11001) {takeDownSale.value = true;}
    });
  };
  // 收藏店铺
  const collectStore = async () => {
    if (storeCollected.value) {
      let cancel = await cancelCollect("STORE", goodsMsg.value.data.storeId);
      if (cancel.data.success) {
        Message.success("已取消收藏");
        storeCollected.value = false;
      }
    } else {
      let collect = await collectGoods("STORE", goodsMsg.value.data.storeId);
      if (collect.data.code === 200) {
        storeCollected.value = true;
        Message.success("收藏店铺成功,可以前往个人中心我的收藏查看");
      }
    }
  };

  // 获取im信息
  const getIMDetailMethods = async () => {
    let res = await getIMDetail();
    if (res.data.success) {
      IMLink.value = res.data.result;
    }
  };
  // 跳转im客服
  const IMService = async () => {
    // 获取访问Token
    const accessToken = storage.getAccessToken();
    await getIMDetailMethods();
    if (!accessToken) {
      Message.error("请登录后再联系客服");
      return;
    }
    window.open(IMLink.value + "?token=" + accessToken + "&id=" + goodsMsg.value.data.storeId + "&goodsId=" + goodsMsg.value.data.goodsId + "&skuId=" + goodsMsg.value.data.id);
  };

  onMounted(() => {
    getGoodsDetail();
  })
</script>

<style scoped lang="less">
  .shop-item-path {
    height: 38px;
    background-color: @light_background_color;
    transition: 0.35s;
    line-height: 38px;
    color: #2c2c2c;
    .shop-nav-container {
      width: 1200px;
      margin: 0 auto;
      position: relative;
      .store-collect {
        position: absolute;
        right: 20px;
        top: 0;
        color: #999;
        display: flex;
        .link {
          text-decoration: none;
          color: @link_color;
        }
        span {
          display: flex;
          align-items: center;
          margin-left: 20px;
          &:hover {
            cursor: pointer;
            color: @theme_color;
          }
        }
      }
    }
  }


</style>
