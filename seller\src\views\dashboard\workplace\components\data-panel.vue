<template>
  <a-grid :cols="24" :row-gap="16" class="panel">
    <a-grid-item
      v-for="(item, index) in panelList.data"
      :key="index"
      class="panel-col"
      :span="{ xs: 12, sm: 12, md: 12, lg: 12, xl: 12, xxl: 6 }"
    >
      <a-space @click="$router.push({ name: item.name })">
        <a-avatar :size="54" class="col-avatar">
          <img alt="avatar" :src="item.icon" />
        </a-avatar>

        <a-statistic
          :title="item.label"
          :value="item.value"
          :precision="0"
          :value-from="0"
          animation
          show-group-separator
        >
          <template v-if="item.label == '订单总额'" #prefix> ¥ </template>
          <template #suffix>
            <span class="unit">{{ item.pecs }}</span>
          </template>
        </a-statistic>
      </a-space>
    </a-grid-item>

    <a-grid-item :span="24">
      <a-divider class="panel-border" />
    </a-grid-item>
  </a-grid>
</template>

<script lang="ts" setup>
  import { reactive, watch } from 'vue';
  import getAssetsImages from '@/utils/assetsImages';

  const props = defineProps({
    res: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });

  const panelList = reactive<any>({
    data: {},
  });

  const getPanelList = (data: any) => {
    panelList.data = {
      goodsNums: {
        icon: getAssetsImages('icon1.svg'),
        label: '商品数量',
        value: data.goodsNum || 0,
        pecs: '件',
        name: 'goods-list',
      },
      orderTotalPrice: {
        icon: getAssetsImages('icon2.svg'),
        label: '订单总额',
        value: data.orderPrice || 0,
        pecs: '元',
        name: 'order',
      },
      previewPerson: {
        icon: getAssetsImages('icon3.svg'),
        label: '访客数量',
        value: data.orderNum || 0,
        pecs: '人',
        name: 'preview',
      },
      orderNums: {
        icon: getAssetsImages('icon4.svg'),
        label: '订单数量',
        value: data.storeUV || 0,
        pecs: '单',
        name: 'order-list',
      },
    };
  };
  watch(
    () => props.res,
    (newValue, oldValue) => {
      getPanelList(newValue);
    }
  );
</script>

<style lang="less" scoped>
  .arco-grid.panel {
    margin-bottom: 0;
    padding: 16px 20px 0 20px;
  }
  .panel-col {
    padding-left: 43px;
    border-right: 1px solid rgb(var(--gray-2));
  }
  .col-avatar {
    margin-right: 12px;
    background-color: var(--color-fill-2);
  }
  .up-icon {
    color: rgb(var(--red-6));
  }
  .unit {
    margin-left: 8px;
    color: rgb(var(--gray-8));
    font-size: 12px;
  }
  :deep(.panel-border) {
    margin: 4px 0 0 0;
  }
</style>
