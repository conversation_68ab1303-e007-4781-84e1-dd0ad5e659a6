<template>
  <div py-16px border-b-1>
    <a-space>
      <div w-90px>{{props.text}}</div>
      <div>
        <t-color-picker format="HEX" :color-modes="['monochrome']" :show-primary-color-preview="false"
          v-model="props.res.data[bind]" />
      </div>
    </a-space>
  </div>
</template>

<script setup lang="ts">
import { DragRule } from '~/views/operation/decoration/app/models/types';

const props = defineProps<{
  res: DragRule,
  text:string
  bind:string
}>()


</script>

<style scoped>
</style>
