/**
 * 模板导航跳转函数
 * 用于处理底部导航等组件的跳转逻辑
 */

export function modelNavigateTo(item: any) {
  const data = item?.url
  if (!data) return

  switch (data.___value) {
    // 商品
    case 'goods':
      return {
        path: '/goodsDetail',
        query: {
          skuId: data.id,
          goodsId: data.goodsId,
        },
      }
    // 分类
    case 'category':
      return {
        path: '/goodsList',
        query: {
          category: data.id,
        },
      }
    // 店铺
    case 'shops':
      return {
        path: '/Merchant',
        query: {
          id: data.id,
        },
      }
    // 微页面
    case 'special':
      return {
        path: '/topic',
        query: {
          id: data.id,
        },
      }
    // 其他预设页面
    case 'other':
      switch (data.title) {
        case '首页':
          return { path: '/' }
        case '分类':
          return { path: '/goodsList' }
        case '购物车':
          return { path: '/cart' }
        case '个人中心':
          return { path: '/user/home' }
        case '收藏商品':
          return { path: '/user/home/<USER>/myFavorites' }
        case '我的订单':
          return { path: '/user/home/<USER>/myOrder' }
        case '领券中心':
          return { path: '/coupon' }
        case '签到':
          return { path: '/user/home/<USER>/signIn' }
        case '秒杀频道':
          return { path: '/seckill' }
        case '拼团频道':
          return { path: '/pintuan' }
        case '砍价':
          return { path: '/bargain' }
        case '积分商城':
          return { path: '/point' }
        case '充值中心':
          return { path: '/user/home/<USER>/moneyManagement' }
        case '优惠购':
          return { path: '/promotion' }
        default:
          return { path: '/' }
      }
    // 自定义链接
    case 'custom':
      if (data.url) {
        if (data.url.startsWith('http')) {
          // 外部链接
          window.open(data.url, '_blank')
          return null
        } else {
          // 内部链接
          return { path: data.url }
        }
      }
      break
    default:
      return { path: '/' }
  }
}

/**
 * 像素转rpx的工具函数
 */
export function px2rpx(px: number): string {
  // 假设设计稿宽度为750rpx对应375px
  const ratio = 750 / 375
  return Math.round(px * ratio) + 'rpx'
}
