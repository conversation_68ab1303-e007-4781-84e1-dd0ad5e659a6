

type Role = 'img' | 'link' | 'height' | 'background' | 'text' | 'round'

export interface DataRule {
  background?: string, // 背景颜色
  height?: number // 框体高度
  list?: Array<any | { img: string, url: string } | { [key: string]: any }> // 数组集合不做约束
  round?: number // 边框圆度
  [key: string]: any,
  /**
   * goodsType 设置
   * one 一行一个
   * two 一行两个
   * three 一行三个
   * big 大图模式
   * scroll 横向滚动
   * flag 标识设置图片变大
   */
  goodsType?: 'one' | 'two' | 'three' | 'big' | 'scroll' | 'flag'
  //  menu中设置 text 只有文字 default 图片+文字
  model?: 'text' | 'default',
  swiper?: 'default' | 'scroll'
}

export interface Model {
  label: string, // 展示label
  model: string, // 模块,
  append?: Array<any>, //可能有用的权限，这个根据不同组件自行判断
  bind?: string //绑定的字段可选填 
  max?:number 
  [key: string]: any 
}

// 块的约束
export interface DragRule {
  name: string // 块的名称
  _id?: number,
  type: string // 块的属性
  roles: Array<Role> // 块拥有哪些权限
  models?: Array<Model>
  data?: DataRule,
  border?: 'normal' | 'round' // 边框
  has?: Array<string> //拥有哪些组件
}
