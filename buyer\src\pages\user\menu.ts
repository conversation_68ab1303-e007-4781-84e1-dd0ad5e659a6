//  * icon  图标
//  * title 一级菜单
//  * display 是否显示菜单
//  * menus 菜单栏
//  * path路径  router 配置的

// 订单中心
const order = [{
  icon: '',
  title: '订单中心',
  menus: [{
    icon: '',
    title: '我的订单',
    path: '/user/home/<USER>/myOrder'
    },
    {
      icon: '',
      title: '收货地址',
      path: '/user/home/<USER>/myAddress'
    },
    {
      icon: '',
      title: '售后订单',
      path: '/user/home/<USER>/afterSale'
    }
  ],
  display: true
}];

// 会员中心
const member = [{
  icon: '',
  title: '会员中心',
  menus: [
    {
      icon: '',
      title: '用户信息',
      path: '/user/home/<USER>/userInformation'
    },
    {
      icon: '',
      title: '账户安全',
      path: '/user/home/<USER>/accountSafe'
    },
    {
      icon: '',
      title: '我的消息',
      path: '/user/home/<USER>/messageList'
    },
    {
      icon: '',
      title: '我的足迹',
      path: '/user/home/<USER>/myTracks'
    },

    {
      icon: '',
      title: '我的收藏',
      path: '/user/home/<USER>/myFavorites'
    },
    {
      icon: '',
      title: '分销推荐',
      path: '/user/home/<USER>/distribution'
    },
    {
      icon: '',
      title: '我的评论',
      path: '/user/home/<USER>/commentList'
    },
    {
      icon: '',
      title: '我的投诉',
      path: '/user/home/<USER>/complainList'
    },
    {
      icon: '',
      title: '我的积分',
      path: '/user/home/<USER>/myPoint'
    }
  ],
  display: true
}];

// 账户中心
const user = [{
  icon: '',
  title: '账户中心',
  menus: [{
    icon: '',
    title: '我的优惠券',
    path: '/user/home/<USER>/coupons'
  },
    {
      icon: '',
      title: '资金管理',
      path: '/user/home/<USER>/moneyManagement'
    },
    // {
    //   icon: '',
    //   title: '邀请新人',
    //   path: 'InviteNew'
    // }
  ],
  display: true
}];
let menuList: any = [];
menuList.push(order[0], member[0], user[0]);

export default menuList
