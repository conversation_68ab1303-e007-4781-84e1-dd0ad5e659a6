/**
 * 工具
 */

export interface StatusRules{
  label:string,
  value:any,
  color?:string
}

export const afterSaleStatusList = [
  // 售后状态列表
  {
    label: '申请中',
    value: 'APPLY',
    color: '',
  },
  {
    label: '通过',
    value: 'PASS',
    color: '',
  },
  {
    label: '拒绝',
    value: 'REFUSE',
    color: '',
  },
  {
    label: '买家退货，待卖家收货',
    value: 'BUYER_RETURN',
    color: '',
  },
  {
    label: '卖家确认收货',
    value: 'SELLER_CONFIRM',
    color: '',
  },
  {
    label: '卖家终止售后',
    value: 'SELLER_TERMINATION',
    color: '',
  },
  {
    label: '买家取消售后',
    value: 'BUYER_CANCEL',
    color: '',
  },
  {
    label: '完成售后',
    value: 'COMPLETE',
    color: '',
  },
  {
    label: '等待平台退款',
    value: 'WAIT_REFUND',
    color: '',
  },
];

// 订单状态列表`
export const orderStatusList = [
  {
    label: '全部',
    value: '',
    color: '',
  },
  {
    label: '未付款',
    value: 'UNPAID',
    color: 'magenta',
  },
  {
    label: '已付款',
    value: 'PAID',
    color: 'blue',
  },
  {
    label: '待发货',
    value: 'UNDELIVERED',
    color: 'orange',
  },
  {
    label: '已发货',
    value: 'DELIVERED',
    color: 'cyan',
  },
  {
    label: '已完成',
    value: 'COMPLETED',
    color: 'green',
  },
  {
    label: '待通知第三方',
    value: 'TAKE',
    color: 'Orange Red',
  },

  {
    label: '待充值',
    value: 'CREATE_ZZ_SUCCESS',
    color: 'green',
  },  
  {
    label: '下单失败',
    value: 'CREATE_ZZ_FAIL',
    color: 'red',
  },  
  {
    label: '充值成功',
    value: 'RECHARGE_SUCCESS',
    color: 'green',
  },  
  {
    label: '充值失败',
    value: 'RECHARGE_FAIL',
    color: 'red',
  }, 
  {
    label: '待使用权益',
    value: 'TO_BE_ALLOCATED',
    color: 'Orange Red',
  },
  {
    label: '已取消',
    value: 'CANCELLED',
    color: 'red',
  },
  {
    label: '待自提',
    value: 'STAY_PICKED_UP',
    color: 'red',
  }
];

// 评论类型
export const gradeList = [
  {
    label: '好评',
    value: 'GOOD',
    color: 'green',
  },
  {
    label: '中评',
    value: 'MODERATE',
    color: 'orange',
  },
  {
    label: '差评',
    value: 'WORSE',
    color: 'red',
  },
];

// 回复状态
export const replyStatus = [
  {
    value: true,
    label: '已回复',
    color: 'green',
  },
  {
    value: false,
    label: '未回复',
    color: 'blue',
  },
];

// 订单来源
export const orderClientType = [
  {
    value: 'H5',
    label: '移动端',
    color: 'purple',
  },
  {
    value: 'PC',
    label: 'PC端',
    color: 'blue',
  },
  {
    value: 'WECHAT_MP',
    label: '小程序端',
    color: 'green',
  },
  {
    value: 'APP',
    label: '移动应用端',
    color: 'magenta',
  },
];

// 订单状态
export const orderClientStatus = [
  {
    value: 'UNDELIVERED',
    label: '待发货',
    color: 'blue',
  },
  {
    value: 'UNPAID',
    label: '未付款',
    color: 'orange',
  },
  {
    value: 'PAID',
    label: '已付款',
    color: 'green',
  },
  {
    value: 'DELIVERED',
    label: '已发货',
    color: 'arcoblue',
  },
  {
    value: 'CANCELLED',
    label: '已取消',
    color: 'red',
  },
  {
    value: 'COMPLETED',
    label: '已完成',
    color: 'green',
  },
  {
    value: 'TAKE',
    label: '待通知第三方',
    color: 'yellow',
  },
  {
    value: 'TO_BE_ALLOCATED',
    label: '待使用权益',
    color: 'yellow',
  },
  {
    label: '待充值',
    value: 'CREATE_ZZ_SUCCESS',
    color: 'green',
  },  
  {
    label: '下单失败',
    value: 'CREATE_ZZ_FAIL',
    color: 'red',
  },  
  {
    label: '充值成功',
    value: 'RECHARGE_SUCCESS',
    color: 'green',
  },  
  {
    label: '充值失败',
    value: 'RECHARGE_FAIL',
    color: 'red',
  }, 
];

// 评论状态
export const commentStatus = [
  {
    value: 'CLOSE',
    label: '隐藏',
    color: 'red',
  },
  {
    value: 'OPEN',
    label: '展示',
    color: 'green',
  },
];

// 服务状态
export const serviceStatus = [
  {
    value: 'APPLY',
    label: '申请中',
    color: 'blue',
  },
  {
    value: 'PASS',
    label: '通过售后',
    color: 'cyan',
  },
  {
    value: 'REFUSE',
    label: '拒绝售后',
    color: 'red',
  },
  {
    value: 'BUYER_RETURN',
    label: '买家退货，待卖家收货',
    color: 'orange',
  },
  {
    value: 'SELLER_RE_DELIVERY',
    label: '商家换货/补发',
    color: 'magenta',
  },
  {
    value: 'SELLER_CONFIRM',
    label: '卖家确认收货',
    color: 'pinkpurple',
  },
  {
    value: 'SELLER_TERMINATION',
    label: '卖家终止售后',
    color: 'orangered',
  },
  {
    value: 'BUYER_CONFIRM',
    label: '买家确认收货',
    color: 'lime',
  },
  {
    value: 'BUYER_CANCEL',
    label: '买家取消售后',
    color: 'purple',
  },
  {
    value: 'COMPLETE',
    label: '完成售后',
    color: 'green',
  },
  {
    value: 'WAIT_REFUND',
    label: '待平台退款',
    color: 'blue',
  },
];

// 商品状态
export const marketEnable = [
  {
    value: 'DOWN',
    label: '下架',
    color: 'red',
  },
  {
    value: 'UPPER',
    label: '上架',
    color: 'green',
  },
];

// 审核状态
export const authFlag = [
  {
    value: 'PASS',
    label: '通过',
    color: 'green',
  },
  {
    value: 'TOBEAUDITED',
    label: '待审核',
    color: 'blue',
  },
  {
    value: 'REFUSE',
    label: '审核拒绝',
    color: 'red',
  },
];

// 商品类型
export const goodsType = [
  {
    value: 'PHYSICAL_GOODS',
    label: '实物商品',
    color: 'blue',
  },
  {
    value: 'VIRTUAL_GOODS',
    label: '虚拟商品',
    color: 'purple',
  },
  // {
  //   value: 'E_COUPON',
  //   label: '电子卡券',
  //   color: 'cyan',
  // },
];

// 销售状态
export const salesModel = [
  {
    value: 'RETAIL',
    label: '零售',
    color: 'orange',
  },
  {
    value: 'WHOLESALE',
    label: '批发',
    color: 'magenta',
  },
  
];

// 投诉状态
export const complaintStatus = [
  {
    value: 'NEW',
    label: '新投诉',
    color: 'purple',
  },
  {
    value: 'CANCEL',
    label: '已撤销',
    color: 'cyan',
  },
  {
    value: 'WAIT_APPEAL',
    label: '待申诉',
    color: 'red',
  },
  {
    value: 'COMMUNICATION',
    label: '对话中',
    color: 'orange',
  },
  {
    value: 'WAIT_ARBITRATION',
    label: '等待仲裁',
    color: 'blue',
  },
  {
    value: 'COMPLETE',
    label: '已完成',
    color: 'green',
  },
];
export const billStatus = [
  {
    value: 'OUT',
    label: '待对账',
    color: 'blue',
  },
  {
    value: 'CHECK',
    label: '待结算',
    color: 'purple',
  },
  {
    value: 'COMPLETE',
    label: '已完成',
    color: 'green',
  },
];

export const billStatusSelect = [
  {
    value: 'OUT',
    label: '待对账',
    color: 'blue',
  },
  {
    value: 'CHECK',
    label: '待结算',
    color: 'purple',
  },
  {
    value: 'COMPLETE',
    label: '已完成',
    color: 'green',
  },
];

export const withdrawSelect = [
  {
    value: 'CREATE_SUCCESS',
    label: '受理成功',
    color: 'orange',
  },
  {
    value: 'SUCCESS',
    label: '提现成功',
    color: 'green',
  },
  {
    value: 'FAIL',
    label: '提现失败',
    color: 'red',
  },
  {
    value: 'REFUND',
    label: '提现退票',
    color: 'blue',
  },
  {
    value: 'CLOSE',
    label: '关单',
    color: 'red',
  },
  {
    value: 'INIT',
    label: '业务单创建',
    color: 'purple',
  },
];
export const receiptStatus = [
  {
    value: 0,
    label: '未开票',
    color: 'red',
  },
  {
    value: 1,
    label: '已开票',
    color: 'green',
  },
];

export const promotionsStatusRender = [
  {
    value: 'NEW',
    label: '未开始',
    color: 'orange',
  },
  {
    value: 'START',
    label: '已开始',
    color: 'green',
  },
  {
    value: 'CLOSE',
    label: '已关闭',
    color: 'red',
  },
  {
    value: 'END',
    label: '已结束',
    color: 'purple',
  }
];

export const promotionsScopeTypeRender = [
  {
    value: 'ALL',
    label: '全品类',
    color: 'green',
  },
  {
    value: 'PORTION_GOODS_CATEGORY',
    label: '商品分类',
    color: 'orange',
  },
  {
    value: 'PORTION_SHOP_CATEGORY',
    label: '店铺分类',
    color: 'pink',
  },
  {
    value: 'PORTION_GOODS',
    label: '指定商品',
    color: 'magenta',
  }
];

export const promotionStatusSelect = [
  {
    value: 'NEW',
    label: '未开始',
    color: 'orange',
  },
  {
    value: 'START',
    label: '已开始/上架',
    color: 'green',
  },
  {
    value: 'END',
    label: '已结束/下架',
    color: 'blue',
  },
  {
    value: 'CLOSE',
    label: '紧急关闭/作废',
    color: 'red',
  },
];

export const promotionStatus = [
  {
    value: 'NEW',
    label: '未开始',
    color: 'orange',
  },
  {
    value: 'START',
    label: '已开始',
    color: 'green',
  },
  {
    value: 'END',
    label: '已结束',
    color: 'blue',
  },
  {
    value: 'CLOSE',
    label: '已关闭',
    color: 'red',
  },
];

export const liveStatus = [
  {
    value: 'NEW',
    label: '未开始',
    color: 'orange',
  },
  {
    value: 'START',
    label: '直播中',
    color: 'green',
  },
  {
    value: 'END',
    label: '已结束',
    color: 'red',
  },
];

export const couponType = [
  {
    value: 'DISCOUNT',
    label: '折扣',
    color: 'orange',
  },
  {
    value: 'PRICE',
    label: '减免现金',
    color: 'green',
  }
];

export const logisticsStatus = [
  {
    value: '1533736232324067329',
    label: '开启',
    color: 'green',
  },
  {
    value: null,
    label: '关闭',
    color: 'red',
  },
];

export const distributionOrderStatus = [
  {
    value: 'COMPLETE_CASH',
    label: '提现完成',
    color: 'green',
  },
  {
    value: 'NO_COMPLETED',
    label: '未完成',
    color: 'orange',
  },
  {
    value: 'WAIT_BILL',
    label: '待结算',
    color: 'purple',
  },
  {
    value: 'WAIT_CASH',
    label: '待提现',
    color: 'blue',
  },
  {
    value: 'CANCEL',
    label: '取消',
    color: 'red',
  },
  {
    value: 'REFUND',
    label: '取消',
    color: 'red',
  },
  {
    value: null,
    label: '暂无状态',
    color: 'magenta',
  },
];

export const fullMinusStatus = [
  {
    value: true,
    label: '满减',
    color: 'green',
  },
  {
    value: false,
    label: '满折',
    color: 'blue',
  },
];

// 分类禁用的状态 deleteFlag
export const deleteFlagStatus = [
  {
    value: false,
    label: '启用',
    color: 'green',
  },
  {
    value: true,
    label: '禁用',
    color: 'red',
  },
];

// 系统类型
export const systemType = [
  {
    value: 'IOS',
    label: '苹果',
    color: 'blue',
  },
  {
    value: 'ANDROID',
    label: '安卓',
    color: 'purple',
  },
];

// 业务类型
export const serviceType = [
  {
    value: 'WALLET_WITHDRAWAL',
    label: '余额提现',
    color: 'purple',
  },
  {
    value: 'WALLET_PAY',
    label: '余额支付',
    color: 'orange',
  },
  {
    value: 'WALLET_REFUND',
    label: '余额退款',
    color: 'green',
  },
  {
    value: 'WALLET_RECHARGE',
    label: '余额充值',
    color: 'blue',
  },
  {
    value: '佣金提成',
    label: 'WALLET_COMMISSION',
    color: 'red',
  },
];

// 充值方式
export const rechargeWay = [
  {
    value: 'ALIPAY',
    label: '支付宝',
    color: 'purple',
  },
  {
    value: 'WECHAT',
    label: '微信',
    color: 'blue',
  },
  {
    value: 'WALLET',
    label: '余额',
    color: 'orange',
  },
  {
    value: 'BANK_TRANSFER',
    label: '线下转账',
    color: 'green',
  },
  {
    value: 'WECHAT_PARTNER',
    label: '微信服务商支付',
    color: 'purple',
  },
  {
    value: null,
    label: '暂未付款',
    color: 'red',
  },
];
export const rechargeWayFilter = [
  {
    value: 'ALIPAY',
    label: '支付宝',
    color: 'purple',
  },
  {
    value: 'WECHAT',
    label: '微信',
    color: 'blue',
  },
  {
    value: 'WALLET',
    label: '余额',
    color: 'orange',
  },
  {
    value: 'BANK_TRANSFER',
    label: '线下转账',
    color: 'green',
  },
  {
    value: 'WECHAT_PARTNER',
    label: '微信服务商支付',
    color: 'purple',
  },
];

// 支付状态
export const payStatus = [
  {
    value: 'UNPAID',
    label: '未付款',
    color: 'gold',
  },
  {
    value: 'PAID',
    label: '已付款',
    color: 'green',
  },
  {
    value: 'CANCEL',
    label: '已取消',
    color: 'red',
  },
  {
    value: 'UNPAID',
    label: '未付款',
    color: 'red',
  },
];

// 支付状态
export const paymentStatus = [
  {
    value: 'PAID',
    label: '已付款',
    color: 'green',
  },
  {
    value: 'UNPAID',
    label: '未付款',
    color: 'red',
  },
];
// 提现状态
export const applyStatus = [
  {
    value: 'APPLY',
    label: '申请中',
    color: 'purple',
  },
  {
    value: 'VIA_AUDITING',
    label: '审核通过',
    color: 'green',
  },
  // {
  //   value: 'SUCCESS',
  //   label: '提现成功',
  //   color: 'blue',
  // },
  {
    value: 'FAIL_AUDITING',
    label: '审核拒绝',
    color: 'red',
  },
];
// 提现状态
export const memberApplyStatus = [
  {
    value: 'APPLY',
    label: '申请中',
    color: 'purple',
  },
  {
    value: 'VIA_AUDITING',
    label: '审核通过',
    color: 'green',
  },
  {
    value: 'FAIL_AUDITING',
    label: '审核拒绝',
    color: 'red',
  },
  {
    value: 'SUCCESS',
    label: '提现成功',
    color: 'blue',
  },
  {
    value: 'ERROR',
    label: '提现失败',
    color: 'blue',
  },
];
// 提现状态
export const afterSaleType = [
  {
    value: 'RETURN_MONEY',
    label: '退款',
    color: 'purple',
  },
  {
    value: 'RETURN_GOODS',
    label: '退货',
    color: 'green',
  },
];
// 售后类型
export const afterSalesType = [
  {
    value: 'RETURN_MONEY',
    label: '退款',
    color: 'purple',
  },
  {
    value: 'RETURN_GOODS',
    label: '退货',
    color: 'green',
  },
  {
    value: 'EXCHANGE_GOODS',
    label: '换货',
    color: 'blue',
  },
];

// 退款状态
export const refundStatus = [
  {
    value: "REFUND",
    label: '已退款',
    color: 'blue',
  },
  {
    value: "UNREFUND",
    label: '待退款',
    color: 'purple',
  },
  {
    value: "ACCOUNT",
    label: '已对账',
    color: 'green',
  },
  {
    value: "ACCOUNT_ERROR",
    label: '对账失败',
    color: 'red',
  },
];
export const refundStatusSearch = [
  {
    value: 'true',
    label: '已退款',
    color: 'green',
  },
  {
    value: 'false',
    label: '未退款',
    color: 'blue',
  },
];
// 店铺状态
export const storeDisable = [
  {
    value: 'OPEN',
    label: '开启中',
    color: 'purple',
  },
  {
    value: 'APPLY',
    label: '申请中',
    color: 'blue',
  },
  {
    value: 'REFUSED',
    label: '审核拒绝',
    color: 'orange',
  },
  {
    value: 'APPLYING',
    label: '审核中',
    color: 'green',
  },
  {
    value: 'CLOSED',
    label: '已关闭',
    color: 'red',
  },
];
// 是否自营
export const selfOperated = [
  {
    value: true,
    label: '自营',
    color: 'green',
  },
  {
    value: false,
    label: '非自营',
    color: 'blue',
  },
];
// 活动类型
export const couponActivityType = [
  {
    value: 'REGISTERED',
    label: '新人赠券',
    color: 'green',
  },
  {
    value: 'SPECIFY',
    label: '精确发券',
    color: 'blue',
  },
  {
    value: 'AUTO_COUPON',
    label: '自动赠券',
    color: 'blue',
  },
  {
    value: 'INVITE_NEW',
    label: '邀新赠券',
    color: 'orange',
  },
];
// 活动范围
export const activityScope = [
  {
    value: 'DESIGNATED',
    label: '指定会员',
    color: 'green',
  },
  {
    value: 'ALL',
    label: '全部会员',
    color: 'blue',
  },
];
// 分销状态
export const distributionStatus = [
  {
    value: 'PASS',
    label: '通过',
    color: 'green',
  },
  {
    value: 'APPLY',
    label: '待审核',
    color: 'blue',
  },
  {
    value: 'REFUSE',
    label: '拒绝',
    color: 'orange',
  },
  {
    value: 'RETREAT',
    label: '清退',
    color: 'red',
  },
  {
    value: 'APPEAL',
    label: '上诉',
    color: 'gold'
  }
];

// 分销提现状态
export const distributionCashStatus = [
  {
    value: 'APPLY',
    label: '待处理',
    color: 'purple',
  },
  {
    value: 'VIA_AUDITING',
    label: '审核通过',
    color: 'green',
  },
  {
    value: 'FAIL_AUDITING',
    label: '审核拒绝',
    color: 'red',
  },
];

// 意见反馈类型
export const feedbackType = [
  {
    value: 'FUNCTION',
    label: '功能建议',
    color: 'purple',
  },
  {
    value: 'OPTIMIZE',
    label: '优化反馈',
    color: 'blue',
  },
  {
    value: 'OTHER',
    label: '其他意见',
    color: 'orange',
  }
];
// 会员列表详情，订单状态
export const orderStatus = [
  {
    value: 'UNPAID',
    label: '未付款',
    color: 'magenta',
  },
  {
    value: 'PAID',
    label: '已付款',
    color: 'blue',
  },
  {
    value: 'UNDELIVERED',
    label: '待发货',
    color: 'orange',
  },
  {
    value: 'DELIVERED',
    label: '已发货',
    color: 'cyan',
  },
  {
    value: 'COMPLETED',
    label: '已完成',
    color: 'green',
  },
  {
    value: 'TAKE',
    label: '待核验',
    color: 'Orange Red',
  },
  {
    value: 'CANCELLED',
    label: '已取消',
    color: 'red',
  },
];
// 会员列表详情，订单类型
export const orderType = [
  {
    value: 'NORMAL',
    label: '普通订单',
    color: 'purple',
  },
  {
    value: 'VIRTUAL',
    label: '虚拟订单',
    color: 'green',
  },
  {
    value: 'GIFT',
    label: '赠品订单',
    color: 'blue',
  },
  {
    value: 'PINTUAN',
    label: '拼团订单',
    color: 'orange',
  },
  {
    value: 'POINTS',
    label: '积分订单',
    color: 'purple',
  },
  {
    value: 'KANJIA',
    label: '砍价订单',
    color: 'blue',
  },
];
// 会员列表详情，订单来源
export const orderSource = [
  {
    value: 'H5',
    label: '移动端',
    color: 'green',
  },
  {
    value: 'PC',
    label: 'PC端',
    color: 'purple',
  },
  {
    value: 'WECHAT_MP',
    label: '小程序',
    color: 'red',
  },
  {
    value: 'APP',
    label: '移动应用端',
    color: 'orange',
  }
];
// 是否默认
export const whetherDefault = [
  {
    value: true,
    label: '是',
    color: 'green',
  },
  {
    value: false,
    label: '否',
    color: 'red',
  },
];
// 投诉状态
export const groupComplainStatus = [
  {
    value: 'NEW',
    label: '未申请',
    color: 'red',
  },
  {
    value: 'NO_APPLY',
    label: '未申请',
    color: 'red',
  },
  {
    value: 'APPLYING',
    label: '申请中',
    color: 'purple',
  },
  {
    value: 'COMPLETE',
    label: '已完成',
    color: 'green',
  },
  {
    value: 'EXPIRED',
    label: '已失效',
    color: 'blue',
  },
  {
    value: 'CANCEL',
    label: '取消投诉',
    color: 'orange',
  },
];
// 获取方式
export const claimStatus = [
  {
    value: 'FREE',
    label: '免费获取',
    color: 'red',
  },
  {
    value: 'ACTIVITY',
    label: '活动获取',
    color: 'green',
  },
  {
    value: 'INSIDE',
    label: '内购',
    color: 'pink',
  }
];
// 会员优惠券状态
export const memberCouponStatus = [
  {
    value: 'NEW',
    label: '已领取',
    color: 'purple',
  },
  {
    value: 'USED',
    label: '已使用',
    color: 'green',
  },
  {
    value: 'EXPIRE',
    label: '已过期',
    color: 'pink',
  },
  {
    value: 'CLOSED',
    label: '已作废',
    color: 'red',
  },
];

// 发送对象
export const messageClientStatus = [
  {
    value: 'member',
    label: '会员',
    color: 'purple',
  },
  {
    value: 'store',
    label: '商家',
    color: 'blue',
  }
]

// 发送范围
export const messageRangeStatus = [
  {
    value: 'ALL',
    label: '全站',
    color: 'green',
  },
  {
    value: 'APPOINT',
    label: '指定商家',
    color: 'blue',
  },
  {
    value: 'MEMBER',
    label: '指定会员',
    color: 'purple',
  }
]

// 补差结果状态
export const subsidiesSearch = [
  {
    value: 'SUCCESS',
    label: '补差成功',
    color: 'green',
  },
  {
    value: 'FAIL',
    label: '补差失败',
    color: 'red',
  },
  {
    value: 'REFUND',
    label: '全额回退补差',
    color: 'pink',
  },
  {
    value: '',
    label: '',
    color: '',
  },
]

// 分帐单状态
export const outOrderSelect = [
  {
    value: 'PROCESSING',
    label: '处理中',
    color: 'pink',
  },
  {
    value: 'FINISHED',
    label: '分账完成',
    color: 'green',
  },
]

// 分账订单状态
export const outOrderResult = [
  {
    value: 'PENDING',
    label: '待分账',
    color: 'pink',
  },
  {
    value: 'SUCCESS',
    label: '分账成功',
    color: 'green',
  },
  {
    value: "CLOSED",
    label: '已关闭',
    color: 'red',
  },
  {
    value: "FAIL",
    label: '分账失败',
    color: 'red',
  },
]
// 售后状态
export const groupAfterSaleStatus = [
  {
    value: 'NEW',
    label: '新订单',
    color: 'red'
  },
  {
    value: 'NOT_APPLIED',
    label: '未申请',
    color: 'red'
  },
  // {
  //   value: 'ALREADY_APPLIED',
  //   label: '已申请',
  //   color: 'purple'
  // },
  {
    value: 'EXPIRED',
    label: '已失效',
    color: 'blue'
  }
]
// 微信进件申请状态
export const applicationStatus = [
  {
    value: 'EDITTING',
    label: '编辑中',
    color: 'geekblue',
  },
  {
    value: 'CHECKING',
    label: '资料校验中',
    color: 'purple',
  },
  {
    value: 'AUTHORIZING',
    label: '待商家授权',
    color: 'magenta',
  },
  {
    value: 'ACCOUNT_NEED_VERIFY',
    label: '待账户验证',
    color: 'cyan',
  },
  {
    value: 'AUDITING',
    label: '审核中',
    color: 'orange',
  },
  {
    value: 'REJECTED',
    label: '已驳回',
    color: 'red',
  },
  {
    value: 'NEED_SIGN',
    label: '待签约',
    color: 'green',
  },
  {
    value: 'FINISH',
    label: '已完成',
    color: 'lime',
  },
  {
    value: 'FROZEN',
    label: '已冻结',
    color: 'blue',
  },
  {
    value: 'CANCELED',
    label: '已作废',
    color: 'yellow',
  },
]

// 会员有效性状态
export const vipEffectiveStatus = [
  {
    value: '0',
    label: '未生效',
    color: 'red',
  },
  {
    value: '1',
    label: '已生效',
    color: 'green',
  },
];

// 会员有效性状态渲染函数
export function vipEffectiveRender(status: string) {
  const statusItem = vipEffectiveStatus.find(item => item.value === status);
  return statusItem ? statusItem.label : '未知';
}

// 付款方式
export const paymentMethod = [
  {
    value: 'WECHAT',
    label: '微信',
    color: 'green'
  },
  {
    value: 'ALIPAY',
    label: '支付宝',
    color: 'blue'
  },
  {
    value: 'WALLET',
    label: '余额支付',
    color: 'yellow'
  },
  {
    value: 'WECHAT_PARTNER',
    label: '微信服务商支付',
    color: 'geekblue'
  },
  {
    value: 'BANK_TRANSFER',
    label: '线下转账',
    color: 'orange'
  }
];

// 配送方式
export const deliveryMethod = [
  {
    value: 'SELF_PICK_UP',
    label: '自提',
    color: 'red',
  },
  {
    value: 'LOCAL_TOWN_DELIVERY',
    label: '同城配送',
    color: 'yellow',
  },
  {
    value: 'LOGISTICS',
    label: '物流',
    color: 'green',
  }
];
