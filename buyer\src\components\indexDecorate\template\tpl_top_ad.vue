<script setup lang="ts">
import { useRouter } from 'vue-router'
import { navigateTo } from './navigate.ts'

const props = defineProps<{
  res: any
}>()
const router = useRouter()
function handleClickTopAd() {
  const path = navigateTo(props.res.data.list[0])

  window.open(router.resolve(path).href, '_blank')
}
</script>

<template>
  <div v-if="props.res" w-full :style="{ backgroundColor: res.data.background, height: `${res.data.height || 80}px` }"
    @click="handleClickTopAd()">
    <img :style="{ height: `${res.data.height}px` }" mx-auto block h-full w-1184px w-1200px :src="res.data.list[0].img">
  </div>
</template>

<style scoped></style>
