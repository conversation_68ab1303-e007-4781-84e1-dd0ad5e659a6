<template>
  <a-card class="general-card" title="分销申请" :bordered="false">
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getDistributionListData"
      :api-params="apiParams"
      @refuse="handleRefuse"
      @pass="handlePass"
      :bordered="true"
    />
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { getDistributionListData, auditDistribution } from '@/api/operation';
  import { ref } from 'vue';
  import useCurrentInstance from '@/hooks/useCurrentInstance';
  import { Message } from '@arco-design/web-vue';

  const tablePageRef = ref<any>();
  //  获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;
  const columnsSearch: Array<SearchRule> = [
    {
      label: '会员名称',
      model: 'memberName',
      disabled: false,
      input: true,
    },
  ];

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '会员名称',
      dataIndex: 'memberName',
    },
    {
      title: '会员姓名',
      dataIndex: 'name',
    },
    {
      title: '提交时间',
      dataIndex: 'createTime',
    },
  ];

  const sortMethods: MethodsRule = {
    title: '操作',
    width: 200,
    methods: [
      {
        title: '通过',
        callback: 'pass',
        type:'text',
        status:'success'
      },
      {
        title: '拒绝',
        callback: 'refuse',
        type:'text',
        status:'danger'
      },
    ],
  };
  // 通过审核
  function handlePass(data: any) {
    const params = {
      memberIds: data.record.id,
      status: 'PASS',
    };
    modal.confirm({
      title: '确认审核',
      content: '您确认要通过审核?',
      alignCenter: false,
      onOk: async () => {
        const res = await auditDistribution(data.record.id, params);
        if (res.data.success) {
          Message.success('操作成功');
          tablePageRef.value.init();
        }
      },
    });
  }
  // 拒绝审核
  function handleRefuse(data: any) {
    const params = {
      memberIds: data.record.id,
      status: 'REFUSE',
    };
    modal.confirm({
      title: '确认审核',
      content: '您确认要拒绝？',
      alignCenter: false,
      onOk: async () => {
        const res = await auditDistribution(data.record.id, params);
        if (res.data.success) {
          Message.success('操作成功');
          tablePageRef.value.init();
        }
      },
    });
  }
  const apiParams = ref({
    distributionStatus: 'APPLY',
  });
</script>

<style scoped></style>
