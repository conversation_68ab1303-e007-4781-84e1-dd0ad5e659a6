<template>
  <a-card class="general-card" title="分销订单" :bordered="false">
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :api="getDistributionOrder"
      :api-params="apiParams"
      :bordered="true"
    >
      <template #thumbnail="{ data }">
        <a-image width="100" height="100" :src="data.thumbnail" />
      </template>
      <template #status="{ data }">
        <a-badge v-if="data.distributionOrderStatus == 'WAIT_BILL'" color = 'blue'>待结算</a-badge>
        <a-badge v-else-if="data.distributionOrderStatus == 'NO_COMPLETED'" color= 'red'>未完成</a-badge>
        <a-badge v-else-if="data.distributionOrderStatus == 'WAIT_CASH'" color='orange'>待提现</a-badge>
        <a-badge v-else-if="data.distributionOrderStatus == 'COMPLETE_CASH'" color='green'>提现完成</a-badge>
        <a-badge v-else-if="data.distributionOrderStatus == 'CANCEL'" color= 'red'>订单取消</a-badge>
        <a-badge v-else-if="data.distributionOrderStatus == 'REFUND'"  color='magenta'>退款</a-badge>
        <a-badge v-else status="normal" text="暂无状态"></a-badge>
      </template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
  import tablePage from '@/components/table-pages/index.vue';
  import searchTable from '@/components/search-column/index.vue';
  import { ColumnsDataRule, SearchRule } from '@/types/global';
  import { getDistributionOrder } from '@/api/operation';
  import { ref } from 'vue';

  const apiParams = ref({});
  const tablePageRef = ref<any>();

  const columnsSearch: Array<SearchRule> = [
    {
      label: '订单编号',
      model: 'orderSn',
      disabled: false,
      input: true,
    },
    {
      label: '分销商',
      model: 'distributionName',
      disabled: false,
      input: true,
    },
    {
      label: '店铺名称',
      model: 'storeId',
      disabled: false,
      input: true,
    },
    {
      label: '订单时间',
      disabled: false,
      datePicker: {
        type: 'range',
      },
    },
  ];
  const columnsTable: ColumnsDataRule[] = [
    {
      title: '订单编号',
      dataIndex: 'orderSn',
      //   slot: true,
      //   slotTemplate: 'thumbnail',
    },
    {
      title: '商品信息',
      dataIndex: 'goodsName',
      width: 300,
      slot: true,
      ellipsis: false,
      slotData: {
        goods: {
          goodsImage: 'image',
          goodsName: 'goodsName',
        },
      },
    },
    {
      title: '分销商',
      dataIndex: 'distributionName',
    },
    {
      title: '店铺名称',
      dataIndex: 'storeName',
    },
    {
      title: '状态',
      dataIndex: 'distributionOrderStatus',
      slot: true,
      slotTemplate:'status'
    },
    {
      title: '佣金金额',
      dataIndex: 'rebate',
      currency: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
    },
  ];
</script>
<style lang="less" >
.arco-badge-status-text{
  font-size: 14px !important;
}
</style>