<template>
  <a-card class="general-card" title="信任登录" :bordered="false">
    <a-tabs
      :default-active-key="authLoginFormData.authLoginType"
      @change="clickAuthLogin"
    >
      <a-tab-pane key="WECHAT_CONNECT" title="微信设置">
        <a-col style="margin-bottom: 10px">
          <a-space
            v-for="(item, index) in authLoginFormData.wechatFormDate"
            :key="index"
          >
            <div bordered class="auth-item">
              <div class="icon-item">
                <img
                  v-if="item.clientType === 'APP'"
                  class="icon"
                  :src="getAssetsImages('app.svg')"
                  alt=""
                />
                <img
                  v-if="item.clientType === 'H5'"
                  class="icon"
                  :src="getAssetsImages('h5.svg')"
                  alt=""
                />
                <img
                  v-if="item.clientType === 'WECHAT_MP'"
                  class="icon"
                  :src="getAssetsImages('wechat_mp.svg')"
                  alt=""
                />
                <img
                  v-if="item.clientType === 'PC'"
                  class="icon"
                  :src="getAssetsImages('pc.svg')"
                  alt=""
                />
              </div>
              <div class="pay-title">{{
                authLoginFormData.way[item.clientType]
              }}</div>
              <a-divider
                orientation="left"
                :style="{ marginTop: '30px', marginBottom: '30px' }"
                >登录设置</a-divider
              >
              <a-form
                ref="authLoginFormRef"
                :model="item"
                :style="{ width: '100%' }"
                layout="horizontal"
                auto-label-width
              >
                <a-form-item label="appId">
                  <a-input v-model="item.appId" type="text" placeholder="" />
                </a-form-item>
                <a-form-item label="appSecret">
                  <a-input
                    v-model="item.appSecret"
                    type="text"
                    placeholder=""
                  />
                </a-form-item>
                <a-form-item>
                  <a-button @click="setupSetting" type="primary">保存设置</a-button>
                </a-form-item>
              </a-form>
            </div>
          </a-space>
        </a-col>
      </a-tab-pane>
      <a-tab-pane key="QQ_CONNECT" title="QQ设置">
        <a-col style="margin-bottom: 10px">
          <a-space
            v-for="(item, index) in authLoginFormData.qqFormDate"
            :key="index"
          >
            <div bordered class="auth-item">
              <div class="icon-item">
                <img
                  v-if="item.clientType === 'APP'"
                  class="icon"
                  :src="getAssetsImages('app.svg')"
                  alt=""
                />
                <img
                  v-if="item.clientType === 'H5'"
                  class="icon"
                  :src="getAssetsImages('h5.svg')"
                  alt=""
                />
                <img
                  v-if="item.clientType === 'WECHAT_MP'"
                  class="icon"
                  :src="getAssetsImages('wechat_mp.svg')"
                  alt=""
                />
                <img
                  v-if="item.clientType === 'PC'"
                  class="icon"
                  :src="getAssetsImages('pc.svg')"
                  alt=""
                />
              </div>
              <div class="pay-title">{{
                authLoginFormData.way[item.clientType]
              }}</div>
              <a-divider
                orientation="left"
                :style="{ marginTop: '30px', marginBottom: '30px' }"
                >登录设置</a-divider
              >
              <a-form
                ref="authLoginFormRef"
                :model="item"
                :style="{ width: '100%' }"
                layout="horizontal"
                auto-label-width
              >
                <a-form-item label="appId">
                  <a-input v-model="item.appId" type="text" placeholder="" />
                </a-form-item>
                <a-form-item label="appSecret">
                  <a-input
                    v-model="item.appSecret"
                    type="text"
                    placeholder=""
                  />
                </a-form-item>
                <a-form-item>
                  <a-button @click="setupSetting" type="primary">保存设置</a-button>
                </a-form-item>
              </a-form>
            </div>
          </a-space>
        </a-col>
      </a-tab-pane>
    </a-tabs>
  </a-card>
</template>

<script setup lang="ts">
  import { ref, watch, reactive, onMounted } from 'vue';
  import useCurrentInstance from '@/hooks/useCurrentInstance';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { VARCHAR255, REQUIRED, VARCHAR20, MOBILE } from '@/utils/validator';
  import { Message } from '@arco-design/web-vue';
  import { unixToDate } from '@/utils/filters';
  import { getSetting, setSetting } from '@/api/index';
  import getAssetsImages from '@/utils/assetsImages';

  interface formInterface {
    way: any;
    authLoginType: string;
    authLoginForm: any;
    wechatFormDate: Array<any>;
    qqFormDate: Array<any>;
  }
  // 数据集
  const authLoginFormData: formInterface = reactive({
    way: {
      // 类型
      APP: '移动应用端',
      H5: '移动端',
      WECHAT_MP: '小程序端',
      PC: 'PC端',
    },
    authLoginType: 'WECHAT_CONNECT',
    authLoginForm: {
      appId: '',
      appSecret: '',
    },
    wechatFormDate: [],
    qqFormDate: [],
  });
  // 进入页面请求第一个配置
  const getSettingData = async () => {
    const res = await getSetting(authLoginFormData.authLoginType);
    if (authLoginFormData.authLoginType == 'WECHAT_CONNECT') {
      // 微信设置
      // authLoginFormData.wechatFormDate = [
      //   {appId: "", appSecret: "", clientType: "PC"},
      //   {appId: "wx1b6b759c3a4d5170", appSecret: "1fc85bb7f4d0d18ffc9ce62f68e3a79f", clientType: "H5"},
      //   {appId: "wxb3bd67c6e91042b2", appSecret: "7836086054d86d36f27399f832cbdaee", clientType: "WECHAT_MP"}
      // ];
      if (res.data.success) {
        authLoginFormData.wechatFormDate =
          res.data.result.wechatConnectSettingItems;
      }
    } else if (authLoginFormData.authLoginType == 'QQ_CONNECT') {
      // QQ设置
      // authLoginFormData.qqFormDate = [
      //   {appId: "", appSecret: "", clientType: "PC"},
      //   {appId: "", appSecret: "", clientType: "H5"}
      // ];
      if (res.data.success) {
        authLoginFormData.qqFormDate = res.data.result.qqConnectSettingItemList;
      }
    }
  };
  // 保存设置
  const setupSetting = async () => {
    let res: any;
    if (authLoginFormData.authLoginType == 'WECHAT_CONNECT') {
      // 微信设置
      res = await setSetting(authLoginFormData.authLoginType, {
        wechatConnectSettingItems: authLoginFormData.wechatFormDate,
      });
    } else if (authLoginFormData.authLoginType == 'QQ_CONNECT') {
      // qq设置
      res = await setSetting(authLoginFormData.authLoginType, {
        qqConnectSettingItemList: authLoginFormData.qqFormDate,
      });
    }
    if (res.data.success) {
      Message.success('保存成功！');
    } else {
      Message.error('保存失败！');
    }
    getSettingData();
  };
  // 信任登录tab栏切换
  const clickAuthLogin = (name: any) => {
    authLoginFormData.authLoginType = name;
    getSettingData();
  };
  // 初始化
  onMounted(() => {
    getSettingData();
  });
</script>

<style scoped lang="less">
  .auth-item {
    width: 350px;
    border: 1px solid #e8eaec;
    padding: 0 16px;
    box-sizing: border-box;
    margin: 10px 20px 0 0;
    border-radius: 4px;
    &:hover {
      box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.1);
      transition-delay: 0.3s;
      transition: all 0.2s;
    }
  }
  .icon-item {
    width: 100%;
    padding: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }
  .ivu-form-item {
    display: flex;

    align-items: center;
  }
  .ivu-row {
    width: 100%;
  }
  .icon {
    width: 100px;
    height: 100px;
  }
  .pay-title {
    text-align: center;
    margin: 10px 0 20px;
  }
</style>
