<template>
  <div>
    <Card _Title="我的积分" :_Size="16"></Card>
    <div class="point-content" v-if="pointObj">
      <span>剩余积分：<span>{{ pointObj.points || 0 }}</span></span>
      <span>累计获得：<span>{{ pointObj.totalPoint || 0 }}</span></span>
    </div>
    <h3>积分日志</h3>
    <a-table :columns="pointColumns" :data="pointData.records" :pagination="false">
      <template #content="{ record }">
        <a-tooltip :content="record.content" position="bottom">
          <div class="ellipsis ellipsis-1 hover-pointer">{{ record.content }}</div>
        </a-tooltip>
      </template>
      <template #pointDetail="{ record }">
        <div :style="{color:record.pointType === 'INCREASE' ? 'green' : 'red'}">
          <span v-if="record.pointType === 'INCREASE'">+</span>{{ record.variablePoint }}
        </div>
      </template>
    </a-table>
    <div class="paginationBox">
      <a-pagination :total="pointData.total" :page-size="apiParams.pageSize" :current="apiParams.pageNumber" show-jumper @change="(number) => {apiParams.pageNumber = number;}"></a-pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, watch } from 'vue';
  import {memberPoint, memberPointHistory} from '@/api/member';

  const loading = ref(false);
  // 积分明细
  const pointObj = ref();
  // 请求参数
  const apiParams = ref({
    pageNumber: 1,
    pageSize: 10,
  });
  // 消息数据
  const pointData = ref<any>({});
  const pointColumns: any = [
    {title: '日志内容', width: 400, dataIndex: 'content',slotName: 'content', align: 'center' },
    {title: '时间', width: 300, dataIndex: 'createTime', align: 'center' },
    {title: '积分明细', width: 200, dataIndex: 'point', slotName: 'pointDetail', align: 'center' }
  ];

  // 获取积分详情
  const getPoint = () => {
    memberPoint().then(res => {
      if (res.data.success) pointObj.value = res.data.result;
    })
  };
  // 获取消息列表
  const getList = () => {
    let params = JSON.parse(JSON.stringify(apiParams.value));
    loading.value = true;
    memberPointHistory(params).then(res => {
      loading.value = false;
      if (res.data.success) {
        pointData.value = res.data.result;
      }
    });
  };

  onMounted(() => {
    getPoint();
    getList();
  });

  watch(() => [apiParams],
    (val) => {
      getList();
    }, { deep: true }
  );
</script>

<style scoped lang="less">
  .point-content {
    text-align: center;
    margin-bottom: 30px;
    > span {
      color: #999;
      margin-right: 100px;
      span {
        color: @theme_color;
        font-size: 24px;
      }
    }
  }
</style>
