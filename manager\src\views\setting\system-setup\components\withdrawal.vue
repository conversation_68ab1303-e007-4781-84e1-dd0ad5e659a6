<template>
  <div style="background-color: #fff">
    <a-form ref="formRef" style="padding: 30px" :model="form" layout="horizontal" auto-label-width>
      <a-divider orientation="left">提现设置</a-divider>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item field="isOpen" label="提现审核是否开启">
            <a-radio-group type="button" v-model="form.apply">
              <a-radio :value="true">开启</a-radio>
              <a-radio :value="false">关闭</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item field="minPrice" label="最低提现金额" :validate-trigger="['change']">
            <a-input-number v-model="form.minPrice" allow-clear :min="0"><template #prefix>￥</template></a-input-number>
          </a-form-item>
          <a-form-item field="type" label="提现方式" :validate-trigger="['change']">
            <a-radio-group type="button" v-model="form.type">
              <a-radio value="WECHAT">微信</a-radio>
              <a-radio value="ALI">支付宝</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item field="wechatAppId" label="微信提现应用ID" :validate-trigger="['change']">
            <a-input v-model="form.wechatAppId" allow-clear/>
          </a-form-item>
          <a-form-item>
            <a-button type="primary"  @click="handleSubmit">保存</a-button>
          </a-form-item>
        </a-col>
      </a-row>

    </a-form>
  </div>
</template>

<script setup lang="ts">
import {getSetting, setSetting} from '@/api/operation';
import {onMounted, ref} from 'vue';
import useCurrentInstance from '@/hooks/useCurrentInstance';
import {Message} from '@arco-design/web-vue';
import {FormInstance} from '@arco-design/web-vue/es/form';

const formRef = ref<FormInstance>();
// 获取modal
const modal = useCurrentInstance().globalProperties?.$modal;

interface formInterface {
  type: string;
  [key: string]: any;
}

// 数据集
const form = ref<formInterface>({
  apply: true,
  minPrice: "",
  type: "",
  wechatAppId: "",
});

async function init() {
  const res = await getSetting('WITHDRAWAL_SETTING');
  form.value = res.data.result;
}

const handleSubmit = async () => {
  // console.log(form.value, 'form.value');
  const result = await setSetting('WITHDRAWAL_SETTING', form.value);
  if (result.data.success) {
    Message.success('设置成功!');
    init();
  }
};

onMounted(() => {
  init();
});
</script>