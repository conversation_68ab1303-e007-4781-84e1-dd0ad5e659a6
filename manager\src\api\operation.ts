import { ParamsRule } from '@/types/global';
import request, { Method } from '@/utils/axios';
import qs from 'query-string';

// eslint-disable-next-line import/prefer-default-export

// 系统设置
export function getSetting(key: string) {
  return request({
    url: `/setting/setting/get/${key}`,
    method: Method.GET,
    needToken: true,
  });
}
// 更新系统配置
export function setSetting(key: string, params: any) {
  return request({
    url: `/setting/setting/put/${key}`,
    method: Method.PUT,
    needToken: true,
    data: params,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

// 查询分销商
export function getDistributionListData(params: ParamsRule) {
  return request({
    url: `/distribution/distribution/getByPage`,
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 审核分销商
export function auditDistribution(id: string | number, params: any) {
  return request({
    url: `/distribution/distribution/audit/${id}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}

// 清退分销商
export function retreatDistribution(id: string | number) {
  return request({
    url: `/distribution/distribution/retreat/${id}`,
    method: Method.PUT,
    needToken: true,
  });
}
// 恢复分销商
export function resumeDistribution(id: string | number) {
  return request({
    url: `/distribution/distribution/resume/${id}`,
    method: Method.PUT,
    needToken: true,
  });
}

// 获取分销商品列表
export function getDistributionGoods(params: ParamsRule) {
  return request({
    url: `/distribution/goods/getByPage`,
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 删除分销商品
export function delDistributionGoods(ids: string | number) {
  return request({
    url: `/distribution/goods/delByIds/${ids}`,
    method: Method.DELETE,
    needToken: true,
  });
}

// 获取分销订单列表
export function getDistributionOrder(params: ParamsRule) {
  return request({
    url: `/distribution/order/getByPage`,
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 获取分销佣金信息
export function getDistributionCash(params: ParamsRule) {
  return request({
    url: `/distribution/cash/getByPage`,
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 审核分销佣金申请
export function auditDistributionCash(id: string | number, params: any) {
  return request({
    url: `/distribution/cash/audit/${id}`,
    method: Method.POST,
    needToken: true,
    data: qs.stringify(params),
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
  })
}

// 获取分销佣金信息
export function getDistributionMember(id:any) {
  return request({
    url: `/distribution/distribution/memberList/${id}`,
    method: Method.GET,
    needToken: true,
  });
}
// 获取分销佣金信息
export function getDistributionGroup( id:string |number ,params: ParamsRule) {
  return request({
    url: `/distribution/distribution/groupList/${id}`,
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 分页查询自定义分词
export function getCustomWordsPage(params: ParamsRule) {
  return request({
    url: `/other/customWords/page`,
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 添加自定义分词
export function insertCustomWords(params: any) {
  return request({
    url: `/other/customWords`,
    method: Method.POST,
    needToken: true,
    params,
  });
}
// 修改自定义分词
export function updateCustomWords(params: any) {
  return request({
    url: `/other/customWords`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
// 删除自定义分词
export function delCustom(id: string | number) {
  return request({
    url: `/other/customWords/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}

// 获取分类列表数据
export function getArticleCategory(params: any) {
  return request({
    url: `/other/articleCategory/all-children`,
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 文章分类添加
export function saveArticleCategory(params: ParamsRule) {
  return request({
    url: `/other/articleCategory`,
    method: Method.POST,
    needToken: true,
    params,
  })
}

// 删除文章分类
export function delArticleCategory(id: string | number) {
  return request({
    url: `/other/articleCategory/${id}`,
    method: Method.DELETE,
    needToken: true
  })
}

// 修改文章分类
export function updateArticleCategory(id: string | number, params: ParamsRule) {
  return request({
    url: `/other/articleCategory/update/${id}`,
    method: Method.PUT,
    needToken: true,
    params
  })
}

// 获取文章列表数据
export function getArticle(params: ParamsRule) {
  return request({
    url: `/other/article/getByPage`,
    method: Method.GET,
    needToken: true,
    params
  })
}

// 文章是否展示修改
export function updateArticleStatus(id: string | number, params: any) {
  return request({
    url: `/other/article/update/status/${id}`,
    method: Method.PUT,
    needToken: true,
    params
  })
}

// 查看文章
export function seeArticle(id: string | number) {
  return request({
    url: `/other/article/${id}`,
    method: Method.GET,
    needToken: true
  })
}

// 删除文章数据
export function delArticle(ids: [string | number]) {
  return request({
    url: `/other/article/delByIds/${ids}`,
    method: Method.DELETE,
    needToken: true
  })
}

// 文章添加
export function saveArticle(params: ParamsRule) {
  return request({
    url: `/other/article`,
    method: Method.POST,
    needToken: true,
    data: params,
    headers: { "Content-type": "application/json" }
  })
}

// 文章修改
export function updateArticle(params: ParamsRule) {
  return request({
    url: `/other/article/update/${params.id}`,
    method: Method.PUT,
    needToken: true,
    data: params,
    headers: { "Content-type": "application/json" }
  })
}

// 获取隐私协议数据
export function getPrivacy(type: string | number) {
  return request({
    url: `/other/article/type/${type}`,
    method: Method.GET,
    needToken: true
  })
}

// 修改隐私协议数据
export function updatePrivacy(id: string | number, type: string | number, params: ParamsRule) {
  return request({
    url: `/other/article/updateArticle/${type}?id=${id}`,
    method: Method.PUT,
    needToken: true,
    data: params,
    headers: { "Content-type": "application/json" }
  })
}


// 获取会员意见反馈
export function getMemberFeedback(params: ParamsRule) {
  return request({
    url: `/other/feedback`,
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 管理员获取会员意见反馈
export function getMemberFeedbackDetail(id: string | boolean) {
  return request({
    url: `/other/feedback/${id}`,
    method: Method.GET,
    needToken: true,
  });
}


// 查询短信发送记录
export function getSmsPage(params: ParamsRule) {
  return request({
    url: `/sms/sms`,
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 获取短信模板
export function getSmsTemplatePage(params: ParamsRule) {
  return request({
    url: `/sms/template/querySmsTemplatePage`,
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 分页查询短信签名
export function getSmsSignPage(params: ParamsRule) {
  return request({
    url: `/sms/sign/querySmsSignPage`,
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 查询站内信模板信息
export function getNoticeMessageData(params: ParamsRule) {
  return request({
    url: `/setting/noticeMessage`,
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 修改站内信状态
export function updateMessageStatus(id: string | number ,status: string) {
  return request({
    url: `/setting/noticeMessage/${id}/${status}`,
    method: Method.PUT,
    needToken: true
  })
}

// 修改站内信模板
export function editNoticeMessage(id: string | number ,params: any) {
  return request({
    url: `/setting/noticeMessage/${id}`,
    method: Method.PUT,
    needToken: true,
    params
  })
}

// 管理员获取发送列表
export function getMessagePage(params: ParamsRule) {
  return request({
    url: `/other/message`,
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 管理员发送消息
export function sendMessage(params: ParamsRule) {
  return request({
    url: '/other/message',
    method: Method.POST,
    needToken: true,
    data: qs.stringify(params),
  });
}

// 管理员获取发送详情列表（商家）
export function getShopMessage(params: ParamsRule) {
  return request({
    url: `/other/storeMessage`,
    method: Method.GET,
    needToken: true,
    params
  })
}

// 管理员获取发送详情列表（会员）
export function getMemberMessage(params: ParamsRule) {
  return request({
    url: `other/memberMessage`,
    method: Method.GET,
    needToken: true,
    params
  })
}

// 删除站内信
export function deleteMessage(id: string | number) {
  return request({
    url: `other/message/${id}`,
    method: Method.DELETE,
    needToken: true
  })
}

// 获取通知类站内信
export function getNoticeMessageDetail(id: string | number) {
  return request({
    url: `/setting/noticeMessage/${id}`,
    method: Method.GET,
    needToken: true
  })
}

// 初始化商品索引
export function createIndex() {
  return request({
    url: `/other/elasticsearch`,
    method: Method.GET,
    needToken: true
  })
}

// 初始化商品索引
export function getProgress() {
  return request({
    url: `/other/elasticsearch/progress`,
    method: Method.GET,
    needToken: true,
  })
}
