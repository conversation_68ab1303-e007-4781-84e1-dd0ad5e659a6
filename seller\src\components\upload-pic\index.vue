<template>
  <div>
    <div>
      <a-upload ref="uploadRef" :action="uploadFile" :headers="{ accessToken: accessToken }" :show-file-list="false"
                :onSuccess="success" :onError="handleError" :data="dataList" @before-upload="beforeUpload">
        <template #upload-button>
          <a-space>
            <a-button type="primary">上传文件</a-button>
          </a-space>
        </template>
      </a-upload>
    </div>
  </div>
</template>

<script setup lang="ts">
  import uploadFile from '@/api/common';

  import {
    handleSuccess,
    handleError,
    beforeUpload,
    accessToken,
  } from '@/components/upload-pic/upload';

  const props = defineProps(['tablePageRef', 'dataList']);
  const emit = defineEmits<{
    (e: 'onSuccess', val: any): void;
  }>();

  function success(res: any) {
    handleSuccess(res);
    props.tablePageRef ? props.tablePageRef.init() : '';
    emit('onSuccess', res);
  }
</script>

<style lang="less" scoped></style>
