// eslint-disable-next-line import/no-cycle
import request, { Method, commonUrl } from '@/utils/axios';
import { ParamsRule } from '@/types/global';

/**
 * 获取当前会员分销信息
 */
export function distribution () {
  return request({
    url: `/distribution`,
    method: Method.GET,
    needToken: true
  });
}

/**
 * 申请成为分销员
 * @param idNumber 身份证号
 * @param name  名字
 */
export function applyDistribution (params?: any) {
  return request({
    url: `/distribution`,
    method: Method.POST,
    needToken: true,
    params
  });
}

/**
 * 获取分销员订单列表
 */
export function getDistOrderList (params: ParamsRule) {
  return request({
    url: `/distribution/order`,
    method: Method.GET,
    needToken: true,
    params
  });
}

/**
 * 获取分销员下级用户列表
 */
export function getDistMemberList (params: ParamsRule) {
  return request({
    url: `/distribution/memberList`,
    method: Method.GET,
    needToken: true,
    params
  });
}

/**
 * 获取分销商商品列表
 */
export function getDistGoodsList (params: ParamsRule) {
  return request({
    url: `/distribution/goods`,
    method: Method.GET,
    needToken: true,
    params
  });
}

/**
 * 分销员提现历史
 */
export function distCashHistory (params: ParamsRule) {
  return request({
    url: `/distribution/cash`,
    method: Method.GET,
    needToken: true,
    params
  });
}

/**
 * 分销员-团队列表
 */
export function getDistGroupList (params: ParamsRule) {
  return request({
    url: `/distribution/groupList`,
    method: Method.GET,
    needToken: true,
    params
  });
}

/**
 * 分销商提现
 */
export function distCash (params: any) {
  return request({
    url: `/distribution/cash`,
    method: Method.POST,
    needToken: true,
    params
  });
}

/**
 * 绑定分销
 * @param distributionId 商品分销ID
 */
export function getGoodsDistribution (distributionId: any) {
  return request({
    url: `/distribution/bindingDistribution/${distributionId}`,
    method: Method.GET,
    needToken: true
  });
}
