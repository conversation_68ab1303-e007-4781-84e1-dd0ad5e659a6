<template>
  <div>
    <div class="pointsTitle" style="justify-content: flex-start; text-align: left;">
      <div style="width: 120px;">
        <div class="points-top-title">
          剩余积分
        </div>
        <div class="points-top-text">
          {{ memberInfo.points ? memberInfo.points : 0 }}
        </div>
      </div>
    </div>
    <div class="point-data" style="margin-top: -5px">
      <tablePage :api-params="apiParams" ref="tablePageRef" :columns="columnsPointTable" :api="getHistoryPointData">
        <template #point="{ data }">
          <span v-if="data.pointType == 'INCREASE'" style="color: green;"> +{{ data.variablePoint }}</span>
          <span v-else style="color: red;">-{{ data.variablePoint }}</span>
        </template>
      </tablePage>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { ColumnsDataRule, SearchRule } from '@/types/global';
import { getHistoryPointData, getMemberInfoData } from '@/api/member';
import tablePage from '@/components/table-pages/index.vue';
import { memberDetailRules } from '../index';
import { stringify } from 'query-string';

const route = useRoute()

// 会员信息
const memberInfo = ref<memberDetailRules>({
  username: '',
  mobile: '',
  lastLoginDate: '',
  disabled: false,
  nickName: '',
  sex: 0,
  birthday: null,
  region: null,
  createTime: ''
})
// 初始化
onMounted(() => {
  getMemberInfoData(route.query.id).then((res: any) => {
    memberInfo.value = res.data.result
  })
})
// TA的积分
const columnsPointTable: any = [
  {
    title: '操作内容',
    dataIndex: 'content',
  },
  {
    title: '操作时间',
    dataIndex: 'createTime',
  },

  {
    title: '之前积分',
    dataIndex: 'beforePoint',
  },
  {
    title: '变动积分',
    dataIndex: 'variablePoint',
    slot: true,
    slotTemplate: 'point',
  },
  {
    title: '当前积分',
    dataIndex: 'points',
  },
];
// 传递的参数
const apiParams = {
  memberId: route.query.id,
};
</script>

<style lang="less" scoped>
@import "../memberDetail.less";
</style>