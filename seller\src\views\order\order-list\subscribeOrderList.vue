<template>
  <a-card class="general-card" title="订阅订单" :bordered="false">
    <!-- 搜索 -->
    <searchTable
      :columns="columnsSearch"
      @reset="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
      @search="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
    ></searchTable>

    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getSubscribeOrderList"
      :api-params="apiParams"
      :bordered="true"
      @orderDetail="handleDetails"
    />
  </a-card>
  <a-modal v-model:visible="showOrderDetail" :align-center="false" :width="800">
    <template #title> 订单详情 </template>
    <a-row :gutter="24" class="grid-row">
      <a-col :span="12">
        <div
          >商品编号：<span>{{ orderDetailInfo.skuSn }}</span></div
        >
      </a-col>
      <a-col :span="12">
        <div
          >商品名称：<span>{{ orderDetailInfo.skuName }}</span></div
        > </a-col
      ><a-col :span="12">
        <div
          >订阅期数：<span>{{ orderDetailInfo.periodsNumber }}</span></div
        >
      </a-col>
    </a-row>
    <a-list :max-height="500">
      <a-list-item v-for="item in orderDetailInfo.itemList">
        <a-list-item-meta
          :title="'第' + item.number + '期'"
          :description="'开始时间：' + item.createTime"
        >
        </a-list-item-meta>
        <template #actions>
          <a-button @click="toOrder(item)">查看订单</a-button>
        </template>
      </a-list-item>
    </a-list>
  </a-modal>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { getSubscribeOrderList, getSubscribeOrderDetail } from '@/api/order';
  import { useRouter } from 'vue-router';
  import { ref } from 'vue';

  const tablePageRef = ref('');
  const router = useRouter();
  const showOrderDetail = ref(false);

  const orderDetailInfo = ref({});

  // 查询列表
  const columnsSearch: Array<SearchRule> = [
    {
      label: '手机号',
      model: 'mobile',
      disabled: false,
      input: true,
    },
    {
      label: '订阅协议号',
      model: 'subscribeSn',
      disabled: false,
      input: true,
    },
    {
      label: '订阅商品编号',
      model: 'skuSn',
      disabled: false,
      input: true,
    },
    {
      label: '订阅商品名称',
      model: 'skuName',
      disabled: false,
      input: true,
    },
    {
      label: '下单时间',
      disabled: false,
      datePicker: {
        type: 'range',
      },
    },
    {
      label: '订单状态',
      model: 'status',
      disabled: false,
      select: {
        options: [
          {
            label: '订阅中',
            value: 0,
          },
          {
            label: '已退订',
            value: 1,
          },
        ],
      },
    },
  ];

  // 表格搜索列表
  const columnsTable: ColumnsDataRule[] = [
    {
      title: '手机号',
      dataIndex: 'mobile',
    },
    {
      title: '订阅时间',
      width: 100,
      dataIndex: 'createTime',
    },
    {
      title: '订阅协议号',
      dataIndex: 'subscribeSn',
    },
    {
      title: '产品编号',
      dataIndex: 'skuSn',
    },
    {
      title: '产品名称',
      dataIndex: 'skuName',
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      slot: true,
      slotData: {
        badge: [
          {
            value: 1,
            label: '已退订',
            color: 'red',
          },
          {
            value: 0,
            label: '订阅中',
            color: 'green',
          },
        ],
      },
    },
    {
      title: '订阅期数',
      dataIndex: 'periodsNumber',
    },
    {
      title: '退订时间',
      width: 100,
      dataIndex: 'endTime',
    },
  ];
  // 操作列表
  const sortMethods: MethodsRule = {
    title: '操作',
    width: 100,
    methods: [
      {
        title: '查看',
        callback: 'orderDetail',
        type: 'text',
        status: 'success',
      },
    ],
  };

  // 查看
  const handleDetails = (val: any) => {
    orderDetailInfo.value = val.record;
    console.log(orderDetailInfo.value.subscribeSn)
    getSubscribeOrderDetail(orderDetailInfo.value.subscribeSn).then((res) => {
      orderDetailInfo.value.itemList = res.data.result;
      showOrderDetail.value = true;
    });
  };

  const toOrder = (item: any) => {
    router.push({
      name: 'order-detail',
      query: {
        id: item.orderSn,
      },
    });
  }
  // 传递的参数
  const apiParams = ref({});
</script>

<style scoped>
  .popover-content {
    display: flex;
  }
</style>
