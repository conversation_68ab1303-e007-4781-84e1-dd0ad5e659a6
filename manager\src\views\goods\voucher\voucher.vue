<template>
  <a-form v-if="initComplete" :model="form" auto-label-width>
    <a-card class="voucher-card" title="板块配置" :bordered="false">
      <a-form-item label="是否展示板块">
        <a-radio-group v-model="form.show_plate">
          <a-radio :value="true">是</a-radio>
          <a-radio :value="false">否</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="菜单角标">
        <a-input v-model="form.plate_badge" :max-length="4" placeholder="最多4个字符" style="width: 220px"/>
      </a-form-item>
    </a-card>
    <a-card class="voucher-card" title="爆款产品" :bordered="false">
      <a-form-item label="是否展示爆款产品">
        <a-radio-group v-model="form.show_explosions">
          <a-radio :value="true">是</a-radio>
          <a-radio :value="false">否</a-radio>
        </a-radio-group>
      </a-form-item>
      <div v-if="form.show_explosions" class="explosions">
        <div v-for="(item, index) in form.explosions" :key="index" class="item-box">
          <b style="margin-left: 24px;">{{ `产品${index + 1}` }}</b>
          <a-form-item label="产品图片">
            <a-upload
              list-type="picture-card"
              :action="uploadFile"
              :headers="{ accessToken: accessToken }"
              image-preview
              :limit="1"
              :onSuccess="(file) => onExplosionsUploadSuccess(file, index)"
              :default-file-list="item.img ? [{ uid: `exp_${index}`,name: item.img, url: item.img}] : undefined"
              @before-upload="beforeUpload"
            ></a-upload>
          </a-form-item>
          <a-form-item label="选择商品">
            <a-button @click="onPickerExpGoods(item, index)" class="goods-pick-btn">
              {{ item.goods ? item.goods.goodsName : '选择商品' }}
            </a-button>
          </a-form-item>
          <a-form-item label="跳转链接">
            <a-button @click="onPickerLink('exp', index)">{{ linkLabel(item.link) || '请选择跳转链接' }}</a-button>
          </a-form-item>
          <div class="btn-view">
            <a-button
              size="mini"
              status="success"
              :disabled="index !== form.explosions.length - 1"
              @click="onAddExp"
            >新增
            </a-button>
            <a-button
              size="mini"
              status="warning"
              :disabled="form.explosions.length <= 1"
              @click="onDeleteExp(index)"
            >删除
            </a-button>
          </div>
        </div>
      </div>
    </a-card>
    <a-card class="voucher-card" title="LOGO板块" :bordered="false">
      <a-form-item label="是否展示LOGO板块">
        <a-radio-group v-model="form.show_logo">
          <a-radio :value="true">是</a-radio>
          <a-radio :value="false">否</a-radio>
        </a-radio-group>
      </a-form-item>
      <div v-if="form.show_logo" class="logo-list">
        <a-tabs
          v-model:active-key="curLogoTabKey"
          type="card-gutter"
          :editable="form.show_logo"
          show-add-button
          auto-switch
          @add="handleAddLogoItem"
          @delete="handleDeleteLogoItem"
        >
          <a-tab-pane
            v-for="(item, index) in form.logoList"
            :key="index"
            :title="item.label"
            :closable="index !== 0"
          >
            <a-form-item label="板块名称">
              <a-input v-model="item.label" placeholder="最多5个字符"/>
            </a-form-item>
            <a-form-item label="LOGO图标">
              <a-upload
                list-type="picture-card"
                :action="uploadFile"
                :headers="{ accessToken: accessToken }"
                image-preview
                :limit="1"
                :onSuccess="(file) => onLogoUploadSuccess(file, index)"
                :default-file-list="item.img ? [{ uid: `logo_${index}`, name: item.img, url: item.img}] : undefined"
                @before-upload="beforeUpload"
              ></a-upload>
            </a-form-item>
            <a-form-item label="产品板块">
              <a-tabs
                v-model:active-key="curLogoPlateTabKey"
                type="card-gutter"
                editable
                show-add-button
                auto-switch
                class="product-plate-tabs"
                @add="handleAddLogoPlateItem(index)"
                @delete="(key) => handleDeleteLogoPlateItem(index, key)"
              >
                <a-tab-pane
                  v-for="(p, pIndex) in  item.plates"
                  :key="pIndex"
                  :title="p.title"
                  :closable="pIndex !== 0"
                  class="plate-tabs-pane"
                >
                  <a-form-item label="产品板块标题">
                    <a-input v-model="p.title" placeholder="请填写产品板块标题"/>
                  </a-form-item>
                  <div v-for="(pd, pdIndex) in p.products" :key="pdIndex" class="item-box">
                    <b style="margin-left: 24px">{{ `产品${pdIndex + 1}` }}</b>
                    <a-form-item label="产品标题">
                      <a-input v-model="pd.title" placeholder="请填写产品标题"/>
                    </a-form-item>
                    <a-form-item label="产品副标题">
                      <a-input v-model="pd.sub_title" placeholder="非必填，不填则不展示"/>
                    </a-form-item>
                    <a-form-item label="产品售价">
                      <a-input v-model="pd.price" placeholder="请填写产品售价"/>
                    </a-form-item>
                    <a-form-item label="选择商品">
                      <a-button @click="onPickerLogoPDGoods(pd, `${index}.${pIndex}.${pdIndex}`)" class="goods-pick-btn">
                        {{ pd.goods ? pd.goods.goodsName : '选择商品' }}
                      </a-button>
                    </a-form-item>
                    <div class="btn-view">
                      <a-button
                        size="mini"
                        status="success"
                        :disabled="pdIndex !== form.logoList[index].plates[pIndex].products.length - 1"
                        @click="onAddLogoPlateProduct(index, pIndex)"
                      >新增
                      </a-button>
                      <a-button
                        size="mini"
                        status="warning"
                        :disabled="form.logoList[index].plates[pIndex].products.length <= 1"
                        @click="onDeleteLogoPlateProduct(index, pIndex, pdIndex)"
                      >删除
                      </a-button>
                    </div>
                  </div>
                </a-tab-pane>
              </a-tabs>
            </a-form-item>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-card>
    <a-card v-if="!form.show_logo" class="voucher-card" title="产品板块" :bordered="false">
      <div class="logo-list">
        <a-tabs
          v-model:active-key="curLogoPlateTabKey"
          type="card-gutter"
          editable
          show-add-button
          auto-switch
          class="product-plate-tabs"
          @add="handleAddLogoPlateItem(0)"
          @delete="(key) => handleDeleteLogoPlateItem(0, key)"
        >
          <a-tab-pane
            v-for="(p, pIndex) in form.logoList[0].plates"
            :key="pIndex"
            :title="p.title"
            :closable="pIndex !== 0"
            class="plate-tabs-pane"
          >
            <a-form-item label="产品板块标题">
              <a-input v-model="p.title" placeholder="请填写产品板块标题"/>
            </a-form-item>
            <div v-for="(pd, pdIndex) in p.products" :key="pdIndex" class="item-box">
              <b style="margin-left: 24px">{{ `产品${pdIndex + 1}` }}</b>
              <a-form-item label="产品标题">
                <a-input v-model="pd.title" placeholder="请填写产品标题"/>
              </a-form-item>
              <a-form-item label="产品副标题">
                <a-input v-model="pd.sub_title" placeholder="非必填，不填则不展示"/>
              </a-form-item>
              <a-form-item label="产品售价">
                <a-input v-model="pd.price" placeholder="请填写产品售价"/>
              </a-form-item>
              <a-form-item label="选择商品">
                <a-button @click="onPickerLogoPDGoods(pd, `${0}.${pIndex}.${pdIndex}`)" class="goods-pick-btn">
                  {{ pd.goods ? pd.goods.goodsName : '选择商品' }}
                </a-button>
              </a-form-item>
              <div class="btn-view">
                <a-button
                  size="mini"
                  status="success"
                  :disabled="pdIndex !== form.logoList[0].plates[pIndex].products.length - 1"
                  @click="onAddLogoPlateProduct(0, pIndex)"
                >新增
                </a-button>
                <a-button
                  size="mini"
                  status="warning"
                  :disabled="form.logoList[0].plates[pIndex].products.length <= 1"
                  @click="onDeleteLogoPlateProduct(0, pIndex, pdIndex)"
                >删除
                </a-button>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-card>
    <a-card class="voucher-card" title="广告位" :bordered="false">
      <div class="adv-list">
        <div v-for="(item, index) in form.advList" :key="index" class="item-box">
          <a-form-item :label="`广告${index + 1}`">
            <a-upload
              list-type="picture-card"
              :action="uploadFile"
              :headers="{ accessToken: accessToken }"
              image-preview
              :limit="1"
              :onSuccess="(file) => onAdvUploadSuccess(file, index)"
              :default-file-list="item.img ? [{ uid: `adv_${index}`, name: item.img, url: item.img}] : undefined"
              @before-upload="beforeUpload"
            ></a-upload>
          </a-form-item>
          <a-form-item label="跳转链接">
            <a-button @click="onPickerLink('adv', index)">{{ linkLabel(item.link) || '请选择跳转链接' }}</a-button>
          </a-form-item>
        </div>
      </div>
    </a-card>
    <a-card class="voucher-card" :bordered="false">
      <a-button
        status="success"
        :loading="saveLoading"
        @click="onSaveForm"
        class="save-btn"
      >保&nbsp;存
      </a-button>
    </a-card>
  </a-form>
  <SkuPicker
    ref="skuPickerRef"
    @change="handleSkuPickerConfirm"
    :goodsOrSku="true"
    :defaultGoodsSelectedList="defaultSkus"
  ></SkuPicker>
  <choose ref="decide" :res="props.res" @callback="receive" />
</template>

<script setup lang="ts">
import uploadFile from '@/api/index'
import { ref } from 'vue'
import store from '@/utils/storage'
import { Message, FileItem } from '@arco-design/web-vue'
import { clone, cloneDeep } from 'lodash-es'
import SkuPicker from '@/components/goods-sku-selector/index.vue'
import choose from '@/views/operation/decoration/components/choose.vue'
import { DragRule } from '@/views/operation/decoration/app/models/types';
import * as API_Other from '@/api/other'
import { useRoute } from 'vue-router'

interface FormData {
  show_plate: boolean
  plate_badge: string
  show_explosions: boolean
  explosions: ExplosionItem[]
  show_logo: boolean
  logoList: LogoItem[]
  advList: AdvItem[]
}
interface ExplosionItem {
  img: string,
  goods: any,
  link: any
}

interface LogoPlateProductItem {
  title: string
  sub_title: string
  price: string
  goods: any
}

interface LogoPlateItem {
  title: string
  products: LogoPlateProductItem[]
}

interface LogoItem {
  label: string
  img: string
  plates: LogoPlateItem[]
}

interface AdvItem {
  img: string
  link: any
}

const route = useRoute()
const initComplete = ref(false)
let pageType = computed(() => {
  switch (route.name) {
    case 'voucherFlow':
      return 'FLOW'
    case 'voucherVip':
      return 'VIP'
    default:
      return 'PHONE'
  }
})
const isRes = ref(false);
const props = defineProps<{
  res?: DragRule,
  item?: {
    img: string,
    url: any,
    [key: string]: any
  }

}>()
const decide = ref<any>(null)

// 添加链接
const pickLinkType = ref('exp')
const expLinkIndex = ref(0)
const onPickerLink = (type: string, index: number) => {
  pickLinkType.value = type
  expLinkIndex.value = index
  decide.value?.open()
}

/**
 * 过滤一下一些没有用的信息
 * 
 */
function filterPath(type: string, url: any): any {
  switch (type) {
    case 'goods':
      return {
        id: url.id,
        goodsId: url.goodsId,
        ___type: type,
        ___key: url.___key,
        ___value: url.___value,
        name: url.goodsName
      }
    case 'category':
      return {
        id: url.id,
        ___type: type,
        ___key: url.___key,
        ___value: url.___value,
        name: url.name
      }
    case 'shops':
      return {
        id: url.id,
        ___type: type,
        ___key: url.___key,
        ___value: url.___value,
        name: url.storeName
      }
    case 'pages':
      return {
        id: url.skuId,
        goodsId: url.goodsId,
        ___type: type,
        ___key: url.___key,
        ___value: url.___value,
        name: url.name
      }
    case 'special':
      return {
        id: url.id,
        ___type: type,
        ___key: url.___key,
        ___value: url.___value,
      }
    case 'other':
      return {
        title: url.label,
        ___type: type,
        ___key: url.___key,
        ___value: url.___value,
      }
    case 'url':
      return {
        title: url.value,
        ___type: type,
        ___key: url.___key,
        ___value: url.___value,
      }
  }
}

// 确认链接
function receive(val: any) {
  const type = val.___type
  console.log(val, "处理前")
  let url = filterPath(type, val)
  url.___value = type;
  let urlData = {
    url: url,
    ...url
  }
  console.log(urlData, "值值");
  switch (pickLinkType.value) {
    case 'exp':
      form.value.explosions[expLinkIndex.value].link = JSON.parse(JSON.stringify(urlData))
      break
    case 'adv':
      form.value.advList[expLinkIndex.value].link = JSON.parse(JSON.stringify(urlData))
      break
  }
}

const linkLabel = (link: Record<string, any>) => {
  if (!link) return ''
  switch (link.___type) {
    case 'goods': return `商品|${link.name}`
    case 'category': return `分类页|${link.name}`
    case 'special': return `微页面|${link.id}`
    case 'shops': return `店铺|${link.name}`
    case 'other': return `其他|${link.title}`
    case 'url': return `H5页|${link.title}`
    default: return link
  }
}

const getVoucherDetail = async () => {
  try {
    let res = (await API_Other.getVoucherDetail(pageType.value)) as Record<string, any>
    res = res.data
    if (!res.success || !res.result || !res.result.pageData) return
    let data: Record<string, any> = {}
    if (res.result.pageData.indexOf('{') === 0) {
      data = JSON.parse(res.result.pageData)
    }
    form.value = data as FormData
  } finally {
    initComplete.value = true
  }
}
onMounted(() => getVoucherDetail())
const makeEmptyLogoPlateProductItem = () => ({
  title: '产品标题',
  sub_title: '产品副标题',
  price: '',
  goods: ''
})
const makeEmptyLogoPlateItem = () => ({
  title: '产品板块标题',
  products: [makeEmptyLogoPlateProductItem()]
})
const makeEmptyLogoItem = (label: string) => ({
  label,
  img: '',
  plates: [makeEmptyLogoPlateItem()]
})
const emptyForm: FormData = {
  show_plate: true,
  plate_badge: '',
  show_explosions: true,
  explosions: [
    {img: '', goods: '', link: ''}
  ] as ExplosionItem[],
  show_logo: false,
  logoList: [makeEmptyLogoItem('基础产品板块')] as LogoItem[],
  advList: [
    { img: '', link: '' },
    { img: '', link: '' },
    { img: '', link: '' },
  ] as AdvItem[]
}
const form = ref(clone(emptyForm))
const accessToken = ref<string>(store.getAccessToken() || '')
const fileFormat = ref<Array<string>>(['jpg', 'jpeg', 'png', 'gif', 'bmp'])
const beforeUpload: any = (file: any) => {
  return new Promise((resolve, reject) => {
    if (!fileFormat.value.includes(file.name.split('.')[file.name.split('.').length - 1])) {
      reject(new Error('上传失败'))
      Message.error(
        `请选择 .jpg .jpeg .png .gif .bmp格式文件`
      )
    } else if (Number((file.size / 1024).toFixed(0)) > 1024) {
      reject(new Error('上传失败'))
      Message.error(`所选文件大小过大，不得超过1M`)
    } else {
      resolve(true)
    }
  })
}
const onExplosionsUploadSuccess = (file: FileItem, index: number) => {
  if (file.status !== 'done') {
    return Message.error('上传失败！')
  }
  form.value.explosions[index].img = file.response.result
}
const onAddExp = () => {
  form.value.explosions.push({img: '', goods: '', link: ''})
}
const onDeleteExp = (index: number) => {
  form.value.explosions.splice(index, 1)
}
const skuPickerRef = ref<InstanceType<typeof SkuPicker>>()
const defaultSkus = ref<any[]>([])
const skuPickerType = ref('')
const skuPickIndex = ref('0')
const onPickerExpGoods = (item: ExplosionItem, index: number) => {
  skuPickerType.value = 'exp'
  skuPickIndex.value = String(index)
  defaultSkus.value = item.goods ? [item.goods] : []
  skuPickerRef.value!.modalData.visible = true
}
const handleSkuPickerConfirm = (val: any) => {
  let goods = val[0] || ''
  if (goods) {
    goods = {
      goodsId: goods.goodsId,
      goodsName: goods.goodsName,
      skuId: goods.id
    }
  }
  const type = skuPickerType.value
  const indexSplits = skuPickIndex.value.split('.')
  if (type === 'exp') {
    form.value.explosions[Number(indexSplits[0])].goods = goods
  } else if (type === 'lpd') {
    const index = Number(indexSplits[0])
    const pIndex = Number(indexSplits[1])
    const pdIndex = Number(indexSplits[2])
    form.value.logoList[index].plates[pIndex].products[pdIndex].goods = goods
  }
}

const curLogoTabKey = ref(0)
const handleAddLogoItem = () => {
  const curIndex = form.value.logoList.length
  const logoItem = makeEmptyLogoItem(`产品板块${curIndex}`)
  form.value.logoList.push(logoItem)
}
const handleDeleteLogoItem = (key: number | string) => {
  form.value.logoList.splice(Number(key), 1)
  curLogoTabKey.value = form.value.logoList.length - 1
}
const onLogoUploadSuccess = (file: FileItem, index: number) => {
  if (file.status !== 'done') {
    return Message.error('上传失败！')
  }
  form.value.logoList[index].img = file.response.result
}
const onPickerLogoPDGoods = (pd: LogoPlateProductItem, index: string) => {
  skuPickerType.value = 'lpd'
  skuPickIndex.value = index
  defaultSkus.value = pd.goods ? [pd.goods] : []
  skuPickerRef.value!.modalData.visible = true
}
const onAddLogoPlateProduct = (index: number, pIndex: number) => {
  const p = makeEmptyLogoPlateProductItem()
  form.value.logoList[index].plates[pIndex].products.push(p)
}
const onDeleteLogoPlateProduct = (index: number, pIndex: number, pdIndex: number) => {
  form.value.logoList[index].plates[pIndex].products.splice(pdIndex, 1)
}
const curLogoPlateTabKey = ref(0)
watch(() => curLogoTabKey.value, () => {
  curLogoPlateTabKey.value = 0
})
const handleAddLogoPlateItem = (index: number) => {
  const logoPlateItem = makeEmptyLogoPlateItem()
  form.value.logoList[index].plates.push(logoPlateItem)
}
const handleDeleteLogoPlateItem = (index: number, key: number | string) => {
  form.value.logoList[index].plates.splice(Number(key), 1)
  curLogoPlateTabKey.value = form.value.logoList[index].plates.length - 1
}

const onAdvUploadSuccess = (file: FileItem, index: number) => {
  if (file.status !== 'done') {
    return Message.error('上传失败！')
  }
  form.value.advList[index].img = file.response.result
}
const saveLoading = ref(false)

const onSaveForm = async () => {
  saveLoading.value = true
  try {
    const params = cloneDeep(form.value)
    const formData = new FormData()
    formData.append('pageType', pageType.value)
    formData.append('pageData', JSON.stringify(params))
    const res = (await API_Other.saveVoucher(formData)) as Record<string, any>
    if (!res.data.success) return
    Message.success('保存成功')
  } finally {
    saveLoading.value = false
  }
}
</script>

<style scoped lang="less">
:deep(.arco-input-wrapper) {
  width: 220px;
}

.goods-pick-btn {
  max-width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.voucher-card {
  :deep(.arco-card-header) {
    border-bottom: none;
    border-top: 1px solid var(--color-neutral-3);
  }

  &:first-child {
    :deep(.arco-card-header) {
      border-top: none;
    }
  }
}

.item-box {
  position: relative;
  width: 400px;
  border: 1px solid #eaeaea;
  padding-top: 20px;
  padding-right: 20px;

  & + .item-box {
    margin-top: 10px;
  }

  .btn-view {
    position: absolute;
    top: 0;
    right: -80px;
    display: flex;
    flex-direction: column;

    .arco-btn + .arco-btn {
      margin-top: 5px;
    }
  }
}
.product-plate-tabs {
  width: calc(100% - 24px);
  .plate-tabs-pane {
    padding: 0 24px 24px;
  }
}
.save-btn {
  min-width: 100px;
}
</style>