<template>
  <div text-12px flex flex-a-c text-center flex-wrap :class="{ 'scroll': props.res.data.swiper === 'scroll' }"
    :style="{ backgroundColor: props.res.data.background, height: props.res.data.height + 'px', borderRadius: props.res.data.round + 'px' }">
    <div v-for="(item, index) in props.res.data.list" class="menu-item" :key="index">
      <img v-if="props.res.data.model === 'default'" :src="item.img" />
      <div :style="{ color: props.res.data.textColor }">{{ item.title }}</div>
    </div>
  </div>
</template>
  
<script setup lang="ts">
import { DragRule } from '@/views/operation/decoration/app/models/types';

const props = defineProps<{
  res: DragRule,
}>()



</script>

<style scoped lang="less">
.menu-item {
  width: 20%;

  >img {
    width: 44px;
    height: 44px;
  }
}

.scroll {
  overflow-x: auto;
  display: -webkit-box !important;
  flex-wrap: nowrap !important;

}
</style>
