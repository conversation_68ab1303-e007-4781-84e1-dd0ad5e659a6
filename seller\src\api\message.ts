import request, { Method, commonUrl } from '@/utils/axios';
import { ParamsRule } from '@/types/global';

/**
 * 回收站还原消息
 * @param ids
 */
export function resetMessage(ids: string | number) {
  return request({
    url: `/message/storeMessage/${ids}/reduction`,
    method: Method.PUT,
    needToken: true,
  });
}
/**
 * 彻底删除消息
 * @param ids
 */
export function clearMessage(ids: string | number) {
  return request({
    url: `/message/storeMessage/${ids}`,
    method: Method.DELETE,
    needToken: true,
  });
}

/**
 * 已读消息放入回收站
 * @param ids
 */
export function deleteMessage(ids: string | number) {
  return request({
    url: `${commonUrl}/common/message/${ids}`,
    method: Method.DELETE,
    needToken: true,
  });
}

/**
 * 分页获取消息推送数据
 */
export function getUserMessages(params: ParamsRule) {
  return request({
    url: `${commonUrl}/common/message`,
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 * 进入消息中心首次加载全部数据
 */
export function unReadCount(params: any) {
  return request({
    url: `${commonUrl}/common/message/unReadCount`,
    method: Method.GET,
    needToken: true,
    params,
  });
}
/**
 * 已读消息
 */
export function read(id: string | number) {
  return request({
    url: `${commonUrl}/common/message/${id}`,
    method: Method.PUT,
    needToken: true,
  });
}
/**
 * 全部已读
 */
export function readAll() {
  return request({
    url: `${commonUrl}/common/message/readAll`,
    method: Method.PUT,
    needToken: true,
  });
}

/**
 * 全部删除
 */
export function deleteAll() {
  return request({
    url: `${commonUrl}/common/message/deleteAll`,
    method: Method.DELETE,
    needToken: true,
  });
}
