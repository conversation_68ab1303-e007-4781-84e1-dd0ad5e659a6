<template>
  <div class="top-nav" :style="{
    height: (props.res?.data?.height || 100) + 'px',
    backgroundColor: props.res?.data?.background || '#ffffff',
    borderRadius: (props.res?.data?.round || 0) + 'px'
  }">
    <div class="nav-container" flex flex-a-c flex-j-between>
      <div 
        v-for="(item, index) in safeList" 
        :key="index"
        class="nav-item"
        flex flex-col flex-a-c flex-j-c
        text-center
        cursor-pointer
        @click="handleNavClick(item, index)"
      >
        <!-- 删除按钮 - 只有中间的菜单可以删除 -->
        <icon-close-circle-fill 
          v-if="canDelete(index)" 
          size="16" 
          color="#e1251b" 
          class="delete-icon" 
          @click.stop="deleteNavItem(index)" 
        />
        
        <!-- 菜单图标 -->
        <div class="nav-icon" mb-4px>
          <img v-if="item.img" :src="item.img" :style="{
            width: (props.res?.data?.iconSize || 32) + 'px',
            height: (props.res?.data?.iconSize || 32) + 'px'
          }" />
          <div v-else class="default-icon" :style="{
            width: (props.res?.data?.iconSize || 32) + 'px',
            height: (props.res?.data?.iconSize || 32) + 'px',
            backgroundColor: '#f0f0f0',
            borderRadius: '4px'
          }"></div>
        </div>
        
        <!-- 菜单标题 -->
        <div class="nav-title" :style="{
          color: props.res?.data?.textColor || '#333333',
          fontSize: (props.res?.data?.fontSize || 12) + 'px'
        }">
          {{ item.title }}
        </div>
      </div>
      
      <!-- 添加按钮 -->
      <div 
        v-if="safeList.length < 5"
        class="add-nav-item"
        flex flex-col flex-a-c flex-j-c
        text-center
        cursor-pointer
        @click="addNavItem"
      >
        <icon-plus-circle size="24" color="#1890ff" />
        <div text-12px color-gray-500 mt-4px>添加菜单</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DragRule } from '@/views/operation/decoration/app/models/types';
import { ref, computed } from 'vue';

const props = defineProps<{
  res: DragRule
}>()

// 默认菜单数据
const defaultNavItems = [
  { title: '首页', img: '', path: '/index' },
  { title: '分类', img: '', path: '/category' },
  { title: '充值中心', img: '', path: '/recharge' },
  { title: '优惠购', img: '', path: '/discount' },
  { title: '我的', img: '', path: '/my' }
];

// 确保数据结构存在 - 初始化部分的修改
if (!props.res.data) {
  props.res.data = { list: [] };
}
if (!props.res.data.list || !Array.isArray(props.res.data.list)) {
  props.res.data.list = [...defaultNavItems];
} else if (props.res.data.list.length === 0) {
  props.res.data.list = [...defaultNavItems];
}

// 设置默认值
if (!props.res.data.height) props.res.data.height = 100;
if (!props.res.data.background) props.res.data.background = '#ffffff';
if (!props.res.data.textColor) props.res.data.textColor = '#333333';
if (!props.res.data.iconSize) props.res.data.iconSize = 32;
if (!props.res.data.fontSize) props.res.data.fontSize = 12;
if (!props.res.data.round) props.res.data.round = 0;

// 安全的列表访问
const safeList = computed(() => {
  return props.res?.data?.list || [];
});

// 判断是否可以删除（第一个和最后一个不能删除）
function canDelete(index: number): boolean {
  // 添加安全检查，确保list存在且有效
  if (!props.res?.data?.list || !Array.isArray(props.res.data.list)) {
    return false;
  }
  
  const list = props.res.data.list;
  return index !== 0 && index !== list.length - 1 && list.length > 2;
}

// 删除导航项
function deleteNavItem(index: number) {
  if (canDelete(index) && props.res?.data?.list) {
    props.res.data.list.splice(index, 1);
  }
}

// 添加导航项函数的修改
function addNavItem() {
  if (!props.res.data) {
    props.res.data = { list: [] };
  }
  if (!props.res.data.list || !Array.isArray(props.res.data.list)) {
    props.res.data.list = [];
  }
  
  if (props.res.data.list.length < 5) {
    props.res.data.list.push({
      title: '新菜单',
      img: '',
      path: '/new'
    });
  }
}

// 处理导航点击
function handleNavClick(item: any, index: number) {
  console.log('导航点击:', item, index);
  // 这里可以添加导航跳转逻辑
}
</script>

<style scoped lang="less">
.top-nav {
  width: 100%;
  position: relative;
  overflow: hidden;
}

.nav-container {
  height: 100%;
  padding: 0 16px;
}

.nav-item {
  flex: 1;
  position: relative;
  min-width: 60px;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
  }
}

.delete-icon {
  position: absolute;
  top: -2px;
  right: -2px;
  z-index: 10;
  background: white;
  border-radius: 50%;
}

.nav-icon {
  img {
    border-radius: 4px;
    object-fit: cover;
  }
}

.default-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #d9d9d9;
}

.nav-title {
  font-weight: 500;
  line-height: 1.2;
  word-break: break-all;
}

.add-nav-item {
  min-width: 60px;
  padding: 8px;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  
  &:hover {
    border-color: #1890ff;
    background-color: rgba(24, 144, 255, 0.05);
  }
}
</style>