<template>
  <div style="height: 100%">
    <a-table :columns="columnsTable" :data="responseResult.chartList" />
  </div>
</template>

<script lang="ts" setup>
  import { defineProps, onMounted, reactive, watch } from 'vue';
  import { getPreviewChart } from '@/api/statisitics';
  import { useUserStore } from '@/store';
  import { PreViewParamsRule } from '@/types/global';

  interface ResponseRule {
    chartList: Array<any>;
    previewChart: any;
  } // 传递的参数

  const columnsTable = [
    {
      title: '日期',
      dataIndex: 'date',
    },
    {
      title: '浏览量',
      dataIndex: 'pvNum',
    },
    {
      title: '访客数',
      dataIndex: 'uvNum',
    },
  ];
  const props = defineProps({
    dateType: {
      type: Object,
      default: () => {
        return {
          recent: 'LAST_SEVEN',
          month: '',
        };
      },
    },
  });
  const responseResult = reactive<ResponseRule>({
    chartList: [],
    previewChart: '',
  });

  // 订单请求参数
  const previewParams = reactive<PreViewParamsRule>({
    searchType: props.dateType.recent, // TODAY ,  YESTERDAY , LAST_SEVEN , LAST_THIRTY
    year: props.dateType.month || new Date().getFullYear(),
    storeId: useUserStore().userInfo.id,
  });

  // 初始化列表
  const initOrderChart = async () => {
    const res = await getPreviewChart(previewParams);
    if (res.data.success) {
      responseResult.chartList = res.data.result;
    }
  };
  onMounted(() => {
    initOrderChart();
  });
  // 监听值的改变 父级值改变
  watch(
    () => props.dateType,
    (val) => {
      previewParams.searchType = val.recent;
      if (val.month) {
        // eslint-disable-next-line prefer-destructuring,no-nested-ternary
        previewParams.month = val.month.split('-')[1]
          ? val.month.split('-')[1].indexOf('0') != -1
            ? val.month.split('-')[1].substr(1)
            : ''
          : '';

        // eslint-disable-next-line prefer-destructuring
        previewParams.year = val.month.split('-')[0];
      }

      initOrderChart();
    },
    {
      deep: true,
      immediate: true,
    }
  );
</script>

<style scoped lang="less">
  .previewChart {
    width: 100%;
  }
</style>
