<template>
  <a-modal
    v-model:visible="visible"
    :width="500"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <template #title> {{ modalTitle }} </template>
    <div>
      <a-form
        ref="formRef"
        :model="formAdd"
        :style="{ width: '400px' }"
        layout="horizontal"
        auto-label-width
        @submit="handleSubmit"
      >
        <a-form-item
          v-if="showParent"
          field="parentId"
          :validate-trigger="['change', 'input']"
          label="上级分类"
        >
          {{ parentTitle }}
          <a-input
            v-model="formAdd.parentId"
            :style="{ width: '320px' }"
            allow-clear
            class="hideInput"
          />
        </a-form-item>
        <a-form-item field="level" label="层级" class="hideInput">
          <a-input v-model="formAdd.level" />
        </a-form-item>
        <a-form-item field="name" label="分类名称" :rules="[REQUIRED]">
          <a-input v-model="formAdd.name" />
        </a-form-item>
        <a-form-item v-if="formAdd.level != 1" field="image" label="分类图标">
          <a-input v-model="formAdd.image" />
          <a-tooltip>
            <a-button><icon-eye /></a-button>
            <template #content>
              <div>
                <img
                  :src="formAdd.image"
                  alt="该资源不存在"
                  style="width: 100%; margin: 0 auto"
                />
                <a
                  style="margin-top: 5px; text-align: right; cursor: pointer"
                  @click="viewImage = true"
                  >查看大图</a
                >
              </div>
            </template>
          </a-tooltip>
          <a-button type="primary" @click="hanlderUpload">上传图片</a-button>
        </a-form-item>
        <a-form-item field="sortOrder" label="排序值" :rules="[REQUIRED]">
          <a-input-number
            v-model="formAdd.sortOrder"
            :style="{ width: '320px' }"
            class="input-demo"
            :min="10"
            :max="999"
          />
        </a-form-item>
        <a-form-item
          label="佣金比例(%)"
          field="commissionRate"
          :rules="[REQUIRED]"
        >
          <a-input-number
            v-model="formAdd.commissionRate"
            :style="{ width: '320px' }"
            class="input-demo"
            :min="10"
            :max="100"
          />
        </a-form-item>
        <a-form-item label="是否启用" field="deleteFlag">
          <a-switch
            v-model="formAdd.deleteFlag"
            :checked-value="0"
            :unchecked-value="1"
          >
            <template #checked> 启用 </template>
            <template #unchecked> 禁用 </template>
          </a-switch>
        </a-form-item>
      </a-form>
    </div>
    <template #footer>
      <div style="text-align: right">
        <a-button style="margin-right: 5px" @click="clearForm">取消</a-button>
        <a-button type="primary"  @click="Submit">提交</a-button>
      </div>
    </template>
  </a-modal>
  <!--<a-modal v-model:visible="ossvisible" :width="1100">-->
    <!--<ossManage :close-model="handleOssOk" @changOssImage="changOssImage"></ossManage>-->
  <!--</a-modal>-->
  <a-modal v-model:visible="showOssManager" :width="1100"  @ok="handleOss" title="oss资源管理" @cancel="showOssManager = false">
    <ossManages @selected="changOssImage" :isSingle="true"></ossManages>
  </a-modal>

  <a-modal
    v-model:visible="viewImage"
    :width="1100"
    @ok="handleOssOk"
    @cancel="handleOssCancel"
  >
    <img
      :src="formAdd.image"
      alt="该资源不存在"
      style="width: 100%; margin: 0 auto; display: block"
    />
    <template #footer>
      <div style="text-align: right">
        <a-button type="text" @click="viewImage = false">关闭</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
  import { insertCategory, updateCategory } from '@/api/goods';
import ossManages from '@/components/oss-manage/index.vue';
import { REQUIRED } from '@/utils/validator';
import { Message } from '@arco-design/web-vue';
import { FormInstance } from '@arco-design/web-vue/es/form';
import { ref } from 'vue';
import { FormRules } from '../index';

  const viewImage = ref<boolean>(false);
  const modalType = ref<number>(0); // 编辑/添加
  const showParent = ref<boolean>(false); // 是否展示上级菜单
  const visible = ref(false);
  const formRef = ref<FormInstance>() as any;
  const showOssManager = ref<boolean>(false); // oss弹框
  const selectedSku = ref(); // 选择的sku
  const parentTitle = ref<string>('');
  const modalTitle = ref<string>(''); // 弹框标题
  const formAdd = ref<FormRules>({
    parentId: 0,
    level: 0,
    image: '',
    sortOrder: 0,
    commissionRate: 0,
    deleteFlag: '0',
    name: '',
    id: '',
  }) as any;
  const handleSubmit = ({ values, errors }: any) => {
    console.log('values:', values, '\nerrors:', errors);
  };
  const emits = defineEmits(['refresh']);
  // 清空表单
  const clearForm = () => {
    formRef.value.resetFields();
    visible.value = false;
  };
  // 上传图片
  const hanlderUpload = () => {
    // ossvisible.value = true;
    showOssManager.value = true;
  };
  const handleOk = () => {
    visible.value = false;
  };
  // 关闭弹框
  const handleCancel = () => {
    clearForm();
  };
  // 图片 url
  // const changOssImage = (e: any) => {
  //   formAdd.value.image = e;
  // };
  // 关闭oss弹框
  const handleOssOk = (e: any) => {
    // ossvisible.value = false;
    showOssManager.value = false;
  };
  const handleOssCancel = (e: any) => {
    // ossvisible.value = false;
    showOssManager.value = false;
  };
  // oss资源确定
  const handleOss = () => {
    showOssManager.value = false;
    formAdd.value.image = selectedSku.value[selectedSku.value.length-1].url;
  };
  // oss资源改变
  const changOssImage = (val:any) => {
    selectedSku.value = [];
    val.forEach((item:any)=>{
      selectedSku.value.push({url:item.url})
    })
  };



  // 添加一级分类
  const Submit = async () => {
    const auth = await formRef.value?.validate();
    if (!auth) {
      if (modalType.value == 0) {
        insertCategory(formAdd.value).then((res: any) => {
          if (res.data.code == 200) {
            Message.success('添加成功');
            emits('refresh');
            clearForm();
          }
        });
      } else {
        updateCategory(formAdd.value).then((res: any) => {
          if (res.data.code == 200) {
            Message.success('修改成功');
            emits('refresh');
            clearForm();
          }
        });
      }
    }
  };
  // 暴露方法变量
  defineExpose({
    visible,
    showParent,
    parentTitle,
    modalTitle,
    formAdd,
    modalType,
  });
</script>

<style scoped lang="less">
  .hideInput {
    display: none;
  }
</style>
