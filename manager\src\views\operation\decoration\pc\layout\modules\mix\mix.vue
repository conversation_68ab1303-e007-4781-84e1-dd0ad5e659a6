<template>
  <div py-16px border-b-1 v-auto-animate>
    <a-tabs v-model:active-key="activeKey"  default-active-key="cardGoodsModel">
      <a-tab-pane key="cardGoodsModel" title="左侧栏" >
      </a-tab-pane>
      <a-tab-pane key="brandModel" title="右侧栏">
      </a-tab-pane>
    </a-tabs>
    <!-- <div>
      <div py-16px border-b-1>
        <div w-80px>模块选择</div>
        <div mt-20px v-if="activeKey === 'left'">
          <a-radio-group @change="changeModal" v-model="props.res.data.leftModal">
            <a-radio v-for="(item) in choseType" :value="item.value">{{ item.label }}</a-radio>
          </a-radio-group>

        </div>
        <div mt-20px v-if="activeKey === 'right'">
          <a-radio-group @change="changeModal" v-model="props.res.data.rightModal">
            <a-radio v-for="(item) in choseType" :value="item.value">{{ item.label }}</a-radio>
          </a-radio-group>
        </div>
      </div>
    </div> -->
    <a-space mt-20px py-16px v-if="current && current.type === 'cardGoods'">
      <div w-90px>文字</div>
      <div>
        <a-input allow-clear @clear="current.data.text = ''" hide-button v-model="current.data.text"
          :style="{ width: '150px' }">
        </a-input>
      </div>
    </a-space>

    <div v-auto-animate>
      <div py-16px class="basic" v-if="current && show">
        <div mt-20px>
          <!-- 拥有的组件权限 -->
          <div class="role" :key="index" v-for="(item, index) in current.roles">
            <component :res="current" :is="roleTemplate[item]"></component>
          </div>
          <div v-if="current.models">
            <div class="role" :key="index" v-for="(item, index) in current.models">
              <component :delete="true" :res="current" v-if="item.bind" :max="item.max" :bind="item.bind" :text="item.label"
                :is="modelsTemplate[item.model]"></component>
              <component :res="current" v-else flag="PC" :text="item.label" :is="modelsTemplate[item.model]"></component>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div mt-20px py-16px v-if="current && current.type === 'brand'">
      <div w-90px>品牌添加</div>
      <div>
        <a-button mt-20px h-40px type="outline" @click="operationBrand" long>添加/修改品牌</a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DragRule } from '@/views/operation/decoration/app/models/types';
// import { brandModel, cardGoodsModel, defaultList } from '@/views/operation/decoration/pc/models/goods';
import {  defaultList } from '@/views/operation/decoration/pc/models/goods';

import roles from '@/views/operation/decoration/pc/layout/modules/roles'
import models from '@/views/operation/decoration/pc/layout/modules/models'
const roleTemplate: any = roles
const modelsTemplate: any = models
import { ref, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
// const templates: any = {
//   brandModel,
//   cardGoodsModel
// }
const props = defineProps<{
  res: DragRule,
}>()
const activeKey = ref<string>('cardGoodsModel')
// const choseType = [{
//   label: "商品",
//   value: "cardGoodsModel"
// }, {
//   label: "品牌",
//   value: "brandModel"
// },]

const current = ref<any>(props.res.data[activeKey.value === 'cardGoodsModel' ? 'leftData' : 'rightData'])


const show = ref<boolean>(true)
// 添加/修改品牌
function operationBrand() {
  if (current.value.type === 'brand') {
    if (current.value.data.brandList.length === 6) {
      Message.error('最多添加6项')
      return
    }
    current.value.data.brandList.push(defaultList)
  }
}

watch(() => activeKey.value, (val) => {
  current.value = props.res.data[activeKey.value === 'cardGoodsModel' ? 'leftData' : 'rightData']
})
watch(() => props.res, (val) => {
  current.value = props.res.data[activeKey.value === 'cardGoodsModel' ? 'leftData' : 'rightData']
},{deep:true,immediate:true})

// 修改模块
// function changeModal(val: any) {
//   show.value = false
//   if (activeKey.value === 'left') {
//     props.res.data.leftData = templates[val]
//     console.log( props.res.data.leftData)
//   } else {
//     props.res.data.rightData = templates[val]
//   }
//   console.log('changeModal', val)
//   current.value = templates[val]
//   console.log("current.value", current.value)
//   setTimeout(() => {
//     show.value = true
//   }, 300)
// }

// onMounted(() => {
//   props.res.data.leftData = templates['cardGoodsModel']
//   props.res.data.rightData = templates['brandModel']
//   current.value = templates['cardGoodsModel']
//   console.log(props.res.data)
// })

// watch(() => [props.res.data.leftModal, props.res.data.rightModal], ([left, right], [oldLeft, oldRight]) => {
//   console.log(left, right)
//   if (left && left !== oldLeft) {

//   }
//   if (right && right !== oldRight) {
//     current.value = templates[right]
//   }

// }, { deep: true })

// watch([props.res.data.leftModal, props.res.data.rightModal], ([left, right]) => {
//   left && (current.value = templates[left])
//   right && (current.value = templates[right])
//   console.log('ccc',left,right)
// }, { deep: true, immediate: true })
</script>

<style scoped>
.basic {
  height: calc(100vh - (64px + 50px + 300px));
  overflow-y: auto;
}

.roles {
  height: calc(100vh - (64px + 50px + 200px));
  overflow-y: auto;
}
</style>
