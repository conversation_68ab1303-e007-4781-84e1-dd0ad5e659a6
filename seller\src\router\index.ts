import storage from '@/utils/storage';
import NProgress from 'nprogress'; // progress bar
import 'nprogress/nprogress.css';
import { createRouter, createWebHistory } from 'vue-router';
import { appRoutes } from './routes';
// NProgress Configuration
NProgress.configure({ showSpinner: true });
const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: 'login',
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/login/index.vue'),
      meta: {
        requiresAuth: false,
      },
    },
    {
      path: '/app-invent',
      name: 'app-invent',
      component: () => import('@/views/operation/decoration/app/app-invent.vue'),
      meta: {
        requiresAuth: false,
      },
    },
    {
      path: '/pc-invent',
      name: 'pc-invent',
      component: () => import('@/views/operation/decoration/pc/pc-invent.vue'),
      meta: {
        requiresAuth: false,
      },
    },
    {
      path: '/workplace',
      name: 'Workplace',
      component: () => import('@/layout/default-layout.vue'),
      meta: {
        requiresAuth: false,
      },
    },
    ...appRoutes,
    {
      path: '/:pathMatch(.*)*',
      name: 'notFound',
      component: () => import('@/views/not-found/index.vue'),
    },
  ],
  scrollBehavior() {
    return { top: 0 };
  },
});


router.beforeEach((to: any, from: any, next: any) => {
  NProgress.start();
  const title = to.name;
  const token: any = storage.getAccessToken();
  if (!token && !storage.getRefreshToken() && title != 'login') {
    // 判断未登录且前往的页面不是登录页
    next({ name: 'login' });
  } else if (token && storage.getRefreshToken() && title == 'login') {
    // 判断已登录且前往的页面是登录页
    next({ name: 'Workplace' });
  } else {
    next();
  }
  NProgress.done();
});

export default router;
