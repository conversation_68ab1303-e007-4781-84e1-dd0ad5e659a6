import { Message } from '@arco-design/web-vue';

import store from '@/utils/storage';

export const accessToken = <string>(store.getAccessToken() || '');


const fileFormat = <Array<string>>(['jpg', 'jpeg', 'png', 'gif', 'bmp']);

// 上传成功
export const handleSuccess = (res: any): string => {
  if (res.response.success) {
    Message.success('图片上传成功');
    return res.response.result
  }
  return res.response.result || ''
};


// 上传失败
export const handleError = () => {
  Message.error('上传失败');
};
// 上传前校验
export const beforeUpload = (file: File): Promise<File> => {
  return new Promise((resolve, reject) => {
    if (!fileFormat.includes(file.name.split('.')[file.name.split('.').length-1])) {
      reject(new Error('上传失败'));
      Message.error(
        `所选文件${file.name.slice(
          -3
        )}格式不正确, 请选择 .jpg .jpeg .png .gif .bmp格式文件`
      );
    } else if ((file.size / 1024) > 5120) {
      reject(new Error('上传失败'));
      Message.error(`所选文件大小过大, 不得超过5M`);
    } else {
      resolve(file);
    }
  });
};
