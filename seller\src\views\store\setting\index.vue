<template>
  <a-row style="margin-bottom: 16px">
    <a-col :span="24">
      <UserPanel />
    </a-col>
  </a-row>
  <a-row class="wrapper">
    <a-col :span="24">
      <a-tabs type="rounded">
        <a-tab-pane
          v-for="item in tabPanes"
          :key="item.key"
          :title="item.title"
        >
          <component :is="item.component" />
        </a-tab-pane>
      </a-tabs>
    </a-col>
  </a-row>
</template>

<script setup lang="ts">
  import UserPanel from './components/user-panel.vue';
  import BasicInformation from './components/basic-information.vue';
  import shipmentsAddress from './components/shipments-address.vue';
  import returnAddress from './components/return-address.vue';
  import storeAddress from './components/store-address.vue';

  const tabPanes = [
    {
      key: 'base',
      title: '基础信息',
      component: BasicInformation,
    },
    {
      key: '',
      title: '发货地址',
      component: shipmentsAddress,
    },
    {
      key: 'return',
      title: '退货地址',
      component: returnAddress,
    },
    {
      key: 'storeAddress',
      title: '自提管理',
      component: storeAddress,
    },
  ];
</script>

<style scoped lang="less">
  :deep(.section-title) {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 14px;
  }
  .wrapper {
    padding: 20px;
  }
</style>
