<script setup lang="ts">
import { useRouter } from 'vue-router'

import config from '@/config'
import getAssetsImages from '@/utils/assetsImages'
import { secrecyMobile } from '@/utils/filters'
import storage from '@/utils/storage'

const router = useRouter();
// 用户信息
const userInfo = computed(() => {
  return storage.getUserInfo()?JSON.parse(storage.getUserInfo()):'';
});

interface Rule {
  icon: string
  label: string
  path: string
}

const appendList: Array<Rule> = [
  {
    icon: 'support',
    label: '帮助中心',
    path: '/article',
  },
  {
    icon: 'feedback',
    label: '我的评论',
    path: '/user/home/<USER>/commentList',
  },
  {
    icon: 'notice',
    label: '收货地址',
    path: '/user/home/<USER>/myAddress',
  },
  {
    icon: 'notification',
    label: '我的消息',
    path: '/user/home/<USER>/messageList',
  },
]
const entranceList: Array<Rule> = [
  {
    icon: 'collage',
    label: '宝贝收藏',
    path: '/user/home/<USER>/myFavorites',
  },
  {
    icon: 'shop',
    label: '收藏店铺',
    path: '/user/home/<USER>/myFavorites?type=STORE',
  },
  {
    icon: 'carts',
    label: '购物车',
    path: '/cart',
  },
  {
    icon: 'story',
    label: '我的足迹',
    path: '/user/home/<USER>/myTracks',
  },
]

function entryControl(val: Rule) {
  const routerUrl = router.resolve({ path: val.path })
  window.open(routerUrl.href, '_blank')
}

onMounted(async () => {

})
</script>

<template>
  <div class="user" w-262px flex flex-col flex-a-c rounded-10px bg-white>
    <div class="user-panel" @click="entryControl({ path: '/user/home/<USER>/myOrder' })">
      <img class="user-face" :src="userInfo.face || getAssetsImages('default.png', 'images')">
      <div class="welcome">
        Hi, {{
          secrecyMobile(userInfo.nickName || `欢迎来到${config.title}`)
        }}
      </div>
    </div>
    <div v-if="userInfo.id">
      <div class="icon-list" />
    </div>
    <div v-else class="flex-j-c flex flex-a-c">
      <a-button type="primary" status="danger" class="btn-block" @click="$router.push('login')">
        登录
      </a-button>
      <a-button type="primary" status="warning" class="btn-block sign-up" @click="$router.push('signUp')">
        注册
      </a-button>
    </div>

    <div class="gray-line" />

    <div class="icon-list flex flex-j-sb">
      <div v-for="(item, index) in entranceList" :key="index" class="icon-item" @click="entryControl(item)">
        <img h-20px w-20px class="icon" :src="`${getAssetsImages(`${item.icon}.png`, 'iconfont')} `">
        <div>
          {{ item.label }}
        </div>
      </div>
    </div>
    <div class="icon-list flex flex-j-sb">
      <div v-for="(item, index) in appendList" :key="index" class="icon-item" @click="entryControl(item)">
        <img h-20px w-20px class="icon" :src="`${getAssetsImages(`${item.icon}.png`, 'iconfont')}`">
        <div>
          {{ item.label }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.btn-block {
  margin-top: 21px;
  margin-bottom: 13px;
  width: 77px;
  height: 28px;
  border-radius: 14px;
  opacity: 1;
  font-size: 13px;
  font-weight: normal;
  border: none;
  line-height: 28px;
  text-align: center;
  cursor: pointer;
  letter-spacing: 0px;

  color: #ffffff;
  background: @theme_color;
}

.sign-up {
  background: #f39519;
  margin-left: 10px;
}

.icon-item {
  cursor: pointer;
  text-align: center;

  div {
    font-size: 11px;
    font-weight: normal;
    line-height: 13px;
    text-align: center;
    letter-spacing: 0px;

    color: #666666;
  }

  .value {
    font-size: 14px;

    line-height: 17px;
    text-align: center;
    font-weight: 400;
    letter-spacing: 0px;
    margin-bottom: 3px;
    color: @theme_color;
  }

  .label {
    font-weight: 400;
    font-size: 12px;

    line-height: 14px;
    text-align: center;
    letter-spacing: 0px;
    color: #666666;
    margin-bottom: 13px;
  }
}

.user-panel {
  cursor: pointer;
  display: flex;
  align-items: center;
  flex-direction: column;
  padding-top: 28px;
  padding-bottom: 25px;
}

.icon-list {
  width: 216px;
}

.icon-list:nth-last-of-type(1) {
  margin-top: 20px;
}

.gray-line {
  width: 216px;
  height: 1px;
  background: #e5e5e5;
  margin-bottom: 13px;
}

.user-face {
  margin-bottom: 12px;
  width: 70px;
  height: 70px;
  border-radius: 50%;
}

.welcome {
  font-size: 14px;
  font-weight: normal;
  line-height: 17px;
  text-align: center;
  letter-spacing: 0px;

  color: #333333;
}

.user {
  height: 333px;
}
</style>
