<template>
  <div>
    <h1>Example</h1>
    <h2>Choice Oss</h2>
    <PickChoiceOss :apis="ossApiList" />
    <h2>Upload</h2>
    <PickUpload :api="mockUploadApi" :accessToken="accessToken" />
    <h2>Goods Sku Selector</h2>
    <a-button @click="open">打开sku选择器</a-button>
    <PickChoiceGoods ref="skuSelector" :apiParams="{}" :api="() => { }" :defaultGoodsSelectedList="[]" :goods="[]" />
    <h2>Table Pages</h2>
    <PickTablePage :columns="columnsTable" :methods="sortMethods" />
    <h2>Search Column</h2>
    <PickSearchColumn :columns="columnsSearch" />
    <h2>Choice City</h2>
    <PickChoiceCity :level="4" address='' :ids="[]" :api="() => { }" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { PickSearchColumn, PickChoiceCity, PickTablePage, PickChoiceGoods, PickUpload, PickChoiceOss } from './packages/index'
const skuSelector = ref<any>(null);
const accessToken = ""
const ossApiList = {
  init() { },
  rename() { },
  delete() { },
  addDir() { },
  updateDir() { },
  initDir() { },

}
const mockUploadApi = () => { }
const columnsSearch: Array<any> = [
  {
    label: '订单编号',
    model: 'orderSn',
    disabled: false,
    input: true,
  },
  {
    label: '会员名称',
    model: 'memberName',
    disabled: false,
    input: true,
  },
  {
    label: '店铺名称',
    model: 'storeName',
    disabled: false,
    input: true,
  },
  {
    label: '发票抬头',
    model: 'receiptTitle',
    disabled: false,
    input: true,
  },

];

const columnsTable: any = [
  {
    title: '账单号',
    dataIndex: 'sn',
  },
  {
    title: '生成时间',
    dataIndex: 'createTime',
  },
  {
    title: '结算时间段',
    dataIndex: 'startTime',
  },
  {
    title: '结算金额',
    dataIndex: 'billPrice',
    currency: true,
  },

];

const sortMethods = {
  title: '操作',
  width: 100,
  methods: [
    {
      title: '查看',
      callback: 'detail',
    },
  ],
};

function open() {
  skuSelector.value.init()
}
</script>

<style scoped></style>
