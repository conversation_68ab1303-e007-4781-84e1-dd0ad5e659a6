<template>
  <router-view v-slot="{ Component, route }">
    <Workplace v-if="route.name === 'Workplace'" />
    <div v-else>
      <component
        :is="Component"
        v-if="route.meta.ignoreCache"
        :key="route.fullPath"
      />
      <keep-alive v-else :include="cacheList">
        <component :is="Component" :key="route.fullPath" />
      </keep-alive>
    </div>
  </router-view>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';
  import { useTabBarStore } from '@/store';
  import Workplace from '@/views/dashboard/workplace/index.vue';

  const tabBarStore = useTabBarStore();
  const cacheList = computed(() => tabBarStore.getCacheList);
</script>

<style scoped lang="less"></style>
