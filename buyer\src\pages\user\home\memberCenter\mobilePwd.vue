<template>
  <div>
    <Card _Title="修改密码" :_Size="16"></Card>
    <a-form ref="smsRef" size="large" layout="vertical" v-if="data.codeStatus"
            :style="{ width: '310px', position: 'relative' }" @submit-success="handleSubmit" :model="data.smsForm">
      <a-form-item :hide-asterisk="true" field="mobile" :rules="[REQUIRED, MOBILE]">
        <a-input v-model="data.smsForm.mobile" max-length="11" allow-clear placeholder="请输入手机号">
          <template #prefix>
            <icon-lock />
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 256 256">
              <path fill="currentColor" d="M231.88 175.08A56.26 56.26 0 0 1 176 224C96.6 224 32 159.4 32 80a56.26 56.26 0 0 1 48.92-55.88a16 16 0 0 1 16.62 9.52l21.12 47.15v.12A16 16 0 0 1 117.39 96c-.18.27-.37.52-.57.77L96 121.45c7.49 15.22 23.41 31 38.83 38.51l24.34-20.71a8.12 8.12 0 0 1 .75-.56a16 16 0 0 1 15.17-1.4l.13.06l47.11 21.11a16 16 0 0 1 9.55 16.62"/>
            </svg>
          </template>
         </a-input>
      </a-form-item>
      <a-form-item :hide-asterisk="true" field="code" :rules="[REQUIRED]">
        <a-input v-model="data.smsForm.code" max-length="6" allow-clear placeholder="请输入手机验证码">
          <template #prefix>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24">
              <g fill="none">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M2 5a3 3 0 0 1 3-3h10a3 3 0 0 1 3 3v6a3 3 0 0 1-3 3h-3.586l-3.707 3.707A1 1 0 0 1 6 17v-3H5a3 3 0 0 1-3-3V5zm20 4v6c0 1-.6 3-3 3h-1v3c0 .333-.2 1-1 1c-.203 0-.368-.043-.5-.113L12.613 18H9l3-3h3c1.333 0 4-.8 4-4V6c1 0 3 .6 3 3z" fill="currentColor"/>
              </g>
            </svg>
          </template>
          <template #append><span class="code-msg hover-pointer" @click="sendCode">{{ data.codeMsg }}</span></template>
        </a-input>
      </a-form-item>
      <a-form-item no-style>
        <a-button @click.stop="verifyBtnClick" :status="data.verifyStatus?'success':'danger'" long :style="{marginBottom: '20px'}" :loading="loading">
          {{ data.verifyStatus?'验证通过': '点击完成安全验证' }}</a-button>
      </a-form-item>
      <a-form-item no-style>
        <a-button html-type="submit" type="primary" status="danger" long :style="{width: '310px'}" :loading="loading">验证</a-button>
      </a-form-item>
      <!-- 拼图验证码 -->
      <verify ref="verifyDom" class="verify-con" verify-type="FIND_USER" @on-change="verifyChange"></verify>
    </a-form>

    <!-- 设置&修改的第二种情况 -->
    <a-form ref="mobilePwdRef" size="large" layout="horizontal" :style="{ width: '400px'}" auto-label-width @submit-success="handleSetPassword" :model="data.mobilePwdForm" v-if="!data.codeStatus">
      <a-form-item field="password" label="新密码" :rules="[REQUIRED, LEAST6]">
        <a-input-password v-model="data.mobilePwdForm.password" value="large" allow-clear placeholder="请输入您的新密码">
          <template #prefix>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24">
              <path fill="currentColor" d="M19 10h1a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V11a1 1 0 0 1 1-1h1V9a7 7 0 0 1 14 0zm-2 0V9A5 5 0 0 0 7 9v1zm-6 4v4h2v-4z"/>
            </svg>
          </template>
        </a-input-password>
      </a-form-item>
      <a-form-item>
        <a-button html-type="submit" type="primary" status="danger" class="mr_10" :loading="loading">修改密码</a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { REQUIRED, LEAST6, MOBILE } from '@/utils/validator';
  import { validateCode, resetPassword } from "@/api/login";
  import { sendSms } from "@/api/common";
  import verify from '@/components/verify/index.vue';
  import { md5 } from '@/utils/md5';
  import { Message } from '@arco-design/web-vue';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const loading = ref(false);
  const verifyDom = ref();
  interface formInterface {
    codeStatus: boolean,
    codeMsg: string | number,
    verifyStatus: boolean,
    interval: any,
    time: number,
    smsForm: {
      mobile: string,
      code: string
    },
    mobilePwdForm: any
  }
  // 数据集
  const data = ref<formInterface>({
    codeStatus: true,
    // 验证码文字
    codeMsg: '发送验证码',
    //  是否图片验证通过
    verifyStatus: false,
    interval: null, // 定时器
    time: 60, // 倒计时
    // 二维码登录表单
    smsForm: {
      mobile: '',
      code: ''
    },
    mobilePwdForm: {
      password: ''
    }
  });
  // 验证码
  const smsRef = ref();
  const mobilePwdRef = ref();

  // 发送手机验证码
  const sendCode = async () => {
    if (data.value.time === 60) {
      if (data.value.smsForm.mobile === "") {
        Message.warning("请先填写手机号");
        return;
      }
      if (!data.value.verifyStatus) {
        Message.warning("请先完成安全验证");
        return;
      }
      let params = {
        mobile: data.value.smsForm.mobile,
        verificationEnums: "FIND_USER"
      };
      sendSms(params).then((res: any) => {
        if (res.data.success) {
          Message.success("验证码发送成功");
          data.value.interval = setInterval(() => {
            data.value.time--;
            if (data.value.time === 0) {
              data.value.time = 60;
              data.value.codeMsg = "重新发送";
              data.value.verifyStatus = false;
              clearInterval(data.value.interval);
            } else {
              data.value.codeMsg = data.value.time;
            }
          }, 1000);
        } else {
          Message.warning(res.message);
        }
      })
    }
  };
  // 开启滑块验证
  const verifyBtnClick = async () => {
    if (!data.value.verifyStatus) {
      if (data.value.smsForm.mobile === "") {
        Message.warning("请先填写手机号");
        return;
      }
      verifyDom.value.init();
    }
  };
  // 验证
  const handleSubmit = async () => {
    const auth = await smsRef.value?.validate();
    if (!auth) {
      const res = await validateCode(data.value.smsForm);
      if (res.data && res.data.success) {
        Message.success('验证通过！');
        data.value.codeStatus = !data.value.codeStatus;
      }
    }
  };
  // 验证是否正确
  async function verifyChange(callback:any) {
    if (!callback.status) return;
    try {
      data.value.verifyStatus = true;
      verifyDom.value.verifyShow = false;
    } catch (error) {
      loading.value = false;
    }
    verifyDom.value.verifyShow = false;
  }

  // 设置新密码
  const handleSetPassword = async () => {
    const auth = await mobilePwdRef.value?.validate();
    if (!auth) {
      let params = JSON.parse(JSON.stringify(data.value.mobilePwdForm));
      params.password = md5(params.password);
      // 修改密码
      resetPassword(params).then(res => {
        if (res.data.success) {
          Message.success('密码修改成功');
          setTimeout(() => {router.go(-1);}, 1000);
        }
      });
    }
  };

  onMounted(() => {
    
  })
</script>

<style scoped lang="less">
  .verify-con {
    position: absolute;
    right: -240px;
    top: -30px;
    z-index: 10;
  }

</style>


