{"type": "module", "private": true, "scripts": {"build": "vite build", "dev": "vite --port 3333 --open", "lint": "eslint .", "typecheck": "vue-tsc --noEmit", "preview": "vite preview", "test": "vitest", "up": "taze major -I"}, "dependencies": {"@formkit/auto-animate": "^0.8.1", "@vueuse/core": "^10.9.0", "axios": "^0.24.0", "js-base64": "^2.5.1", "vue": "^3.4.27", "vue-router": "^4.3.2"}, "devDependencies": {"@antfu/eslint-config": "^2.18.1", "@arco-design/web-vue": "^2.55.2", "@iconify-json/carbon": "^1.1.33", "@types/node": "^20.12.12", "@unocss/eslint-config": "^0.60.2", "@unocss/eslint-plugin": "^0.60.2", "@unocss/reset": "^0.60.2", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue-macros/volar": "^0.19.1", "@vue/test-utils": "^2.4.6", "eslint": "^8.56.0", "eslint-plugin-format": "^0.1.1", "jsdom": "^24.0.0", "lint-staged": "^15.2.2", "pnpm": "^9.1.1", "taze": "^0.13.8", "typescript": "^5.4.5", "unocss": "^0.60.2", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^0.27.0", "unplugin-vue-macros": "^2.9.2", "unplugin-vue-router": "^0.8.6", "vite": "^5.2.11", "vitest": "^1.6.0", "vue-tsc": "^2.0.19"}}