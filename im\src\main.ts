import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router/auto'
import { autoAnimatePlugin } from '@formkit/auto-animate/vue'

import App from './App.vue'
import '@unocss/reset/tailwind.css'
import './styles/main.css'
import 'uno.css'
import '@arco-design/web-vue/dist/arco.css'

const app = createApp(App)
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
})

app.use(router)
app.use(autoAnimatePlugin)
app.mount('#app')
