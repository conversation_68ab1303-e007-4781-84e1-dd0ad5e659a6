<template>
  <div>
    <a-space>
      <a-select v-for="level in props.level || 4" :key="level" v-model="targetData.city[level - 1].id"
        :style="{ width: props.width || '120px' }" :placeholder="targetData.city[level - 1].label"
        :loading="targetData.city[level - 1].loading" @change="changeCity(targetData.city[level - 1])">
        <a-option v-for="(item, index) in targetData.city[level - 1].value" :key="index" :value="item.id">{{ item.name
        }}</a-option>
      </a-select>
    </a-space>
  </div>
</template>

<script lang="ts" setup>
import { getChildRegion } from '@/api/common';
import { onMounted, reactive } from 'vue';

interface CityRule {
  city: Array<{
    label: string;
    value: Array<{
      [key: string]: any;
    }>;
    id: string | number;
    type: string;
    loading: boolean;
    address: string;
    [key: string]: any;
  }>;
}

const targetData = reactive<CityRule>({
  city: [
    {
      label: '省',
      value: [],
      type: 'province',
      id: '',
      address: '',
      loading: false,
    },
    {
      label: '市',
      value: [],
      type: 'city',
      id: '',
      address: '',
      loading: false,
    },
    {
      label: '区',
      value: [],
      type: 'area',
      id: '',
      address: '',
      loading: false,
    },
    {
      label: '街道',
      value: [],
      type: 'street',
      id: '',
      address: '',
      loading: false,
    },
  ],
});
const emit = defineEmits<{ (e: 'callback', obj: object): void }>();
const props = defineProps<{
  level?: number;
  width?: string | number;
  ids: any;
  address: any;
}>();
// 渲染city
const renderCity = async (id: string | number = 0, type: string) => {
  const target = targetData.city.find((item) => {
    return item.type == type;
  });
  target!.loading = true;
  const res = await getChildRegion(id);
  targetData.city.forEach((item) => {
    if (item.type == type) {
      item.value = res.data.result;
    }
  });
  target!.loading = false;
};

// 选择城市之后进行回调操作
const changeCity = (val: Record<string, any>) => {
  // 看下当前选择城市的索引
  const cityIndex = targetData.city.findIndex((city) => {
    return val.type == city.type;
  });
  const cityData = targetData.city[cityIndex];
  targetData.city[cityIndex].address = targetData.city[cityIndex].value.find(
    (item: Record<string, any>) => item.id == val.id
  )?.name;

  // 如果是最后一项 回调出去
  if (cityIndex + 1 == (props.level || 4)) {
    const cityName: string[] = [];
    const cityId: (string | number)[] = [];
    const cityWay = targetData.city.map((item) => {
      cityName.push(item.address);
      cityId.push(item.id);
      return {
        id: item.id,
        name: item.address,
      };
    });
    const cityRes = {
      ids: cityId,
      cities: cityName,
      values: cityWay,
    };
    emit('callback', cityRes);
    return;
  }

  //  得出索引之后将大于它的索引的item设置为空
  targetData.city.forEach((item, index) => {
    if (index > cityIndex) {
      item.value = [];
      item.id = '';
    }
  });

  renderCity(cityData.id, targetData.city[cityIndex + 1].type);
};

// 初始化city
const init = () => {
  renderCity(0, 'province');
  if (props.ids) {
    const cityData = ['province','city','area','street', ''];
    const ids = props.ids.split(',');
    const address = props.address.split(',');
    targetData.city = targetData.city.map((item, index) => {
      item.id = ids[index];
      item.address = address[index];
      renderCity(item.id, cityData[index+1]);
      return item;
    });
  }
};

onMounted(() => {
  init();
});
</script>
