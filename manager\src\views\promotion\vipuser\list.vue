<template>
  <a-card class="general-card" title="会员管理" :bordered="false">
    <searchTable
      :columns="columnsSearch"
      time-type="timestamp"
      @reset="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
      @search="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
    ></searchTable>

    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getVipUserList"
      :api-params="apiParams"
      :checkbox="true"
      :bordered="true"
      @selectTableChange="selectTableChange"
    >

      <template #vipEffective="{ data }">
        <a-tag
          v-for="item in vipEffectiveStatus"
          :key="item.value"
          v-show="data.vipEffective === item.value"
          :color="item.color"
        >
          {{ item.label }}
        </a-tag>
        <a-tag v-if="!vipEffectiveStatus.find(item => item.value === data.vipEffective)" color="gray">
          未知
        </a-tag>
      </template>
      <template #edit="{ data }">
        <a-button
          style="margin-right: 10px"
          type="text"
          status="success"
          @click="see(data)"
          >查看</a-button
        >
      </template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';



  import { ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { getVipUserList } from '@/api/vipuser';
  import { vipEffectiveStatus } from '@/utils/tools';

  const router = useRouter();
  const tablePageRef = ref<any>();
  const apiParams = ref<any>({});
  const selectList = ref([]); // 接受子组件传过来的值
  const columnsSearch: Array<SearchRule> = [
    {
      label: '会员名称',
      model: 'username',
      disabled: false,
      input: true,
    },
    {
      label: '领取人手机号',
      model: 'mobile',
      disabled: false,
      input: true,
    }
  ];

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '用户昵称',
      dataIndex: 'nickName',
    },
    {
      title: '领取人手机号',
      dataIndex: 'mobile',
    },
    {
      title: '是否有效',
      dataIndex: 'vipEffective',
      slot: true,
      slotTemplate: 'vipEffective',
    },
    {
      title: '领取时间',
      dataIndex: 'vipStartDate',
    },
    {
      title: '结束时间',
      dataIndex: 'vipEndDate',
    },
  ];

  // 操作列表
  const sortMethods: MethodsRule = {
    title: '操作',
    width: 120,
    methods: [
      {
        title: '查看',
        slot: true,
        slotTemplate: 'edit',
      },
    ],
  };
  // 父组件传过来的数据
  const selectTableChange = (val: any) => {
    if (val) {
      selectList.value = val;
    }
  };
  // 查看会员详情
  const see = (v: any) => {
    console.log('查看会员详情:', v);
    // 跳转到会员详情页面
    router.push({
      name: 'vip-user-detail',
      query: {
        id: v.id,
        data: JSON.stringify(v) // 临时传递数据，实际应该通过API获取
      }
    });
  };

</script>
