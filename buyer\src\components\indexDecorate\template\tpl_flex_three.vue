<script setup lang="ts">
import { useRouter } from 'vue-router'
import { navigateTo } from './navigate.ts'
import type { DragRule } from '@/views/operation/decoration/app/models/types'

const props = defineProps<{
  res: DragRule
}>()
const router = useRouter()
function handleClickItem(item: any) {
  const path = navigateTo(item)

  window.open(router.resolve(path).href, '_blank')
}
</script>

<template>
  <div v-if="props.res" class="wrapper" flex flex-j-sb flex-a-c>
    <div v-for="(item, index) in props.res.data.list" :key="index" h-165px w-385px @click="handleClickItem(item)">
      <img class="img" :src="item.img">
    </div>
  </div>
</template>

<style scoped>
.wrapper {
  background-color: #f3f3f6;
}

.img {
  width: 100%;
  height: 100%;
  display: block;
}
</style>
