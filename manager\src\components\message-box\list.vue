<template>
  <a-list :bordered="false">
    <a-list-item
      v-for="item in props.renderList"
      :key="item.id"
      action-layout="vertical"
    >
      <template #extra>
        <div v-if="item.status === 'UN_READY'" class="render-item">
          <div>
            <a-tag color="red">未读消息</a-tag>
          </div>
          <div
            ><a-button size="mini" @click="handleClickTarget(item.id, 'read')"
              >标为已读</a-button
            ></div
          >
        </div>
        <div v-else-if="item.status === 'ALREADY_READY'" class="render-item">
          <a-tag color="green">已读消息</a-tag>
          <div
            ><a-button
              size="mini"
              @click="handleClickTarget(item.id, 'deleted')"
              >删除</a-button
            ></div
          >
        </div>
        <div v-else-if="item.status === 'ALREADY_REMOVE'" class="render-item">
          <div
            ><a-button size="mini" @click="handleClickTarget(item.id, 'reset')"
              >还原</a-button
            ></div
          >
          <div
            ><a-button
              status="danger"
              size="mini"
              @click="handleClickTarget(item.id, 'clear')"
              >彻底删除</a-button
            ></div
          >
        </div>
      </template>
      <div class="item-wrap">
        <a-list-item-meta>
          <template #title>
            <a-space :size="4">
              <span>{{ item.title }}</span>
            </a-space>
          </template>
          <template #description>
            <div>
              <a-typography-paragraph
                :ellipsis="{
                  rows: 1,
                }"
                >{{ item.content }}</a-typography-paragraph
              >
              <a-typography-text class="time-text">
                {{ item.createTime }}
              </a-typography-text>
            </div>
          </template>
        </a-list-item-meta>
      </div>
    </a-list-item>
    <div
      v-if="props.renderList.length && props.renderList.length < 3"
      :style="{ height: (showMax - props.renderList.length) * 86 + 'px' }"
    ></div>
    <template #empty></template>
  </a-list>
</template>

<script setup lang="ts">

  const showMax = 3;
  const props = defineProps<any>({
    renderList: {
      type: Array,
      default: () => {return []}
    },
  });
  const emit = defineEmits<{ (e: 'itemClick', obj: object): void }>();
  // 点击按钮之后进行回调
  const handleClickTarget = (id: string | number, type: string) => {
    emit('itemClick', { id, type });
  };
</script>

<style scoped lang="less">
  :deep(.arco-list) {
    .arco-list-item {
      min-height: 86px;
      border-bottom: 1px solid rgb(var(--gray-3));
    }
    .arco-list-item-extra {
      position: absolute;
      right: 20px;
    }
    .arco-list-item-meta-content {
      flex: 1;
    }
    .item-wrap {
      cursor: pointer;
    }
    .time-text {
      font-size: 12px;
      color: rgb(var(--gray-6));
    }
    .arco-list-footer {
      padding: 0;
      height: 50px;
      line-height: 50px;
      // border-top: 1px solid rgb(var(--gray-3));
      .arco-space-item {
        width: 100%;
        border-right: 1px solid rgb(var(--gray-3));
        &:last-child {
          border-right: none;
        }
      }
      .add-border-top {
        border-top: 1px solid rgb(var(--gray-3));
      }
    }
    .footer-wrap {
      text-align: center;
    }
    .arco-typography {
      margin-bottom: 0;
    }
    .add-border {
      border-top: 1px solid rgb(var(--gray-3));
    }
    .render-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      > * {
        margin: 5px 0;
      }
    }
  }
</style>
