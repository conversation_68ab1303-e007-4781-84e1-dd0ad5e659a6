import request, { Method } from '@/utils/axios';
import { ParamsRule } from '@/types/global';
import qs from "query-string";

export function getMemberReview(params: ParamsRule) {
  return request({
    url: '/member/evaluation',
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 会员评价查看详情
export const getMemberInfoReview = (id: number | string) => {
  return request({
    url: `/member/evaluation/get/${id}`,
    method: Method.GET,
    needToken: true,
  });
};
// 回复评价信息
export const replyMemberReview = (id: number | string, params: any) => {
  return request({
    url: `/member/evaluation/reply/${id}`,
    method: Method.PUT,
    needToken: true,
    data: qs.stringify(params)
  });
};
