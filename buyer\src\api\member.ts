// eslint-disable-next-line import/no-cycle
import request, { Method, commonUrl } from '@/utils/axios';
import { ParamsRule } from '@/types/global';

// 查询账户余额
export function getMembersWallet () {
  return request({
    url: '/wallet/wallet',
    method: Method.GET,
    needToken: true
  });
}

// 查询余额列表
export function getDepositLog (params: ParamsRule) {
  return request({
    url: '/wallet/log',
    method: Method.GET,
    needToken: true,
    params
  });
}

// 查询充值记录
export function getRecharge (params: ParamsRule) {
  return request({
    url: '/wallet/recharge',
    method: Method.GET,
    needToken: true,
    params
  });
}

// 查询提现记录
export function getWithdrawApply (params: ParamsRule) {
  return request({
    url: '/wallet/withdrawApply',
    method: Method.GET,
    needToken: true,
    params
  });
}

// 在线充值
export function recharge (params: any) {
  return request({
    url: '/trade/recharge',
    method: Method.POST,
    needToken: true,
    data: params
  });
}

// 提现
export function withdrawalApply (params: any) {
  return request({
    url: '/wallet/wallet/withdrawal',
    method: Method.POST,
    needToken: true,
    data: params
  });
}

// 收藏商品
export function collectGoods (type: number | string, id: number | string) {
  return request({
    url: `/member/collection/add/${type}/${id}`,
    method: Method.POST,
    needToken: true
  });
}

// 收藏店铺
export function collectStore (type: number | string, id: number | string) {
  return request({
    url: `/member/collection/add/${type}/${id}`,
    method: Method.POST,
    needToken: true
  });
}

// 取消 收藏商品
export function cancelCollect (type: number | string, id: number | string) {
  return request({
    url: `/member/collection/delete/${type}/${id}`,
    method: Method.DELETE,
    needToken: true
  });
}

// 取消 收藏店铺
export function cancelStoreCollect (type: number | string, id: number | string) {
  return request({
    url: `/member/collection/delete/${type}/${id}`,
    method: Method.DELETE,
    needToken: true
  });
}

// 查看是否收藏商品
export function isCollection (type: number | string, goodsId: number | string) {
  return request({
    url: `/member/collection/isCollection/${type}/${goodsId}`,
    method: Method.GET,
    needToken: true
  });
}

// 查看是否收藏店铺
export function isStoreCollection (type: number | string, goodsId: any) {
  return request({
    url: `/member/collection/isCollection/${type}/${goodsId}`,
    method: Method.GET,
    needToken: true
  });
}

// 会员收藏商品列表
export function collectList (params: ParamsRule) {
  return request({
    url: `/member/collection/${params.type}`,
    method: Method.GET,
    needToken: true,
    params
  });
}

// 会员收藏店铺列表
export function storeCollectList (params: ParamsRule) {
  return request({
    url: `/member/collection/${params.type}`,
    method: Method.GET,
    needToken: true,
    params
  });
}

// 单个商品评价
export function goodsComment (params: ParamsRule) {
  return request({
    url: `/member/evaluation/${params.goodsId}/goodsEvaluation`,
    method: Method.GET,
    needToken: false,
    params
  });
}

// 商品各评价类别数量
export function goodsCommentNum (goodsId: number | string) {
  return request({
    url: `/member/evaluation/${goodsId}/evaluationNumber`,
    method: Method.GET,
    needToken: false
  });
}

// 添加会员评价
export function addEvaluation (params: any) {
  return request({
    url: `/member/evaluation`,
    method: Method.POST,
    needToken: true,
    params
  });
}

// 会员评价详情
export function evaluationDetail (id: any) {
  return request({
    url: `/member/evaluation/get/${id}`,
    method: Method.GET,
    needToken: true
  });
}

// 发票分页列表
export function receiptList () {
  return request({
    url: `/trade/receipt`,
    method: Method.GET,
    needToken: true
  });
}

// 保存发票信息
export function saveReceipt (params: ParamsRule) {
  return request({
    url: `/trade/receipt`,
    method: Method.POST,
    needToken: true,
    params
  });
}

// 获取可领取优惠券列表
export function couponList (params: any) {
  return request({
    url: `/promotion/coupon`,
    method: Method.GET,
    needToken: true,
    params
  });
}

// 会员优惠券列表
export function memberCouponList (params: ParamsRule) {
  return request({
    url: `/promotion/coupon/getCoupons`,
    method: Method.GET,
    needToken: true,
    params
  });
}

// 会员优惠券列表
export function canUseCouponList (params: ParamsRule) {
  return request({
    url: `/promotion/coupon/canUse`,
    method: Method.GET,
    needToken: true,
    params
  });
}

// 领取优惠券
export function receiveCoupon (couponId: number | string) {
  return request({
    url: `/promotion/coupon/receive/${couponId}`,
    method: Method.GET,
    needToken: true
  });
}

// 获取申请售后列表
export function afterSaleList (params: ParamsRule) {
  return request({
    url: `/order/afterSale/page`,
    method: Method.GET,
    needToken: true,
    params
  });
}

// 获取申请售后页面信息
export function afterSaleInfo (sn: number | string) {
  return request({
    url: `/order/afterSale/applyAfterSaleInfo/${sn}`,
    method: Method.GET,
    needToken: true
  });
}

// 获取申请售后、投诉原因
export function afterSaleReason (serviceType: number | string) {
  return request({
    url: `/order/afterSale/get/afterSaleReason/${serviceType}`,
    method: Method.GET,
    needToken: true
  });
}
// 获取申请售后详情
export function afterSaleDetail (sn: any) {
  return request({
    url: `/order/afterSale/get/${sn}`,
    method: Method.GET,
    needToken: true
  });
}
// 售后日志
export function afterSaleLog (sn: any) {
  return request({
    url: `/order/afterSale/get/getAfterSaleLog/${sn}`,
    method: Method.GET,
    needToken: true
  });
}

// 申请售后
export function applyAfterSale (params: ParamsRule) {
  return request({
    url: `/order/afterSale/save/${params.orderItemSn}`,
    method: Method.POST,
    needToken: true,
    params
  });
}

// 取消售后申请
export function cancelAfterSale (afterSaleSn: string | number) {
  return request({
    url: `/order/afterSale/cancel/${afterSaleSn}`,
    method: Method.POST,
    needToken: true
  });
}

// 投诉商品
export function handleComplain (data: any) {
  return request({
    url: `/order/complain`,
    method: Method.POST,
    needToken: true,
    data
  });
}
// 分页获取我的投诉列表
export function complainList (params: ParamsRule) {
  return request({
    url: `/order/complain`,
    method: Method.GET,
    needToken: true,
    params
  });
}

/**
 * 获取投诉详情
 */
export function getComplainDetail (id: any) {
  return request({
    url: `/order/complain/${id}`,
    method: Method.GET,
    needToken: true
  });
}

/**
 * 取消投诉
 */
export function clearComplain (id: number | string) {
  return request({
    url: `/order/complain/status/${id}`,
    method: Method.PUT,
    needToken: true
  });
}

/**
 * 我的足迹
 * @param {Number} pageNumber 页码
 * @param {Number} pageSize 页数
 */
export function tracksList (params: ParamsRule) {
  return request({
    url: `/member/footprint`,
    method: Method.GET,
    needToken: true,
    params
  });
}

/**
 * 清空足迹
 */
export function clearTracks () {
  return request({
    url: `/member/footprint`,
    method: Method.DELETE,
    needToken: true
  });
}

/**
 * 根据id删除足迹
 * @param {String} ids id集合
 */
export function clearTracksById (ids: number | string) {
  return request({
    url: `/member/footprint/delByIds/${ids}`,
    method: Method.DELETE,
    needToken: true
  });
}

/**
 * 获取会员积分
 */
export function memberPoint (params?: ParamsRule) {
  return request({
    url: `/member/memberPointsHistory/getMemberPointsHistoryVO`,
    method: Method.GET,
    needToken: true,
    params
  });
}

/**
 * 积分历史
 */
export function memberPointHistory (params: ParamsRule) {
  return request({
    url: `/member/memberPointsHistory/getByPage`,
    method: Method.GET,
    needToken: true,
    params
  });
}
/**
 * 分页获取会员站内信
 * @param {Object} params 请求参数，包括pageNumber、pageSize、status
 */
export function memberMsgList (params: ParamsRule) {
  return request({
    method: "get",
    url: `${commonUrl}/common/message`,
    params: params,
    needToken: true,
  });
}
/**
 * 设置消息为已读
 * @param {String} messageId 消息id
 */

export function readMemberMsg (id: number | string) {
  return request({
    method: "put",
    url: `${commonUrl}/common/message/${id}`,
    needToken: true,
  });
}
/**
 * 删除会员消息
 * @param {String} messageId 消息id
 */
export function delMemberMsg (id: number | string) {
  return request({
    method: "delete",
    url: `${commonUrl}/common/message/${id}`,
    needToken: true,
  });
}
