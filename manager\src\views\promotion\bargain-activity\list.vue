<template>
  <a-card class="general-card" title="砍价活动" :bordered="false">
    <a-tabs @change="(val) => { apiParams.promotionStatus = val }" :default-active-key="bargainStatus">
      <a-tab-pane key="NEW" title="未开始"></a-tab-pane>
      <a-tab-pane key="START" title="已开始"></a-tab-pane>
      <a-tab-pane key="END" title="已结束"></a-tab-pane>
      <a-tab-pane key="CLOSE" title="已关闭"></a-tab-pane>
    </a-tabs>
    <searchTable :columns="columnsSearch" time-type="timestamp"
      @reset="(val) => { apiParams = { ...apiParams, ...val } }"
      @search="(val) => { apiParams = { ...apiParams, ...val } }">
    </searchTable>
    <a-row style="margin-bottom: 16px">
      <a-col :span="16">
        <a-space>
          <a-button type="primary" @click="add">添加砍价</a-button>
        </a-space>
      </a-col>
    </a-row>
    <tablePage ref="tablePageRef" :columns="columnsTable" :methods="sortMethods" :api="getKanJiaGoodsList"
      :api-params="apiParams" @delete="handleDelete" :bordered="true">
      <template #price="{ data }">{{
        data.price
          ? unitPrice(data.price, '¥')
          : (data.couponDiscount || 0) + '折'
      }}</template>

      <template #time="{ data }">{{ data.startTime }}<span style="margin: 0 10px">-</span>{{ data.endTime }}</template>
      <template #action="{ data }">
        <a-button v-if="data.promotionStatus === 'CLOSE' || data.promotionStatus === 'NEW'" type="text" status="warning"
          @click="edit(data)" style="margin-right: 10px;">编辑</a-button>
        <a-button type="text" status="success" v-else @click="edit(data, 'onlyView')"
          style="margin-right: 10px;">查看</a-button>
        <a-button type="text" status="danger" @click="handleDelete(data)">删除</a-button>
      </template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
import { delKanJiaGoods, getKanJiaGoodsList } from '@/api/promotion';
import searchTable from '@/components/search-column/index.vue';
import tablePage from '@/components/table-pages/index.vue';
import useCurrentInstance from '@/hooks/useCurrentInstance';
import { ColumnsDataRule, MethodsRule, SearchRule } from '@/types/global';
import { unitPrice } from '@/utils/filters';
import { promotionStatusSelect, promotionsStatusRender } from '@/utils/tools';
import { Message } from '@arco-design/web-vue';
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter()
const tablePageRef = ref<any>();
const bargainStatus = ref<string>('START');
const apiParams = ref<any>({ sort: 'startTime', promotionStatus: bargainStatus.value });
// 获取modal
const modal = useCurrentInstance().globalProperties?.$modal;
const columnsSearch: Array<SearchRule> = [
  {
    label: '商品名称',
    model: 'goodsName',
    disabled: false,
    input: true,
  },
  {
    label: '活动时间',
    model: 'selectDate',
    disabled: false,
    datePicker: {
      type: 'range',
    },
  },
];

const columnsTable: ColumnsDataRule[] = [
  {
    title: '商品名称',
    dataIndex: 'goodsName',
    slot: true,
    width: 300,
    slotData: {
      goods: {
        goodsImage: 'thumbnail',
        goodsName: 'goodsName',
      },
    },
  },
  {
    title: '库存数量',
    dataIndex: 'stock',
    width: 100,
  },

  {
    title: '剩余活动库存',
    dataIndex: 'stock',
    width: 100,
    // slot: true,
    // slotTemplate: 'price',
  },
  {
    title: '每人最低砍',
    dataIndex: 'lowestPrice',
    width: 150,
    currency: true,
  },
  {
    title: '每人最高砍',
    dataIndex: 'highestPrice',
    width: 150,
    currency: true,
  },
  {
    title: '结算价格',
    dataIndex: 'settlementPrice',
    width: 100,
    currency: true,
  },
  {
    title: '活动时间',
    width: 400,
    ellipsis: false,
    dataIndex: 'startTime',
    slot: true,
    slotTemplate: 'time',
  },
  {
    title: '状态',
    dataIndex: 'promotionStatus',
    width: 100,
    slot: true,
    slotData: {
      badge: promotionsStatusRender,
    },
  },
];

// todo 本页面操作列表为选择展示
const sortMethods: MethodsRule = {
  title: '操作',
  width: 260,
  methods: [
    {
      title: '操作',
      callback: 'action',
      slot: true,
      slotTemplate: 'action'
    },
  ],
};

// 回调删除
function handleDelete(data: any) {
  modal.confirm({
    title: '确认删除',
    content: `确认需要删除此砍价商品?`,
    alignCenter: false,
    onOk: async () => {
      const res = await delKanJiaGoods(data.id);
      if (res.data.success) {
        Message.success('删除成功');
        tablePageRef.value.init();
      }
    },
  });
}
// 添加砍价
const add = () => {
  router.push({ name: 'add-kanJia-activity-goods' })
}
// 编辑
const edit = (val: any, type?: string) => {
  const data = {
    id: val.id,
    onlyView: 'false'
  }
  type ? data.onlyView = 'true' : 'false'
  router.push({ name: 'edit-kanJia-activity-goods', query: data })
}

</script>
