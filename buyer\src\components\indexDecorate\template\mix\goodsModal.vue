<script setup lang="ts">
import { useRouter } from 'vue-router'
import { navigateTo } from '../navigate.ts'
import type { DragRule } from '@/views/operation/decoration/app/models/types'
import { unitPrice } from '@/utils/filters'

const props = defineProps<{
  res: DragRule
}>()
const router = useRouter()
function handleClickItem(item: any) {
  const path = navigateTo(item)

  window.open(router.resolve(path).href, '_blank')
}

function handleClickGoods(item: type) {
  const path = {
    path: '/goodsDetail',
    query: {
      skuId: item.id,
      goodsId: item.goodsId,
    },
  }

  window.open(router.resolve(path).href, '_blank')
}
</script>

<template>
  <div v-if="props.res" h-343px w-584px flex rounded-10px bg-white class="box-shadow">
    <div class="left-side" @click="handleClickItem(props!.res!.data!.list![0])">
      <img :src="props!.res!.data!.list![0].img">
    </div>
    <div w-387px>
      <div flex justify-end flex-a-c>
        <div v-if="props!.res!.data!.text" class="badge">
          {{ props.res.data.text }}
        </div>
      </div>
      <div v-if="props.res.data.goodsList.length" class="goods-list">
        <div v-for="(item, index) in props.res.data.goodsList" :key="index" class="goods-item"
          @click="handleClickGoods">
          <div>
            <img h-90px w-90px :src="item.img || item.thumbnail">
          </div>
          <div>
            <div class="goods-title" line-clamp-2>
              {{ item.title }}
            </div>
            <div class="goods-price">
              ￥{{ unitPrice(item.price) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.left-side {
  overflow: hidden;
  width: 196.7px;
  height: 343.7px;

  >img {
    width: 100%;
    height: 100%;
  }
}

.badge {
  min-width: 50px;
  padding: 0 10px;
  height: 27px;
  line-height: 27px;
  border-radius: 13.3px 0 0 13.3px;
  opacity: 1;
  background: #f31947;
  font-size: 12.6px;
  font-weight: 400;
  text-align: center;
  letter-spacing: 0;
  color: #fff;
  margin-top: 26px;
  margin-bottom: 17px;
}

.goods-list {
  flex-wrap: wrap;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 0 16px;
  display: flex;
}

.goods-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 173.6px;
  height: 119px;
  border-radius: 9.8px;
  opacity: 1;
  -webkit-transition: 0.35s;
  transition: 0.35s;
  background: #fff;
  margin-bottom: 9px;
}

.goods-title {
  font-size: 13px;
  font-weight: 400;
  line-height: 16px;
  text-align: center;
  letter-spacing: 0;
  color: #333;
}

.goods-price {
  font-size: 14px;
  font-weight: 400;
  line-height: 17px;
  letter-spacing: 0;
  color: #f31947;
  margin-top: 8px;
  margin-bottom: 10px;
}
</style>
