<template>
  <div>
    <a-form ref="thirdFormRef" :model="thirdForm" @submit="next" auto-label-width layout="horizontal">
      <h4>基础信息</h4>
      <div :style="{ width: '500px', paddingLeft: '50px'}" >
        <a-form-item field="storeName" label="店铺名称" :rules="[REQUIRED]">
          <a-input v-model="thirdForm.storeName" placeholder="请填写店铺名称"></a-input>
        </a-form-item>
        <a-form-item field="storeLogo" label="店铺logo" :rules="[REQUIRED]">
          <div>
            <a-upload list-type="picture-card" :action="uploadFile" :headers="{ accessToken: accessToken }" image-preview :limit="1"
                      :file-list="thirdForm.storeLogo" @success="storeLogoSuccess" @before-remove="storeLogoRemove" @before-upload="beforeUpload"></a-upload>
            <div class="describe">请压缩图片在2M以内，格式为gif，jpg，png</div>
          </div>
        </a-form-item>
        <a-form-item field="goodsManagementCategory" label="店铺经营类目" :rules="[REQUIRED]">
          <a-select :style="{width:'320px'}" placeholder="" v-model="thirdForm.goodsManagementCategory" multiple>
            <a-option v-for="item in categoryList" :value="item.id" :key="item.id">{{item.name}}</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="storeAddressIdPath" label="店铺所在地" :rules="[REQUIRED]">
          <span>{{ thirdForm.storeAddressPath || '暂无地址' }}</span>
          <a-button @click="openAddress()" size="mini" type="outline" status="danger" class="ml_10">选择</a-button>
        </a-form-item>
        <a-form-item field="storeAddressDetail" label="店铺详细地址" :rules="[REQUIRED]">
          <a-input v-model="thirdForm.storeAddressDetail" placeholder="请填写店铺详细地址"></a-input>
        </a-form-item>
        <a-form-item field="storeDesc" label="店铺简介" :rules="[REQUIRED]">
          <a-textarea v-model="thirdForm.storeDesc" :max-length="200" allow-clear show-word-limit :auto-size="{minRows:4,maxRows:6}" placeholder="请输入店铺简介"></a-textarea>
        </a-form-item>
        <a-form-item field="" label="">
          <a-button class="mr_10" @click="emit('handleChange', 2)">返回</a-button>
          <a-button html-type="submit" type="primary" status="danger" :loading="loading">提交平台审核</a-button>
        </a-form-item>
      </div>
    </a-form>

    <MultipleMap ref="multipleMap" @callback="addressCallback"></MultipleMap>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { REQUIRED } from '@/utils/validator';
  import { applyThird } from '@/api/shopentry';
  import { getCategory } from '@/api/goods';
  import uploadFile from '@/api/common';
  import { accessToken, beforeUpload } from '@/utils/upload';

  const props = defineProps({
    content: {
      content: Object,
      default: () => {return {storeLogo: '', goodsManagementCategory: ''}}
    },
  });
  const emit = defineEmits(['handleChange']);
  const loading = ref(false);
  const thirdFormRef = ref();
  const thirdForm = ref<any>({ storeLogo: [] });
  const categoryList = ref<any>([]); // 分类数据
  const multipleMap = ref();

  // 上传图片--店铺logo
  const storeLogoSuccess = (res: any, file: any) => {
    thirdForm.value.storeLogo.push({url: res.response.result});
  };
  // 删除图片----店铺logo
  const storeLogoRemove = (file: any) => {
    thirdForm.value.storeLogo = thirdForm.value.storeLogo.filter((i: any) => i.url !== file.url);
  };
  // 选择店铺所在地
  const openAddress = () => {
    if (multipleMap.value) {
      multipleMap.value.open();
    }
  };
  // 选择地址回调
  const addressCallback = (val: any) => {
    if(val.type === 'select'){
      const paths = val.data.map((item: any) => item.name).join(',');
      const ids = val.data.map((item: any) => item.id).join(',');
      thirdForm.value.storeAddressIdPath = ids;
      thirdForm.value.storeAddressPath = paths;
      thirdForm.value.storeCenter = val.data[val.data.length - 1].center;
    }
  };

  // 获取商品分类
  const getCategoryList = () => {
    getCategory(0).then((res) => {
      if (res.data.success) categoryList.value = res.data.result;
    });
  };
  // 提交平台审核
  const next = async () => {
    const auth = await thirdFormRef.value?.validate();
    if (!auth) {
      loading.value = true;
      let params = JSON.parse(JSON.stringify(thirdForm.value));
      params.storeLogo = thirdForm.value.storeLogo.map((item: any) => item.url).toString();
      params.goodsManagementCategory = thirdForm.value.goodsManagementCategory.toString();
      applyThird(params).then((res) => {
        loading.value = false;
        if (res.data.success) emit('handleChange', 4);
      }).catch(() => {
        loading.value = false;
      });
    }
  };

  onMounted(() => {
    getCategoryList();
    if (props.content) {
      thirdForm.value = JSON.parse(JSON.stringify(props.content));
      if (thirdForm.value.storeLogo) {
        let params = props.content.storeLogo.split(',');
        thirdForm.value.storeLogo = params.map((item: any) => {return {url: item}});

      } else {
        thirdForm.value.storeLogo = [];
      }
      if (props.content.goodsManagementCategory) {
        thirdForm.value.goodsManagementCategory = props.content.goodsManagementCategory.split(',');
      }
    }
  })
</script>

<style scoped lang="less">
  h4 {
    margin-bottom: 10px;
    padding: 0 10px;
    border: 1px solid #ddd;
    background-color: #f8f8f8;
    font-weight: bold;
    color: #333;
    font-size: 14px;
    line-height: 40px;
    text-align: left;
  }
  .describe {
    font-size: 12px;
    color: #999;
    margin: 8px 0;
  }
</style>
