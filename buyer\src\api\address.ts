// eslint-disable-next-line import/no-cycle
import request, { Method, commonUrl } from '@/utils/axios';
import { ParamsRule } from '@/types/global';


// 会员收货地址列表
export function memberAddress () {
  return request({
    url: '/member/address',
    needToken: true,
    method: Method.GET
  });
}
// 会员收货地址列表(新增)
export function memberAddressList () {
  return request({
    url: '/member/address/list',
    needToken: true,
    method: Method.GET
  });
}

// 添加收货地址
export function newMemberAddress (params: ParamsRule) {
  return request({
    url: '/member/address',
    needToken: true,
    method: Method.POST,
    data: params
  });
}

// 编辑收货地址
export function editMemberAddress (params: ParamsRule) {
  return request({
    url: '/member/address',
    needToken: true,
    method: Method.PUT,
    params
  });
}

// 删除收货地址
export function delMemberAddress (id: number | string) {
  return request({
    url: `/member/address/delById/${id}`,
    needToken: true,
    method: Method.DELETE
  });
}

// 根据id获取会员地址详情
export function getAddrDetail (id: number | string) {
  return request({
    url: `/member/address/get/${id}`,
    needToken: true,
    method: Method.GET
  });
}

// 传给后台citycode 获取城市街道等id
export function handleRegion (params: ParamsRule) {
  return request({
    url: `${commonUrl}/common/common/region/region`,
    needToken: true,
    method: Method.GET,
    params
  });
}

