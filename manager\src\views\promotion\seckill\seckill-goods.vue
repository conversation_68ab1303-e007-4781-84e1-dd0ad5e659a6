<template>
  <div class="seckill-goods">
    <a-card :bordered="false">
      <tablePage :columns="columns" :dataList="data" :enablePagination="false" :bordered="true">
        <template #hours="{ data }">
          <a-tag v-for="item in data.hours " :key="item">{{ `${item}:00` }}</a-tag>
        </template>
        <template #promotionStatuS="{ data }">
          <!-- START -->
          <span v-if="data.promotionStatus == 'NEW'"> <a-badge status="success" style="margin-right: 10px;" />新建</span>
          <span v-if="data.promotionStatus == 'success'"> <a-badge status="danger"
              style="margin-right: 10px;" />开始</span>
          <span v-if="data.promotionStatus == 'END'"> <a-badge status="danger" style="margin-right: 10px;" />结束</span>
          <span v-if="data.promotionStatus == 'CLOSE'"> <a-badge status="danger" style="margin-right: 10px;" />废弃</span>
        </template>
      </tablePage>
      <tablePage :columns="goodsColumns" :api="seckillGoodsList" :apiParams="apiParams" ref="tablePageRef" style="margin-top: 20px;" :bordered="true">
        <template #time="{ data }">
          <a-tag>{{ `${data.timeLine}:00` }}</a-tag>
        </template>
        <template #action="{ data, rowIndex }">
          <a-button type="text" status="danger" @click="delGoods(data, rowIndex)">删除</a-button>
        </template>
      </tablePage>
    </a-card>
  </div>
</template>

<script setup lang='ts'>
import { ref, onMounted } from "vue"
import { seckillDetail, seckillGoodsList, delSeckillGoods } from '@/api/promotion';
import tablePage from '@/components/table-pages/index.vue';
import { useRoute } from 'vue-router';
import { ColumnsDataRule } from '@/types/global';
import {
  promotionStatus,
} from '@/utils/tools';
import useCurrentInstance from '@/hooks/useCurrentInstance';
import { Message } from '@arco-design/web-vue';

const tablePageRef = ref<any>();
//  获取modal
const modal = useCurrentInstance().globalProperties?.$modal;
const route = useRoute()
// 传递的参数
const apiParams = {
  seckillId: route.query.id
};
const data = ref<any>([])
const columns: ColumnsDataRule[] = [
  {
    title: '活动名称',
    dataIndex: 'promotionName',
  },
  {
    title: '活动开始时间',
    dataIndex: 'startTime',
  },
  {
    title: '报名截止时间',
    dataIndex: 'applyEndTime',
  },
  {
    title: "时间场次",
    dataIndex: 'hours',
    slot: true,
    slotTemplate: 'hours'

  },
  {
    title: '活动状态',
    dataIndex: 'promotionStatuS',
    slot: true,
    slotTemplate: 'promotionStatuS'
  },
]
const goodsColumns: ColumnsDataRule[] = [
  {
    title: '商品名称',
    dataIndex: 'goodsName',
  },
  {
    title: '商品价格',
    dataIndex: 'originalPrice',
    currency: true,
  },
  {
    title: '库存',
    dataIndex: 'quantity',
  },
  {
    title: '活动价格',
    dataIndex: 'price',
    currency: true,
  },
  {
    title: '商家名称',
    dataIndex: 'storeName',
  },
  {
    title: '活动场次',
    dataIndex: 'time',
    slot: true,
    slotTemplate: 'time'
  },
  {
    title: '操作',
    dataIndex: 'action',
    slot: true,
    slotTemplate: 'action'
  },
]
const getSeckillMsg = () => {
  seckillDetail(route.query.id).then((res: any) => {
    if (res.data.result) {
      data.value = []
      data.value.push({
        ...res.data.result,
        hours: res.data.result.hours.split(',')
      })
    }
  })
}
const init = () => {
  getSeckillMsg()
}
onMounted(() => {
  init()
})
// 删除
const delGoods = (row: any, index: any) => {
  modal.confirm({
    title: '确认删除',
    content: '您确认要删除该商品吗?删除后不可恢复',
    alignCenter: false,
    onOk: async () => {
      const params = {
        seckillId: row.seckillId,
        id: row.id,
      };
      const res = await delSeckillGoods(params);
      if (res.data.success) {
        Message.success('删除成功！');
        tablePageRef.value.init();
      }
    },
  });
}
</script>

<style lang="less" scoped>
:deep(.arco-tag-size-medium) {
  margin-right: 10px;
}

.operation {
  margin: 10px 0;
}

.reason {
  cursor: pointer;
  color: #2d8cf0;
  font-size: 12px;
}
</style>
