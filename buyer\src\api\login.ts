import qs from 'query-string';
// eslint-disable-next-line import/no-cycle
import request, { Method, gatewayUrl } from '@/utils/axios';
import { ParamsRule } from '@/types/global';

/**
 * 刷新token
 */
export function refreshTokenMethod(token: string) {
  return request({
    url: `/passport/login/refresh/${token}`,
    method: Method.GET,
    params: token,
    needToken: false,
  });
}

/**
 * 账号密码登录
 * */
export function login (params: any) {
  return request({
    url: '/passport/member/userLogin',
    method: Method.POST,
    needToken: false,
    data: params,
    headers: {
      'clientType': 'PC',
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 * 手机号验证码登录
 */
export function smsLogin (params: any) {
  return request({
    url: '/passport/member/smsLogin',
    method: Method.POST,
    needToken: false,
    data: params,
    headers: { 'clientType': 'PC' }
  });
}

/**
 * 获取用户信息
 * */
export function getMemberMsg() {
  return request({
    url: '/passport/member',
    method: Method.GET,
    needToken: true
  });
}

/**
 * 第三方登录 支付宝，微博，qq,微信
 */
export function webLogin (type: string | number) {
  window.open(`${gatewayUrl}/passport/connect/connect/login/web/${type}`, 'blank');
}

/**
 * 注册
 */
export function regist (params: ParamsRule) {
  return request({
    url: '/passport/member/register',
    method: Method.POST,
    needToken: false,
    data: params
  });
}

/**
 * 忘记密码  验证手机验证码
 */
export function validateCode (params: any) {
  return request({
    url: `/passport/member/resetByMobile`,
    method: Method.POST,
    needToken: false,
    params
  });
}

/**
 * 忘记密码 重置密码
 */
export function resetPassword (params: ParamsRule) {
  return request({
    url: `/passport/member/resetPassword`,
    method: Method.POST,
    needToken: false,
    params
  });
}

/**
 * 第三方登录成功 回调接口
 */
export function loginCallback (uuid: any) {
  return request({
    url: `/passport/connect/connect/result?state=${uuid}`,
    method: Method.GET,
    needToken: false
  });
}

export function getSCLoginCode(params: ParamsRule) {
  return request({
    url: `/passport/member/pc_session`,
    method: Method.POST,
    needToken: false,
    params
  });
}
export function sCLogin(token: string | number, params: ParamsRule) {
  return request({
    url: `/passport/member/session_login/`+token,
    method: Method.POST,
    needToken: false,
    params
  });
}
