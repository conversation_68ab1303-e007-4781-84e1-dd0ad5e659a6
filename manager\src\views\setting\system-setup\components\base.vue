<template>
  <div style="background-color: #fff">
    <a-form ref="formRef" style="padding: 30px" :model="form.form" layout="horizontal" auto-label-width>
      <a-divider orientation="left">基础设置</a-divider>

      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item field="siteName" label="站点名称" :validate-trigger="['change']">
            <a-input v-model="form.form.siteName" allow-clear />
          </a-form-item>

          <a-form-item field="icp" label="icp" :validate-trigger="['change']">
            <a-input v-model="form.form.icp" allow-clear />
          </a-form-item>

          <a-form-item field="staticPageAddress" label="站点地址" :validate-trigger="['change']">
            <a-input v-model="form.form.staticPageAddress" allow-clear />
          </a-form-item>

          <a-form-item field="staticPageWapAddress" label="wap站点地址" :validate-trigger="['change']">
            <a-input v-model="form.form.staticPageWapAddress" allow-clear />
          </a-form-item>

          <a-form-item field="domainLogo" label="运营后台LOGO" :rules="[REQUIRED]" :validate-trigger="['change']"
            validate-phone>
            <a-input v-model="form.form.domainLogo" />
            <a-tooltip>
              <a-button><icon-eye /></a-button>
              <template #content>
                <div>
                  <img :src="form.form.domainLogo" alt="该资源不存在" style="width: 100%; margin: 0 auto" />
                  <a style="margin-top: 5px; text-align: right; cursor: pointer">查看大图</a>
                </div>
              </template>
            </a-tooltip>
            <a-button type="primary" @click="hanlderUpload('domainLogo')">上传图片</a-button>
          </a-form-item>

          <a-form-item field="storeSideLogo" label="店铺后台Logo" :rules="[REQUIRED]" :validate-trigger="['change']"
            validate-phone>
            <a-input v-model="form.form.storeSideLogo" />
            <a-tooltip>
              <a-button><icon-eye /></a-button>
              <template #content>
                <div>
                  <img :src="form.form.storeSideLogo" alt="该资源不存在" style="width: 100%; margin: 0 auto" />
                  <a style="margin-top: 5px; text-align: right; cursor: pointer">查看大图</a>
                </div>
              </template>
            </a-tooltip>
            <a-button type="primary" @click="hanlderUpload('storeSideLogo')">上传图片</a-button>
          </a-form-item>

          <a-form-item field="buyerSideLogo" label="用户端Logo" :rules="[REQUIRED]" :validate-trigger="['change']"
            validate-phone>
            <a-input v-model="form.form.buyerSideLogo" />
            <a-tooltip>
              <a-button><icon-eye /></a-button>
              <template #content>
                <div>
                  <img :src="form.form.buyerSideLogo" alt="该资源不存在" style="width: 100%; margin: 0 auto" />
                  <a style="margin-top: 5px; text-align: right; cursor: pointer">查看大图</a>
                </div>
              </template>
            </a-tooltip>
            <a-button type="primary" @click="hanlderUpload('buyerSideLogo')">上传图片</a-button>
          </a-form-item>

          <a-form-item field="domainIcon" label="运营后台Icon" :rules="[REQUIRED]" :validate-trigger="['change']"
            validate-phone>
            <a-input v-model="form.form.domainIcon" />
            <a-tooltip>
              <a-button><icon-eye /></a-button>
              <template #content>
                <div>
                  <img :src="form.form.domainIcon" alt="该资源不存在" style="width: 100%; margin: 0 auto" />
                  <a style="margin-top: 5px; text-align: right; cursor: pointer">查看大图</a>
                </div>
              </template>
            </a-tooltip>
            <a-button type="primary" @click="hanlderUpload('domainIcon')">上传图片</a-button>
          </a-form-item>

          <a-form-item field="storeSideIcon" label="店铺后台Icon" :rules="[REQUIRED]" :validate-trigger="['change']"
            validate-phone>
            <a-input v-model="form.form.storeSideIcon" />
            <a-tooltip>
              <a-button><icon-eye /></a-button>
              <template #content>
                <div>
                  <img :src="form.form.storeSideIcon" alt="该资源不存在" style="width: 100%; margin: 0 auto" />
                  <a style="margin-top: 5px; text-align: right; cursor: pointer">查看大图</a>
                </div>
              </template>
            </a-tooltip>
            <a-button type="primary" @click="hanlderUpload('storeSideIcon')">上传图片</a-button>
          </a-form-item>

          <a-form-item field="buyerSideIcon" label="用户端Icon" :rules="[REQUIRED]" :validate-trigger="['change']"
            validate-phone>
            <a-input v-model="form.form.buyerSideIcon" />
            <a-tooltip>
              <a-button><icon-eye /></a-button>
              <template #content>
                <div>
                  <img :src="form.form.buyerSideIcon" alt="该资源不存在" style="width: 100%; margin: 0 auto" />
                  <a style="margin-top: 5px; text-align: right; cursor: pointer">查看大图</a>
                </div>
              </template>
            </a-tooltip>
            <a-button type="primary" @click="hanlderUpload('buyerSideIcon')">上传图片</a-button>
          </a-form-item>

          <a-form-item>
            <div>
              <a-alert mb-20px>
                修改店铺端Logo以及Icon信息会有1个小时的缓存时间，默认情况下需要您清空浏览器缓存才能及时看到修改后的效果。
              </a-alert>
              
            </div>
          </a-form-item>

          <a-form-item field="specialId" label="默认微页面ID" :validate-trigger="['change']">
            <a-input v-model="form.form.specialId" allow-clear />
          </a-form-item>

          <a-form-item field="storeId" label="默认店铺ID" :validate-trigger="['change']">
            <a-input v-model="form.form.storeId" allow-clear />
          </a-form-item>

          <a-form-item field="voucherSwitch" label="是否开启充值中心">
            <a-radio-group type="button" v-model="form.form.voucherSwitch">
              <a-radio value="true">开启</a-radio>
              <a-radio value="false">关闭</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item field="closeImg" label="广告图" :rules="[REQUIRED]" :validate-trigger="['change']"
            validate-phone v-if="form.form.voucherSwitch == 'false'">
            <a-input v-model="form.form.closeImg" />
            <a-tooltip>
              <a-button><icon-eye /></a-button>
              <template #content>
                <div>
                  <img :src="form.form.closeImg" alt="该资源不存在" style="width: 100%; margin: 0 auto" />
                  <a style="margin-top: 5px; text-align: right; cursor: pointer">查看大图</a>
                </div>
              </template>
            </a-tooltip>
            <a-button type="primary" @click="hanlderUpload('closeImg')">上传图片</a-button>
          </a-form-item>

          <a-button type="primary" @click="handleSubmit">保存</a-button>
        </a-col>
      </a-row>
    </a-form>

    <a-modal v-model:visible="showOssManager" :width="1100" @ok="handleOss" title="oss资源管理"
      @cancel="showOssManager = false">
      <ossManages @selected="changOssImage" :isSingle="true"></ossManages>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { getSetting, setSetting } from '@/api/operation';
import { ref, onMounted, reactive, inject } from 'vue';
import useCurrentInstance from '@/hooks/useCurrentInstance';
import { VARCHAR255, REQUIRED, VARCHAR20 } from '@/utils/validator';
import { Message } from '@arco-design/web-vue';
import { FormInstance } from '@arco-design/web-vue/es/form';
import ossManages from '@/components/oss-manage/index.vue';
import { useFavicon } from '@vueuse/core'
const reload: any = inject("reload");
const showOssManager = ref<boolean>(false); // oss弹框
const selectedSku = ref(); // 选择的sku
const currentImgType = ref<any>(); // 当前要上传图片的字段
const formRef = ref<FormInstance>();
// 获取modal
const modal = useCurrentInstance().globalProperties?.$modal;
interface formInterface {
  form: {
    siteName: string;
    staticPageAddress: string;
    staticPageWapAddress: string;
    domainLogo: string;
    storeSideLogo: string;
    buyerSideLogo: string;
    domainIcon: string;
    storeSideIcon: string;
    buyerSideIcon: string;
    [key: string]: any;
  }
}
// 数据集
const form = ref<formInterface>({
  form: {
    siteName: '',
    icp: '',
    staticPageAddress: '',
    staticPageWapAddress: '',
    domainLogo: '',
    storeSideLogo: '',
    buyerSideLogo: '',
    domainIcon: '',
    storeSideIcon: '',
    buyerSideIcon: '',
  }
});
async function init() {
  const res = await getSetting('BASE_SETTING');
  form.value.form = res.data.result;
  console.log('数据集', form.value.form);

}
const handleSubmit = async () => {
  const auth = await formRef.value?.validate();
  if (!auth) {
    const result = await setSetting('BASE_SETTING', form.value.form);
    if (result.data.success) {
      Message.success('设置成功!');
      const icon = useFavicon()
      const title  = useTitle()

      localStorage.setItem("icon", form.value.form.domainLogo);
      localStorage.setItem("domainIcon", form.value.form.domainIcon);
      localStorage.setItem("title", form.value.form.siteName);

      icon.value = form.value.form.domainIcon
      title.value = form.value.form.siteName + " - 运营后台";
      init();
      reload();
    } else {
      Message.error('保存失败！');
    }
  }
};
// 上传图片
const hanlderUpload = (type: any) => {
  // ossvisible.value = true;
  showOssManager.value = true;
  currentImgType.value = type;
};
// oss资源确定
const handleOss = () => {
  showOssManager.value = false;
  form.value.form[currentImgType.value] = selectedSku.value[selectedSku.value.length - 1].url;
};
// oss资源改变
const changOssImage = (val: any) => {
  selectedSku.value = [];
  val.forEach((item: any) => {
    selectedSku.value.push({ url: item.url })
  })
};

onMounted(() => {
  init();
});
</script>
