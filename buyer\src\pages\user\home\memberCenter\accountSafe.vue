<template>
  <div>
    <Card _Title="账户安全" :_Size="16"></Card>

    <a-row class="account-safe">
      <a-col :span="1"></a-col>
      <a-col :span="2">
        <svg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 24 24"><path fill="#ef4444" d="M17 14h-4.341a6 6 0 1 1 0-4H23v4h-2v4h-4zM7 14a2 2 0 1 0 0-4a2 2 0 0 0 0 4"/></svg>
      </a-col>
      <a-col :span="16">
        <div>登录密码</div>
        <div class="global-color mt_10">互联网账号存在被盗风险，建议您定期更改密码以保护账户安全。</div>
      </a-col>
      <a-col :span="4">
        <a-button size="small" type="outline" status="danger" class="mr_10" :loading="loading" @click="toLoginPwd">修改密码</a-button>
      </a-col>
    </a-row>
    <a-row class="account-safe">
      <a-col :span="1"></a-col>
      <a-col :span="2">
        <svg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 24 24"><path fill="#ef4444" d="M17 14h-4.341a6 6 0 1 1 0-4H23v4h-2v4h-4zM7 14a2 2 0 1 0 0-4a2 2 0 0 0 0 4"/></svg>
      </a-col>
      <a-col :span="16">
        <div>支付密码</div>
        <div class="global-color mt_10">设置/修改当前支付密码</div>
      </a-col>
      <a-col :span="4">
        <a-button size="small" type="outline" status="danger" class="mr_10" :loading="loading" @click="toPaymentPwd">{{paymentPwdStatus?'修改密码':'设置密码'}}</a-button>
      </a-col>
    </a-row>


  </div>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router';
  import { ref, onMounted } from 'vue';
  import { checkPassword } from '@/api/account';

  const router = useRouter();
  const loading = ref(false);
  // 是否设置支付密码
  const paymentPwdStatus = ref<any>(false);

  // 校验是否设置支付密码
  const inspectPassword = () => {
    checkPassword().then(res => {
      paymentPwdStatus.value = res.data;
    })
  };

  // 修改支付密码
  const toLoginPwd = () => {
    router.push({ path: '/user/home/<USER>/updatePwdTab', query: {} });
  };
  // 修改/设置支付密码
  const toPaymentPwd = () => {
    router.push({ path: '/user/home/<USER>/paymentPwd', query: {} });
  };


  onMounted(() => {
    inspectPassword();
  })
</script>

<style scoped lang="less">
  .account-safe {
    border-bottom: 1px solid @light_border_color;
    align-items: center;
    padding: 20px 0;
  }
</style>
