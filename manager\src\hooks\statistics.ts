import { reactive } from 'vue';
/**
 * 封装 recent-time 以及 统计部分时重复冗余代码
 * */
interface CallbackRule {
  date: {
    month: string;
    recent: string;
  };
}
export const defaultDateType = reactive<CallbackRule>({
  date: {
    recent: 'LAST_SEVEN',
    month: '',
  },
});

// 回调时间 月份或者最近日期
export const handleClickTimeChange: any = (callback: {
  month: string;
  recent: string;
}) => {
  defaultDateType.date = callback;
};
