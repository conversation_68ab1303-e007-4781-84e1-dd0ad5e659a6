<template>
  <div class="cart-box pb_10">
    <a-spin :loading="loading" style="width: 100%">
      <div class="logo">
        <div>
          <router-link to="/"><img :src="logoImg" /></router-link>
          <div>结算页</div>
        </div>
        <div class="available-area">
          <div class="cart-steps">
            <span :class="stepIndex === 0 ? 'active' : ''">1.我的购物车</span>
            <span :class="stepIndex === 1 ? 'active' : ''">2.填写订单信息</span>
            <span :class="stepIndex === 2 ? 'active' : ''">3.成功提交订单</span>
          </div>
        </div>
      </div>
      <div class="divider mt_10 mb_20"></div>
      <!--结算页主体-->
      <div class="pay-container">
        <!--收货人信息-->
        <div class="card" v-if="selectedDeliverMethod === 'LOGISTICS' && goodsType !== 'VIRTUAL_GOODS'">
          <div class="card-head">
            <span>收货人信息</span>
            <span @click="goAddressManage">管理收货人地址</span>
          </div>
          <div class="card-body">
            <div class="address-manage">
              <div v-for="(item, index) in addressList" v-show="moreAddr?true:index<3" :key="index" :class="selectedAddress.id===item.id?'border-red':''"
                   class="address-item" @click="selectAddress(item)" @mouseenter="showEditBtn = index" @mouseleave="showEditBtn = ''">
                <div>
                  <span>{{ item.name }}</span>
                  <a-tag v-if="item.isDefault" class="ml_10" color="red">默认</a-tag>
                  <a-tag v-if="item.alias" class="ml_10" color="orange">{{ item.alias }}</a-tag>
                </div>
                <div style="margin-bottom: 4px;">{{ item.mobile }}</div>
                <div class="ellipsis ellipsis-2">{{ unitAddress(item.consigneeAddressPath) }} {{ item.detail }}</div>
                <div v-show="showEditBtn === index" class="edit-btn">
                  <span @click.stop="editAddress(item.id)">修改</span>
                  <span v-if="!item.isDefault" class="ml_10" @click.stop="delAddress(item)">删除</span>
                </div>
                <div v-show="selectedAddress.id === item.id" class="corner-icon">
                  <div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" class="icon">
                      <path fill="#ffffff" d="m10 15.17l9.192-9.191l1.414 1.414L10 17.999l-6.364-6.364l1.414-1.414z"/>
                    </svg>
                  </div>
                </div>
              </div>
              <div class="add-address" @click="editAddress('')">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24"><path fill="#999999" d="M13 11h9v2h-9v9h-2v-9H2v-2h9V2h2z"/></svg>
                <div>添加新地址</div>
              </div>
            </div>
            <div v-if="addressList.length > 3" class="more-addr hover-color hover-pointer" @click="moreAddr = !moreAddr">
              {{ moreAddr ? "收起地址" : "更多地址" }}
              <svg v-show="!moreAddr" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="#999999" d="m12 13.171l4.95-4.95l1.414 1.415L12 16L5.636 9.636L7.05 8.222z"/></svg>
              <svg v-show="moreAddr" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="#999999" d="m12 10.828l-4.95 4.95l-1.414-1.414L12 8l6.364 6.364l-1.414 1.414z"/></svg>
            </div>
          </div>
        </div>
        <!--自提点信息-->
        <div class="card" v-if="selectedDeliverMethod === 'SELF_PICK_UP'">
          <div class="card-head">
            <span>自提点信息</span>
          </div>
          <div class="card-body">
            <div class="address-manage">
              <div v-for="(item, index) in storeAddressList" v-show="storeMoreAddr?true:index<4" :key="index" :class="selectedAddress.id===item.id?'border-red':''" class="address-item"
                   @click="selectStoreAddress(item)" @mouseenter="showEditBtn = index" @mouseleave="showEditBtn = ''">
                <div><span>{{ item.addressName }}</span></div>
                <div style="margin-bottom: 4px;">{{ item.mobile }}</div>
                <div class="ellipsis ellipsis-2">{{ unitAddress(item.address) }} {{ item.detail }}</div>
                <div v-show="selectedStoreAddress.id === item.id" class="corner-icon">
                  <div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" class="icon">
                      <path fill="#ffffff" d="m10 15.17l9.192-9.191l1.414 1.414L10 17.999l-6.364-6.364l1.414-1.414z"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
            <div v-if="storeAddressList.length > 4" class="more-addr hover-color hover-pointer" @click="storeMoreAddr = !storeMoreAddr">
              {{ storeMoreAddr ? "收起地址" : "更多地址" }}
              <svg v-show="!storeMoreAddr" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="#999999" d="m12 13.171l4.95-4.95l1.414 1.415L12 16L5.636 9.636L7.05 8.222z"/></svg>
              <svg v-show="storeMoreAddr" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="#999999" d="m12 10.828l-4.95 4.95l-1.414-1.414L12 8l6.364 6.364l-1.414 1.414z"/></svg>
            </div>
          </div>
        </div>
        <!--配送方式-->
        <div class="card" v-if="goodsType !== 'VIRTUAL_GOODS'">
          <div class="card-head">
            <span>配送方式</span>
          </div>
          <div class="card-body">
            <div class="delivery-method">
              <div v-for="(item, index) in shippingMethod" v-show="moreAddr ? true : index < 3" :key="index" :class="selectedDeliverMethod === item.value ? 'border-red' : ''"
                   class="method-item" @click="selectDeliverMethod(item)" @mouseenter="showEditBtn = item.value" @mouseleave="showEditBtn = ''">
                <div>{{ item.label }}</div>
                <div v-show="selectedDeliverMethod === item.value" class="corner-icon">
                  <div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" class="icon">
                      <path fill="#ffffff" d="m10 15.17l9.192-9.191l1.414 1.414L10 17.999l-6.364-6.364l1.414-1.414z"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!--商品信息-->
        <div class="card">
          <div class="card-head">
            <span>商品信息</span>
            <span @click="router.push('/cart')">返回购物车</span>
          </div>
          <div class="card-body">
            <div v-for="(shop, shopIndex) in goodsList" :key="shopIndex" class="goods-msg">
              <div v-if="shop.checked">
                <div class="shop-name">
                <span class="hover-color hover-pointer" @click="goShopPage(shop.storeId)">{{shop.storeName}}</span>
                </div>
                <div class="goods-list">
                  <div v-for="(goods, goodsIndex) in shop.checkedSkuList" :key="goodsIndex" class="goods-item">
                    <span class="hover-color" @click="goGoodsDetail(goods.goodsSku.id, goods.goodsSku.goodsId)">
                      <img :src="goods.goodsSku.thumbnail" alt=""/>
                      <span style="vertical-align: top">{{goods.goodsSku.goodsName}}</span>
                    </span>
                    <span class="goods-price">{{ unitPrice(goods.purchasePrice, "￥") }}</span>
                    <span>x{{ goods.num }}</span>
                    <span>{{ goods.goodsSku.quantity > 0 ? "有货" : "无货" }}</span>
                    <span class="goods-price">{{ unitPrice(goods.subTotal, "￥") }}</span>
                  </div>
                </div>
                <div class="order-mark">
                  <a-textarea placeholder="订单备注" v-model="shop.remark" :max-length="60" allow-clear show-word-limit :auto-size="{minRows:2,maxRows:2}" style="width: 600px"></a-textarea>
                  <span style="font-size: 12px; color: #999;margin-top: 5px;">提示：请勿填写有关支付、收货、发票方面的信息</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!--发票信息-->
        <div class="card">
          <div class="card-head">
            <span class="flex">
              <span style="flex-shrink: 0;margin-right: 10px">发票信息</span>
              <a-alert type="warning">开企业抬头发票须填写纳税人识别号，以免影响报销</a-alert>
            </span>
          </div>
          <div class="card-body">
            <div class="inovice-content mt_10">
              <span>{{ invoiceData.receiptTitle }}</span>
              <span>{{ invoiceData.receiptContent }}</span>
              <span @click="editInvoice">编辑</span>
            </div>
          </div>
        </div>
        <!--优惠券-->
        <div class="card">
          <div class="card-head">
            <span>优惠券</span>
          </div>
          <div class="card-body">
            <div v-if="couponList.length === 0" style="text-align: center;color: #999999;margin: 30px 0;">无可用优惠券</div>
            <div v-else class="coupon-list flex mt_10">
              <div v-for="(item, index) in couponList" :key="index" class="coupon-item">
                <div class="c-left">
                  <div>
                    <span v-if="item.couponType === 'PRICE'" class="fontsize_12 price-color">{{ unitPrice(item.price, '￥') }}</span>
                    <span v-if="item.couponType === 'DISCOUNT'" class="fontsize_12 price-color"><span class="fontsize-18">{{ item.discount }}</span>折</span>
                    <span class="describe">满{{ item.consumeThreshold }}元可用</span>
                  </div>
                  <p>使用范围：{{ useScope(item) }}</p>
                  <p style="color: #999999;">有效期：{{ item.endTime }}</p>
                </div>
                <img v-if="usedCouponId.includes(item.id)" alt="" class="used" src="../../assets/images/geted.png"/>
                <b></b>
                <a class="c-right hover-pointer" @click="useCoupon(item.id, true)">立即使用</a>
                <a v-if="usedCouponId.includes(item.id)" class="c-right hover-pointer" @click="useCoupon(item.id, false)">放弃优惠</a>
                <i class="circle-top"></i>
                <i class="circle-bottom"></i>
              </div>
            </div>
          </div>
        </div>
        <!--订单价格-->
        <div class="order-price">
          <div>
            <span>{{ totalNum }}件商品，总商品金额：</span><span>{{ unitPrice(priceDetailDTO.goodsPrice, "￥") }}</span>
          </div>
          <div v-if="priceDetailDTO.freightPrice > 0">
            <span>运费：</span><span>{{ unitPrice(priceDetailDTO.freightPrice, "￥") }}</span>
          </div>
          <div v-if="priceDetailDTO.discountPrice > 0">
            <span>优惠金额：</span><span>-{{ unitPrice(priceDetailDTO.discountPrice, "￥") }}</span>
          </div>
          <div v-if="priceDetailDTO.couponPrice > 0">
            <span>优惠券金额：</span><span>-{{ unitPrice(priceDetailDTO.couponPrice, "￥") }}</span>
          </div>
          <div v-if="$route.query.way === 'POINTS'">
            <span>应付积分：</span><span class="actrual-price">{{ priceDetailDTO.payPoint }}</span>
          </div>
          <div v-else>
            <span>应付金额：</span><span class="actrual-price">{{ unitPrice(priceDetailDTO.flowPrice, "￥") }}</span>
          </div>
        </div>
      </div>
      <!-- 底部支付栏 -->
      <div class="order-footer width_1200">
      <div class="pay ml_20" @click="pay">提交订单</div>
      <div v-if="addressList.length && selectedDeliverMethod === 'LOGISTICS'" class="pay-address">
        配送至：{{unitAddress(selectedAddress.consigneeAddressPath)}} {{selectedAddress.detail}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;收货人：{{selectedAddress.name}}&nbsp;&nbsp;{{selectedAddress.mobile}}
      </div>
      <div v-if="addressList.length && selectedDeliverMethod === 'SELF_PICK_UP'" class="pay-address">
        自提地点：{{selectedStoreAddress.address}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;联系方式：{{selectedStoreAddress.mobile}}
      </div>
    </div>
    </a-spin>
    <!-- 选择地址模态框-->
    <AddressManage ref="addressRef" @callback="addrCallback"></AddressManage>
    <!--添加发票模态框-->
    <InvoiceModal ref="invoiceRef" :invoiceData="invoiceData" @callback="getInvMsg"></InvoiceModal>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { Message, Modal } from '@arco-design/web-vue';
  import { unitPrice, unitAddress } from '@/utils/filters';
  import { useRoute, useRouter } from 'vue-router';
  import { useUserStore } from '@/stores/user';
  import { storeToRefs } from 'pinia';
  import { delMemberAddress, memberAddress, memberAddressList } from "@/api/address";
  import { cartGoodsPay, createTrade, selectAddr, selectCoupon, setShipMethod, setStoreAddressId, shippingMethodList } from "@/api/cart";
  import { getStoreAddress } from "@/api/shopentry"

  const route = useRoute();
  const router = useRouter();
  // 状态管理
  const store = useUserStore();
  const { logoImg } = storeToRefs(store);
  const loading = ref(false); // 加载状态

  const selectedStoreAddress = ref<any>('m');
  const selectMethod = ref<any>('');
  const stepIndex = ref<any>(1); // 顶部步骤条状态
  const showEditBtn = ref<any>(''); // 鼠标移入显示编辑按钮
  const storeMoreAddr = ref<any>(false); //
  // 发票数据
  const invoiceData = ref<any>({receiptTitle: "个人", receiptContent: "不开发票",});
  // 获取收货列表表单参数
  const searchForm = ref<any>({pageNumber: 1, pageSize: 100});
  const shippingMethod = ref<Array<any>>([]);
  const storeAddressList = ref<Array<any>>([]);
  const shippingWay = ref<Array<any>>([{value: "LOGISTICS", label: "物流",}, {value: "SELF_PICK_UP", label: "自提",},]);
  const selectedDeliverMethod = ref<any>('LOGISTICS');
  const addressList = ref<Array<any>>([]); // 地址列表
  const selectedAddress = ref<any>({}); // 所选地址
  const goodsList = ref<Array<any>>([]); // 商品列表
  const priceDetailDTO = ref<any>({}); // 商品价格
  const totalNum = ref<number>(0); // 购买数量
  const goodsType = ref<any>(""); // 商品类型
  const addrId = ref<any>(""); // 编辑地址传入的id
  const moreAddr = ref<any>(false); // 更多地址
  const couponList = ref<Array<any>>([]); // 可用优惠券列表
  const usedCouponId = ref<Array<any>>([]); // 已使用优惠券id
  const selectedCoupon = ref<any>({}); // 已选优惠券对象
  const storeId = ref<any>(''); //店铺Id
  const skuList = ref<Array<any>>([]);
  const addressRef = ref<any>();  // 选择地址模态框
  const invoiceRef = ref<any>(''); // 添加发票模态框


  // 选择地址
  const selectAddress = (item: any) => {
    let params = { way: route.query.way, shippingAddressId: item.id };
    selectAddr(params).then((res) => {
      if (res.data.success) {
        Message.success("选择收货人信息成功");
        selectMethod.value = item;
        getGoodsDetail();
      }
    });
  };
  // 添加/修改地址
  const editAddress = (id: any) => {
    addrId.value = id;
    addressRef.value.init(id);
  };
  // 添加/编辑地址回显
  const addrCallback = () => {
    getAddress();
  };
  // 删除地址
  const delAddress = (item: any) => {
    Modal.confirm({
      title: '提示',
      content: `你确定删除这个收货地址？`,
      okButtonProps: {type: "primary", status: "danger"},
      onOk: () => {
        delMemberAddress(item.id).then((res) => {
          if (res.data.success) {
            Message.success("删除成功");
            getAddress();
          }
        });
      }
    });
  };
  // 跳转地址管理页面
  const goAddressManage = () => {
    router.push("/user/home/<USER>/myAddress");
  };
  // 订单商品详情
  const getGoodsDetail = () => {
    loading.value = true;
    cartGoodsPay({way: route.query.way}).then((res) => {
      loading.value = false;
      if (res.data.success) {
          if (!res.data.result.checkedSkuList || res.data.result.checkedSkuList.length === 0) {
            if (res.data.result.skuList && res.data.result.skuList[0]) {
              Modal.warning({
                title: "购物车存在无效商品！",
                content: "[" + res.data.result.skuList[0].goodsSku.goodsName + "]" + res.data.result.skuList[0].errorMessage });
            }
            router.push({path: "/cart", replace: true});
          }
          let remarkList = goodsList.value.map((item) => {return item.remark || '';});
          goodsList.value = res.data.result.cartList.map((item: any, index: any) => {
            item.remark = remarkList[index];
            return item;
          });
          priceDetailDTO.value = res.data.result.priceDetailDTO;
          skuList.value = res.data.result.skuList;
          goodsType.value = res.data.result.skuList[0].goodsSku.goodsType;
          storeId.value = goodsList.value[0].storeId;
          if (res.data.result.receiptVO) {
            invoiceData.value = res.data.result.receiptVO;
          }
          let notSupArea = res.data.result.notSupportFreight;
          selectedCoupon.value = {};
          if (res.data.result.platformCoupon)
            selectedCoupon.value[res.data.result.platformCoupon.memberCoupon.id] = res.data.result.platformCoupon;
          if (res.data.result.storeCoupons && Object.keys(res.data.result.storeCoupons)) {
            for (let i = 0; i < Object.keys(res.data.result.storeCoupons).length; i++) {
              let storeMemberCouponsId = Object.keys(res.data.result.storeCoupons)[i];
              let storeCouponId = res.data.result.storeCoupons[storeMemberCouponsId].memberCoupon.id;
              selectedCoupon.value[storeCouponId] = res.data.result.storeCoupons[storeMemberCouponsId];
            }
          }
          if (notSupArea) {
            let content = [] as any;
            let title = "";
            notSupArea.forEach((e: any) => {title = e.errorMessage;content.push(e.goodsSku.goodsName);});
            Modal.warning({title: "以下商品超出配送区域" || title, content: content.toString(),});
          }
          if (res.data.result.memberAddress) {
            selectedAddress.value = res.data.result.memberAddress;
          }
          getAddress();
          getStoreAddressList();
          totalNum.value = 0;
          for (let i = 0; i < skuList.value.length; i++) {
            totalNum.value += skuList.value[i].num;
          }
          usedCouponId.value = [];
          couponList.value = res.data.result.canUseCoupons;
          const couponKeys = Object.keys(selectedCoupon.value);
          if (couponKeys.length) {
            couponList.value.forEach((e) => {
              if (selectedCoupon.value[e.id] && e.id === selectedCoupon.value[e.id].memberCoupon.id) {
                usedCouponId.value.push(e.id);
              }
            });
          }
        }
    }).catch(() => {
      loading.value = false;
    });
  };
  // 获取收货地址列表
  const getAddress = () => {
    memberAddressList().then((res) => {
      if (res.data.success) {
        addressList.value = res.data.result;
        addressList.value.forEach((e, index) => {
          if (e.id === selectedAddress.value.id && index > 2) {
            moreAddr.value = true;
          }
        });
      }
    });
  };
  const getStoreAddressList = async () => {
    getStoreAddress(storeId.value, searchForm.value).then(res => {
      if (res.data.success) {
        storeAddressList.value = res.data.result.records;
        storeAddressList.value.forEach((e, index) => {
          if (e.id === selectedAddress.value.id && index > 2) {
            storeMoreAddr.value = true;
          }
        });
      }
    })
  };

  // 选择自提地址
  const selectStoreAddress = (item: any) => {
    // 选择自提地址
    setStoreAddressId(item.id, route.query.way).then((res) => {
      if (res.data.success) {
        Message.success("选择自提地址成功");
        selectedStoreAddress.value = item;
        getGoodsDetail();
      }
    });
  };

  // 获取配送方式列表
  const getDistribution = async () => {
    let shopRes = await shippingMethodList({way: route.query.way});
    let shopList: any;
    if (shopRes.data.success) {
      shopList = shopRes.data.result;
      let way = [] as any;
      shippingWay.value.forEach((item) => {
        shopList.forEach((child: any) => {
          if (item.value === child) {
            way.push(item);
          }
        });
      });
      shippingMethod.value = way;
    }
  };
  // 选择配送方式
  const selectDeliverMethod = (item: any) => {
    let params = { way: route.query.way, shippingMethod: item.value };
    setShipMethod(params).then((res) => {
      if (res.data.success) {
        selectedDeliverMethod.value = item.value;
        getGoodsDetail();
      }
    });
  };
  // 编辑发票信息
  const editInvoice = () => {
    invoiceRef.value.init();
  };
  // 获取发票信息
  const getInvMsg = (item: any) => {
    if (item) {
      init();
      invoiceRef.value.visible = false;
    }
  };
  // 优惠券可用范围
  const useScope = (item: any) => {
    let goods = "全部商品";
    switch (item.scopeType) {
      case "ALL":
        goods = "全部商品";
        break;
      case "PORTION_GOODS":
        goods = "部分商品";
        break;
      case "PORTION_GOODS_CATEGORY":
        goods = item.scopeName + "分类商品";
        break;
    }
    if (item.storeName !== 'platform' && item.scopeType === 'ALL') {
      goods = item.storeName + '店铺';
    }
    return `${goods}可用`;
  };
  // 使用优惠券
  const useCoupon = (id: any, used: any) => {
    let params = {
      way: route.query.way,
      memberCouponId: id,
      used: used, // true 为使用， false为弃用
    };
    selectCoupon(params).then((res) => {
      if (res.data.success) init();
    });
  };
  // 跳转店铺首页
  const goShopPage = (id: any) => {
    let routeUrl = router.resolve({path: "/merchant", query: { id },});
    window.open(routeUrl.href, "_blank");
  };
  // 跳转商品详情
  const goGoodsDetail = (skuId: any, goodsId: any) => {
    let routeUrl = router.resolve({path: "/goodsDetail", query: {skuId, goodsId},});
    window.open(routeUrl.href, "_blank");
  };
  // 结算
  const pay = () => {
    const params = { client: "PC", remark: [], way: route.query.way } as any;
    goodsList.value.forEach((e) => {
      if (e.remark) {
        params.remark.push({remark: e.remark, storeId: e.storeId,});
      }
    });
    if (!params.remark.length) delete params.remark;
    loading.value = true;
    createTrade(params).then((res) => {
        loading.value = false;
        if (res.data.success) {
          if (params.way === "POINTS") {
            // 积分支付不需要跳转支付页面
            router.push("/payment/payDone");
          } else {
            router.push({ path: "/payment/payment", query: {orderType: "TRADE", sn: res.data.result.sn }});
          }
        }
      }).catch(() => {loading.value = false;});
  };

  // 初始化数据
  const init = () => {
    getGoodsDetail();
    getDistribution();
  };
  onMounted(() => {
    init();
  })
</script>

<style scoped lang="less">
  /*顶部*/
  .cart-box {
    background-color: @light_background_color;
    .logo {
      width: 1200px;
      height: 90px;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      margin: 0 auto;
      div:nth-child(1) {
        display: flex;
        justify-content: space-between;
        align-items: center;
        img {
          max-height: 80px;
          height: auto;
          cursor: pointer;
        }
        div:nth-child(2) {
          width: 200px;
          color: #999;
          font-size: 16px;
          margin: 0 20px;
          span {
            color: @theme_color;
          }
        }
      }
    }
    .divider {
      width: 100%;
      height: 2px;
      background-color: @theme_color;
    }
    /** 步骤条和配送区域总体 */
    .available-area {
      display: flex;
      align-items: center;
      justify-content: space-between;
      /*margin-bottom: 15px;*/
    }
    /** step步骤条 */
    .cart-steps {
      height: 30px;
      display: flex;
      align-items: center;
      margin-top: 10px;
      span {
        color: @light_content_color;
        height: 30px;
        text-align: center;
        line-height: 30px;
        display: inline-block;
        padding: 0 15px;
      }
      .ivu-icon {
        color: @light_content_color;
        font-size: 20px;
        margin: 0 15px;
      }
      .active {
        border-radius: 50px;
        background-color: #ff8f23;
        color: #fff;
      }
      .active-arrow {
        color: #ff8f23;
      }
    }
  }
  /*结算页主体*/
  .pay-container {
    margin: 20px auto;
    background-color: #fff;
    min-height: 200px;
    padding: 15px 25px;
    width: 1200px;
    box-sizing: border-box;
  }
  /*公共表头*/
  .card:nth-of-type(1) > div {
    margin-top: 0;
  }
  .card-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #dddddd;
    height: 40px;
    margin-top: 20px;
    span:nth-child(1) {
      font-size: 18px;
    }
    span:nth-child(2) {
      font-size: 12px;
      color: @link_color;
      cursor: pointer;
      &:hover {
        color: @theme_color;
      }
    }
  }
  /** 地址管理 */
  .address-manage {
    display: flex;
    flex-wrap: wrap;
    > div {
      border: 1px dotted #949494;
      width: 258px;
      height: 100px;
      margin: 10px 10px 0 0;
      padding: 10px;
      cursor: pointer;
      color: #999;
    }
    > div:nth-of-type(4n) {
      margin-right: 0;
    }
    .add-address {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      margin-right: 0;
      .ivu-icon {
        font-size: 24px;
      }
    }
    .address-item {
      position: relative;
      font-size: 14px;
      > div:nth-child(1) {
        margin-bottom: 10px;
        span {
          margin-right: 10px;
        }
        > span:nth-child(1) {
          color: #000000;
          font-size: 14px;
        }
      }
      .edit-btn {
        font-size: 12px;
        position: absolute;
        top: 15px;
        right: 20px;
        color: @theme_color;
      }
      .corner-icon {
        position: absolute;
        right: -1px;
        bottom: -1px;
        div {
          width: 0;
          border-top: 30px solid transparent;
          border-right: 30px solid @theme_color;
        }
        .icon {
          font-size: 12px;
          position: absolute;
          bottom: 0;
          right: 1px;
          transform: rotate(-15deg);
          color: #fff;
        }
      }
    }
    .border-red {
      border-color: @theme_color;
    }
  }
  .more-addr {
    display: flex;
    align-items: center;
    margin: 8px 0;
    color: #999999;
  }
  /*配送方式*/
  .delivery-method {
    display: flex;
    flex-wrap: wrap;
    > div {
      border: 1px dotted #949494;
      width: 50px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      margin: 20px 20px 0 0;
      cursor: pointer;
      color: #999;
    }
    .method-item {
      position: relative;
      font-size: 12px;
      > div:nth-child(1) {
        margin-bottom: 10px;
        span {
          margin-right: 10px;
        }
        > span:nth-child(1) {
          color: #000000;
          font-size: 14px;
        }
      }
      .edit-btn {
        font-size: 12px;
        position: absolute;
        top: 15px;
        right: 20px;
        color: @theme_color;
        span:hover {
          border-bottom: 1px solid @theme_color;
        }
      }
      .corner-icon {
        position: absolute;
        right: -1px;
        bottom: -1px;
        div {
          width: 0;
          border-top: 20px solid transparent;
          border-right: 20px solid @theme_color;
        }
        .icon {
          font-size: 12px;
          position: absolute;
          bottom: 0;
          right: 1px;
          transform: rotate(-15deg);
          color: #fff;
        }
      }
    }
    .border-red {
      border-color: @theme_color;
    }
  }
  /*商品信息*/
  .goods-msg {
    .shop-name {
      display: flex;
      justify-content: space-between;
      margin-top: 12px;
      > span:nth-child(1) {
        font-weight: bold;
        .ivu-icon {
          color: #ff8f23;
          &:hover {
            color: @theme_color;
          }
        }
      }
      > span:nth-child(2) {
        color: #999;
        position: relative;
        display: flex;
        width: 200px;
      }
      .delivery-list {
        position: absolute;
        right: 0;
        top: 20px;
        background-color: #f3fafe;
        box-shadow: 0px 0px 5px #b9b2b2;
        display: flex;
        flex-wrap: wrap;
        width: 200px;
        min-height: 100px;
        padding: 10px;
        li {
          width: 90px;
          height: 30px;
          text-align: center;
          &:hover {
            cursor: pointer;
          }
        }
      }
    }
    .goods-list {
      width: 1150px;
      background-color: #f8f8f8;
      margin: 10px 0 20px 0;
      .goods-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px 0;
        margin: 0 20px;
        border-bottom: 1px dotted #999;
        &:last-child {
          border: none;
        }
        img {
          width: 48px;
          height: 48px;
        }
        > span {
          text-align: center;
          width: 100px;
        }
        > span:nth-child(1) {
          font-size: 12px;
          flex: 1;
          text-align: left;
          > span {
            margin-left: 10px;
          }
        }
        > span:last-child {
          color: @theme_color;
          font-weight: bold;
        }
        .goods-price {
          font-size: 16px;
        }
      }
    }
    .order-mark {
      display: flex;
      flex-direction: column;
    }
  }
  /*发票信息*/
  .inovice-content {
    > span {
      margin-right: 10px;
      line-height: 30px;

    }
    > span:last-child {
      color: @theme_color;
      cursor: pointer;
      &:hover {
        border-bottom: 1px solid @theme_color;
      }
    }
  }
  /*优惠券*/
  .coupon-list {
    max-height: 260px;
    overflow: scroll;
    flex-wrap: wrap;
    .coupon-item {
      width: 260px;
      height: 125px;
      margin-right: 10px;
      margin-bottom: 10px;
      box-sizing: border-box;
      margin-left: 10px;
      position: relative;
      border: 1px solid #eee;
      .c-left {
        padding: 10px 30px 10px 10px;
        .describe {
          background-color: #fff4ec;
          color: #F31947;
          padding: 0 5px;
          margin-left: 10px;
          font-size: 13px;
        }
      }
      .c-right {
        width: 30px;
        height: 125px;
        text-align: center;
        box-sizing: border-box;
        padding: 26px 0;
        position: absolute;
        right: 0;
        top: 0;
        background-color: #F31947;
        color: #fff;
        font-size: 16px;
      }
      b {
        position: absolute;
        z-index: 2;
        top: 0;
        display: block;
        width: 3px;
        height: 100%;
        background: url("../../assets/images/small-circle.png") top left repeat-y;
        right: 28px;
      }
      i {
        position: absolute;
        width: 15px;
        height: 15px;
        right: 22px;
        border: 1px solid #eee;
        background-color: #fff;
        border-radius: 20px;
        &:after {
          content: "";
          position: absolute;
          width: 18px;
          height: 10px;
          left: -2px;
          background-color: #fff;
        }
      }
      i.circle-top {
        top: -7px;
        &::after {top: -4px;}
      }
      i.circle-bottom {
        bottom: -10px;
        &::after {bottom: -1px;}
      }
      .used {
        position: absolute;
        top: 60px;
        right: 40px;
        width: 50px;
        height: 50px;
      }
    }
  }
  /** 订单价格 */
  .order-price {
    text-align: right;
    margin-top: 30px;
    font-size: 16px;
    color: #999;
    > div > span:nth-child(2) {
      width: 130px;
      text-align: right;
      display: inline-block;
      margin-top: 10px;
    }
    .actrual-price {
      color: @theme_color;
      font-weight: bold;
      font-size: 20px;
    }
  }
  /** 底部支付栏 */
  .order-footer {
    z-index: 20;
    height: 50px;
    background-color: @light_white_background_color;
    transition: 0.35s;
    color: @title_color;
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
    border-top: 1px solid #ddd;
    margin: 10px auto;
    width: 1200px;
    div {
      text-align: center;
    }
    position: sticky;
    bottom: 0;
    .pay {
      background-color: @theme_color;
      width: 150px;
      font-size: 20px;
      color: #fff;
      height: 100%;
      line-height: 50px;
      cursor: pointer;
    }
  }


  :deep(.arco-alert) {
    padding: 0 8px;
    height: 24px;
  }
</style>
