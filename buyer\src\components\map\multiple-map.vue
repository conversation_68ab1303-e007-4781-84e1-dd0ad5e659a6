<template>
  <a-modal v-model:visible="enableMap" width="800px">
    <template #title>选择地区</template>
    <div class="">
      <div class="selector">
        <div class="selector-item" v-for="(plant, plantIndex) in Object.keys(data)" :key="plantIndex">
          <div v-for="(item, index) in data[plant]" :key="index" class="map-item" :class="{ 'active': chiosend[plantIndex].id === item.id }"
               @click="init(item, plantIndex != Object.keys(data).length - 1 ? Object.keys(data)[plantIndex + 1] : 0, plantIndex)">
            {{ item.name }}</div>
        </div>
      </div>
    </div>
    <template #footer>
      <a-button @click="enableMap = false;">取消</a-button>
      <a-button @click="mapFinished" status="danger" type="primary">完成</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { getRegion } from "@/api/common";

  const enableMap = ref(false);
  const data = ref<any>({
    province: [], //省
    city: [], //市
    area: [], //区
    street: [], //街道
  });
  const chiosend = ref<Array<any>>([]);
  const mapDefault = ref('select');
  const emit = defineEmits(['callback']);

  // 初始化内容
  async function open(val = {}) {
    enableMap.value = true;
    init({ id: 0 }, 'province');
  }

  const init =(val: any, level: number | string = 'province', index?: any) => {
    if (level === 0) {
      // 说明选择到了街道，将街道id存入数组
      chiosend.value.splice(3, 1, val);
    }
    else {
      getRegion(val.id).then((res) => {
        if (res.data.result.length && val.id !== 0) {
          chiosend.value[index] = val
        } else if(!res.data.result.length){
          chiosend.value[index] = val
        }
        data.value[level] = res.data.result;
        if (level == 'city') {
          data.value.area = [];
          data.value.street = [];
          chiosend.value.splice(1, 3, "","","");
        }
        if (level == 'area') {
          data.value.street = []
          chiosend.value.splice(2, 2, "","");
        }
        if (level == 'street') {
          chiosend.value.splice(3, 1, "");
        }
      });
    }
  };

  // 选择地址回调
  const mapFinished = () => {
    if(!chiosend.value[0]){
      Message.error("请选择地址");
      return;
    }
    const params = chiosend.value.filter((item) => item!=="" && item.value !== "");
    enableMap.value = false;
    emit('callback', {
      type: mapDefault.value,
      data: params
    });
    data.value = {province: [], city: [], area: [], street: [],}
  };




  onMounted(() => {
    chiosend.value = new Array(4).fill("");
  });
  defineExpose({
    open
  });
</script>

<style scoped lang="less">
  .selector {
    height: 400px;
    padding: 10px 0;
    display: flex;
  }
  .selector-item {
    width: 100%;
    flex: 1;
    overflow: auto;
  }
  .map-item {
    width: 100%;
    padding: 10px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    box-sizing: border-box;
    &:hover {
      background: @background_color;
    }
  }
  .active {
    background: @background_color;
  }
</style>
