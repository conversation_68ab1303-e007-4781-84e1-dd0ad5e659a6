<template>
  <div overflow-hidden :style="{ borderRadius: (props.res.border === 'round' ? props.res.data.round : 0) + 'px' }">
    <a-image v-if="show" display-block width="364px" :src="props.res.data.list[0].img"
      :height="props.res.data.height + 'px'" :preview="false" class="image"></a-image>
  </div>
</template>

<script setup lang="ts">
import { getScalImageSize } from '@/views/operation/decoration/components/tool'
import { watch, ref } from 'vue'
const props = defineProps<{
  res: any
}>()
const show = ref<boolean>(true)
watch(() => props.res.data.list[0].img, async (val: any) => {
  show.value = false
  const res = await getScalImageSize(val, 390, 800)
  props.res.data.height = res.tempHeight
  show.value = true

}, { deep: true })



</script>

<style scoped>
.image {
  width: 100%;
  display: block;
}
</style>
