<template>
  <div py-30px border-b-1>
    <div w-90px>{{ props.text }}</div>
    <!-- 商品列表 -->
    <div v-if="props.res.data[props.bind || 'list']?.length" flex flex-a-c flex-wrap gap-10px rounded my-20px
      class="mini-box">
      <div @click="open" v-for="(item, index) in props.res.data[props.bind || 'list']" :key="index">
        <img class="mini-goods" :src="item.img || item.thumbnail" />
      </div>
    </div>
    <a-button mt-20px h-40px type="outline" @click="open" long>添加/修改商品</a-button>

    <chooseGoods :bind="props.bind || 'list'" :max="props.max" :res="props.res" ref="choose" @callback="checkedProduct" />
  </div>
</template>


<script setup lang="ts">
import { DragRule } from '@/views/operation/decoration/app/models/types';
import chooseGoods from '@/views/operation/decoration/components/choose-goods.vue';
import { ref } from 'vue';
const props = defineProps<{
  res: DragRule,
  text: string,
  bind?: string,
  max?:number
}>()
const choose = ref<any>(null)

function open() {
  choose.value.open()
}

// 选中商品的回调
function checkedProduct(list: Array<any>) {
  const filterGoods = list.map((item) => {
    return {
      goodsName: item.goodsName,
      goodsType: item.goodsType,
      title: item.goodsName,
      img: item.small,
      thumbnail: item.thumbnail,
      id: item.id,
      goodsId: item.goodsId,
      storeName: item.storeName,
      storeId: item.storeId,
      price: item.price,
      quantity: item.quantity,
      salesModel: item.salesModel,
      marketEnable: item.marketEnable
    }
  })
  props.res.data[props.bind || 'list'] = filterGoods
}
</script>

<style scoped>
.mini-box {
  background: #ededed;
  padding: 4px 4px;
}

.mini-goods {
  width: 59px;
  height: 59px;
}
</style>
