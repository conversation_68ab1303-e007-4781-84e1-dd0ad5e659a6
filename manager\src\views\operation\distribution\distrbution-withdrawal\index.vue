<template>
  <a-card class="general-card" title="分销提现" :bordered="false">
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getDistributionCash"
      :api-params="apiParams"
      :bordered="true"
    >
      <template #btnList="{ data }">
        <a-space>
          <a-button
            v-if="data?.distributionCashStatus != 'APPLY'"
            type="text" status="success"
            @click="seeCashWithdrawal(data)"
          >
            查看</a-button
          >
          <a-button v-else  @click="toExamine(data)" type="text">审核</a-button>
        </a-space>
      </template>
    </tablePage>
    <!-- 查看 -->
    <a-modal
      v-model:visible="seeWithdrawal"
      :align-center="false"
      width="500px"
    >
      <template #title> 查看 </template>
      <a-form
        ref="formRef"
        :style="{ width: '460px' }"
        layout="horizontal"
        auto-label-width
        :model="showList"
      >
        <a-form-item label="编号：">
          <span>{{ showList.sn }}</span>
        </a-form-item>
        <a-form-item label="名称：">
          <span>{{ showList.distributionName }}</span>
        </a-form-item>
        <a-form-item label="金额：">
          <span>{{ unitPrice(Number(showList.price), '￥') }}</span>
        </a-form-item>
      </a-form>
      <template #footer>
        <div style="text-align: right">
          <a-button type="text" @click="seeWithdrawal = false">取消</a-button>
        </div>
      </template>
    </a-modal>
    <!-- 审核 -->
    <a-modal
      v-model:visible="roleModalVisible"
      :align-center="false"
      width="500px"
    >
      <template #title> 查看 </template>
      <a-form
        ref="formRef"
        :style="{ width: '460px' }"
        layout="horizontal"
        auto-label-width
        :model="showList"
      >
        <a-form-item label="编号：">
          <span>{{ showList.sn }}</span>
        </a-form-item>
        <a-form-item label="名称：">
          <span>{{ showList.distributionName }}</span>
        </a-form-item>
        <a-form-item label="金额：">
          <span> {{ unitPrice(Number(showList.price), '￥') }}</span>
        </a-form-item>
      </a-form>
      <template #footer>
        <div style="text-align: right">
          <a-button @click="submitRole('FAIL_AUDITING')" type="primary" status="danger">拒绝</a-button>
          <a-button
            style="margin-left: 10px"
            type="primary"
            status="success"
            @click="submitRole('VIA_AUDITING')"
            >通过</a-button
          >
        </div>
      </template>
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { getDistributionCash, auditDistributionCash } from '@/api/operation';
  import { distributionCashStatus } from '@/utils/tools';
  import { ref } from 'vue';
  import { useRoute } from 'vue-router'
  import { Message } from '@arco-design/web-vue';
  import { unitPrice } from '@/utils/filters';

  const tablePageRef = ref<any>();
  const seeWithdrawal = ref<boolean>(false); // 查看
  const roleModalVisible = ref<boolean>(false); // 审核
  const showList = ref<any>({
    // 可操作选择
    sn: '',
    price: '',
    distributionName: '',
  });
  const columnsSearch: Array<SearchRule> = [
    {
      label: '会员名称',
      model: 'memberName',
      disabled: false,
      input: true,
    },
    {
      label: '编号',
      model: 'sn',
      disabled: false,
      input: true,
    },
    {
      label: '状态',
      model: 'distributionCashStatus',
      disabled: false,
      select: {
        options: distributionCashStatus,
      },
    },
  ];

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '编号',
      dataIndex: 'sn',
    },
    {
      title: '会员名称',
      dataIndex: 'distributionName',
    },
    {
      title: '申请金额',
      dataIndex: 'price',
      currency: true,
    },
    {
      title: '申请时间',
      dataIndex: 'createTime',
    },
    {
      title: '处理时间',
      dataIndex: 'updateTime',
    },
    {
      title: '状态',
      dataIndex: 'distributionCashStatus',
      slot: true,
      slotData: {
        badge: distributionCashStatus,
      },
    },
  ];

  const sortMethods: MethodsRule = {
    title: '操作',
    width: 200,
    methods: [
      {
        slot: true,
        slotTemplate: 'btnList',
      },
    ],
  };

  const apiParams = ref({
    order: 'desc',
    distributionCashStatus:useRoute().query.distributionCashStatus
  });
  // 查看
  const seeCashWithdrawal = (data: any) => {
    console.log(data, 'data');
    showList.value = data;
    seeWithdrawal.value = true;
  };
  // 审核
  const toExamine = (v: any) => {
    showList.value = v;
    roleModalVisible.value = true;
    console.log(showList.value, v, 'vvvv');
  };
  // 审核拒绝或通过
  const submitRole = (v: any) => {
    const params = {
      applyId: '',
      result: false,
    };
    params.applyId = showList.value.id;
    params.result = v;

    auditDistributionCash(params.applyId, {result: params.result}).then((res: any) => {
      if (res.data.code == 200) {
        Message.success('操作成功');
        roleModalVisible.value = false;
        tablePageRef.value.init();
      }
    });
  };
</script>

<style scoped></style>
