<template>
  <a-card class="general-card" title="历史积分" :bordered="false">
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :api="getHistoryPointData"
      :api-params="apiParams"
      :bordered="true"
    >
     <template #variablePoint="{ data }">
        <span v-if="data.pointType =='INCREASE'" style="color:green">+ {{data.variablePoint}}</span>
        <span v-else  style="color:red"> - {{data.variablePoint}}</span>
    </template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { getHistoryPointData } from '@/api/member';
  import { ref } from 'vue';

  const apiParams = ref({});
  const tablePageRef = ref('');
  const columnsSearch: Array<SearchRule> = [
    {
      label: '会员名称',
      model: 'memberName',
      disabled: false,
      input: true,
    },
  ];

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '会员名称',
      dataIndex: 'memberName',   
    },
    {
      title: '操作内容',
      dataIndex: 'content',
      width:300,
    },

    {
      title: '之前积分',
      dataIndex: 'beforePoint',
    },
    {
      title: '变动积分',
      dataIndex: 'variablePoint',
      slot: true,
      slotTemplate: 'variablePoint',
    },
    {
      title: '当前积分',
      dataIndex: 'points',
    },
     {
      title: '操作时间',
      dataIndex: 'createTime',
    },
  ];
</script>
