<template>
    <a-card :bordered="false" :style="{ width: '100%' }">
        <a-tabs default-active-key="1">
            <a-tab-pane key="1" title="基本信息">
                <a-form :model="userForm" >
                    <a-form-item field="avatar"  label="用户头像：">
                        <!-- <a-image :src="userForm.avatar"></a-image> -->
                        <a-upload
                            image-preview
                            list-type="picture-card"
                            :action="uploadFile"
                            :headers="{accessToken:accessToken}"
                            :onSuccess="handleSuccess"
                            :onError="handleError"
                            @before-upload="beforeUpload"
                            :file-list="avatar"
                            :limit="1"
                            ></a-upload>
                    </a-form-item>
                    <a-form-item field="username"  label="用户名：">
                       {{userForm.username}}
                    </a-form-item>
                    <a-form-item field="nickname"  label="昵称：">
                        <a-input style="width: 300px;"
                            v-model="userForm.nickName"
                            placeholder="请输入昵称"
                        />
                    </a-form-item>
                    <a-form-item>
                        <a-button type="primary"  @click="saveEdit">保存</a-button>
                    </a-form-item>
                </a-form>
            </a-tab-pane>
            <a-tab-pane key="2" title="安全设置">
                <div class="item">
                    <div class="title">账号密码<a-button style="margin-left: 50px;" type="primary" @click="$router.push({ name: 'change-password' })">修改</a-button></div>
                    
                </div>
            </a-tab-pane>
        </a-tabs>
    </a-card>
</template>
<script lang="ts" setup >
import { reactive ,ref,onMounted } from "vue";
import store from '@/utils/storage';
import uploadFile from '@/api/index';
import { Message } from '@arco-design/web-vue';
import { userInfoEdit } from '@/api/index'
import Cookies from 'js-cookie';
import { useUserStore } from '@/store';
import useUser from '@/hooks/user';

// const { logout } = useUser();
const { userInfo } = useUserStore();
const avatar = ref();
// 携带toekn
const accessToken = ref<string>(store.getAccessToken() || '');
const fileFormat = ref<Array<string>>(['jpg', 'jpeg', 'png', 'gif', 'bmp'])
const currMenu = ref('')
const userForm = ref({
    avatar:"",
    nickname:""
}) as any
const changeMenu =() =>{

}
// 头像上传成功回调
const handleSuccess = (res:any) => {
    if(res.response.success){
        Message.success('成功')
        userForm.value.avatar = res.response.result
    }
}
// 头像上传失败回调
const handleError = () => {
    Message.error("上传失败")
}
// 头像上传前校验
const beforeUpload: any = (file: { name: string; size: number; }) => {
    return new Promise((resolve,reject) => {
        if(!fileFormat.value.includes(file.name.split('.')[file.name.split('.').length-1])){
            reject(new Error('上传失败'))
            Message.error(`请选择 .jpg .jpeg .png .gif .bmp格式文件`)
        }else if(Number((file.size / 1024).toFixed(0)) > 1024){
            reject(new Error('上传失败'))
            Message.error(`所选文件大小过大，不得超过1M`)
        }else{
            resolve(true)
        }
    })
}
// 保存
const saveEdit = () =>{
    userInfoEdit(userForm.value).then((res:any) => {
        if(res.data.success){
            Message.success('保存成功')
            // 更新用户信息
            Cookies.set("userInfoManager",userForm.value)

        }
    })
}
const init = () => {
    userForm.value = userInfo
    avatar.value =userInfo.avatar?[{url: userInfo.avatar}]:[];
}
onMounted(() => {
    init()
});
</script>
<style lang="less" scoped>
.item{
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 50px;
    padding-top: 14px;
    padding-bottom: 14px;
    padding-right: 200px;
    border-bottom: 1px solid #e8e8e8;

    .title {
      color: rgba(0, 0, 0, .65);
      margin-bottom: 4px;
      font-size: 14px;
      line-height: 22px;
    }
}
</style>