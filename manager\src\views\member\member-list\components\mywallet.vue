<template>
  <div>
    <div class="pointsTitle" style="justify-content: flex-start; text-align: left">
    <div style="min-width: 160px; margin-right: 20px">
      <div class="points-top-title"> 余额 </div>
      <div class="points-top-text">
        {{ memberWalletInfo?.memberWallet ? `￥${memberWalletInfo.memberWallet}.00` : '￥' + '0.00' }}
      </div>
    </div>
    <div style="min-width: 160px">
      <div class="points-top-title"> 冻结余额 </div>
      <div class="points-top-text">
        {{ memberWalletInfo?.memberFrozenWallet ? `￥${memberWalletInfo.memberFrozenWallet}.00` : '￥' + '0.00' }}
      </div>
    </div>
  </div>
  <tablePage  ref="tablePageRef" :api-params="apiParams" :columns="columnsWalletTable"  :api="getUserWallet" :bordered="true">
    <template #money="{ data }">
      <span v-if="data.money > 0" style="color: green">  {{ unitPrice(data.money, '￥') }}</span>
      <span v-else style="color: red"> {{ unitPrice(data.money, '￥') }} </span>
    </template>
  </tablePage>
  </div>
</template>

<script lang="ts" setup>
  import { serviceType } from '@/utils/tools';
  import { useRoute } from 'vue-router';
  import { getUserWallet, getMemberWallet } from '@/api/member';
  import { ColumnsDataRule, SearchRule } from '@/types/global';
  import tablePage from '@/components/table-pages/index.vue';
  import { onMounted, ref } from 'vue';
  import { unitPrice } from '@/utils/filters';

  const memberWalletInfo = ref();
  // TA的余额
  const columnsWalletTable: ColumnsDataRule[] = [
    {
      title: '会员名称',
      dataIndex: 'memberName',
      width:200,
    },
    {
      title: '业务类型',
      dataIndex: 'serviceType',
      width:160,
      slot: true,
      slotData: {
        tag: serviceType,
      },
    },
    {
      title: '变动金额',
      dataIndex: 'money',
      slot: true,
      slotTemplate: 'money',
      width:200,
    },
    {
      title: '变动时间',
      dataIndex: 'createTime',
      width:300,
    },
    {
      title: '变动明细',
      dataIndex: 'detail',
    },
  ];
  const route = useRoute();
  onMounted(() => {
    getMemberWallet({ memberId: route.query.id }).then((res: any) => {
      console.log(res);
      if (res.data.code == 200) {
        memberWalletInfo.value = res.data.result;
      }
    });
  });
  // 传递的参数
  const apiParams = {
    memberId: route.query.id,
  };
</script>

<style lang="less" scoped>
  @import '../memberDetail.less';
</style>
