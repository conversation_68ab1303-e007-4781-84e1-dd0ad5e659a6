import request, { Method } from '@/utils/axios';

import { ParamsRule } from '@/types/global';

import qs from 'query-string';

// 获取首页楼层装数据
export function getHomePage(params: ParamsRule) {
  return request({
    url: `/settings/pageData/${params.pageClientType}/pageDataList`,
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 修改首页页面开启状态
export function changeHomePage(id: string | number) {
  return request({
    url: `/settings/pageData/release/${id}`,
    method: Method.PUT,
    needToken: true,
  });
}

// 添加楼层装修
export function opeartionHomePage(params: any, type = 'save') {
  let url = `/settings/pageData/${type}`;
  if (type !== 'save') {
    url = `/settings/pageData/${type}/${params.id}`;
  }
  return request({
    url,
    method: type === 'save' ? Method.POST : Method.PUT,
    needToken: true,

    data: qs.stringify(params),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  });
}

export function removeHomePage(id: string | number) {
  return request({
    url: `/settings/pageData/removePageData/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}

export function getHomePageDetail(id: string | number) {
  return request({
    url: `/settings/pageData/${id}`,
    method: Method.GET,
    needToken: true,
  });
}

export function getCurrentPermissionList(id?: string | number) {
  return request({
    url: `/menu/memberMenu?version=v3`,
    method: Method.GET,
    needToken: true,
  });
}
