<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { articleCateList, articleDetail, articleList } from '@/api/common'

const route = useRoute()

const categoryList = ref<Array<any>>([]) // 文章分类列表
const essayList = ref<Array<any>>([]) // 分类下的文章列表
const essayDetail = ref<any>({}) // 文章详情
const params = ref({ // 请求参数
  pageNumber: 1,
  pageSize: 100,
  categoryId: '',
  sort: 'sort',
})
const showList = ref(true)// 展示文章列表
const defultOpenKeys = ref<Array<any>>([])
const defultSelectedKeys = ref<Array<any>>([])

// 点击菜单项时触发
function handleSubMenu() {
}
// 点击子菜单时触发
function handleMenuItem(key: any) {
  getList(key)
  essayDetail.value = ''
  showList.value = true
}
// 文章分类列表
function getCateList() {
  articleCateList().then((res) => {
    if (res.data.success) {
      categoryList.value = res.data.result
      if (route.query.id) {
        defultSelectedKeys.value.push(essayDetail.value.categoryId)
        categoryList.value.forEach((e) => {
          if (e.children.length) {
            e.children.forEach((i: any) => {
              if (i.id === essayDetail.value.categoryId)
                defultOpenKeys.value.push(e.id)
            })
          }
          else {
            delete e.children
          }
        })
      }
      else {
        defultSelectedKeys.value.push(categoryList.value[0].children[0].id)
        defultOpenKeys.value.push(categoryList.value[0].id)
      }
      getList(defultSelectedKeys.value[0])
    }
  })
}
// 文章列表
function getList(id: any) {
  params.value.categoryId = id
  articleList(params.value).then((res) => {
    if (res.data.success)
      essayList.value = res.data.result.records
  })
}
// 获取文章详情
async function getDetail(id: any) { // 文章详情
  await articleDetail(id).then((res) => {
    if (res.data.success) {
      essayDetail.value = res.data.result
      showList.value = false
    }
  })
}

onMounted(async () => {
  const articleId = route.query.id
  if (articleId)
    await getDetail(articleId)

  getCateList()
})
</script>

<template>
  <div class="home-bgcolor">
    <!-- 搜索框、logo -->
    <Search />
    <!-- 商品分类 -->
    <!-- <CateNav :showNavBar="true" :showAlways="false"></CateNav> -->
    <div class="title-bg">
      <p><span>文章帮助中心</span></p>
    </div>
    <div class="home-container pb_20">
      <div class="side-bar">
        <div class="side-title mb_10">
          文章分类列表
        </div>
        <!-- 循环导航栏 -->
        <a-menu :style="{ width: '200px' }" :default-open-keys="defultOpenKeys"
          :default-selected-keys="defultSelectedKeys" @sub-menu-click="handleSubMenu" @menu-item-click="handleMenuItem">
          <a-sub-menu v-for="(menu) in categoryList" :key="menu.id">
            <template #title>
              {{ menu.articleCategoryName }}
            </template>
            <a-menu-item v-for="(children) in menu.children" :key="children.id">
              {{ children.articleCategoryName }}
            </a-menu-item>
          </a-sub-menu>
        </a-menu>
      </div>
      <div class="main-body">
        <!-- 文章列表 -->
        <div v-show="showList" class="essay-list">
          <div v-for="(article, index) in essayList" v-if="essayList && essayList.length" :key="index"
            class="essay-item hover-pointer mt_10 mb_10 pl_20" @click="getDetail(article.id)">
            {{ article.title }}
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24">
              <path fill="#438CDE"
                d="m19.164 12l-6.207-6.207l-1.414 1.414L16.336 12l-4.793 4.793l1.414 1.414zm-5.65 0L7.307 5.793L5.893 7.207L10.686 12l-4.793 4.793l1.414 1.414z" />
            </svg>
          </div>
          <Empty v-else />
        </div>
        <!-- 文章详情 -->
        <div v-show="!showList" class="essay-detail">
          <div class="back-btn mb_10 hover-pointer" @click="showList = true">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24">
              <path fill="#438CDE"
                d="m4.836 12l6.207 6.207l1.414-1.414L7.664 12l4.793-4.793l-1.414-1.414zm5.65 0l6.207 6.207l1.414-1.414L13.314 12l4.793-4.793l-1.414-1.414z" />
            </svg>返回上一级
          </div>
          <h2 class="mt_20 mb_20">
            {{ essayDetail.title }}
          </h2>
          <div class="mt_10 mb_10" v-html="essayDetail.content" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
@import '../user/home.less';

.title-bg {
  border-top: 2px solid @theme_color;
  height: 40px;
  width: 100%;
  background-color: #999999;

  p {
    width: 1200px;
    font-size: 20px;
    height: 40px;
    line-height: 40px;
    color: #fff;
    margin: 0 auto;

    span {
      display: block;
      width: 260px;
      text-align: center;
    }
  }
}

.side-title {
  background-color: #999999;
  color: #ffffff;
  text-align: center;
  height: 34px;
  line-height: 34px;
  font-size: 18px;
}

.essay-list {
  .essay-item {
    color: @link_color;
    display: flex;
    align-items: center;
    height: 50px;
    line-height: 50px;
    font-size: 14px;
    border-radius: 6px;
    background-color: #fbfbfb;
    box-sizing: border-box;
  }
}

.essay-detail {
  color: #333333;
  line-height: 24px;
  font-size: 14px;

  .back-btn {
    display: flex;
    align-items: center;
    color: @link_color;
  }

  p {
    margin: 4px 0 !important;
  }
}
</style>
