<template>
  <a-card class="general-card" title="商家对账" :bordered="false">
    <!-- 搜索 -->
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <!-- 表格 -->
    <tablePage ref="tablePageRef" :columns="columnsTable" :methods="sortMethods" :api="getBuyBillPage"
      :api-params="apiParams" @detail="detailed" :bordered="true">
      <template #time="{ data }">{{ data.startTime }}<span style="margin: 0 10px">-</span>{{ data.endTime }}</template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
import searchTable from '@/components/search-column/index.vue';
import tablePage from '@/components/table-pages/index.vue';
import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
import { getBuyBillPage } from '@/api/shops';
import { useRouter } from 'vue-router';
import { billStatus } from '@/utils/tools';
import { ref } from 'vue';

const router = useRouter()
const tablePageRef = ref('');
// 查询海选列表
const columnsSearch: Array<SearchRule> = [
  {
    label: '账单编号',
    model: 'sn',
    disabled: false,
    input: true,
  },
  {
    label: '出帐时间',
    disabled: false,
    datePicker: {
      type: 'range',
    },
  },
];

// 表格搜索列表
const columnsTable: ColumnsDataRule[] = [
  {
    title: '账单号',
    dataIndex: 'sn',
    width: 160,
  },
  {
    title: '生成时间',
    dataIndex: 'createTime',
    width: 160,
  },
  {
    title: '结算时间段',
    width: 250,
    ellipsis: false,
    dataIndex: 'startTime',
    slot: true,
    slotTemplate: 'time',
  },

  {
    title: '店铺名称',
    dataIndex: 'storeName',
    width: 160,
  },
  {
    title: '结算金额',
    dataIndex: 'billPrice',
    width: 160,
    currency: true,
  },
  {
    title: '状态',
    dataIndex: 'billStatus',
    slot: true,
    width: 100,
    slotData: {
      badge: billStatus,
    },
  },
];
// 操作列表
const sortMethods: MethodsRule = {
  title: '操作',
  width: 200,
  methods: [
    {
      title: '详细',
      callback: 'detail',
      type:'text',
      status:'success'
    },
  ],
};

const apiParams = ref({
  billStatus: 'CHECK',
});
// 详细
const detailed = (v: any) => {
  localStorage.setItem('settleID', v.record.id)
  router.push({
    name: 'bill-detail', query: {
      id: v.record.id
    }
  })
}
</script>

<style scoped></style>
