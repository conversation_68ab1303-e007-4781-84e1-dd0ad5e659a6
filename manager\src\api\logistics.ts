import request, { Method } from '@/utils/axios';
import { ParamsRule } from '@/types/global';


export function getLogistics(params: ParamsRule) {
  return request({
    url: '/other/logistics/getByPage',
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 删除物流公司
export function delLogistics(id: number | string) {
  return request({
    url: `/other/logistics/delete/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}
// 添加物流公司
export function addLogistics(params: any) {
  return request({
    url: '/other/logistics/save',
    method: Method.POST,
    needToken: true,
    params,
  });
}
// 编辑物流公司
export function updateLogistics(id: number | string, params: any) {
  return request({
    url: `/other/logistics/${id}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
