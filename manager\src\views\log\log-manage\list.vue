<template>
  <a-card class="general-card" title="日志管理" :bordered="false">
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <a-row style="margin-bottom: 16px">
      <a-col :span="16">
        <a-space>
          <a-button
            type="primary"
            @click="
              (val) => {
                tablePageRef.init(val);
              }
            "
          >
            刷新
          </a-button>
          <!-- <a-button type="dashed"> 开启提示 </a-button> -->
          <!-- <a-button type="dashed" @click="openTip = !openTip">
            {{ openTip ? "关闭提示" : "开启提示" }}
          </a-button> -->
        </a-space>
      </a-col>
    </a-row>
    <!-- <a-row v-show="openTip">
      <a-alert>
        <span>展示详细内容</span>
      </a-alert>
    </a-row> -->
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :api="getLogListData"
      :api-params="apiParams"
      :bordered="true"
    />
    <!-- <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getGoodsListDataSeller"
      @detail="JumpProductDetails"
    /> -->
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { getLogListData } from '@/api/setting';
  import { ref } from 'vue';
  // import { useRouter } from 'vue-router';

  const apiParams = ref({});
  const tablePageRef = ref<any>();
  // const router = useRouter();
  // const openTip<boolean>=ref(false)
  const columnsSearch: Array<SearchRule> = [
    {
      label: '搜索日志',
      model: 'searchKey',
      disabled: false,
      input: true,
    },
    {
      label: '操作人',
      model: 'operatorName',
      disabled: false,
      input: true,
    },
    {
      label: '下单时间',
      disabled: false,
      datePicker: {
        type: 'range',
      },
    },
  ];

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '操作名称',
      dataIndex: 'name',
    },
    {
      title: '日志内容',
      dataIndex: 'customerLog',
    },
    {
      title: '操作用户',
      dataIndex: 'username',
    },
    {
      title: '操作时间',
      dataIndex: 'createTime',
    },
  ];

  // const columnsTable_dev: ColumnsDataRule[] = [
  //   {
  //     title: '操作名称',
  //     dataIndex: 'name',
  //   },
  //   {
  //     title: '日志内容',
  //     dataIndex: 'customerLog',
  //   },
  //   {
  //     title: '操作用户',
  //     dataIndex: 'username',
  //   },
  //    {
  //     title: 'IP',
  //     dataIndex: 'ip',
  //   },
  //   {
  //     title: 'IP信息',
  //     dataIndex: 'ipInfo',
  //   },
  //   {
  //     title: '请求路径',
  //     dataIndex: 'requestUrl',
  //   },
  //    {
  //     title: '请求类型',
  //     dataIndex: 'requestType',
  //   },
  //    {
  //     title: '请求参数',
  //     dataIndex: 'requestUrl',
  //   },
  //    {
  //     title: '耗时-毫秒',
  //     dataIndex: 'costTime',
  //   },
  //   {
  //     title: '操作时间',
  //     dataIndex: 'createTime',
  //   },
  // ];

  // const sortMethods: MethodsRule = {
  //   title: '操作',
  //   width: 250,
  //   fixed: 'right',
  //   methods: [
  //     {
  //       title: '查看',
  //       callback: 'detail',
  //     },

  //     {
  //       title: '下架',
  //       callback: 'detail',
  //     },
  //   ],
  // };
</script>
