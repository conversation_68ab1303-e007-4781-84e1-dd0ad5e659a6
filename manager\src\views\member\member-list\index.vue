<template>
  <div>
    <a-card>
      <pageheader />
    </a-card>
    <a-card class="mt_10">
      <a-tabs default-active-key="point">
        <a-tab-pane
          v-for="item in tabPanes"
          :key="item.key"
          :title="item.title"
        >
          <component :is="item.component" />
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import pageheader from './components/pageheader.vue';
  import mypoint from './components/mypoint.vue';
  import myorders from './components/myorders.vue';
  import myaddress from './components/myaddress.vue';
  import mywallet from './components/mywallet.vue';
  import myreceipt from './components/myreceipt.vue';

  const tabPanes = [
    {
      key: 'point',
      title: 'TA的积分',
      component: mypoint,
    },
    {
      key: 'order',
      title: 'TA的订单',
      component: myorders,
    },
    {
      key: 'address',
      title: 'TA收货地址',
      component: myaddress,
    },
    {
      key: 'wallet',
      title: 'TA的余额',
      component: mywallet,
    },
    {
      key: 'receipt',
      title: 'TA的发票',
      component: myreceipt,
    },
  ];
</script>

<style lang="less" scoped></style>
