<template>
  <div>
    <a-drawer :width="966" :visible="show" @ok="handleOk" @cancel="handleCancel" unmountOnClose>
      <template #title>
        选择链接
      </template>
      <a-tabs mt-10px :default-active-key="active" v-model="active" @change="(val: any) => { active = val }">
        <a-tab-pane v-for="(item) in tabList" :key="item.value" :title="item.label"> </a-tab-pane>
      </a-tabs>
      <div v-auto-animate>
        
        <goods templateModel="template" @selectTableChange="callback" v-if="active === 'goods'" />
        <shop templateModel="template" @selectTableChange="callback" v-if="active === 'shops'" />
        <category v-if="active === 'category'" @selectTableChange="callback" templateModel="template" />
        <articleTpl templateModel="template" @selectTableChange="callback" v-if="active === 'pages'"></articleTpl>
        <other v-if="active === 'other'" @selectTableChange="callback"></other>
        <h5View v-if="active === 'url'" @selectTableChange="callback"></h5View>
        <sectionTpl :pageClientType="pageClientType" templateModel="template" @selectTableChange="callback" v-if="active === 'special'"></sectionTpl>
      </div>
    </a-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import goods from '@/views/goods/goods-list/list.vue'
// import goods from '@/views/operation/decoration/components/choose-goods.vue'
import category from '@/views/goods/category/list.vue'
import shop from '@/views/shop/shop-list/index.vue'
import sectionTpl from '@/views/operation/decoration/components/design-list.vue'
import articleTpl from '@/views/operation/article/article-list/index.vue'
import other from '@/views/operation/decoration/components/other.vue'
import h5View from '@/views/operation/decoration/components/h5-view.vue'
import { tabList } from '@/views/operation/decoration/components/receive'
import { onMounted } from 'vue';
import { useRoute } from 'vue-router'
const emit = defineEmits(['callback'])
const active = ref('goods')
const route = useRoute()
const pageClientType = ref<string>('')
// import { DragRule } from '@/views/operation/decoration/app/models/types';
// const props = defineProps<{
//   res?: DragRule | any,
// }>()

onMounted(()=>{
  if(route.name === 'pc-invent'){
    pageClientType.value = 'PC'
  }
})


const show = ref(false);
const open = () => {
  show.value = true
}
defineExpose({ open })

// 选值回调
function callback(val: any) {
  emit('callback', val)
}

const handleOk = () => {
  show.value = false;
};
const handleCancel = () => {
  show.value = false;
}
</script>

<style scoped>
</style>
