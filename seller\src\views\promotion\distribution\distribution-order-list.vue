<template>
  <a-card class="general-card" title="分销订单" :bordered="false">
    <searchTable
      :columns="columnsSearch"
      time-type="timestamp"
      @reset="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
      @search="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
    >
    </searchTable>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :api="getDistributionOrder"
      :api-params="apiParams"
      :bordered="true"
    >
      <template #status="{ data }">
        <a-badge v-if="data.distributionOrderStatus == 'WAIT_BILL'" color="blue"
          >待结算</a-badge
        >
        <a-badge
          v-else-if="data.distributionOrderStatus == 'NO_COMPLETED'"
          color="red"
          >未完成</a-badge
        >
        <a-badge
          v-else-if="data.distributionOrderStatus == 'WAIT_CASH'"
          color="orange"
          >待提现</a-badge
        >
        <a-badge
          v-else-if="data.distributionOrderStatus == 'COMPLETE_CASH'"
          color="green"
          >提现完成</a-badge
        >
        <a-badge 
          v-else-if="data.distributionOrderStatus == 'CANCEL'" 
          color="red"
          >订单取消</a-badge
        >
        <a-badge 
          v-else-if="data.distributionOrderStatus == 'REFUND'" 
          color="magenta"
          >退款</a-badge
        >
        <a-badge v-else status="normal" text="暂无状态"></a-badge>
      </template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { getDistributionOrder } from '@/api/promotion';
  import { ref } from 'vue';

  const tablePageRef = ref('');
  const apiParams = ref({});

  const columnsSearch: Array<SearchRule> = [
    {
      label: '订单编号',
      model: 'orderSn',
      disabled: false,
      input: true,
    },
    {
      label: '订单时间',
      model: 'goodsName',
      disabled: false,
      datePicker: {
        type: 'range',
      },
    },
  ];

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '订单编号',
      dataIndex: 'orderSn',
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
      slot: true,
      width: 300,
      ellipsis: false,
      slotData: {
        goods: {
          goodsImage: 'image',
          goodsName: 'goodsName',
        },
      },
    },
    {
      title: '状态',
      dataIndex: 'distributionOrderStatus',
      slot: true,
      slotTemplate: 'status',
    },
    {
      title: '佣金金额',
      dataIndex: 'rebate',
      currency: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
    },
    {
      title: '解冻日期（T+1）',
      dataIndex: 'settleCycle',
    },
  ];
</script>

<style lang="less">
  .arco-badge-status-text {
    font-size: 14px !important;
  }
</style>
