<template>
  <a-col class="banner">
    <a-col :span="8">
      <a-typography-title :heading="5" style="margin-top: 0">
        欢迎回来 {{ userInfo.name }}
        <a-button style="margin-left: 20px" type="outline" @click="im">点击登录客服</a-button>
        <a-button style="margin-left: 10px" type="text" @click="clearStorage">点击清除缓存</a-button>
      </a-typography-title>
    </a-col>
    <a-divider class="panel-border" />


    <a-modal v-model:visible="visible" title="提示" @before-ok="confirmClearStorage">
      <a-alert title="提示">
        清空缓存会清空当前登录的账户信息、本地缓存，是否确认清空？
      </a-alert>
    </a-modal>
  </a-col>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { useUserStore } from '@/store';
import store from '@/utils/storage';
import { getIMDetail } from '@/api/common';
import { getStoreInfo } from '@/api/login';
import { Message } from '@arco-design/web-vue';
import { useRouter } from 'vue-router';
import Cookies from 'js-cookie';
const router = useRouter();
const visible = ref<boolean>(false);
const userStore = useUserStore();
const userInfo = computed(() => {
  return {
    name: userStore.userInfo.storeName,
  };
});
const accessToken = ref(store.getAccessToken());
const IMLink = ref('');

// 获取im信息
const getIMDetailMethods = async () => {
  const res = await getIMDetail();
  console.log(res, 'ressss');
  if (res.data.success) {
    IMLink.value = res.data.result;
  }
};


const clearStorage = () => {
  visible.value = true;
}


const confirmClearStorage = () => {
  localStorage.clear();
  router.push('/login')
  .then(() => {
    // 重新刷新页面
    location.reload()
  })
}

// 点击登录im的时候需要去判断一下当前店铺信息是否失效
// 失效的话重新请求刷新token保证最新的token去访问im
const im = async () => {
  // 获取访问Token
  getIMDetailMethods();
  const userInfo = await getStoreInfo();

  if (userInfo.data.success && IMLink.value) {
    window.open(`${IMLink.value}?token=${accessToken.value}`);
  } else {
    Message.error('请登录后再联系客服');
  }
};
</script>

<style scoped lang="less">
.banner {
  width: 100%;
  padding: 20px 20px 0 20px;
  background-color: var(--color-bg-2);
  border-radius: 4px 4px 0 0;
}

:deep(.arco-icon-home) {
  margin-right: 6px;
}
</style>
