<template>
  <a-card class="general-card" title="微信进件" :bordered="false">
    <!-- 搜索 -->
    <searchTable :columns="columnsSearch"  @reset=" (val) => { apiParams = { ...apiParams, ...val }; } " @search=" (val) => { apiParams = { ...apiParams, ...val };}"> </searchTable>
    <a-button style="margin-bottom: 15px" type="primary" @click="add" >服务商进件</a-button>
    <!-- 表格 -->
    <tablePage ref="tablePageRef" :columns="columnsTable" :methods="sortMethods" :api="getConstruction" :api-params="apiParams" @detail="seeStore" :bordered="true">
      <template #btnList="{ data }">
        <a-space>
          <a-button type="text" status="warning" @click="edit(data)" >编辑</a-button>
          <a-button  v-if="data.status != 'FINISH'" @click="tosync(data)"  type="text">同步</a-button>
          <a-button v-if="data.status === 'ACCOUNT_NEED_VERIFY'" @click="asyncPayAccount(data)" type="text"  status="success" >汇款账户验证信息</a-button>
        </a-space>
      </template>
      <template #organizationType="{ data }">
        <span v-if="data.organizationType === '2401'">小微商户</span>
        <span v-if="data.organizationType === '2500'">个体卖家</span>
        <span v-if="data.organizationType === '4'">个体工商户</span>
        <span v-if="data.organizationType === '2'">企业</span>
        <span v-if="data.organizationType === '3'">事业单位</span>
        <span v-if="data.organizationType === '1708'">社会组织</span>
        <span v-if="data.organizationType === '2502'">政府机关</span>
      </template>
    </tablePage>
  </a-card>
</template>
  
  <script setup lang="ts">
import searchTable from '@/components/search-column/index.vue';
import tablePage from '@/components/table-pages/index.vue';
import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
import { getConstruction, syncStatus } from '@/api/shops';
import { applicationStatus } from '@/utils/tools';
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useAppStore } from '@/store';
import { Message } from '@arco-design/web-vue';

const router = useRouter();
const tablePageRef = ref('');
const apiParams = ref();
const appStore = useAppStore();
// 主体类型

const organizationTypeWay =  [
  {label: "小微商户", value: "2401",},
  {label: "个体卖家", value: "2500",},
  {label: "个体工商户", value: "4",},
  {label: "企业", value: "2",},
  {label: "事业单位", value: "3",},
  {label: "社会组织", value: "1708",},
  {label: "政府机关", value: "2502",},
];
// 查询列表
const columnsSearch: Array<SearchRule> = [
  {
    label: '商户名称',
    model: 'merchantShortname',
    disabled: false,
    input: true,
  },
  {
    label: '业务申请编号',
    model: 'outRequestNo',
    disabled: false,
    input: true,
  },
  {
    label: '主体类型',
    model: 'organizationType',
    disabled: false,
    input: true,
  },
  {
    label: '获取方式',
    model: 'organizationType',
    disabled: false,
    select: {
      options: organizationTypeWay,
    },
  },
  {
    label: '创建时间',
    disabled: false,
    datePicker: {
      type: 'range',
    },
  },
];

// 表格搜索列表
const columnsTable: ColumnsDataRule[] = [
  {
    title: '商户名称',
    dataIndex: 'merchantShortname',
  },
  {
    title: '主体类型',
    dataIndex: 'organizationType',
    slot: true,
    slotTemplate: 'organizationType',
  },
  {
    title: '业务申请编号',
    dataIndex: 'outRequestNo',
  },
  {
    title: '申请状态',
    dataIndex: 'status',
    slot: true,
    slotData: {
      badge: applicationStatus,
    },
  },
  {
    title: '错误信息',
    dataIndex: 'errorMessage',
  },
  {
    title: '特约商户号',
    dataIndex: 'subMchid',
  },
  {
    title: '签约链接',
    dataIndex: 'signUrl',
  },
];
// 操作列表
const sortMethods: MethodsRule = {
  title: '操作',
  width: 350,
  methods: [
    {
      slot: true,
      slotTemplate: 'btnList',
    },
  ],
};
// 编辑
const edit = (data: any) => {
  const url = router.resolve({
    name: 'submit-application',
    query: { id: data.outRequestNo },
  });
  window.open(url.href, '_blank');
};
// 同步
const tosync = (data: any) => {
  syncStatus(data.outRequestNo).then((res) => {
    if (res.data.success) {
      Message.success("同步成功");
      tablePageRef.value?.init();
    }
  });
};
// 汇款账户验证信息
const asyncPayAccount = (data: any) => {};
// 添加
const add = () => {
  const url = router.resolve({ name: 'submit-application', query: {} });
  window.open(url.href, '_blank');
};
// 查看
const seeStore = (v: any) => {
  router.push({ name: 'shop-operation', query: { id: v.record.id } });
};
</script>
  
  <style scoped></style>
  