<template>
  <div>
    <a-modal
      v-model:visible="descFlag"
      :align-center="false"
      :footer="false"
      width="auto"
      @submit="handleSubmitModal"
    >
      <template #title> 查看用户{{ editMemberModal.data.username }} </template>
      <a-form
        ref="formRef"
        :model="editMemberModal"
        :style="{ width: '650px' }"
      >
        <a-form-item field="province" label="头像">
          <img :src="editMemberModal.data.face" alt="" class="face" />
          <a-button type="text" @click="() => (showOssManager = true)">修改</a-button>
        </a-form-item>
        <a-form-item field="province" label="用户名">
          <a-input
            v-model="editMemberModal.data.username"
            disabled
            :style="{ width: '320px' }"
          />
        </a-form-item>
        <a-form-item field="province" label="用户昵称">
          <a-input
            v-model="editMemberModal.data.nickName"
            :style="{ width: '320px' }"
          />
        </a-form-item>
        <a-form-item field="province" label="性别">
          <a-radio-group v-model="editMemberModal.data.sex" type="button">
            <a-radio :value="1">男</a-radio>
            <a-radio :value="0">女</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item field="province" label="修改密码">
          <a-input-password
            v-model="editMemberModal.data.newPassword"
            :style="{ width: '320px' }"
            allow-clear
          />
        </a-form-item>
        <a-form-item field="province" label="生日">
          <a-date-picker
            v-model="editMemberModal.data.birthday"
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item field="province" label="所在地">
          <a-input
            v-if="updateRegion"
            v-model="editMemberModal.data.region"
            :style="{ width: '320px' }"
            allow-clear
            disabled
          />
          <city v-if="updateRegion == false" @callback="cityRes" :ids="editMemberModal.data.regionId" :address="editMemberModal.data.region"></city>
          <a-button
            v-if="updateRegion == true"
            type="text"
            @click="() => (updateRegion = false)"
            >修改</a-button
          >
        </a-form-item>
        <a-form-item label="操作">
          <a-button html-type="handleSubmitModal" type="primary">保存</a-button>
        </a-form-item>
      </a-form>
    </a-modal>
    <!--<a-modal v-model:visible="ossModel" :width="1100" @ok="handleOk" @cancel="handleCancel">-->
      <!--<ossManage :close-model="handleOk" @changOssImage="changOssImage"></ossManage>-->
    <!--</a-modal>-->
    <a-modal v-model:visible="showOssManager" :width="1100"  @ok="handleOss" title="oss资源管理" @cancel="showOssManager = false">
      <ossManages @selected="changOssImage" :isSingle="true"></ossManages>
    </a-modal>

  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import ossManage from '@/views/setting/oss-manage/index.vue';
  import ossManages from '@/components/oss-manage/index.vue';
  import city from '@/components/m-city/index.vue';
  import { md5 } from '@/utils/md5';
  import { Message } from '@arco-design/web-vue';
  import { updateMember } from '@/api/member';

  const descFlag = ref<boolean>(false); // 修改会员弹框
  const updateRegion = ref<boolean>(true); // 所在地隐藏
  const ossModel = ref<boolean>(false); // oss弹框
  const showOssManager = ref<boolean>(false); // oss弹框
  const selectedSku = ref(); // 选择的sku

  const editMemberModal = reactive({
    // 编辑
    data: {
      face: '',
      username: '',
      nickName: '',
      sex: '0',
      newPassword: null,
      birthday: '',
      region: '',
      id: [],
      regionId: [],
    },
  });
  const emit = defineEmits(['init']);
  // oss弹框关闭
  const handleOk = () => {
    // ossModel.value = false;
    showOssManager.value = false;
  };
  const handleCancel = () => {
    // ossModel.value = false;
    showOssManager.value = false;
  };
  // // oss传过来的值
  // const changOssImage = (val: any) => {
  //   editMemberModal.data.face = val;
  // };
  // oss资源确定
  const handleOss = () => {
    showOssManager.value = false;
    editMemberModal.data.face = selectedSku.value[selectedSku.value.length-1].url;
  };
  // oss资源改变
  const changOssImage = (val: any) => {
    selectedSku.value = [];
    val.forEach((item: any)=>{
      selectedSku.value.push({url:item.url})
    })
  };
  // 级联传过来的值
  const cityRes = (val: any) => {
    editMemberModal.data.region = val.cities.join(',');
    editMemberModal.data.regionId = val.ids.join(',');
  };
  // 提交
  const handleSubmitModal = () => {
    const {
      nickName,
      sex,
      username,
      face,
      id,
      birthday,
      region,
      regionId,
      newPassword,
    } = editMemberModal.data;

    const params = {
      birthday,
      face,
      id,
      nickName,
      // newPassword: md5(editMemberModal.data.newPassword),
      password: '',
      region,
      regionId,
      sex: Number(sex),
      username,
    };

    if (newPassword) {
      params.password = md5(newPassword);
    }
    // if (newPassword) {
    //   params.password = md5(newPassword);
    // }
    updateMember(JSON.parse(JSON.stringify(params))).then((res: any) => {
      if (res.data.code == 200) {
        descFlag.value = false;
        Message.success(`修改成功!`);
        emit('init', true);
      }
    });
  };
  defineExpose({
    descFlag,
    editMemberModal,
  });
</script>

<style lang="less" scoped>
  .face {
    width: 60px;
    height: 60px;
    border-radius: 50%;
  }

  .edit {
    margin-left: 10px;
  }
</style>
