<template>
  <div>
    <Card _Title="评价详情" :_Size="16"></Card>
    <div>
      <span class="light-text-color">创建人：</span>
      <span>{{ secrecyMobile(orderGoods.createBy)}}</span>
      <span class="light-text-color ml_20">{{orderGoods.createTime}}</span>
    </div>
    <!-- 物流评分、服务评分 -->
    <div class="delivery-rate">
      <div class="title">物流服务评价：</div>
      <div class="rate-list flex">
        <span>物流评价：<a-rate v-model="orderGoods.deliveryScore" disabled /></span>
        <span>服务评价：<a-rate v-model="orderGoods.serviceScore" disabled /></span>
        <span>描述评价：<a-rate v-model="orderGoods.descriptionScore" disabled /></span>
      </div>
    </div>
    <!-- 添加订单评价  左侧商品详情  右侧评价框 -->
    <div class="goods-eval flex pt_20 pb_20">
      <div class="goods-con">
        <img :src="orderGoods.goodsImage" class="hover-pointer" alt="" width="100" @click="goGoodsDetail(orderGoods.skuId, orderGoods.goodsId)">
        <p class="hover-pointer hover-color" @click="goGoodsDetail(orderGoods.skuId, orderGoods.goodsId)">{{orderGoods.goodsName}}</p>
      </div>
      <div class="eval-con">
        <div>
          <span class="light-text-color">商品评价：</span>
          <a-radio-group v-model="orderGoods.grade" type="button" class="mb_10" disabled>
            <a-radio value="GOOD">好评</a-radio>
            <a-radio value="MODERATE">中评</a-radio>
            <a-radio value="WORSE">差评</a-radio>
          </a-radio-group>
          <a-textarea v-model="orderGoods.content" :max-length="500" allow-clear show-word-limit :auto-size="{minRows:4,maxRows:6}" disabled></a-textarea>
          <div style="display: flex; align-items: center;">
            <template v-if="orderGoods.images">
              <div class="demo-upload-list" v-for="(img, index) in orderGoods.images.split(',')" :key="index">
                <a-image width="60" height="60" :src="img"/>
              </div>
            </template>
          </div>
          <div class="light-text-color mt_20">商家回复：<span>{{ orderGoods.reply }}</span></div>
          <div style="display: flex; align-items: center;">
            <template v-if="orderGoods.replyImage">
              <div class="demo-upload-list" v-for="(img, index) in orderGoods.replyImage.split(',')" :key="index">
                <a-image width="60" height="60" :src="img"/>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
  import { useRouter, useRoute } from 'vue-router';
  import { ref, onMounted } from 'vue';
  import { secrecyMobile } from '@/utils/filters';
  import { evaluationDetail } from '@/api/member';

  const router = useRouter();
  const route = useRoute();
  const orderGoods = ref<any>({});

  const getOrderDetail = () => {
    evaluationDetail(route.query.id).then(res => {
      if (res.data.success) {
        orderGoods.value = res.data.result;
      }
    })
  };

  // 跳转商品详情
  const goGoodsDetail = (skuId: any, goodsId: any) => {
    let routeUrl = router.resolve({path: "/goodsDetail", query: { skuId, goodsId },});
    window.open(routeUrl.href, "_blank");
  };



  onMounted(() => {
    getOrderDetail();
  });
</script>

<style scoped lang="less">
  .delivery-rate {
    display: flex;
    align-items: flex-end;
    margin: 30px 0;
    .title {
      font-size: 18px;
    }
    .rate-list > span {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      height: 20px;
      /*line-height: 20px;*/
      margin-right: 30px;
      color: @light_text_color;
    }
  }
  .goods-eval {
    border-top: 1px solid #eeeeee;
    border-bottom: 1px solid #eeeeee;
    .goods-con {
      width: 30%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
    }
    .eval-con {
      width: 70%;
    }
  }

  .demo-upload-list {
    display: inline-block;
    width: 60px;
    height: 60px;
    text-align: center;
    line-height: 60px;
    border: 1px solid transparent;
    border-radius: 4px;
    overflow: hidden;
    background: #fff;
    position: relative;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
    margin-right: 4px;
    margin-top: 10px;
  }
  .demo-upload-list img {
    width: 100%;
    height: 100%;
  }
</style>
