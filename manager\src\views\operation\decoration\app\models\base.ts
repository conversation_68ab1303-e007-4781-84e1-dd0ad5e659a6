import { DragRule } from './types'

const baseModel: Array<DragRule> = [
  {
    type: "whiteSpace",
    name: "空白",
    roles: ['height'],
    border: 'normal',
    models: [{
      label: "背景颜色",
      model: "background",
      bind: "background"
    },],
    data: {
      height: 20,
    }
  },
  {
    type: "search",
    name: "搜索",
    roles: ['height', 'round'],
    models: [
      {
        label: "菜单标题",
        model: "text"
      },
      {
        label: "背景颜色",
        model: "background",
        bind: "background"
      },
    ],
    border: 'normal',
    data: {
      height: 35,
      text: '搜索',
      textColor: "#999999",
      background: "#ededed",
      align: "center"
    }
  },
]
// 基本组件
export default baseModel

