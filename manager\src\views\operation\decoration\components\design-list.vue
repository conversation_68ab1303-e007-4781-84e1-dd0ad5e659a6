<template>
  <a-card class="general-card" :bordered="false">
    <a-row v-if="selectedMember" style="margin-bottom: 16px">
      <a-col :span="16">
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon>
              <icon-plus />
            </template>
            添加页面-{{ pageType === 'INDEX' ? '首页' : '微页面' }}
          </a-button>
        </a-space>
      </a-col>
    </a-row>
    <a-tabs @change="(val) => changePageType(val)" v-model="pageType">
      <a-tab-pane key="INDEX" v-if="isNormal" title="首页">
      </a-tab-pane>
      <a-tab-pane key="SPECIAL" title="微页面">
      </a-tab-pane>

    </a-tabs>
    <tablePage @update="update" @delete="remove" ref="tablePageRef" @selectTableChange="chosenSetion"
      :checkbox="isNormal ? false : true" :radio="isNormal ? false : true" :columns="columnsTable" :methods="sortMethods"
      :api="getHomePage" :api-params="apiParams">
      <template #pageShow="{ data }">
        <a-switch @change="handleChangePage(data)" checked-value="OPEN" unchecked-value="CLOSE" :loading="loading"
          v-model="data.pageShow">
          <template #checked>
            开
          </template>
          <template #unchecked>
            关
          </template>
        </a-switch>
      </template>
    </tablePage>
    <a-modal v-model:visible="visible" @cancel="visible = false" :on-before-ok="confirmRemove" unmountOnClose>
      <template #title>
        提示
      </template>
      <div>确定要删除当前 {{ currentData.name }} ?
      </div>
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">

import { changeHomePage, getHomePage, removeHomePage } from '@/api/setting';
import tablePage from '@/components/table-pages/index.vue';
import { ColumnsDataRule, MethodsRule } from '@/types/global';

import { Message } from '@arco-design/web-vue';
import { onMounted, ref, } from 'vue';
import { useRouter } from 'vue-router';
// 组件模式
const props = defineProps({
  templateModel: {
    type: String,
    default: 'normal',
  },
  pageClientType: {
    type: String,
    default: 'H5'
  },

})
const currentData = ref<any>({});
const visible = ref<boolean>(false);
const isNormal: boolean = props.templateModel === 'normal';
const loading = ref<boolean>(false);
const tablePageRef = ref<any>('');
const router = useRouter();
const emit = defineEmits<{ (e: 'callback', data: object): void, (e: 'selectTableChange', data: object): any }>();
const pageType = ref<string>(isNormal ? 'INDEX' : 'SPECIAL');
const selectedMember = ref<boolean>(true); // 是否显示按钮



let apiParams = {
  pageType: pageType.value,
  pageClientType: props.pageClientType,
};
const originColumns: ColumnsDataRule[] = [
{
    title: '页面ID',
    dataIndex: 'id',
  },
  {
    title: '页面名称',
    dataIndex: 'name',
  },
  {
    title: '页面状态',
    dataIndex: 'pageShow',
    slot: true,
    slotTemplate: "pageShow"
  },

];
let columnsTable: ColumnsDataRule[] = [
{
    title: '页面ID',
    dataIndex: 'id',
  },
  {
    title: '页面名称',
    dataIndex: 'name',
  },
  {
    title: '页面状态',
    dataIndex: 'pageShow',
    slot: true,
    slotTemplate: "pageShow"
  },

];

let sortMethods: MethodsRule = {
  title: '操作',
  width: 300,
  fixed: 'right',
  methods: [
    {
      title: '修改',
      callback: 'update',
      slot: true,

    },
    {
      title: '删除',
      callback: 'delete',
      slot: true,

    },

  ],
};

function update(val: any) {
  let url: any;

  if (props.pageClientType === 'H5')
    url = router.resolve({
      path: '/app-invent',
      query: {
        id: val.record.id,
        moduleName: val.record.name
      },
    })
  else {
    url = router.resolve({
      path: '/pc-invent',
      query: {
        id: val.record.id,
        moduleName: val.record.name
      },
    })
  }
  window.open(url.href, '_blank');
}

async function confirmRemove() {
  const res = await removeHomePage(currentData.value.id)
  if (res.data.success) {
    Message.success('删除成功')!
    tablePageRef.value.init()
  }
}

async function remove(val: any) {
  currentData.value = val.record
  visible.value = true
}

function changePageType(val: any) {
  pageType.value = val;
  apiParams.pageType = val
  if (val === 'INDEX') {
    columnsTable = JSON.parse(JSON.stringify(originColumns))
  } else {
    columnsTable.splice(2, 2)
  }
  init(true);
}

async function handleChangePage(val: any) {
  loading.value = true;
  const res = await changeHomePage(val.id);
  loading.value = false;
  if (res.data.success) {
    init(true);
  }
}

// 选中回调
function chosenSetion(val: any) {
  emit('selectTableChange', { ...val[0], ___type: 'special' })
}

function handleAdd() {
  let url: any;
  if (props.pageClientType === 'H5') {
    url = router.resolve({ path: '/app-invent', query: { pageType: pageType.value } });
  } else {
    url = router.resolve({
      path: '/pc-invent',
      query: { pageType: pageType.value }
    })
  }

  window.open(url.href, '_blank');
}
const init = (v: boolean) => {
  if (v == true) {
    tablePageRef.value.init();
  }
};
onMounted(() => {
  if (!isNormal) {
    columnsTable.splice(1, 1)
  }

})

defineExpose({
  selectedMember,
  sortMethods,
});
</script>

<style lang="less" scoped>
.face {
  width: 60px;
  height: 60px;
  border-radius: 50%;
}

.edit {
  margin-left: 10px;
}
</style>
