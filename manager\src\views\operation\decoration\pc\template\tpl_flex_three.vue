<template>
  <div flex flex-a-c flex-j-sb v-if="props.res">
    <div w-385px h-165px v-for="(item, index) in props.res.data.list" :key="index">
      <img class="img" :src="item.img" >
    </div>
  </div>
</template>

<script setup lang="ts">
import { DragRule } from '@/views/operation/decoration/app/models/types';

const props = defineProps<{
  res: DragRule,
}>()
</script>

<style scoped>
.img{
  width: 100%;
  height: 100%;
  display: block;
}
</style>
