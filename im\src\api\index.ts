import request, { Method } from '~/utils/axios'

// 获取用户浏览足迹
export function getViewHistory(client: 'store' | 'buyer') {
  return request({
    url: `/${client}/member/footprint`,
    method: Method.GET,
  })
}

// 获取聊天列表服务接口
export function getTalkList(client: 'store' | 'talk') {
  return request({
    url: client === 'store' ? `/im/talk/${client}/list` : `/im/talk/list`,
    method: Method.GET,
  })
}

export function getTalkDetail(params: { talkId: string, pageNumber: number }) {
  return request({
    url: `/im/message`,
    method: Method.GET,
    params,
  })
}

// 获取店铺相关设置信息
export function getStoreInfo() {
  return request({
    url: '/store/member/user',
    method: Method.GET,
  })
}

// 获取用户相关设置信息
export function getUserInfo() {
  return request({
    url: '/buyer/passport/member',
    method: Method.GET,
  })
}
