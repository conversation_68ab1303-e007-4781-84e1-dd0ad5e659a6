@font-face {
  font-family: 'icomoon';
  src:  url('icomoon.eot?jvagvf');
  src:  url('icomoon.eot?jvagvf#iefix') format('embedded-opentype'),
    url('icomoon.ttf?jvagvf') format('truetype'),
    url('icomoon.woff?jvagvf') format('woff'),
    url('icomoon.svg?jvagvf#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-wallet:before {
  content: "\e905";
  color: #3c56c6;
}
.icon-qrcode:before {
  content: "\e904";
  color: #999;
}
.icon-customer-service:before {
  content: "\e900";
}
.icon-next:before {
  content: "\e901";
}
.icon-qq:before {
  content: "\e902";
}
.icon-wechat:before {
  content: "\e903";
}
