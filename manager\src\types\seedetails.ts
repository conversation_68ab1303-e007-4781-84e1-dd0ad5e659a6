// 详情返回数据约束
export interface DetailRule {
  data: {
    categoryName: string[];
    sellingPoint: string;
    goodsUnit: string;
    salesModel?: string | boolean;
    id?: string;
    price: number;
    cost?: string;
    goodsGalleryList: string[];
    wholesaleList: null | any;
    intro: string;
    mobileIntro: string;
    tags?: string | null;
    [key: string]: any;
    // authFlag: string;
    // authMessage?: any;
    // brandId: string;
    // buyCount: number;
    // categoryPath: string;
    // commentNum: number;
    // createBy: number;
    // createTime: string;
    // deleteFlag: boolean;
    // goodsParamsDTOList: string | number[];
    // goodsType: string;
    // goodsVideo?: any;
    // grade: number;
    // marketEnable: string;
    // original: string;
    // params: string;
    // quantity: number;
    // recommend: boolean;
    // selfOperated: boolean;
    // skuList: Array<{
    //   authFlag: string;
    //   authMessage: any;
    //   big: string;
    //   brandId: string;
    //   buyCount: number;
    //   categoryPath: string;
    //   commentNum: any | null;
    //   cost: number;
    //   createBy: string;
    //   createTime: string;
    //   deleteFlag: boolean;
    //   freightTemplateId: string;
    //   goodsGalleryList: string[];
    //   goodsId: string;
    //   goodsName: string;
    //   goodsType: string;
    //   goodsUnit: string;
    //   goodsVideo?: any | null;
    //   grade: 100;
    //   intro: string;
    //   marketEnable: string;
    //   mobileIntro: string;
    //   original: string;
    //   price: number;
    //   promotionFlag?: any | null;
    //   promotionPrice?: any | null;
    //   quantity: number;
    //   recommend: Boolean;
    //   salesModel: string;
    //   selfOperated: boolean;
    //   sellingPoint: string;
    //   simpleSpecs: string;
    //   small: string;
    //   sn: string;
    //   specList: Array<{
    //     specImage: string[];
    //     specName: string;
    //     specType?: any | null;
    //     specValue?: any | null;
    //   }>;
    //   storeCategoryPath: string;
    //   storeId: string;
    //   storeName: string;
    //   templateId: null | any;
    //   thumbnail: string;
    //   underMessage?: null | any;
    //   updateBy: string;
    //   updateTime: string;
    //   viewCount?: null | any;
    //   weight: number;
    // }>;
    // small: string;
    // storeCategoryPath: string;
    // storeId: string;
    // storeName: string;
    // templateId: string;
    // thumbnail: string;
    // underMessage: string;
    // updateBy: string;
    // updateTime: string;
  };
}
// table-pages的表格方法封装约束
export interface ColumnsDataRule {
  title: string;
  width: number;
}
// 规格表头方法封装约束
export interface skuColumnRule {
  title: string;
  dataIndex?: string; // 表格显示的数据
  width?: string | number;
  slotName?: boolean | string;
  [key: string]: unknown;
  // skuColumn?: Array<{ title?: string; key?: string }>;
}
// 初始化详情skuList数据;
export interface skuListRule {
  specs: string;
  sn: string;
  weight: number;
  cost?: number | string;
  price?: number | string;
  image?: string;
  [key: string]: any;
}
