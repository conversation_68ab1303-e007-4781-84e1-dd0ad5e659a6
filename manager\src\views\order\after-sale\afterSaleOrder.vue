<template>
  <div class="search">
    <a-card title="售后单详情" :bordered="false">
      <a-steps :current="current" line-less style="padding: 30px 10% 0 10%" class="steps-box">
        <a-step>申请中</a-step>
        <a-step v-if="afterSaleInfo.data.serviceStatus === 'REFUSE'">拒绝售后</a-step>
        <a-step v-else>通过售后</a-step>
        <template v-if="afterSaleInfo.data.serviceStatus !== 'REFUSE'">
          <a-step v-if="afterSaleInfo.data.serviceType === 'RETURN_GOODS' && afterSaleInfo.data.serviceStatus !== 'BUYER_CANCEL'">买家退货，待卖家收货</a-step>
          <a-step v-if="afterSaleInfo.data.serviceType === 'RETURN_GOODS' && afterSaleInfo.data.serviceStatus !== 'SELLER_TERMINATION' && afterSaleInfo.data.serviceStatus !== 'BUYER_CANCEL'">卖家确认收货</a-step>
          <a-step v-if="afterSaleInfo.data.serviceType === 'RETURN_MONEY' && afterSaleInfo.data.serviceStatus !== 'BUYER_CANCEL'">待平台退款</a-step>
          <a-step v-if="afterSaleInfo.data.serviceType === 'SELLER_TERMINATION' || afterSaleInfo.data.serviceStatus === 'SELLER_TERMINATION'">卖家终止售后</a-step>
          <a-step v-else-if="afterSaleInfo.data.serviceType === 'BUYER_CANCEL' || afterSaleInfo.data.serviceStatus === 'BUYER_CANCEL'">买家取消售后</a-step>
          <a-step v-else>完成售后</a-step>
        </template>
      </a-steps>
    </a-card>
    <a-card
      class="general-card"
      :style="{ paddingTop: '5px' }"
      :bordered="false"
    >
      <div class="main-content">
        <div class="div-flow-left">
          <div class="div-form-default">
            <h4>退货申请</h4>
            <dl>
              <dt>退货状态</dt>
              <dd>{{ afterSaleInfo.data.serviceName }}</dd>
            </dl>
            <dl>
              <dt>退货退款编号</dt>
              <dd>{{ afterSaleInfo.data.sn }}</dd>
            </dl>
            <dl>
              <dt>退货退款原因</dt>
              <dd>{{ afterSaleInfo.data.reason }}</dd>
            </dl>
            <dl>
              <dt>申请退款金额</dt>
              <dd>{{ unitPrice(afterSaleInfo.data.applyRefundPrice, '¥') }}</dd>
            </dl>
            <dl v-if="afterSaleInfo.data.actualRefundPrice">
              <dt>实际退款金额</dt>
              <dd>{{
                unitPrice(afterSaleInfo.data.actualRefundPrice, '¥')
              }}</dd>
            </dl>
            <dl v-if="afterSaleInfo.data.refundPoint">
              <dt>退还积分</dt>
              <dd>{{ afterSaleInfo.data.refundPoint }}</dd>
            </dl>
            <dl>
              <dt>退货数量</dt>
              <dd>{{ afterSaleInfo.data.num }}</dd>
            </dl>
            <dl>
              <dt>问题描述</dt>
              <dd>{{ afterSaleInfo.data.problemDesc }}</dd>
            </dl>
            <dl>
              <dt>凭证</dt>
              <dd v-if="afterSaleImage && afterSaleImage.length">
                <div
                  v-for="(item, index) in afterSaleImage"
                  :key="index"
                  class="div-img"
                >
                  <img class="complain-img" :src="item" />
                </div>
              </dd>
              <dd v-else>暂无凭证</dd>
            </dl>
          </div>
          <div
            v-if="afterSaleInfo.data.serviceStatus == 'APPLY'"
            class="div-form-default"
          >
            <h4>处理意见</h4>
            <dl>
              <dt>商家</dt>
              <dd>
                <div class="div-content">
                  {{ afterSaleInfo.data.storeName }}
                </div>
              </dd>
            </dl>
            <dl>
              <dt>是否同意</dt>
              <dd>
                <div class="div-content">
                  <a-radio-group v-model="params.serviceStatus" type="button">
                    <a-radio value="PASS">同意</a-radio>
                    <a-radio value="REFUSE">拒绝</a-radio>
                  </a-radio-group>
                </div>
              </dd>
            </dl>
            <dl>
              <dt>申请退款金额</dt>
              <dd>{{ unitPrice(afterSaleInfo.data.applyRefundPrice, '¥') }}</dd>
            </dl>
            <dl>
              <dt>实际退款金额</dt>
              <dd>
                <a-input-number 
                  :min="0"
                  disabled
                  v-model="afterSaleInfo.data.applyRefundPrice"
                  :style="{ width: '260px' }"
                  allow-clear
                />
              </dd>
            </dl>
            <dl>
              <dt>备注信息</dt>
              <dd>
                <a-textarea v-model="params.remark" allow-clear />
              </dd>
            </dl>
            <dl>
              <dd>
                <div style="text-align: right; width: 45%; margin-top: 10px">
                  <a-button
                    type="primary"
                    :loading="submitLoading"
                    style="margin-left: 5px"
                    @click="handleSubmit"
                  >
                    确定
                  </a-button>
                </div>
              </dd>
            </dl>
          </div>
          <div
            v-if="afterSaleInfo.data.serviceStatus != 'APPLY'"
            class="div-form-default"
          >
            <h4>商家处理</h4>
            <dl>
              <dt>商家</dt>
              <dd>
                <div class="div-content">
                  {{ afterSaleInfo.data.storeName }}
                </div>
              </dd>
            </dl>
            <dl>
              <dt>备注信息</dt>
              <dd>
                {{ afterSaleInfo.data.auditRemark || '暂无备注信息' }}
              </dd>
            </dl>
          </div>
        </div>
        <div class="div-flow-center"></div>
        <div class="div-flow-right">
          <div class="div-form-default">
            <h4>相关商品交易信息</h4>
            <dl>
              <dt>
                <img :src="afterSaleInfo.data.goodsImage" height="60" />
              </dt>
              <dd>
                <div class="div-zoom">
                  <a
                    class="Hyperlink"
                    @click="
                      store.viewGoodsDetail(
                        afterSaleInfo.data.goodsId,
                        afterSaleInfo.data.skuId
                      )
                    "
                    >{{ afterSaleInfo.data.goodsName }}</a
                  >
                 
                </div>
                <div style="color: #999; font-size: 12px;">
                  数量：x{{ afterSaleInfo.data.num }}
                </div>
              </dd>
            </dl>
          </div>
          <div class="div-form-default">
            <h4>订单相关信息</h4>
            <dl>
              <dt>订单编号</dt>
              <dd>
                <a class="Hyperlink" @click="handleOrderDetails(afterSaleInfo.data.orderSn)">{{ afterSaleInfo.data.orderSn }}</a>
              </dd>
            </dl>
            <dl v-if="afterSaleInfo.data.bankDepositName">
              <dt>银行开户行</dt>
              <dd>
                {{ afterSaleInfo.data.bankDepositName }}
              </dd>
            </dl>
            <dl v-if="afterSaleInfo.data.bankAccountName">
              <dt>银行开户名</dt>
              <dd>
                {{ afterSaleInfo.data.bankAccountName }}
              </dd>
            </dl>
            <dl v-if="afterSaleInfo.data.bankAccountNumber">
              <dt>银行卡号</dt>
              <dd>
                {{ afterSaleInfo.data.bankAccountNumber }}
              </dd>
            </dl>
          </div>

          <div
            v-if="
              afterSaleInfo.data.afterSaleAllowOperationVO &&
              afterSaleInfo.data.afterSaleAllowOperationVO.refund
            "
            class="div-form-default"
          >
            <h4>平台退款</h4>
            <dl>
              <dt>银行开户行</dt>
              <dd>
                {{ afterSaleInfo.data.bankDepositName }}
              </dd>
            </dl>
            <dl>
              <dt>银行开户名</dt>
              <dd>
                {{ afterSaleInfo.data.bankAccountName }}
              </dd>
            </dl>
            <dl>
              <dt>银行卡号</dt>
              <dd>
                {{ afterSaleInfo.data.bankAccountNumber }}
              </dd>
            </dl>
            <dl>
              <dt>备注信息</dt>
              <dd>
                <a-input
                  v-model="refundPriceForm.data.remark"
                  clearable
                  style="width: 260px"
                />
              </dd>
            </dl>
            <dl>
              <dd>
                <div style="text-align: right; width: 45%; margin-top: 10px">
                  <a-button
                    type="primary"
                    :loading="submitLoading"
                    style="margin-left: 5px"
                    @click="refundPriceSubmit"
                  >
                    退款
                  </a-button>
                </div>
              </dd>
            </dl>
          </div>
          <div
            v-if="
              afterSaleInfo.data.showDelivery &&
              afterSaleInfo.data.serviceType === 'RETURN_GOODS'
            "
            class="div-form-default"
          >
            <h4>物流信息</h4>
            <dl>
              <dt>收货商家</dt>
              <dd>{{ afterSaleInfo.data.storeName }}</dd>
            </dl>
            <dl>
              <dt>收货商家手机</dt>
              <dd>{{ storeMsg.data.salesConsigneeMobile }}</dd>
            </dl>
            <dl>
              <dt>收货地址</dt>
              <dd>
                {{ storeMsg.data.salesConsigneeAddressPath }}
                {{ storeMsg.data.salesConsigneeDetail }}
              </dd>
            </dl>
            <dl>
              <dt>物流公司</dt>
              <dd>{{ afterSaleInfo.data.mlogisticsName }}</dd>
            </dl>
            <dl>
              <dt>物流单号</dt>
              <dd>
                {{ afterSaleInfo.data.mlogisticsNo }}
              </dd>
            </dl>
            <dl>
              <dt>操作</dt>
              <dd>
                <a-button
                  type="primary"
                  style="margin-left: 5px"
                  @click="logisticsSeller()"
                  >查询物流</a-button
                >
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </a-card>
    <!-- 查询物流 -->
    <a-modal v-model:visible="logisticsModal" :closable="false">
      <template #title> 查询物流 </template>
      <div class="layui-layer-wrap">
        <dl>
          <dt>售后单号：</dt>
          <dd>
            <div class="text-box">{{ route.query.id }}</div>
          </dd>
        </dl>
        <dl>
          <dt>物流公司：</dt>
          <dd>
            <div class="text-box">{{ afterSaleInfo.data.mlogisticsName }}</div>
          </dd>
        </dl>
        <dl>
          <dt>快递单号：</dt>
          <dd>
            <div class="text-box">{{ afterSaleInfo.data.mlogisticsNo }}</div>
          </dd>
        </dl>
        <div class="div-express-log">
          <ul class="express-log">
            <div v-if="Object.keys(logisticsInfo).length">
              <li v-for="(item, index) in logisticsInfo.traces" :key="index">
                <span class="time">{{ item.AcceptTime }}</span>
                <span class="detail">{{ item.AcceptStation }}</span>
              </li>
            </div>
            <div>
              <li style="text-align: center">暂无物流信息</li>
            </div>
          </ul>
        </div>
      </div>
      <template #footer>
        <div style="text-align: right">
          <a-button type="text" @click="logisticsModal = false">取消</a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import {
afterSaleSellerReview,
getAfterSaleOrderDetail,
getAfterSaleTraces,
refundPrice,
storeAddress,
} from '@/api/order';
import useCurrentInstance from '@/hooks/useCurrentInstance';
import { usePathJumpStore } from '@/store/index';
import getAssetsImages from '@/utils/assetsImages';
import { unitPrice } from '@/utils/filters';
import { Message } from '@arco-design/web-vue';
import VueQrcode from '@chenfengyuan/vue-qrcode';
import { onMounted, reactive, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

  const current = ref(1);
  const store = usePathJumpStore();
  const qrcode = getAssetsImages('qrcode.svg');
  const route:any = useRoute();
  const router = useRouter();
  const afterSaleStatusList = [
    // 售后状态列表
    {
      name: '申请中',
      status: 'APPLY',
    },
    {
      name: '通过',
      status: 'PASS',
    },
    {
      name: '拒绝',
      status: 'REFUSE',
    },
    {
      name: '买家退货，待卖家收货',
      status: 'BUYER_RETURN',
    },
    {
      name: '卖家确认收货',
      status: 'SELLER_CONFIRM',
    },
    {
      name: '卖家终止售后',
      status: 'SELLER_TERMINATION',
    },
    {
      name: '买家取消售后',
      status: 'BUYER_CANCEL',
    },
    {
      name: '完成售后',
      status: 'COMPLETE',
    },
    {
      name: '等待平台退款',
      status: 'WAIT_REFUND',
    },
  ];
  const afterSaleInfo = reactive({
    // 售后详情
    data: {
      applyRefundPrice: 0,
      serviceName: '',
      sn: '',
      reason: '',
      actualRefundPrice: 0,
      refundPoint: '',
      num: '',
      problemDesc: '',
      serviceStatus: '',
      storeName: '',
      auditRemark: '',
      goodsImage: '',
      goodsId: '',
      skuId: '',
      orderSn: '',
      bankDepositName: '',
      bankAccountName: '',
      bankAccountNumber: '',
      afterSaleAllowOperationVO: {
        refund: '',
      },
      showDelivery: false,
      serviceType: '',
      mlogisticsName: '',
      mlogisticsNo: '',
      goodsName: '',
    },
  });
  const storeMsg = reactive({
    // 商家地址信息
    data: {
      salesConsigneeMobile: '',
      salesConsigneeAddressPath: '',
      salesConsigneeDetail: '',
    },
  });
  const logisticsInfo = ref({
    traces: [],
  }) as any; // 物流信息

  const refundPriceForm = reactive({
    data: {
      remark: '',
    },
  });
  // 商家处理意见
  const params = ref({
    serviceStatus: 'PASS',
    remark: '',
    actualRefundPrice: 0,
  });
  const afterSaleImage = ref([]); // 会员申诉图片
  const submitLoading = ref<boolean>(false);
  const modal = useCurrentInstance().globalProperties?.$modal; // 获取modal
  const logisticsModal = ref<boolean>(false); // 查询物流弹框
  // 根据订单状态判断是否显示物流信息
  const showDelivery = (status: any) => {
    let flag = false;
    afterSaleStatusList.forEach((e: any, index: number) => {
      if (e.status === status && index >= 3 && index !== 6) {
        flag = true;
      }
    });
    return flag;
  };
  // 获取订单状态中文
  const filterOrderStatus = (status: any) => {
    const ob = afterSaleStatusList.filter((e: any) => {
      return e.status === status;
    });
    return ob[0].name;
  };

  const init = async () => {
    const res = await getAfterSaleOrderDetail(route.query.id);
    if (res.data.code == 200) {
      afterSaleInfo.data = res.data.result;
      afterSaleInfo.data.showDelivery = showDelivery(
        afterSaleInfo.data.serviceStatus
      );
      afterSaleInfo.data.serviceName = filterOrderStatus(
        afterSaleInfo.data.serviceStatus
      );
      // 退货地址去掉逗号
      afterSaleImage.value = (res.data.result.afterSaleImage || []).split(',');
      // 如果显示物流信息，展示商家地址
      if (afterSaleInfo.data.showDelivery) {
        storeAddress(route.query.id).then((res: any) => {
          if (res.data.code == 200) {
            const obj = res.data.result;
            obj.salesConsigneeAddressPath =
              obj.salesConsigneeAddressPath.replaceAll(',', '');
            storeMsg.data = obj;
          }
        });
      }
      // 售后单流程图
      if (afterSaleInfo.data && afterSaleInfo.data.serviceStatus) {
        const type = afterSaleInfo.data.serviceType;  // 售后类型
        const status = afterSaleInfo.data.serviceStatus;  //  售后状态
        if (status === 'APPLY') { // 申请中
          current.value = 1;
        }else if (status === 'PASS' || status === 'REFUSE') {  //通过售后 || 拒绝售后
          current.value = 2;
        }
        if (type === 'RETURN_GOODS') { // 退货
          if (status === 'BUYER_RETURN' || status === 'BUYER_CANCEL') {  // 买家退货，待卖家收货 || 买家取消售后
            current.value = 3;
          } else if (status === 'SELLER_CONFIRM' || status === 'SELLER_TERMINATION') { // 卖家确认收货 || 卖家终止售后
            current.value = 4;
          } else if (status === 'COMPLETE') { // 完成售后
            current.value = 5;
          }
        } else if (type === 'RETURN_MONEY') { // 退款
          if (status === 'WAIT_REFUND' || status === 'BUYER_CANCEL') { // 待平台退款 || 买家取消售后
            current.value = 3;
          } else if (status === 'COMPLETE') { //  完成售后
            current.value = 4;
          }
        }
      }
    }
  };
  onMounted(() => {
    init();
  });

  // 平台退款
  const refundPriceSubmit = () => {
    if (refundPriceForm.data.remark == '') {
      Message.success('请输入退款备注');
      return;
    }
    modal.confirm({
      title: '确认退款',
      content: `请确认退款？`,
      alignCenter: false,
      onOk: async () => {
        const res = await refundPrice(
          route.query.id,
          refundPriceForm.data.remark
        );
        if (res.data.code == 200) {
          Message.success('退款成功');
          init();
        }
      },
    });
  };
  // 回复
  const handleSubmit = () => {
    if (params.value.remark == '') {
      Message.error('请输入备注信息');
      return;
    }
    afterSaleSellerReview(route.query?.id || '', params.value).then((res: any) => {
      submitLoading.value = false;
      if (res.data.code == 200) {
        Message.success('审核成功');
        params.value.remark = '';
        init();
      }
    });
  };
  // 查询物流
  const logisticsSeller = () => {
    logisticsModal.value = true;
    getAfterSaleTraces(route.query.id).then((res: any) => {
      if (res.data.code == 200) {
        logisticsInfo.value.traces = res.result;
      }
    });
  };

  const handleOrderDetails = (sn: any) => {
    const url = router.resolve({name: 'order-detail', query: {id: sn}});
    window.open(url.href, '_blank');
  };
</script>

<style lang="less" scoped>
  .Hyperlink {
    margin-right: 10px;
    color: #2d8cf0;
    font-size: 14px;
    cursor: pointer;
  }

  .main-content {
    min-height: 600px;
    padding: 10px;
  }

  .div-flow-left {
    width: 49%;
    letter-spacing: normal;
    display: inline-block;
    border-right: solid #f5f5f5 1px;

    .div-form-default {
      width: 97%;
      h4 {
        font-weight: 600;
        line-height: 22px;
        background-color: #f5f5f5;
        padding: 6px 0 6px 12px;
        border-bottom: solid 1px #e7e7e7;
      }

      dl {
        font-size: 0;
        line-height: 30px;
        clear: both;
        padding: 0;
        margin: 0;
        border-bottom: dotted 1px #e6e6e6;
        overflow: hidden;
        display: flex;

        dt {
          flex: 2;
          vertical-align: top;
          text-align: right;
          padding: 15px 1% 15px 0;
          margin: 0;
          font-size: 14px;
        }

        dd {
          flex: 10;
          padding: 15px 0 15px 1%;
          margin: 0;
          border-left: 1px solid #f0f0f0;
          font-size: 14px;
        }
      }
    }
  }

  dl dt {
    width: 100px;
    text-align: right;
  }

  .div-express-log {
    max-height: 300px;
    border: solid 1px #e7e7e7;
    background: #fafafa;
    overflow-y: auto;
    overflow-x: auto;
  }

  .express-log {
    margin-right: -10px;
    margin: 5px;
    padding: 10px;
    list-style-type: none;

    .time {
      width: 30%;
      display: block;
      float: left;
    }

    .detail {
      width: 60%;
      margin-left: 30px;
      display: inline-block;
    }

    li {
      line-height: 30px;
    }
  }

  .layui-layer-wrap {
    dl {
      border-top: solid 1px #f5f5f5;
      margin-top: -1px;
      overflow: hidden;

      dt {
        font-size: 14px;
        line-height: 28px;
        display: inline-block;
        padding: 8px 1% 8px 0;
        color: #999;
      }

      dd {
        font-size: 14px;
        line-height: 28px;
        display: inline-block;
        padding: 8px 0 8px 8px;
        border-left: solid 1px #f5f5f5;

        .text-box {
          line-height: 40px;
          color: #333;
          word-break: break-all;
        }
      }
    }
  }

  .div-img {
    width: 130px;
    height: 130px;
    text-align: center;
    float: left;
  }

  .div-flow-center {
    width: 2%;
    display: inline-block;
  }

  .div-flow-right {
    width: 49%;
    vertical-align: top;
    word-spacing: normal;
    display: inline-block;

    .div-form-default {
      width: 97%;

      h4 {
        font-weight: 600;
        line-height: 22px;
        background-color: #f5f5f5;
        padding: 6px 0 6px 12px;
        border-bottom: solid 1px #e7e7e7;
      }

      dl {
        font-size: 0;
        line-height: 30px;
        clear: both;
        padding: 0;
        margin: 0;
        border-bottom: dotted 1px #e6e6e6;
        overflow: hidden;
        display: flex;

        dt {
          display: inline-block;
          flex: 2;
          vertical-align: top;
          text-align: right;
          padding: 15px 1% 15px 0;
          margin: 0;
          font-size: 14px;
        }

        dd {
          display: inline-block;
          flex: 10;
          padding: 15px 0 15px 1%;
          margin: 0;
          border-left: 1px solid #f0f0f0;
          font-size: 14px;
        }
      }
    }
  }

  .complain-img {
    width: 120px;
    height: 120px;
    text-align: center;
  }

  .qrcode {
    width: 15px;
  }

  .steps-box {
    width: 100%;
    display: flex;
    > .arco-steps-item  {
      flex: auto;
      display: flex;
      justify-content: center;
    }
  }
</style>
