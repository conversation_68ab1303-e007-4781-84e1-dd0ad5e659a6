import { useAppStore } from '@/store'
import { getCurrentPermissionList } from '@/api/setting'
import routers from '@/router';


function resolveComponent(name: string) {
  const modules = import.meta.glob('@/views/**/**/*.vue');
  const importPage = modules[`${name}`] as any;
  if (!importPage) {
    throw new Error(`Unknown page: ${name}`);
  }
  return importPage;
};


export async function initRouterNode(reload: boolean = false) {
  let menuTree: any = []
  const appStore: any = useAppStore();
  if (!appStore.appRoute.length || reload) {
    const res = await getCurrentPermissionList();
    menuTree = res.data.result

  } else {
    menuTree = appStore.appRoute
  }
  const data: any = menuTree
  appStore.updateRoute(menuTree)
  data.forEach((item: any) => {
    console.log(item);
    routers.addRoute({
      name: item.name,
      path: `/${item.path}`,
      component: () => import('@/layout/default-layout.vue'),
      children: item.children.map((child: any) => {
        return {
          name: child.name,
          path: `/${child.path}`,
          component: resolveComponent(`/src/${child.frontRoute}.vue`),
        };
      }),
    });
  });
}
