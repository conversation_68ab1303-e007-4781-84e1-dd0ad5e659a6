<template>
  <div class="content-goods-publish">
    <div class="success">
      <div class="arco-result-icon arco-result-icon-success">
        <div class="arco-result-icon-tip">
          <svg
            viewBox="0 0 48 48"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            stroke="currentColor"
            class="arco-icon arco-icon-check"
            stroke-width="4"
            stroke-linecap="butt"
            stroke-linejoin="miter"
          >
            <path d="M41.678 11.05 19.05 33.678 6.322 20.95"></path>
          </svg>
        </div>
      </div>
      <h1 class="arco-result-title">恭喜您，商品发布成功 !</h1>
      <div class="arco-result-subtitle">您还可以继续以下操作 ！！</div>
      <div class="arco-result-extra">
        <div
          class="arco-space arco-space-horizontal arco-space-align-center operation-wrap"
        >
          <div class="arco-space-item">
            <button
              style="margin-right: 16px"
              class="arco-btn arco-btn-primary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal"
              type="button"
              @click="addGoods"
              >发布商品</button
            >
          </div>
          <div class="arco-space-item">
            <button
              class="arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal"
              type="button"
              @click="gotoGoodsList"
              >商品列表</button
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { useRouter } from 'vue-router';
  import { inject } from 'vue';

  const router = useRouter();
  const activestep = inject('activestep') as any;
  // 返回商品列表
  const gotoGoodsList = () => {
    router.push({ name: 'goods-list' });
  };
  // 发布商品
  const addGoods = () => {
    router.go(0);
    // router.push({ name: 'goods-operation' });
    // activestep.value = 1
  };
</script>

<style lang="less" scoped>
  @import './addGoods.less';
  .content-goods-publish {
    // margin: 150px 0 0;
    box-sizing: border-box;
    width: 100%;
    padding: 32px 32px 24px;
    height: 500px;
  }
  .arco-result-icon {
    margin-bottom: 16px;
    font-size: 30px;
    text-align: center;
    margin-top: 150px;
  }
  .arco-result-icon-success .arco-result-icon-tip {
    color: rgb(var(--success-6));
    background-color: var(--color-success-light-1);
  }
  .arco-result-title {
    color: var(--color-text-1);
    font-weight: 500;
    font-size: 24px;
    line-height: 1.5715;
    text-align: center;
  }
  .arco-result-extra {
    margin-top: 20px;
    text-align: center;
  }
  .arco-result-subtitle {
    color: var(--color-text-2);
    font-size: 14px;
    line-height: 1.5715;
    text-align: center;
  }
</style>
