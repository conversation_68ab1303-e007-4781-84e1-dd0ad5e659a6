/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AAlert: typeof import('@arco-design/web-vue')['Alert']
    AAvatar: typeof import('@arco-design/web-vue')['Avatar']
    ABreadcrumb: typeof import('@arco-design/web-vue')['Breadcrumb']
    AButton: typeof import('@arco-design/web-vue')['Button']
    ACard: typeof import('@arco-design/web-vue')['Card']
    ACarousel: typeof import('@arco-design/web-vue')['Carousel']
    ACarouselItem: typeof import('@arco-design/web-vue')['CarouselItem']
    ACheckbox: typeof import('@arco-design/web-vue')['Checkbox']
    ACheckboxGroup: typeof import('@arco-design/web-vue')['CheckboxGroup']
    ACol: typeof import('@arco-design/web-vue')['Col']
    ADatePicker: typeof import('@arco-design/web-vue')['DatePicker']
    AddressManage: typeof import('./components/addressManage/index.vue')['default']
    ADoption: typeof import('@arco-design/web-vue')['Doption']
    ADrawer: typeof import('@arco-design/web-vue')['Drawer']
    ADropdown: typeof import('@arco-design/web-vue')['Dropdown']
    AForm: typeof import('@arco-design/web-vue')['Form']
    AFormItem: typeof import('@arco-design/web-vue')['FormItem']
    AImage: typeof import('@arco-design/web-vue')['Image']
    AInput: typeof import('@arco-design/web-vue')['Input']
    AInputNumber: typeof import('@arco-design/web-vue')['InputNumber']
    AInputPassword: typeof import('@arco-design/web-vue')['InputPassword']
    AInputSearch: typeof import('@arco-design/web-vue')['InputSearch']
    ALink: typeof import('@arco-design/web-vue')['Link']
    AMenu: typeof import('@arco-design/web-vue')['Menu']
    AMenuItem: typeof import('@arco-design/web-vue')['MenuItem']
    AModal: typeof import('@arco-design/web-vue')['Modal']
    AOption: typeof import('@arco-design/web-vue')['Option']
    APagination: typeof import('@arco-design/web-vue')['Pagination']
    APopover: typeof import('@arco-design/web-vue')['Popover']
    AProgress: typeof import('@arco-design/web-vue')['Progress']
    ARadio: typeof import('@arco-design/web-vue')['Radio']
    ARadioGroup: typeof import('@arco-design/web-vue')['RadioGroup']
    ARate: typeof import('@arco-design/web-vue')['Rate']
    ARow: typeof import('@arco-design/web-vue')['Row']
    ASelect: typeof import('@arco-design/web-vue')['Select']
    ASpace: typeof import('@arco-design/web-vue')['Space']
    ASpin: typeof import('@arco-design/web-vue')['Spin']
    AStep: typeof import('@arco-design/web-vue')['Step']
    ASteps: typeof import('@arco-design/web-vue')['Steps']
    ASubMenu: typeof import('@arco-design/web-vue')['SubMenu']
    ASwitch: typeof import('@arco-design/web-vue')['Switch']
    ATable: typeof import('@arco-design/web-vue')['Table']
    ATabPane: typeof import('@arco-design/web-vue')['TabPane']
    ATabs: typeof import('@arco-design/web-vue')['Tabs']
    ATag: typeof import('@arco-design/web-vue')['Tag']
    ATextarea: typeof import('@arco-design/web-vue')['Textarea']
    ATooltip: typeof import('@arco-design/web-vue')['Tooltip']
    AUpload: typeof import('@arco-design/web-vue')['Upload']
    AVerificationCode: typeof import('@arco-design/web-vue')['VerificationCode']
    BrandModal: typeof import('./components/indexDecorate/template/mix/brandModal.vue')['default']
    Card: typeof import('./components/card/index.vue')['default']
    CateNav: typeof import('./components/nav/cateNav.vue')['default']
    CountDown: typeof import('./components/countDown/index.vue')['default']
    Drawer: typeof import('./components/drawer/index.vue')['default']
    DrawerPage: typeof import('./components/drawer/drawerPage.vue')['default']
    Empty: typeof import('./components/empty/index.vue')['default']
    FirstApply: typeof import('./components/shopEnty/firstApply.vue')['default']
    FixedBar: typeof import('./components/fixedBar/index.vue')['default']
    GoodsClassNav: typeof import('./components/nav/goodsClassNav.vue')['default']
    GoodsModal: typeof import('./components/indexDecorate/template/mix/goodsModal.vue')['default']
    HoverSearch: typeof import('./components/header/hoverSearch.vue')['default']
    InvoiceModal: typeof import('./components/invoiceModal/index.vue')['default']
    MagnifyingGlass: typeof import('./components/magnifyingGlass/index.vue')['default']
    Mix: typeof import('./components/indexDecorate/template/mix/mix.vue')['default']
    ModelForm: typeof import('./components/indexDecorate/modelForm.vue')['default']
    MultipleMap: typeof import('./components/map/multiple-map.vue')['default']
    MyFavorites: typeof import('./components/myFavorites/index.vue')['default']
    MyOrder: typeof import('./components/myOrder/index.vue')['default']
    Promotion: typeof import('./components/goodsDetail/promotion.vue')['default']
    README: typeof import('./components/README.md')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Search: typeof import('./components/search/index.vue')['default']
    SecondApply: typeof import('./components/shopEnty/secondApply.vue')['default']
    ShowGoods: typeof import('./components/goodsDetail/showGoods.vue')['default']
    ShowGoodsDetail: typeof import('./components/goodsDetail/showGoodsDetail.vue')['default']
    TheFooter: typeof import('./components/TheFooter.vue')['default']
    TheHeader: typeof import('./components/TheHeader.vue')['default']
    ThirdApply: typeof import('./components/shopEnty/thirdApply.vue')['default']
    TopAdvertising: typeof import('./components/topAdvertising/index.vue')['default']
    Tpl_block: typeof import('./components/indexDecorate/template/tpl_block.vue')['default']
    Tpl_category: typeof import('./components/indexDecorate/template/tpl_category.vue')['default']
    Tpl_flex_four: typeof import('./components/indexDecorate/template/tpl_flex_four.vue')['default']
    Tpl_flex_three: typeof import('./components/indexDecorate/template/tpl_flex_three.vue')['default']
    Tpl_goods: typeof import('./components/indexDecorate/template/tpl_goods.vue')['default']
    Tpl_mix: typeof import('./components/indexDecorate/template/tpl_mix.vue')['default']
    Tpl_pin_banner: typeof import('./components/indexDecorate/template/tpl_pin_banner.vue')['default']
    Tpl_pin_nav: typeof import('./components/indexDecorate/template/tpl_pin_nav.vue')['default']
    Tpl_recommend: typeof import('./components/indexDecorate/template/tpl_recommend.vue')['default']
    Tpl_seckill: typeof import('./components/indexDecorate/template/tpl_seckill.vue')['default']
    Tpl_top_ad: typeof import('./components/indexDecorate/template/tpl_top_ad.vue')['default']
    Tpl_user_panel: typeof import('./components/indexDecorate/template/tpl_user_panel.vue')['default']
    Verify: typeof import('./components/verify/index.vue')['default']
  }
}
