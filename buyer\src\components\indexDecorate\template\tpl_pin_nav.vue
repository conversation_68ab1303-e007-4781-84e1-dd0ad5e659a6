<script setup lang="ts">
import { useRouter } from 'vue-router'
import { navigateTo } from './navigate.ts'

const props = defineProps<{
  res: any
}>()
const router = useRouter()
function handleClickItem(item: any) {
  const path = navigateTo(item)

  window.open(router.resolve(path).href, '_blank')
}
</script>

<template>
  <div v-if="props.res" flex>
    <div class="nav-con"
      :style="{ borderRadius: `${props.res.data.round}px`, backgroundColor: props.res.data.background, color: props.res.data.textColor }"
      h-46px w-914px>
      <div v-for="(item, index) in props.res.data.list" :key="index" class="nav-item" @click="handleClickItem(item)">
        {{ item.title }}
        <div class="colum" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.nav-con {
  width: 914px;
  height: 46px;
  border-radius: 10px;
  background: #fff;
  line-height: 46px;
  overflow: hidden;
  list-style: none;
  display: flex;
}

.nav-item {
  cursor: pointer;
  width: 103px;
  text-align: center;
  position: relative;
}

.colum {
  top: 15px;
  line-height: 46px;
  height: 14.7px;
  opacity: 1;
  background: #cbc8c8;
  position: absolute;
  right: 0;
  width: 1px;
}
</style>
