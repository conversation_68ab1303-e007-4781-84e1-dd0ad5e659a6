<template>
  <div style="width: 100%">
    <a-card>
      <a-button type="primary"  @click="handleAddParamsGroup">
        添加
      </a-button>
    </a-card>
    <div class="row">
      <a-card v-if="paramsGroup.length == 0">
        暂无参数绑定信息
      </a-card>
      <div class="paramsGroup" v-else>
        <a-card style="width: 350px; margin: 7px" v-for="(group, index) in paramsGroup" :key="index" :bordered="false">
          <template #title>
            <icon-interaction />&nbsp;{{ group.groupName }}
          </template>
          <template #extra>
            <a-dropdown :popup-max-height="false">
              <a href="javascript:void(0)">
                操作
                <icon-down />
              </a>
              <template #content>
                <a-doption @click="handleEditParamsGroup(group)">编辑</a-doption>
                <a-doption @click="handleDeleteParamGroup(group)">删除</a-doption>
              </template>
            </a-dropdown>
          </template>
          <template v-if="group.params && group.params.length > 0">
            <div v-for="(param, paramId) in group.params" :key="paramId" class="params">
              <span>{{ param.paramName }}</span>
              <span>
                <a-button type="text" @click="handleEditParams(group, param)">编辑</a-button>
                <a-button type="text" size="small" style="color: #f56c6c"
                  @click="handleDeleteParam(group, param)">删除</a-button>
              </span>
            </div>
          </template>
          <div v-else style="align-content: center">暂无数据...</div>
          <div style="text-align: center">
            <a-button type="text" @click="handleAddParams(group)">添加</a-button>
          </div>
        </a-card>
      </div>
    </div>
  </div>
  <a-modal :width="500" v-model:visible="dialogParamsGroupVisible">
    <template #title>{{ modalTitle }} </template>
    <a-form ref="paramGroupFormRules" :model="paramGroupForm">
      <a-form-item field="groupName" label="参数名称" :rules="[REQUIRED]">
        <a-input v-model="paramGroupForm.groupName" />
      </a-form-item>
    </a-form>
    <template #footer>
      <div style="text-align: right">
        <a-button style="margin-right: 5px" @click="dialogParamsGroupVisible = false">取消</a-button>
        <a-button type="primary"  @click="submitParamGroupForm">提交</a-button>
      </div>
    </template>
  </a-modal>
  <a-modal :width="500" v-model:visible="dialogParamsVisible">
    <template #title>{{ modalTitle }} </template>
    <a-form :model="paramForm">
      <a-form-item field="paramName" label="参数名称">
        <a-input v-model="paramForm.paramName" />
      </a-form-item>
      <a-form-item field="options" label="可选值">
        <a-select :style="{ width: '320px' }" placeholder="输入后回车添加" v-model="paramForm.options" multiple allow-create>
          <!--<a-option v-for="(item, itemIndex) in ops.options" :key="itemIndex" :value="item">{{ item }}</a-option>-->
        </a-select>
      </a-form-item>
      <a-form-item field="specName3" label="选项">
        <a-checkbox value="1" v-model="paramForm.required">必填</a-checkbox>
        <a-checkbox value="1" v-model="paramForm.isIndex">可索引</a-checkbox>
      </a-form-item>
      <a-form-item field="sort" label="排序">
        <a-input-number v-model="paramForm.sort" class="input-demo" :min="0" />
      </a-form-item>
    </a-form>
    <template #footer>
      <div style="text-align: right">
        <a-button style="margin-right: 5px" @click="dialogParamsVisible = false">取消</a-button>
        <a-button type="primary"  @click="submitParamForm">提交</a-button>
      </div>
    </template>
</a-modal>
</template>

<script setup lang='ts'>
import { ref, reactive, onMounted } from "vue"
import { getCategoryParamsListData, deleteParamsGroup, insertGoodsParams, updateGoodsParams } from '@/api/goods';
import { useRoute } from 'vue-router';
import { REQUIRED } from '@/utils/validator';
import { FormInstance } from '@arco-design/web-vue/es/form';
import { insertParamsGroup, updateParamsGroup, deleteParams } from '@/api/goods';
import { Message } from '@arco-design/web-vue';
import useCurrentInstance from '@/hooks/useCurrentInstance';

const paramGroupFormRules = ref<FormInstance>();
const route = useRoute()
/** 参数组 */
const paramsGroup = ref([]) as any
const paramGroupForm = ref({
  groupName: '',
  categoryId: '',
  id: ''
}) as any // 参数表单
/** 参数值 */
const ops = ref({
  options: [],
}) as any
/** 参数组添加或编辑弹出框 */
const dialogParamsGroupVisible = ref<boolean>(false)
const dialogParamsVisible = ref<boolean>(false)
/** 参数表单 */
const paramForm = ref({
  sort: 1,
  paramName: '',
  paramType: 0,
  options: '',
  required: false,
  isIndex: false,
  groupId: '',
  categoryId: '',
  id: null
}) as any
/** 添加或编辑标识 */
const modalType = ref<number>(0)
/** 添加或编辑标题 */
const modalTitle = ref<string>('')
// 获取modal
const modal = useCurrentInstance().globalProperties?.$modal;
// 初始化
const getDataList = () => {
  getCategoryParamsListData(route.query.id).then((res: any) => {
    if (res.data.code == 200) {
      paramsGroup.value = res.data.result;
    }
  })
}
// 初始化
onMounted(() => {
  getDataList()
})

// 添加参数
const handleAddParamsGroup = () => {
  paramGroupForm.value = {};
  ops.value = {};
  paramGroupForm.value.categoryId = route.query.id;
  modalType.value = 0;
  modalTitle.value = '添加参数组';
  dialogParamsGroupVisible.value = true

}
// 弹出修改参数组框
const handleEditParamsGroup = (group: any) => {
  paramGroupForm.value = {
    groupName: group.groupName,
    categoryId: route.query.id,
    id: group.groupId,
  };
  modalType.value = 1;
  modalTitle.value = '修改参数组';
  dialogParamsGroupVisible.value = true
}
// 删除参数数据方法
const deleteModal = (group: any, difference?: number) => {
  modal.confirm({
    title: '确认删除',
    content: `您确认要删除${difference == 1 ? group.groupName : group.paramName}?`,
    alignCenter: false,
    onOk: async () => {
      const res = difference == 1 ? await deleteParamsGroup(group.groupId) : await deleteParams(group.id)
      if (res.data.success) {
        Message.success('删除成功！');
        getDataList()
      }
    },
  });
}
// 删除参数方法
const handleDeleteParam = (group: any, param: any) => {
  deleteModal(param, 0)
}
// 删除参数组方法
const handleDeleteParamGroup = (group: any) => {
  deleteModal(group, 1)
}
// true还是false
function whether(bol: any) {
  return bol == 1
}
// 弹出修改参数框
const handleEditParams = (group: any, param: any) => {
  paramForm.value = {
    paramName: param.paramName,
    options: param.options.split(","),
    required: whether(param.required),
    isIndex: whether(param.isIndex),
    groupId: param.groupId || "",
    categoryId: param.categoryId || "",
    sort: param.sort || 1,
    id: param.id,
  }
  ops.value.options = paramForm.value.options;
  modalType.value = 1;
  modalTitle.value = '修改参数';
  dialogParamsVisible.value = true
}
// 保存参数组
const submitParamGroupForm = async () => {
  const auth = await paramGroupFormRules.value?.validate();
  if (!auth) {
    if (modalType.value == 0) {
      const res = await insertParamsGroup(paramGroupForm.value)
      if (res.data.code == 200) {
        Message.success('参数组修改成功');
        getDataList()
      }
    } else {
      const res = await updateParamsGroup(paramGroupForm.value)
      if (res.data.code == 200) {
        Message.success('参数组修改成功');
        getDataList()
      }
    }
    dialogParamsGroupVisible.value = false
  }
}
// 弹出添加参数框
const handleAddParams = (group: any) => {
  paramForm.value = {
    paramName: "",
    paramType: 1,
    options: "",
    required: false,
    isIndex: false,
    sort: 0,
    groupId: group.groupId,
    categoryId: route.query.id
  }
  modalTitle.value = '添加参数';
  modalType.value = 0
  dialogParamsVisible.value = true
}
// 保存参数
const submitParamForm = async () => {
  const auth = await paramGroupFormRules.value?.validate();
  const params = {
    ...paramForm.value,
    options:paramForm.value.options?paramForm.value.options.filter(Boolean):[],
    isIndex: Number(paramForm.value.isIndex),
    required: Number(paramForm.value.required)
  }
  if (modalType.value == 0) {
    insertGoodsParams(params).then((res: any) => {
      if (res.data.code == 200) {
        Message.success('参数添加成功');
        getDataList()
        dialogParamsVisible.value = false
      }
    })
  } else {
    updateGoodsParams(params).then((res: any) => {
      if (res.data.code == 200) {
        Message.success('参数修改成功');
        getDataList()
        dialogParamsVisible.value = false
      }
    })
  }
}
</script>

<style lang="less" scoped>
.row {
  overflow: hidden;
  margin: 20px 0;
}

.params {
  align-items: center;
  display: flex;
  padding: 3px;
  background-color: #f5f7fa;
  font-size: 14px;
  justify-content: space-between;
}





.paramsGroup {
  flex-wrap: wrap;
  display: flex;
  flex-direction: row;
  width: 100%;
}
</style>
