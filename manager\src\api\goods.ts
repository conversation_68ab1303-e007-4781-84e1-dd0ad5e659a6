import request, { Method } from '@/utils/axios';

import { ParamsRule } from '@/types/global';
import qs from 'query-string';

// 商品列表
export function getGoodsListDataSeller(params: ParamsRule) {
  return request({
    url: '/goods/goods/list',
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 下架商品
export function lowGoods(id: string | number, params: any) {
  return request({
    url: `/goods/goods/${id}/under`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}

// 上架商品
export function upGoods(id: string | number) {
  return request({
    url: `/goods/goods/${id}/up`,
    method: Method.PUT,
    needToken: true,
  });
}
// 审核商品
export function authGoods(id: string | number, params: any) {
  return request({
    url: `/goods/goods/${id}/auth`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
// 批量删除商品
export function deleteGoods(params: any){
  return request({
    url:'/goods/goods/delete',
    method:Method.PUT,
    needToken:true,
    params
  })
}
// 获取草稿商品分页列表
export function getAuthGoodsListData(params: ParamsRule) {
  return request({
    url: '/goods/goods/auth/list',
    method: Method.GET,
    needToken: true,
    params,
  });
}

//  获取商品分页列表
export function getGoodsListData(params: ParamsRule) {
  return request({
    url: '/goods/goods/list',
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 获取分类
export function getCategory() {
  return request({
    url: '/goods/category/all',
    method: Method.GET,
    needToken: true,
  });
}

// 获取子分类
export function getAllCategory() {
  return request({
    url: '/goods/category/allChildren',
    method: Method.GET,
    needToken: true,
  });
}

// 删除商品分类
export function delCategory(id: number | string) {
  return request({
    url: `/goods/category/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}

// 获取品牌列表
export function getGoodsBrand(params: ParamsRule) {
  return request({
    url: '/goods/brand/getByPage',
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 删除品牌
export function delBrand(id: number | string) {
  return request({
    url: `/goods/brand/delByIds/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}

// 添加品牌
export function addBrand(params: any) {
  return request({
    url: `/goods/brand`,
    method: Method.POST,
    needToken: true,
    params,
  });
}
// 修改品牌设置
export function updateBrand(id: number | string, params: any) {
  return request({
    url: `/goods/brand/${id}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
// 禁用品牌
export function disableBrand(id: number | string, params: any) {
  return request({
    url: `/goods/brand/disable/${id}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}

// 获取规格列表
export function getSkuList(params: ParamsRule) {
  return request({
    url: '/goods/spec',
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 删除规格
export function delSpec(id: number | string) {
  return request({
    url: `/goods/spec/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}

// 获取计量单位列表
export function getUnitList(params: ParamsRule) {
  return request({
    url: '/goods/goodsUnit',
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 删除商品计量单位
export function delGoodsUnit(id: number | string) {
  return request({
    url: `/goods/goodsUnit/delete/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}
// 添加商品计量单位
export function addGoodsUnit(params: any) {
  return request({
    url: `/goods/goodsUnit`,
    method: Method.POST,
    needToken: true,
    params,
  });
}
// 编辑商品计量单位
export function updateGoodsUnit(id: number | string, params: any) {
  return request({
    url: `/goods/goodsUnit/${id}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}

// 商品详情列表
export function getGoodsDetail(params: any) {
  return request({
    url: `/goods/goods/get/${params}`,
    method: Method.GET,
    needToken: true,
    params,
  });
}
//  添加商品分类
export function insertCategory(params: ParamsRule) {
  return request({
    url: `/goods/category`,
    method: Method.POST,
    needToken: true,
    params,
  });
}
// 编辑商品分类
export function updateCategory(params: ParamsRule) {
  return request({
    url: `/goods/category`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
// 禁用分类
export function disableCategory(id: number | string, params: any) {
  return request({
    url: `/goods/category/disable/${id}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
// 获取所有可用品牌
export function getBrandListData() {
  return request({
    url: `/goods/brand/all`,
    method: Method.GET,
    needToken: true,
  });
}
// 根据分类id获取关联品牌
export function getCategoryBrandListData(id: string | number) {
  return request({
    url: `/goods/categoryBrand/${id}`,
    method: Method.GET,
    needToken: true,
  });
}
// 保存获取关联品牌
export function saveCategoryBrand(id: string | number, params: any) {
  return request({
    url: `/goods/categoryBrand/${id}`,
    method: Method.POST,
    needToken: true,
    data: qs.stringify(params),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  });
}
// 根据分类id获取关联规格
export function getCategorySpecListData(id: string | number) {
  return request({
    url: `/goods/categorySpec/${id}`,
    method: Method.GET,
    needToken: true,
  });
}
// 获取所有可用规格
export function getSpecificationList() {
  return request({
    url: `/goods/spec/all`,
    method: Method.GET,
    needToken: true,
  });
}
// 保存获取关联规格
export function saveCategorySpec(id: string | number, params: any) {
  return request({
    url: `/goods/categorySpec/${id}`,
    method: Method.POST,
    needToken: true,
    data: qs.stringify(params),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  });
}
// 查询分类绑定参数信息
export function getCategoryParamsListData(id: any) {
  return request({
    url: `/goods/categoryParameters/${id}`,
    method: Method.GET,
    needToken: true,
  });
}
// 获取分类列表数据
export function getAllCategoryList(id: string | number) {
  return request({
    url: `/goods/category/${id}/all-children`,
    method: Method.GET,
    needToken: true,
  });
}
// 保存参数组
export function insertParamsGroup(params: ParamsRule) {
  return request({
    url: `/goods/categoryParameters`,
    method: Method.POST,
    needToken: true,
    params,
  });
}
// 更新参数组
export function updateParamsGroup(params: ParamsRule) {
  return request({
    url: `/goods/categoryParameters`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
// 删除参数组
export function deleteParamsGroup(id: number | string) {
  return request({
    url: `/goods/categoryParameters/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}
export function deleteParams(id: number | string) {
  return request({
    url: `/goods/parameters/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}
// 保存参数
export function insertGoodsParams(params: ParamsRule) {
  return request({
    url: `/goods/parameters`,
    method: Method.POST,
    needToken: true,
    data: qs.stringify(params),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  });
}
// 更新参数
export function updateGoodsParams(params: ParamsRule) {
  return request({
    url: `/goods/parameters`,
    method: Method.PUT,
    needToken: true,
    data: qs.stringify(params),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  });
}
//  添加或修改规格设置
export function insertSpec(params: ParamsRule) {
  return request({
    url: `/goods/spec`,
    method: Method.POST,
    needToken: true,
    data: qs.stringify(params),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  });
}
//  添加或修改规格设置
export function updateSpec(id: string | number, params: ParamsRule) {
  return request({
    url: `/goods/spec/${id}`,
    method: Method.PUT,
    needToken: true,
    data: qs.stringify(params),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  });
}
// 获取分类列表数据
export function getCategoryTree() {
  return request({
    url: `/goods/category/allChildren`,
    method: Method.GET,
    needToken: true,
  });
}
// 保存平台优惠券
export function savePlatformCoupon(params: ParamsRule) {
  return request({
    url: `/promotion/coupon`,
    method: Method.POST,
    needToken: true,
    data: params,
    headers: { 'Content-Type': 'application/json' },
  });
}
// 修改平台优惠券
export function editShopCoupon(params: ParamsRule) {
  return request({
    url: `/promotion/coupon`,
    method: Method.PUT,
    needToken: true,
    data: params,
    headers: { 'Content-Type': 'application/json' },
  });
}
//  获取单个优惠券
export function getPlatformCoupon(id: any) {
  return request({
    url: `/promotion/coupon/${id}`,
    method: Method.GET,
    needToken: true,
  });
}
//  获取商品sku分页列表
export function getGoodsSkuData(params: ParamsRule) {
  return request({
    url: `/goods/goods/sku/list`,
    method: Method.GET,
    needToken: true,
    params,
  });
}
