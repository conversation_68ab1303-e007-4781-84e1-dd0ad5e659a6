import request, { Method } from '@/utils/axios';
import { ParamsRule } from '@/types/global';

export function getBillPage(params: ParamsRule) {
  return request({
    url: '/payment/bill/getByPage',
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 获取商家结算单流水分页
export function getSellerFlow(id: string | number, params: ParamsRule) {
  return request({
    url: `/payment/bill/${localStorage.getItem('settleID')}/getStoreFlow`,
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 获取商家结算单详细
export function getBillDetail(id: any, params?: ParamsRule) {
  return request({
    url: `/payment/bill/get/${id}`,
    method: Method.GET,
    needToken: true,
    params,
  });
}

// 获取商家分销订单流水分页
export function getDistributionFlow(id: string | number, params: ParamsRule) {
  return request({
    url: `/payment/bill/${localStorage.getItem(
      'settleID'
    )}/getDistributionFlow`,
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 商家核对结算单
export function putReconciliation(id: any, params?: ParamsRule) {
  return request({
    url: `/payment/bill/check/${id}`,
    method: Method.PUT,
    needToken: true,
    params,
  });
}
