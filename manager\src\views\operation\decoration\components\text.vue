<template>
  <div py-16px border-b-1 class="search">
    <a-space py-22px>
      <div w-90px>文本位置</div>
      <div>
        <a-radio-group v-model="props.res.data.align" type="button">
          <a-radio value="left">左</a-radio>
          <a-radio value="center">中</a-radio>
          <a-radio value="right">右</a-radio>
        </a-radio-group>
      </div>
    </a-space>
    <a-space py-22px>
      <div w-90px>{{ props.text }}</div>
      <div>
        <a-input allow-clear @clear="props.res.data.text = ''" hide-button v-model="props.res.data.text"
          :style="{ width: '150px' }" :placeholder="`请输入${props.text}`" :min="0" :max="100">
        </a-input>
      </div>
    </a-space>
    <div flex-a-c flex>
      <div w-90px>{{ props.text + '颜色' }}</div>
      <div ml-4px>
        <t-color-picker format="HEX" :color-modes="['monochrome']" :show-primary-color-preview="false"
          v-model="props.res.data.textColor" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DragRule } from '@/views/operation/decoration/app/models/types';
const props = defineProps<{
  res: DragRule,
  text: string,
}>()

</script>

<style scoped>
.search {
  width: 100%;
}
</style>
