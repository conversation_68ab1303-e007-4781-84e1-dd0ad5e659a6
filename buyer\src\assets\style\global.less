// 主题颜色参考：https://tailwindcss.com/docs/customizing-colors
// 引入主题色

// 明亮主题颜色

@theme_color: #ef4444;
@primary_color: #60a5fa;
@link_color: #438cde;
@price_color: #F31947;

@background_color: #eeeeee;
@light_background_color: #F5F5F7;
@light_white_background_color: #FFFFFF;
@light_border_color: #e3e3e3;
@border_color: #dddee1;
@text_color: #333333;
@light_text_color: #999999;
@light_content_color: #495060;
@title_color: #8c8c8c;
@light_title_color: #1c2438;

// 暗黑主题颜色
@dark_background_color: #141414;
@dark_sub_background_color: #1d1d1d; //稍微浅一点的
@dark_content_color: #d5d5d5;


//自动移滚动条样式
::-webkit-scrollbar {
  width: 1px;
  height: 5px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(50, 50, 50, 0.3);
  border-radius: 1em;
}

::-webkit-scrollbar-track {
  background-color: rgba(50, 50, 50, 0.1);
  border-radius: 1em;
}

// 多行文本超出省略号
.ellipsis {
  /*width: 100%;*/
  overflow: hidden;
  //-webkit-line-clamp: 2;  // 设置行数
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  white-space: pre-wrap;
}
.ellipsis-1 {
  -webkit-line-clamp: 1;
}
.ellipsis-2 {
  -webkit-line-clamp: 2;
}
.ellipsis-3 {
  -webkit-line-clamp: 3;
}

// 分页样式
.paginationBox {
  margin-top: 18px;
  display: flex;
  flex-direction: row-reverse;
}

.clearfix::after {
  content: "";
  display: block;
  clear: both;
}

// 全局变量
.global-color {
  color: @theme_color;
}
.price-color {
  color: @price_color;
}
.text-color {
  color: @text_color;
}
.light-text-color {
  color: @light_text_color;
}

.hover-pointer {
  cursor: pointer;
}
.hover-color:hover {
  color: @theme_color !important;
  cursor: pointer;
}

.flex {
  display: flex;
}
.text-center {
  text-align: center;
}

.fontsize-12 {
  font-size: 12px;
}
.fontsize-14 {
  font-size: 14px;
}
.fontsize-16 {
  font-size: 16px;
}
.fontsize-18 {
  font-size: 18px;
}
.fontsize-20 {
  font-size: 20px;
}
.fontsize-22 {
  font-size: 22px;
}

.ml_10 {
  margin-left: 10px;
}
.mr_10 {
  margin-right: 10px;
}
.mt_10 {
  margin-top: 10px;
}
.mb_10 {
  margin-bottom: 10px;
}
.ml_20 {
  margin-left: 20px;
}
.mr_20 {
  margin-right: 20px;
}
.mt_20 {
  margin-top: 20px;
}
.mb_20 {
  margin-bottom: 20px;
}

.pl_10 {
  padding-left: 10px;
}
.pr_10 {
  padding-right: 10px;
}
.pt_10 {
  padding-top: 10px;
}
.pb_10 {
  padding-bottom: 10px;
}
.pl_20 {
  padding-left: 20px;
}
.pr_20 {
  padding-right: 20px;
}
.pt_20 {
  padding-top: 20px;
}
.pb_20 {
  padding-bottom: 20px;
}

.arco-radio-button.arco-radio-checked {
  color: @theme_color !important;
}

.home-bgcolor {
  min-height: 100vh;
  background-color: @light_background_color;
}
