<template>
  <div style="background-color: #fff">
    <a-form ref="formRef" style="padding: 30px" :model="form" layout="horizontal" auto-label-width>
      <a-divider orientation="left">第三方积分</a-divider>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item field="ratio" label="积分兑换比例" :rules="[REQUIRED]" :validate-trigger="['change']">
            <div>
              <a-input-number v-model="form.ratio" placeholder="" allow-clear/>
            </div>
          </a-form-item>
          <a-form-item>
              <a-button type="primary" @click="handleSubmit">保存</a-button>
            </a-form-item>
        </a-col>
      </a-row>

    </a-form>
  </div>
</template>

<script setup lang="ts">
  import { getSetting, setSetting } from '@/api/operation';
  import { onMounted, ref } from 'vue';
  import useCurrentInstance from '@/hooks/useCurrentInstance';
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { VARCHAR255, REQUIRED, VARCHAR20 } from '@/utils/validator';

  const formRef = ref<FormInstance>();
  // 获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;

  interface formInterface {
    ratio: number
  }

  // 数据集
  const form = ref<formInterface>({
      ratio: 0
  });

  async function init() {
    const res = await getSetting('NEW_POINT_SETTING');
    form.value = res.data.result;
  }

  const handleSubmit = async () => {
    const auth = await formRef.value?.validate();
    if (!auth) {
      const result = await setSetting('NEW_POINT_SETTING', form.value);
      if (result.data.success) {
        Message.success('设置成功!');
        init();
      }
    }
  };

  onMounted(() => {
    init();
  });
</script>
