import request, { Method } from '@/utils/axios';

/**
 * 保存充值配置表单
 * @param data
 */
export function saveVoucher(data: Record<string, any>) {
  return request({
    url: `/other/voucher/add`,
    method: Method.POST,
    needToken: true,
    data
  })
}

/**
 * 获取充值配置表单
 * @param type
 */
export function getVoucherDetail(type: string) {
  return request({
    url: `/other/voucher/${type}`,
    method: Method.GET,
    needToken: true
  })
}