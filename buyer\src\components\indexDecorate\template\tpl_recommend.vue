<script setup lang="ts">
import { useRouter } from 'vue-router'
import { navigateTo } from './navigate.ts'
import { unitPrice } from '@/utils/filters'

const props = defineProps<{
  res: any
}>()
const router = useRouter()
function handleClickItem(item: any) {
  const path = navigateTo(item)

  window.open(router.resolve(path).href, '_blank')
}

function handleClickGoods(item: type) {
  const path = {
    path: '/goodsDetail',
    query: {
      skuId: item.id,
      goodsId: item.goodsId,
    },
  }

  window.open(router.resolve(path).href, '_blank')
}
</script>

<template>
  <div>
    <div
      :style="{ textAlign: props.res.data.align, color: props.res.data.textColor, backgroundColor: props.res.data.background }"
      class="text" py-16px text-35px line-height-42px>
      {{ props.res.data.text }}
    </div>
    <div>
      <div class="card flex">
        <div class="left" h-554px w-346px>
          <img block h-554px w-346px :src="props.res.data.leftData.img" alt="">
        </div>
        <div class="right flex">
          <!-- 商品区 -->
          <div class="goods-list">
            <div v-for="(item, index) in props.res.data.list" :key="index" class="goods-item"
              @click="handleClickGoods(item)">
              <div>
                <div class="goods-name">
                  {{ item.title }}
                </div>
                <div class="goods-desc">
                  {{ item.desc }}
                </div>
              </div>
              <img class="goods-img" :src="item.img" alt="">
            </div>
          </div>
          <!-- 热卖区 -->
          <div class="hot-list">
            <div class="hot-title">
              最近热卖
            </div>
            <div v-for="(item, index) in props.res.data.sellList" :key="index" class="hot-item flex flex-a-c"
              @click="handleClickItem(item)">
              <img :src="item.img" alt="">
              <div class="hot-goods">
                <div class="hot-goods-title">
                  {{ item.title }}
                </div>
                <div>
                  <div class="hot-price">
                    ￥{{ unitPrice(item.price) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.text {
  color: #333;
}

.goods-name {
  margin-top: 23px;
  font-size: 25px;
  font-weight: normal;
  line-height: 30px;
  text-align: center;
  letter-spacing: 0px;

  color: #333333;
}

.hot-title {
  margin-left: 25px;
  line-height: 63px;
  font-size: 17px;
  font-weight: normal;

  letter-spacing: 0px;
  color: #333333;
  height: 63px;
}

.goods-img {
  width: 190px;
  height: 156px;
  border-radius: 10px;
  margin-top: 19px;
}

.goods-desc {
  font-size: 14px;
  font-weight: normal;
  line-height: 17px;
  text-align: center;
  margin-top: 11px;
  letter-spacing: 0px;

  color: #333333;
}

.right {
  background: #fff;
}

.goods-list {
  display: flex;
  width: 558px;
  flex-wrap: wrap;
}

.hot-price {
  margin-top: 10.3px;
  font-size: 24px;
  font-weight: normal;
  line-height: 29px;
  letter-spacing: 0px;

  color: #f31947;
}

.hot-goods-title {
  font-size: 13px;
  font-weight: normal;
  line-height: 16px;
  letter-spacing: 0px;

  color: #333333;
}

.hot-goods {
  margin-left: 14px;
}

.goods-item {
  width: 278px;
  height: 277px;
  text-align: center;
  box-sizing: border-box;
  border-right: 1.4px solid #e2e2e2;

  &:nth-of-type(3) {
    border-top: 1.4px solid #e2e2e2;
  }

  &:nth-of-type(4) {
    border-top: 1.4px solid #e2e2e2;
  }
}

.hot-list {
  width: 279px;
}

.hot-item {
  box-sizing: border-box;
  border-top: 1.4px solid #e2e2e2;
  height: 122px;
  padding-left: 14px;
  align-items: center;

  >img {
    width: 76.3px;
    height: 77.7px;
  }
}
</style>
