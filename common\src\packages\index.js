import PickChoiceGoods from './choice-goods/index.vue'
import PickChoiceCity from './choice-city/index.vue'
import PickChoiceOss from './choice-oss/index.vue'
import PickSearchColumn from './search-column/index.vue'
import PickTablePage from './table-pages/index.vue'
import PickUpload from './uploader/index.vue'
export { PickChoiceCity, PickSearchColumn, PickTablePage, PickChoiceGoods, PickUpload, PickChoiceOss }

const component = [PickChoiceCity, PickSearchColumn, PickTablePage, PickChoiceGoods, PickUpload, PickChoiceOss]

const Pickmall = {
  install: function (Vue, options) {
    component.forEach(item => {
      Vue.component(item.name, item)
    })
  }
}

export default Pickmall;
