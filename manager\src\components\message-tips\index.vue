<template>
    <a-dropdown trigger="click">
          <a-button type="text"><icon-calendar-clock/>待办事项</a-button>
          <template #content>
            <a-doption v-if="props.res.balanceCash">
              <a-space @click="$router.push({ name: 'withdraw-apply' })">
                <span> 待处理预存款提现申请 </span>
                <a-badge :count="props.res.balanceCash" />
              </a-space>
            </a-doption>
            <a-doption v-if="props.res.complain">
              <a-space @click="$router.push({ name: 'order-complaint' })">
                <span> 待处理投诉审核 </span>
                <a-badge :count="props.res.complain" />

              </a-space>
            </a-doption>
            <a-doption v-if="props.res.distributionCash">
              <a-space @click="$router.push({ name: 'distrbution-withdrawal' })">
                <span> 待处理分销商提现申请 </span>
                <a-badge :count="props.res.distributionCash" />

              </a-space>
            </a-doption>
            <a-doption v-if="props.res.goods">
              <a-space @click="$router.push({ name: 'auth-goods-list' })">
                <span> 待处理商品审核 </span>
                <a-badge :count="props.res.goods" />

              </a-space>
            </a-doption>
            <a-doption v-if="props.res.refund">
              <a-space @click="$router.push({ name: 'after-sale' })">
                <span> 待处理售后申请 </span>
                <a-badge :count="props.res.refund" />

              </a-space>
            </a-doption>
            <a-doption v-if="props.res.store">
              <a-space @click="$router.push({ name:'shop-audit' })">
                <span> 待处理店铺入驻审核 </span>
                <a-badge :count="props.res.store" />

              </a-space>
            </a-doption>
            <a-doption v-if="props.res.waitPayBill">
              <a-space @click="$router.push({ name : 'shop-reconciliation'})">
                <span> 待与商家对账 </span>
                <a-badge :count="props.res.waitPayBill" />

              </a-space>
            </a-doption>
          </template>
        </a-dropdown>
  </template>
  
  <script setup lang="ts">
    import { onMounted , ref } from 'vue';

    const props = defineProps({
    res: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });
  // const init =()=>{
  //   Object.keys(props.res).forEach((item) => {
  //       value.value = parseInt(value) + parseInt(props.res[item]);
  //     });
  // }
  </script>
  
  <style scoped lang="less">

  </style>
  