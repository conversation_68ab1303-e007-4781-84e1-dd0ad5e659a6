FROM node:21 AS build-stage

WORKDIR /seller
COPY ./seller/package*.json ./
RUN yarn config set registry https://registry.npmmirror.com --global
RUN yarn
COPY ./seller .
RUN yarn build

WORKDIR /manager
COPY ./manager/package*.json ./
RUN yarn config set registry https://registry.npmmirror.com --global
RUN yarn config set disturl https://registry.npmmirror.com/dist --global
RUN yarn
COPY ./manager .
RUN yarn build

# production stage
FROM nginx:stable AS production-stage
COPY ./start-nginx.sh /usr/bin/start-nginx.sh
RUN chmod +x /usr/bin/start-nginx.sh

# COPY --from=build-stage /buyer/dist /usr/share/nginx/html/buyer
COPY --from=build-stage /seller/dist /usr/share/nginx/html/seller
COPY --from=build-stage /manager/dist /usr/share/nginx/html/manager
COPY ./nginx.conf /etc/nginx/nginx.conf
EXPOSE 80

CMD ["/bin/sh","/usr/bin/start-nginx.sh"]