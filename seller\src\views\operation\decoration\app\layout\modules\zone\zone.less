
li{
  list-style: none;
}
ul {
  margin: 0;
  padding: 0;
}
// ------------ 热区
.hz-u-img {
  display: block;
  width: 100%;
  max-width: 100%;
  height: auto;
  user-select: none;
}
.hz-m-area {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  cursor: crosshair;
}

.hz-m-item {
  position: absolute;
  display: block;
}
.hz-m-wrap {
  position: relative;

 

  

}
.hz-m-box {
  position: relative;
  width: 100%;
  height: 100%;
  box-shadow: 0 0 6px #000;
  background-color:rgb(var(--arcoblue-4));
  font-size: 12px;
  cursor: pointer;
  color: #fff;
  opacity: 0.8;

  &>li {
    position: absolute;
    text-align: center;
    user-select: none;
  }

  &.hz-z-hidden>li {
    display: none;
  }

  &.hz-m-hoverbox:hover {
    box-shadow: 0 0 0 2px #373950;

    .hz-icon:hover {
      background-color: #373950;
    }
  }

  

}
.hz-u-square {
  width: 8px;
  height: 8px;
  opacity: 0.8;

  &:after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 4px;
    height: 4px;
    border-radius: 4px;
    background-color: #fff;
  }
}
.hz-u-square-tl {
  top: -4px;
  left: -4px;
  cursor: nw-resize;
}

.hz-u-square-tc {
  top: -4px;
  left: 50%;
  transform: translateX(-50%);
  cursor: n-resize;
}

.hz-u-square-tr {
  top: -4px;
  right: -4px;
  cursor: ne-resize;
}

.hz-u-square-cl {
  top: 50%;
  left: -4px;
  transform: translateY(-50%);
  cursor: w-resize;
}

.hz-u-square-cr {
  top: 50%;
  right: -4px;
  transform: translateY(-50%);
  cursor: w-resize;
}.hz-u-square-bl {
  bottom: -4px;
  left: -4px;
  cursor: sw-resize;
}

.hz-u-square-bc {
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  cursor: s-resize;
}

.hz-u-square-br {
  bottom: -4px;
  right: -4px;
  cursor: se-resize;
}


.hz-icon {
  width: 24px;
  height: 24px;
  line-height: 24px;
  font-size: 20px;
  text-align: center;

  &:hover {
    background-color:rgb(var(--arcoblue-4));
    opacity: 0.8;
  }
}

.hz-u-index {
  top: 0;
  left: 0;
  width: 24px;
  height: 24px;
  line-height: 24px;
  background-color: #000;
  z-index: 100;
}



.hz-small-icon {
  border: 0;
  border-radius: 0;
}

&.hz-z-hidden>li {
  display: none;
}

&.hz-m-hoverbox:hover {
  box-shadow: 0 0 0 2px #373950;
}

// 以下是一些额外的样式，可用于提高可用性和美观性

.hz-m-wrap {
  overflow: hidden;
}

.hz-m-box {
  z-index: 999;
}

.hz-m-item {
  opacity: 0.6;
}

.hz-m-box:hover .hz-m-item {
  opacity: 1;
}

.hz-u-square {
  &:hover {
    cursor: pointer;
  }
}
