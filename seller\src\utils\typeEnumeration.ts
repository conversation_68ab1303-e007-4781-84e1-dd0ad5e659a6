/**
 * 状态枚举
 * */
export const orderClientStatus: any = {
  UNDELIVERED: '待发货',
  UNPAID: '未付款',
  PAID: '已付款',
  DELIVERED: '已发货',
  CANCELLED: '已取消',
  COMPLETED: '已完成',
  TAKE: '待核验',
  STAY_PICKED_UP: '待自提',
};

// 付款状态
export const payStatusClient: any = {
  UNPAID: '未付款',
  PAID: '已付款',
};

// 支付方式
export const paymentMethodClient: any = {
  ONLINE: '在线支付',
  ALIPAY: '支付宝',
  BANK_TRANSFER: '银行卡',
};

// 售后单类型
export const serviceTypeClient: any = {
  CANCEL: '取消',
  RETURN_GOODS: '退货',
  EXCHANGE_GOODS: '换货',
  RETURN_MONEY: '退款',
};

// 售后单状态
export const serviceStatusClient: any = {
  APPLY: '申请售后',
  PASS: '通过售后',
  REFUSE: '换货',
  RETURN_MONEY: '拒绝售后',
  BUYER_RETURN: '买家退款，等待卖家收货',
  SELLER_RE_DELIVERY: '商家换货/补发',
  SELLER_CONFIRM: '卖家确认收货',
  SELLER_TERMINATION: '卖家终止售后',
  BUYER_CONFIRM: '买家确认收货',
  BUYER_CANCEL: '买家取消售后',
  WAIT_REFUND: '等待平台退款',
  COMPLETE: '完成售后',
};

// 售后类型
export const serviceTypeList: any = {
  CANCEL: '取消',
  RETURN_GOODS: '退货',
  EXCHANGE_GOODS: '换货',
  RETURN_MONEY: '退款',
};
