<template>
  <div >
    <a-button style="margin: 10px 0;" @click="hanlderUpload('uploadImg')">上传图片</a-button>
    <div style="border: 2px solid #eee; border-radius: 10px;">
      <Toolbar
        style="border-bottom: 1px solid #ccc"
        :editor="editorRef"
        :defaultConfig="toolbarConfig"
        :mode="mode"
        ref="editorData"
    ></Toolbar>
    <Editor style="min-height: 380px; overflow-y: hidden; text-align: left;"
        :defaultConfig="editorConfig"
        :mode="mode"
        @onCreated="handleCreated"
        @onChange="handleChange"
        v-model="valueHtml"
        >
    </Editor>
    </div>    
    <a-modal v-model:visible="showOssManager" :width="1100"  @ok="handleOss" title="oss资源管理" @cancel="showOssManager = false">
      <ossManages @selected="changOssImage" :isSingle="true"></ossManages>
    </a-modal>
  </div>
</template>
<script setup>
  import '@wangeditor/editor/dist/css/style.css' // 引入 css
  import { onBeforeUnmount, ref, shallowRef, onMounted, nextTick, watch } from 'vue'
  import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
  import axios from 'axios'
  import uploadFile from '@/api/index';
  import storage from '@/utils/storage';
  import ossManages from '@/components/oss-manage/index.vue';

  const showOssManager = ref(false); // oss弹框
  const editorData = ref(null)
  // initValue: 父组件传递过来的富文本框初始值，这里会实时监听，更新初始值，放置在弹窗中使用，没有钩子函数的更新。
  // getEditorContent() 方法，父组件通过这个方法获取富文本编辑器的内容，包括数组格式的和html格式的内容。
  const selectedSku = ref(); // 选择的sku
  // 初始值
  const props = defineProps({
    initValue: String
  });
  const emits = defineEmits(['getEditorContent']);
  // const emits = defineEmits([''])
  let mode = ref('default');
  // 编辑器实例，必须用 shallowRef
  const editorRef = shallowRef();
  // 内容 HTML
  const valueHtml = ref('');
  // 模拟 ajax 异步获取内容
  onMounted(() => {
    nextTick(() => { // 界面节点更新完以后再修改值。
      if(props.initValue){
        valueHtml.value = props.initValue
      }
    })
  });
  // 工具栏配置
  const toolbarConfig = {
    toolbarKeys: [
      // 菜单 key
      'headerSelect',
      'bold', // 加粗
      'italic', // 斜体
      'through', // 删除线
      'underline', // 下划线
      'bulletedList', // 无序列表
      'numberedList', // 有序列表
      'color', // 文字颜色
      'insertLink', // 插入链接
      'fontSize', // 字体大小
      'lineHeight', // 行高
      // 'uploadImage', // 上传图片
      'delIndent', // 缩进
      'indent', // 增进
      'deleteImage',//删除图片
      'divider', // 分割线
      'insertTable', // 插入表格
      'justifyCenter', // 居中对齐
      'justifyJustify', // 两端对齐
      'justifyLeft', // 左对齐
      'justifyRight', // 右对齐
      'undo', // 撤销
      'redo', // 重做
      'clearStyle', // 清除格式
      'fullScreen', // 全屏
      'emotion',
    ]
  };
  const editorConfig = {
    placeholder: '请输入内容...', // 配置默认提示
  }
  // 组件销毁时，也及时销毁编辑器
  onBeforeUnmount(() => {
    const editor = editorRef.value;
    if (editor == null) return;
    editor.destroy()
  });
  const handleCreated = (editor) => {
    editorRef.value = editor // 创建富文本编辑器
  };
  const handleChange = (info) => {
    // info.children 数组包含了数据类型和内容，valueHtml.value内容的html格式
    emits('getEditorContent', info.children, valueHtml.value)
  };


    // 上传图片
    const hanlderUpload = (type) => {
      showOssManager.value = true;
    };
    // oss资源确定
    const handleOss = () => {
      showOssManager.value = false;
      const node ={
        type:"image",
        src:selectedSku.value[selectedSku.value.length-1].url,
        style:{with:'',height:''},
        children: [{ text: "" }],
      }
      editorData.value.editor.insertNode(node)
    };
    // oss资源改变
    const changOssImage = (val) => {
      selectedSku.value = [];
      val.forEach((item)=>{
        selectedSku.value.push({url:item.url})
      })
    };

  watch(()=>props.initValue, (value) => {  // 父组件获取初始值
    valueHtml.value = value
  })
</script>
<style lang="less" scoped>
 :deep(.w-e-text-container){
        height: 300px !important;/*!important是重点，因为原div是行内样式设置的高度300px*/
    }
</style>