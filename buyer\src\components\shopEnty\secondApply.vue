<template>
  <div>
    <a-form ref="secondFormRef" :model="secondForm" @submit="next" auto-label-width layout="horizontal">
      <h4>基础信息</h4>
      <div :style="{ width: '500px', paddingLeft: '50px'}" >
        <a-form-item field="settlementBankAccountName" label="银行开户名" :rules="[REQUIRED]">
          <a-input v-model="secondForm.settlementBankAccountName" placeholder="请填写银行开户名"></a-input>
        </a-form-item>
        <a-form-item field="settlementBankAccountNum" label="银行账号" :rules="[REQUIRED]">
          <a-input v-model="secondForm.settlementBankAccountNum" placeholder="请填写银行账号"></a-input>
        </a-form-item>
        <a-form-item field="settlementBankBranchName" label="开户行支行名称" :rules="[REQUIRED]">
          <a-input v-model="secondForm.settlementBankBranchName" placeholder="请填开户行支行名称"></a-input>
        </a-form-item>
        <a-form-item field="settlementBankJointName" label="支行联行号" :rules="[REQUIRED]">
          <a-input v-model="secondForm.settlementBankJointName" placeholder="支行联行号"></a-input>
        </a-form-item>
        <a-form-item field="" label="">
          <a-button class="mr_10" @click="emit('handleChange', 1)">返回</a-button>
          <a-button html-type="submit" type="primary" status="danger" :loading="loading">填写其他信息</a-button>
        </a-form-item>
      </div>




    </a-form>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { REQUIRED } from '@/utils/validator';
  import { applySecond } from '@/api/shopentry';

  const props = defineProps({
    content: {
      content: Object,
      default: () => {return {}}
    },
  });
  const emit = defineEmits(['handleChange']);

  const loading = ref(false);
  const secondFormRef = ref();
  const secondForm = ref<any>({});

  // 下一步
  const next = async () => {
    const auth = await secondFormRef.value?.validate();
    if (!auth) {
      loading.value = true;
      applySecond(secondForm.value).then((res) => {
        loading.value = false;
        if (res.data.success) emit('handleChange', 3);
      }).catch(() => {loading.value = false;});
    }
  };

  onMounted(() => {
    if (props.content && Object.keys(props.content).length) {
      secondForm.value = JSON.parse(JSON.stringify(props.content));
    }
  })
</script>

<style scoped lang="less">
  h4 {
    margin-bottom: 10px;
    padding: 0 10px;
    border: 1px solid #ddd;
    background-color: #f8f8f8;
    font-weight: bold;
    color: #333;
    font-size: 14px;
    line-height: 40px;
    text-align: left;
  }
  .describe {
    font-size: 12px;
    color: #999;
    margin: 8px 0;
  }
</style>
