<template>
  <div>
    <a-card :style="{ width: '100%' }">
      <a-form ref="formRef" :model="form" :style="{ width: '600px' }">
        <div class="base-info-item">
          <div class="form-item-view">
            <h4>商品信息</h4>
            <a-form-item label="商品名称">
              <div>{{ form.goodsName }}</div>
            </a-form-item>
            <a-form-item label="SKU编码">
              <div>{{ form.skuId }}</div>
            </a-form-item>
            <a-form-item label="店铺名称">
              <div>{{ form.goodsSku.storeName }}</div>
            </a-form-item>
            <a-form-item label="商品价格">
              <div>{{ unitPrice(form.goodsSku.price, '¥') }}</div>
            </a-form-item>
            <a-form-item label="商品库存">
              <div>{{ form.goodsSku.quantity }}</div>
            </a-form-item>
            <a-form-item field="settlementPrice" label="结算价格" :rules="[REQUIRED]">
              <a-input-number :disabled="isDisiabled" v-model="form.settlementPrice"
                :style="{ width: '260px' }" class="input-demo" allow-clear />
            </a-form-item>
            <a-form-item field="lowestPrice" label="最低可砍" :rules="[REQUIRED]">
              <a-input-number :disabled="isDisiabled" v-model="form.lowestPrice" :style="{ width: '260px' }"
                class="input-demo" allow-clear />
            </a-form-item>
            <a-form-item field="highestPrice" label="最高可砍" :rules="[REQUIRED]">
              <a-input-number :disabled="isDisiabled" v-model="form.highestPrice" :style="{ width: '260px' }"
                class="input-demo" allow-clear />
            </a-form-item>
            <a-form-item field="stock" label="活动库存" :rules="[REQUIRED]">
              <a-input-number :disabled="isDisiabled" v-model="form.stock" :style="{ width: '260px' }"
                class="input-demo" allow-clear />
            </a-form-item>

            <a-form-item field="rangeTime" label="活动时间" :rules="[REQUIRED]">
              <a-range-picker @change="changPicker" :disabled="isDisiabled"
                style="width: 254px; margin-bottom: 20px;" v-model="form.rangeTime" />
            </a-form-item>
            <div>
              <a-button style="margin-right: 5px" @click="closeCurrentPage">返回</a-button>
              <a-button type="primary"  v-if='!isDisiabled' @click="handleSubmit">提交</a-button>
            </div>
          </div>
        </div>
      </a-form>
    </a-card>
  </div>
</template>

<script setup lang='ts'>
import { ref, reactive, onMounted } from "vue"
import { getKanJiaActivityGoodsById, editKanJiaActivityGoods } from "@/api/promotion";
import { useRoute, useRouter } from 'vue-router';
import { unitPrice } from '@/utils/filters';
import { REQUIRED } from '@/utils/validator';
import { FormInstance } from '@arco-design/web-vue/es/form';
import { dayFormatHHssMM } from '@/utils/filters';
import { money } from '@/utils/filters';
import { Message } from '@arco-design/web-vue';



const formRef = ref<FormInstance>();// 表单
const route = useRoute()
const router = useRouter()
const { id, onlyView } = route.query
const isDisiabled = onlyView == 'true' ? true : false;
const form = ref({
  goodsName: '',
  rangeTime: [],
  startTime: '',
  endTime: '',
  skuId: 0,
  settlementPrice: '',
  lowestPrice: '',
  stock: '',
  highestPrice: '',
  goodsSku: {
    storeName: '',
    price: '',
    quantity: ''
  },
}) as any
const getKanJiaActivityGoods = async () => {
  const res = await getKanJiaActivityGoodsById(route.query.id)
  if (res.data.success) {
    form.value = res.data.result
    form.value.rangeTime = []
    form.value.rangeTime.push(new Date(form.value.startTime), new Date(form.value.endTime))
  }
}
const init = () => {
  getKanJiaActivityGoods()
}
onMounted(() => {
  init()
})
// 提交
const handleSubmit = async () => {
  const auth = await formRef.value?.validate();
  if (!auth) {
    const params = JSON.parse(JSON.stringify(form.value));
    if (form.value.rangeTime.length <= 0) {
      Message.warning('请选择活动时间');
      return;
    }
    params.startTime = form.value.startTime
    params.endTime = form.value.endTime
    delete params.rangeTime
    // 校验库存参数
    if (params.stock <= 0 || params.stock > params.goodsSku.quantity) {
      Message.error("活动库存不能为0且不能超过商品库存");
      return
    }
    // 结算价格金额格式校验
    if (!money.test(params.settlementPrice)) {
      Message.error("结算价格金额格式不正确");
      return
    }
    // 结算价格金额格式校验
    if (params.settlementPrice < 0 || params.settlementPrice > params.price) {
      Message.error("结算价格金额不能为0且不能超过商品价格");
      return
    }
    // 最高砍价校验
    if (!money.test(params.highestPrice)) {
      Message.error("最高可砍金额格式错误");
      return
    }
    if (params.highestPrice <= 0 || params.highestPrice > params.price) {
      Message.error("最高可砍金额不能为0且不能超过商品价格");
      return
    }
    // 最低砍价校验
    if (!money.test(params.lowestPrice)) {
      Message.error("最低可砍金额格式错误");
      return
    }
    if (params.lowestPrice <= 0 || params.lowestPrice > params.price) {
      Message.error("最低可砍金额不能为0");
      return
    }
    // 校验最高最低砍价金额
    if (params.lowestPrice > params.highestPrice) {
      Message.error("最低砍价金额不能大于最高砍价金额");
      return
    }
    const res = await editKanJiaActivityGoods(params)
    if (res.data.success) {
      Message.success("砍价活动修改成功");
      router.push({ name: 'bargain-activity' })
    }
  }
}
// 返回
const closeCurrentPage = () => {
  router.push({ name: 'bargain-activity' })
}
// 选择日期
const changPicker = (val: any) => {
  const [startTime, endTime] = val
  form.value.startTime = dayFormatHHssMM(startTime.substring(0, 10))
  form.value.endTime = dayFormatHHssMM(endTime.substring(0, 10))
}
</script>

<style lang="less" scoped>
h4 {
  margin-bottom: 10px;
  padding: 0 10px;
  border: 1px solid #ddd;
  background-color: #f8f8f8;
  font-weight: bold;
  color: #333;
  font-size: 14px;
  line-height: 40px;
  text-align: left;
}

.describe {
  font-size: 12px;
  margin-left: 10px;
  color: #999;
}

.effectiveDays {
  font-size: 12px;
  color: #999;

  >* {
    margin: 0 4px;
  }
}

.tips {
  font-size: 12px;
  color: #999;
}
</style>
