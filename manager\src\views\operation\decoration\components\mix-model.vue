<template>
  <div class="card" py-16px overflow-y-auto my-10px v-for="(item, index) in props.res.data[props.bind || 'list']"
    :key="index">
    <div flex flex-a-c flex-j-sb>
      <div>
        {{ props.text + (index + 1) }}
      </div>
    </div>
    <imageModel :item="item" v-if="item['img']" />
    <!-- 判断如果有title则展示文字修改 -->
    <div py-16px border-b-1 v-if="item.title || item.title === ''">
      <a-space>
        <div w-90px> {{ props.text + (index + 1) }}标题</div>
        <a-input allow-clear @clear="item.title = ''" hide-button v-model="item.title" :style="{ width: '150px' }"
          :placeholder="`请输入${props.text + (index + 1)}`" :min="0" :max="100">
        </a-input>
      </a-space>
    </div>
    <!-- 判断如果有desc则展示描述修改 -->
    <div py-16px border-b-1 v-if="item.desc || item.desc === ''">
      <a-space>
        <div w-90px> {{ props.text + (index + 1) }}描述</div>
        <a-input allow-clear @clear="item.desc = ''" hide-button v-model="item.desc" :style="{ width: '150px' }"
          :placeholder="`请输入${props.text + (index + 1)}`" :min="0" :max="100">
        </a-input>
      </a-space>
    </div>
    <emptyLinkModel :item="item" />
    <div v-if="props.delete" text-center color-gray cursor-pointer @click="remove(index)">删除</div>
  </div>
</template>

<script setup lang="ts">
import { DragRule } from '@/views/operation/decoration/app/models/types';
import imageModel from '@/views/operation/decoration/components/img.vue';
// 导入真正的链接组件
import emptyLinkModel from '@/views/operation/decoration/components/link.vue';
import { Message } from '@arco-design/web-vue';
const props = defineProps<{
  res: DragRule,
  text: string,
  bind?: string
  delete?: boolean,
}>()

function remove(index: number) {
  if (props.res.data[props.bind || 'list'].length === 1) {
    Message.error('请最少保留一个组件')
  } else {
    props.res.data[props.bind || 'list'].splice(index, 1)
  }
}
</script>

<style scoped lang="less">
.card {
  border-top: 1px solid var(--color-neutral-3);
  border-bottom: 1px solid var(--color-neutral-3);
}
</style>
