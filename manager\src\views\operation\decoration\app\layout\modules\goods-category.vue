<template>
  <div py-30px border-b-1 v-for="(item, index) in props.res.data.category" :key="index">
    <div>分类模块{{ index + 1 }}</div>
    <a-space mt-20px>
      <div w-90px>标题</div>
      <div>
        <a-input :max-length="5" v-model="item.title" placeholder="请输入分类标题" />
      </div>
    </a-space>
    <a-space mt-20px>
      <div w-90px>描述</div>
      <div>
        <a-input :max-length="6" v-model="item.desc" placeholder="请输入分类描述" />
      </div>
    </a-space>

    <goodsChoice :model="item" :res="res" text="商品选择" />
    <div text-center cursor-pointer @click="del(index)" color-warmGray>删除</div>

  </div>
  <a-button v-if="res.data.category.length < 4" @click="add()" long>添加分类</a-button>
</template>


<script setup lang="ts">
import { DragRule } from '@/views/operation/decoration/app/models/types';
import goodsChoice from '@/views/operation/decoration/components/goods-choice.vue'
import { onMounted } from 'vue';
import { Message } from "@arco-design/web-vue";
const props = defineProps<{
  res: DragRule,
  text: string
}>()

onMounted(() => {

});


function del(index: number) {
  // 最少保留一个分类
  if (props.res.data.category.length > 1) {
    props.res.data.category.splice(index, 1)
  } else {
    Message.warning('至少保留一个分类')
    return
  }
}

function add() {
  props.res.data.category.push({
    title: "分类" + props.res.data.category.length,
    desc: "描述",
    goods: []
  })
}
</script>

<style scoped></style>
