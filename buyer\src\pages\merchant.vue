<template>
  <div>
    <a-spin :loading="loading" style="width: 100%">
      <!--搜索栏-->
      <Search></Search>
      <!--店铺logo-->
      <div class="shop-item-path">
        <div class="shop-nav-container">
          <div><span class="fontsize-18 mr_20">{{ storeMsg.storeName || 'xx店铺' }}</span><span  class="store-desc" v-html="storeMsg.storeDesc"></span></div>
          <div class="store-collect">
            <span @click="handleCollectStore">
              <template v-if="storeCollected">
                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 24 24"><path fill="#ef4444" d="M12.001 4.529a5.998 5.998 0 0 1 8.242.228a6 6 0 0 1 .236 8.236l-8.48 8.492l-8.478-8.492a6 6 0 0 1 8.48-8.464"/></svg>
                已收藏店铺
              </template>
              <template v-else>
                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 24 24"><path fill="#FFFFFF" d="M12.001 4.529a5.998 5.998 0 0 1 8.242.228a6 6 0 0 1 .236 8.236l-8.48 8.492l-8.478-8.492a6 6 0 0 1 8.48-8.464m6.826 1.641a3.998 3.998 0 0 0-5.49-.153l-1.335 1.198l-1.336-1.197a4 4 0 0 0-5.686 5.605L12 18.654l7.02-7.03a4 4 0 0 0-.193-5.454"/></svg>
                收藏店铺
              </template>
            </span>
            <span @click="IMService(storeMsg.storeId)">联系客服</span>
          </div>
        </div>
      </div>
      <!--店铺分类-->
      <div class="store-category">
        <div class="cate-list">
          <div class="cate-item" @click="searchByCate({ id: '', labelName: '店铺推荐' })">首页</div>
          <div v-for="(cate, index) in cateList" :key="index" class="cate-item">
            <a-dropdown trigger="hover" v-if="cate.children.length" @click="searchByCate(cate)">
              <div>{{ cate.labelName }}
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"><path fill="#ffffff" d="m12 13.171l4.95-4.95l1.414 1.415L12 16L5.636 9.636L7.05 8.222z"/></svg>
              </div>
              <template #content>
                <a-doption v-for="sec in cate.children" :key="sec.id" @click="searchByCate(sec)">
                  <div>{{ sec.labelName }}</div>
                </a-doption>
              </template>
            </a-dropdown>
            <span v-else @click="searchByCate(cate)">{{cate.labelName}}</span>
          </div>
        </div>
      </div>
      <!-- 楼层装修部分 -->
      <div v-if="storeMsg.pageShow && storeMsg.pageShow !== '1'">
        <!--<model-form ref="modelForm" :data="modelForm"></model-form>-->
      </div>
      <!--店铺主体-->
      <div v-else class="shop-box pb_20">
        <div class="promotion-decorate">{{ cateName }}</div>
        <div class="goods-list">
          <Empty v-if="goodsData.length === 0"/>
          <div v-for="(item, index) in goodsData" v-else :key="index" class="goods-show-info" @click="goGoodsDetail(item.id, item.goodsId)">
            <div class="goods-show-img"><img :src="item.thumbnail" height="210" width="210" alt=""/></div>
            <div class="goods-show-price"><span class="seckill-price text-danger">{{ unitPrice(item.price , "￥") }}</span></div>
            <div class="goods-show-detail"><span>{{ item.goodsName }}</span></div>
            <div class="goods-show-num">已有<span>{{ item.commentNum || 0 }}</span>人评价</div>
          </div>
        </div>
        <!--分页-->
        <div class="paginationBox mt_10 pb_20">
          <a-pagination :total="total" :current="params.pageNumber" :page-size="params.pageSize" show-page-size
                        @change="(number) => {params.pageNumber = number;}" @page-size-change="(number) => {params.pageSize = number; params.pageNumber = 1;}" >
          </a-pagination>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { unitPrice } from '@/utils/filters';
  import storage from '@/utils/storage';
  import { useRoute, useRouter } from 'vue-router';
  import { getCateById, getDetailById } from "@/api/shopentry";
  import { cancelStoreCollect, collectStore, isStoreCollection } from "@/api/member";
  import { goodsList } from "@/api/goods";
  import { getFloorStoreData } from "@/api/index";
  import { getIMDetail } from "@/api/common";
  import { getMemberMsg } from "@/api/login";

  const route = useRoute();
  const router = useRouter();
  const loading = ref(false); // 加载状态
  // 店铺装修的内容
  const modelForm = ref({list: []}); // 楼层装修数据
  const topAdvert = ref({}); // 顶部广告
  const showNav = ref(false); // 是否展示分类栏
  // const topSearchShow = ref(false); // 滚动后顶部搜索栏展示
  const carouselLarge = ref(false); // 不同轮播分类尺寸
  const carouselOpacity = ref(false); // 不同轮播分类样式,
  const storeMsg = ref<any>({}); // 店铺信息
  const cateList = ref<Array<any>>([]); // 店铺分裂
  const goodsData = ref<Array<any>>([]); // 商品列表
  const total = ref(0); // 商品数量
  // 请求参数
  const params = ref({
    pageNumber: 1,
    pageSize: 20,
    keyword: "",
    storeId: route.query.id,
    storeCatId: "",
  });
  const cateName = ref("店铺推荐"); // 分类名称
  const storeCollected = ref(false); // 是否收藏
  const IMLink = ref(''); // IM链接

  // 获取店铺信息
  const getStoreMsg = () => {
    getDetailById(route.query.id).then((res) => {
      if (res.data.success) {
        storeMsg.value = res.data.result;
        // 判断是否收藏
        if (storage.getUserInfo()) {
          isStoreCollection("STORE", route.query.id).then((res) => {
            if (res.data.success && res.data.result) {
              storeCollected.value = true;
            }
          });
        }
        //判定如果开启楼层展示，则获取页面信息 否则读取商品信息
        if (storeMsg.value.pageShow && storeMsg.value.pageShow === '1') {
          getIndexData();
        } else {
          getGoodsList();
          getCateList();
        }
        // let that = this;
        // window.onscroll = function () {
        //   let top = document.documentElement.scrollTop || document.body.scrollTop;
        //   if (top > 300) {
        //     that.topSearchShow = true;
        //   } else {
        //     that.topSearchShow = false;
        //   }
        // };
      }
    });
  };
  // 获取首页装修数据
  const getIndexData = () => {
    getFloorStoreData({clientType: "PC", num: route.query.id, pageType: 'STORE'}).then((res) => {
        if (res.data.success) {
          let dataJson = JSON.parse(res.data.result.pageData);
          // 秒杀活动不是装修的数据，需要调用接口判断是否有秒杀商品
          // 轮播图根据不同轮播，样式不同
          for (const element of dataJson.list) {
            let type = element.type;
            if (type === "carousel2") {
              carouselLarge.value = true;
            } else if (type === "carousel1") {
              carouselLarge.value = true;
              carouselOpacity.value = true;
            }
          }
          modelForm.value = dataJson;
          storage.setNavList(dataJson.list[1]);
          showNav.value = true;
          topAdvert.value = dataJson.list[0];
        }
      }
    );
  };
  // 商品信息
  const getGoodsList = () => {
    loading.value = true;
    goodsList(params.value).then((res) => {
        if (res.data.success) {
          goodsData.value = res.data.result.records;
          total.value = res.data.result.total;
          loading.value = false;
        }
      }).catch(()=>{ loading.value = false; });
  };
  // 店铺分类
  const getCateList = () => {
    getCateById(route.query.id).then((res) => {
      if (res.data.success) {
        cateList.value = res.data.result;
      }
    });
  };
  // 搜索同分类下商品
  const searchByCate = (cate: any) => {
    params.value.storeCatId = cate.id;
    cateName.value = cate.labelName;
  };
  // 立即购买-跳转商品详情
  const goGoodsDetail = (skuId: any, goodsId: any) => {
    let routeUrl = router.resolve({path: "/goodsDetail", query: { skuId, goodsId },});
    window.open(routeUrl.href, "_blank");
  };
  // 收藏店铺/取消收藏店铺
  const handleCollectStore = async () => {
    if (storeCollected.value) {
      let cancel = await cancelStoreCollect("STORE", storeMsg.value.storeId);
      if (cancel.data.success) {
        Message.success("已取消收藏");
        storeCollected.value = false;
      }
    } else {
      let collect = await collectStore("STORE", storeMsg.value.storeId);
      if (collect.data.code === 200) {
        storeCollected.value = true;
        Message.success("收藏店铺成功,可以前往个人中心我的收藏查看");
      }
    }
  };
  // 跳转im客服
  const IMService = async (id: any) => {
    // 获取访问Token
    const accessToken = storage.getAccessToken();
    await getIMDetailMethods();
    const userInfo = await getMemberMsg();
    if (userInfo.data.success) {
      window.open(IMLink.value + "?token=" + accessToken + "&id=" + id || storeMsg.value.storeId);
    } else {
      Message.error("请登录后再联系客服");
      return;
    }
  };
  // 获取im信息
  const getIMDetailMethods = async () => {
    let res = await getIMDetail();
    if (res.data.success) {
      IMLink.value = res.data.result;
    }
  };


  onMounted(() => {
    getStoreMsg();
  });
  watch(() => params.value, (val) => {
    getGoodsList();
    }, { deep: true }
  );
</script>

<style scoped lang="less">
  @import "../assets/style/goodsList";
  .shop-item-path {
    height: 38px;
    background-color: #666666;
    transition: 0.35s;
    line-height: 38px;
    color: #ffffff;
    .shop-nav-container {
      width: 1200px;
      margin: 0 auto;
      position: relative;
      > div:nth-of-type(1) {
        display: flex;
        align-items: center;
        .store-desc {
          display: inline-block;
          width: 800px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          > :deep(p) {
            margin: 0 !important;
            padding: 0 !important;
          }
        }
      }
      .store-collect {
        position: absolute;
        right: 20px;
        top: 0;
        display: flex;
        .link {
          text-decoration: none;
          color: @link_color;
        }
        span {
          display: flex;
          align-items: center;
          margin-left: 20px;
          &:hover {
            cursor: pointer;
            color: @theme_color;
          }
        }
      }
    }
  }
  .store-category {
    background-color: #005aa0;
    color: #fff;
    .cate-list {
      width: 1200px;
      margin: 0 auto;
      clear: left;
      height: 30px;
      line-height: 30px;
      .cate-item {
        margin-right: 25px;
        float: left;
        > div {
          display: flex;
          align-items: center;
        }
      }
      .cate-item:hover {
        cursor: pointer;
      }
    }
  }
  /*店铺主体*/
  .shop-box {
    background-color: @light_background_color;
    overflow: hidden;
    .paginationBox {
      width: 1200px;
      margin: 0 auto;
      background-color: @light_white_background_color;
      box-sizing: border-box;
      padding: 10px 20px;
    }
  }
  .promotion-decorate {
    width: 200px;
    text-align: center;
    font-size: 25px;
    position: relative;
    font-weight: bold;
    margin: 30px auto 30px;
    color: @text_color;
    &::before,
    &::after {
      content: "";
      display: inline-block;
      width: 25px;
      height: 20px;
      background-size: 50px 20px;
      background-repeat: no-repeat;
      background-position: 0 0;
      position: absolute;
      top: 10px;
      left: -3px;
    }
    &::after {
      background-position: -24px 0;
      right: -3px;
      left: auto;
    }
  }
</style>
