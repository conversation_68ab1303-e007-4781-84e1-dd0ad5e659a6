<template>
    <div style="background-color: #fff">
      <a-form ref="formRef" style="padding: 30px" :model="form" layout="horizontal" auto-label-width>
        <a-divider orientation="left">中智分发平台配置</a-divider>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item field="domain" label="Url地址" :rules="[REQUIRED]" :validate-trigger="['change']">
              <div>
                <a-input v-model="form.domain" placeholder="" allow-clear/>
              </div>
            </a-form-item>
            <a-form-item field="account" label="account账号" :rules="[REQUIRED]" :validate-trigger="['change']">
              <div>
                <a-input v-model="form.account" placeholder="" allow-clear/>
              </div>
            </a-form-item>
            <a-form-item field="cc" label="cc合作编码" :rules="[REQUIRED]" :validate-trigger="['change']">
              <div>
                <a-input v-model="form.cc" placeholder="" allow-clear/>
              </div>
            </a-form-item>
            <a-form-item field="sk" label="sk密钥" :rules="[REQUIRED]" :validate-trigger="['change']">
              <div>
                <a-input v-model="form.sk" placeholder="" allow-clear/>
              </div>
            </a-form-item>
            <a-form-item>
              <a-button type="primary" @click="handleSubmit">保存</a-button>
            </a-form-item>
          </a-col>
        </a-row>
  
      </a-form>
    </div>
  </template>
  
  <script setup lang="ts">
    import { getSetting, setSetting } from '@/api/operation';
    import { onMounted, ref } from 'vue';
    import useCurrentInstance from '@/hooks/useCurrentInstance';
    import { Message } from '@arco-design/web-vue';
    import { FormInstance } from '@arco-design/web-vue/es/form';
    import { VARCHAR255, REQUIRED, VARCHAR20 } from '@/utils/validator';
  
    const formRef = ref<FormInstance>();
    // 获取modal
    const modal = useCurrentInstance().globalProperties?.$modal;
  
    interface formInterface {
        domain: string;
        account: string;
        cc: string;
        sk: string;
    }
  
    // 数据集
    const form = ref<formInterface>({
        domain: '',
        account: '',
        cc: '',
        sk: '',
    });
  
    async function init() {
      const res = await getSetting('ZHONGZHI_SETTING');
      form.value = res.data.result;
    }
  
    const handleSubmit = async () => {
      const auth = await formRef.value?.validate();
      if (!auth) {
        const result = await setSetting('ZHONGZHI_SETTING', form.value);
        if (result.data.success) {
          Message.success('设置成功!');
          init();
        }
      }
    };
  
    onMounted(() => {
      init();
    });
  </script>
  