<script setup lang="ts">
import '@arco-design/web-vue/es/message/style/css.js'
import { onMounted, ref } from 'vue'
import storage from '@/utils/storage'
import { getAutoCoup, indexData } from '@/api/index'
import { seckillByDay } from '@/api/promotion'

const topSearchShow = ref(false) // 滚动后顶部搜索展示栏

// 楼层装修数据
const modelFormRef = ref()
// 活动优惠券modal
const couponModal = ref(false)
// 优惠券列表
const autoCoupList = ref<any>([])

// 是否调用自动发券
function handleAutoCoup() {
  const data = new Date()
  const datas = data.getDate()
  const hours = data.getHours()
  const flagCoup = storage.getGetTimes() // 缓存
  if (flagCoup && flagCoup != undefined && flagCoup != null) { // 判断当前是否有缓存
    if (Number(datas) > Number(flagCoup)) { // 判断缓存是否小于当前天数
      if (Number(hours) >= 6) { // 超过或等于6时清楚缓存
        storage.setGetTimes(datas) // 存储缓存
        getcps()
      }
    }
  }
  else {
    getcps()
  }
}
function getcps() {
  const data = new Date()
  const datas = data.getDate()
  getAutoCoup().then((res) => { // 调用自动发券
    if (res.data && res.data.success) {
      autoCoupList.value.push(...res.data.result)
      const objs = {} as any
      autoCoupList.value = autoCoupList.value.reduce((cur: any, next: any) => {
        // 对象去重
        if (next.id != undefined)
          objs[next.id] ? '' : (objs[next.id] = true && cur.push(next))
        return cur
      }, [])
      if (autoCoupList.value != '' && autoCoupList.value.length > 0) {
        // couponModal.value = true;
      }
      storage.setGetTimes(datas) // 存储缓存
    }
  })
}

onMounted(() => {
  window.onscroll = function () {
    const top = document.documentElement.scrollTop || document.body.scrollTop
    if (top > 300)
      topSearchShow.value = true
    else
      topSearchShow.value = false
  }
  if (storage.getUserInfo())
    handleAutoCoup()
})
</script>

<template>
  <div class="home-bgcolor">
    <!-- 楼层装修部分 -->
    <ModelForm ref="modelFormRef" />
    <!-- <div style="min-height: 1000px">楼层装修部分</div> -->
    <!-- 底部栏 -->
    <TheFooter />
    <!-- 侧边栏 -->
    <fixedBar class="fixed-bar" :class="{ 'show-fixed': topSearchShow }" />

    <!-- 活动优惠券modal -->
    <a-modal v-model:visible="couponModal" width="350px">
      <template #title>
        活动优惠券
      </template>
      <div class="" />
      <template #footer>
        <a-button status="danger" type="primary" @click="couponModal = false">
          确定
        </a-button>
      </template>
    </a-modal>
  </div>
</template>

<route lang="yaml">
  meta:
    layout: default
</route>

<style scoped lang="less">
/*侧边栏*/
.fixed-bar {
  opacity: 0 !important;
  transform: translateY(-10px);
  transition: 0.35s;
  z-index: 999999;
  height: 0px !important;
  overflow: hidden;
  position: fixed;
  right: 150px;
  top: 300px;
}

.show-fixed {
  height: 354px !important;
  opacity: 1 !important;
  transform: translateY(0);
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0);
}
</style>
