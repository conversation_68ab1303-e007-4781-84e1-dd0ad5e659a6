<template>
  <div py-30px border-b-1>
    <a-space w-100vw>
      <div w-90px>添加热区</div>
      <div>
        <div v-auto-animate>
          <div v-if="props.res.data.list[0].zoneInfo.length">
            <div v-for="(item, index) in props.res.data.list[0].zoneInfo">
              <div py-8px v-if="item.___type">
                <span>{{ item.___type }}</span>
                <span>-{{ item.___key }}</span>
              </div>
            </div>

          </div>
          <a-button mt-10px @click="handleClickArea()" :hoverable="false">
            添加/修改热区
          </a-button>
          <!-- (isRes && props!.res!.data.list![0]!.url.___type) || (!isRes && props.item?.url.___type) -->
        </div>

      </div>
    </a-space>
    <a-modal :visible="show" @ok="handleOk" @cancel="show = false" :width="800" unmountOnClose>
      <template #title>
        绘制热区
      </template>
      <div h-400px>
        <div flex gap-10px h-400px>
          <div class="box box-left" rounded>
            <div relative>
              <img max-w-full max-h-500px class="hz-u-img" :src="props.res.data.list[0].img" />
              <ul v-if="!reload" class="hz-m-area" v-add-item>
                <zone class="hz-m-item" @reloadOn="reload = true" @changeInfo="changeInfo"
                  :res="props.res.data.list[0].zoneInfo" :ref="`zone${index}`" :setting="item" :index="index" :key="index"
                  v-for="(item, index) in props.res.data.list[0].zoneInfo">
                </zone>
              </ul>
            </div>
          </div>
          <div class="box box-right" relative rounded>
            <div h-360px overflow-y-auto>
              <div p-8px :key="index" v-for="(item, index) in props.res.data.list[0].zoneInfo">
                <div flex flex-a-c flex-j-sb>
                  <div>
                    <span>{{ index + 1 }}</span>

                    <span cursor-pointer ml-10px @click="handleClickLink(index)">
                      <span v-if="!item.___type">请选择链接</span>
                      <span v-else>
                        <span>{{ item.___type }}</span>
                        <span>-{{ item.___key }}</span>
                      </span>
                    </span>
                  </div>
                  <div>
                    <a-tooltip content="修改">
                      <icon-edit @click="handleClickLink(index)" cursor-pointer />
                    </a-tooltip>
                    <a-tooltip content="删除">
                      <icon-delete @click="delArea(index)" ml-16px cursor-pointer />
                    </a-tooltip>
                  </div>
                </div>
              </div>
            </div>
            <div absolute bottom-0 w-full>
              <a-button type="outline" long @click="addHotArea()">添加热区</a-button>
            </div>
          </div>
        </div>
      </div>
    </a-modal>
    <choose ref="decide" :res="props.res" @callback="receive" />
  </div>
</template>



<script setup >
import { ref, provide } from 'vue';
import choose from '@/views/operation/decoration/components/choose.vue';
import zone from '@/views/operation/decoration/app/layout/modules/zone.vue'
import _ from './zone/index.js'
import { getCurrentInstance } from 'vue';
import { tabList } from '@/views/operation/decoration/components/receive'
const reload = ref(false)

provide('area-reload', reload)

const vAddItem = {
  mounted: function (el, binding, vnode) {
    const MIN_LIMIT = _.MIN_LIMIT
    el.addEventListener('mousedown', handleMouseDown, { passive: false })

    function handleMouseDown(e) {
      // console.log('additem', e)
      e && e.preventDefault()

      let itemInfo = {
        top: _.getDistanceY(e, el),
        left: _.getDistanceX(e, el),
        width: 0,
        height: 0
      }
      let container = _.getOffset(el)

      // Only used once at the beginning of init
      let setting = {
        topPer: _.decimalPoint(itemInfo.top / container.height),
        leftPer: _.decimalPoint(itemInfo.left / container.width),
        widthPer: 0,
        heightPer: 0
      }
      let preX = _.getPageX(e)
      let preY = _.getPageY(e)


      addItem(setting)// 这里去添加并发送了add通知，不应该发送通知

      window.addEventListener('mousemove', handleChange, { passive: false })
      window.addEventListener('mouseup', handleMouseUp, { passive: false })

      function handleChange(e) {
        e && e.preventDefault()

        let moveX = _.getPageX(e) - preX
        let moveY = _.getPageY(e) - preY
        preX = _.getPageX(e)
        preY = _.getPageY(e)

        // Not consider the direction of movement first, consider only the lower right drag point
        let minLimit = 0
        // 添加热区时，判定鼠标释放时，满足（热区大于48*48时）条件时生效
        let styleInfo = _.dealBR(itemInfo, moveX, moveY, minLimit)

        // Boundary value processing 改变热区大小时边界条件的处理
        itemInfo = _.dealEdgeValue(itemInfo, styleInfo, container, props.res.data.list[0].zoneInfo)

        Object.assign(el.lastElementChild.style, {
          top: `${itemInfo.top}px`,
          left: `${itemInfo.left}px`,
          width: `${itemInfo.width}px`,
          height: `${itemInfo.height}px`
        })
      }

      function handleMouseUp() {
        console.log('handleMouseUp')
        let perInfo = {
          topPer: _.decimalPoint(itemInfo.top / container.height),
          leftPer: _.decimalPoint(itemInfo.left / container.width),
          widthPer: _.decimalPoint(itemInfo.width / container.width),
          heightPer: _.decimalPoint(itemInfo.height / container.height),
        }

        if (isOverRange()) {
          overRange() // 判断超出个数限制，给overRange钩子抛回调
        } else if (container.height < MIN_LIMIT && itemInfo.width > MIN_LIMIT) {
          changeItem(Object.assign(perInfo, {
            topPer: 0,
            heightPer: 1
          }), true)
        } else if (container.width < MIN_LIMIT && itemInfo.height > MIN_LIMIT) {
          changeItem(Object.assign(perInfo, {
            leftper: 0,
            widthPer: 1
          }), true)
        } else if (itemInfo.width > MIN_LIMIT && itemInfo.height > MIN_LIMIT) {
          changeItem(perInfo, true)
        } else {
          // 当添加区域超出范围或小于最小区域（48*48）时触发，删除当亲绘制的热区并发送erase事件通知
          eraseItem()
        }

        window.removeEventListener('mousemove', handleChange)
        window.removeEventListener('mouseup', handleMouseUp)
      }
    }

    el.$destroy = () => el.removeEventListener('mousedown', handleMouseDown)
  },
  unmounted: function (el) {
    el.$destroy()
  }
}
const chooseIndex = ref('')
const decide = ref(null)
const emit = defineEmits(['overRange'])
const show = ref(false)
const props = defineProps({
  res: {
    type: null,
    default: ''
  },
  max: {
    type: null,
    default: ""
  }
})

function handleClickArea() {
  show.value = true
}

// 删除热区
function delArea(index) {
  props.res.data.list[0].zoneInfo.splice(index, 1)
}

// 点击确认
function handleOk() {
  show.value = false
}

function changeInfo(res) {
  let { info, index } = res;
  // 改变热区并发送change通知
  Object.assign(props.res.data.list[0].zoneInfo[index], info);
  const instance = getCurrentInstance();
  instance?.proxy?.$forceUpdate();
  setTimeout(() => {
    reload.value = false
  }, 0)

}

function convertNumberToDecimal(num) {
  if (num >= 10000) {
    return num / 100000;
  } else if (num >= 1000) {
    return num / 10000;
  } else if (num >= 100) {
    return num / 1000;
  } else if (num >= 10) {
    return num / 100;
  }
  return 0
}

// 新增热区
async function addHotArea() {
  let perInfo = {
    topPer: 0.15,
    leftPer: 0.3,
    widthPer: 0.2,
    heightPer: 0.2,
    // ___key:"",
    // ___type:"",
    // ___value:""
  };
  let images = await getImageSize(props.res.data.list[0].img);
  if (images) {
    if (images.height >= 1000) {
      perInfo.heightPer = convertNumberToDecimal(images.height) / (images.height / 1000);
    } else {
      perInfo.heightPer = convertNumberToDecimal(images.height);
    }
    perInfo.widthPer = convertNumberToDecimal(images.width) / 2;
  }
  props.res.data.list[0].zoneInfo.push(perInfo)

}
// 点击选择链接
function handleClickLink(index) {
  chooseIndex.value = index
  decide.value?.open()
}


// 获取图片大小
function getImageSize(url) {
  return new Promise(function (resolve, reject) {
    let image = new Image();
    image.onload = function () {
      resolve({
        width: image.width,
        height: image.height,
      });
    };
    image.onerror = function () {
      reject(new Error("error"));
    };
    image.src = url;
  });
}
function eraseItem(index = props.res.data.list[0].zoneInfo.length - 1) {
  props.res.data.list[0].zoneInfo.splice(index, 1);

}

function editZone(index) {
  [`zone${index}`][0].showModalFn(index);
}
function delZone(index) {
  [`zone${index}`][0].delItem(index);
}

function addItem(setting) {
  props.res.data.list[0].zoneInfo.push(setting);
}

function isOverRange() {
  return props.max && props.res.data.list[0].zoneInfo.length > props.max;
}
function overRange() {
  const index = props.res.data.list[0].zoneInfo.value.length - 1;
  props.res.data.list[0].zoneInfo.splice(index, 1);
  emit("overRange", index);
}

function changeItem(info, isAdd) {
  console.log('changeItem')
  const index = props.res.data.list[0].zoneInfo.length - 1;
  // 改变热区并发送change通知
  Object.assign(props.res.data.list[0].zoneInfo[index], info);

  // hasChange("change");
  // isAdd && emit("add", props.res.data.list[0].zoneInfo[index]);
}


// 过滤返回___key值
function filterKey() {
  // console.log(isRes)
  // // 判断当前组件传值返回
  return props.res.data.list[0].zoneInfo[chooseIndex.value].___key
}
// 过滤返回___value值
function filterValue() {
  // 判断当前组件传值返回
  return props.res.data.list[0].zoneInfo[chooseIndex.value].___value
}
/**
 * 过滤一下一些没有用的信息
 * 
 */
function filterPath(type, url) {
  console.log(type, "内部");
  switch (type) {
    case 'goods':
      return {
        id: url.id,
        goodsId: url.goodsId,
        ___type: type,
        ___key: filterKey(),
        ___value: filterValue(),
        
      }
    case 'category':
      return {
        id: url.id,
        ___type: type,
        ___key: filterKey(),
        ___value: filterValue(),
        
      }
    case 'shops':
      return {
        id: url.id,
        ___type: type,
        ___key: filterKey(),
        ___value: filterValue(),
        
      }
    case 'marketing':
      return {
        id: url.skuId,
        goodsId: url.goodsId,
        ___type: type,
        ___key: '',
        ___value: filterValue(),
        
      }
    case 'pages':
      return {
        id: url.skuId,
        goodsId: url.goodsId,
        ___type: type,
        ___key: filterKey(),
        ___value: filterValue(),
        
      }
    case 'special':
      return {
        id: url.id,
        ___type: type,
        ___key: filterKey(),
        ___value: filterValue()
      }
    case 'other':
      return {
        title: url.label,
        ___type: type,
        ___key: filterKey(),
        ___value: filterValue(),
        
      }
    case 'url':
      return {
        title: url.value,
        ___type: type,
        ___key: filterKey(),
        ___value: filterValue()
      }
  }
}


// 选择链接返回
function receive(val) {
  console.log(val, "选择链接返回");
  const type = val.___type
  const originZoneInfo = JSON.parse(JSON.stringify(props.res.data.list[0].zoneInfo[chooseIndex.value]))
  const filterData = { ...originZoneInfo,...filterPath(type,val)  }
  console.log(filterPath(type,val), "filterPath(type,val)");
  console.log(originZoneInfo, "originZoneInfo");
  console.log({...filterData}, "filterData");
  props.res.data.list[0].zoneInfo[chooseIndex.value] = filterData
  props.res.data.list[0].zoneInfo[chooseIndex.value].___value = type
  props.res.data.list[0].zoneInfo[chooseIndex.value].___type = tabList.find((item) => {
    props.res.data.list[0].zoneInfo[chooseIndex.value].___key = val[item._bind];
    return item.value === type
  })?.label
}
</script>

<style scoped lang="less">
@import './zone/zone.less';

.box {
  position: relative;
  flex: 1;
}

.box-left {
  height: 100%;
  background: #f7f7f7;
  overflow-y: auto;
}
</style>
