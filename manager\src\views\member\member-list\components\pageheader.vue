<template>
  <div>
    <a-card style="height: 280px; padding: 12px 12px 0px">
      <div class="head-title">基本信息</div>
      <div class="detail-body">
        <a-row>
          <a-col :span="6">
            <div class="">
              <div class="info">
                <div class="head-info">
                  <!-- <a-avatar :src="memberInfo.face" /> -->
                  <img :src="memberInfo.face" alt="" class="face">
                  <div>
                    <div
                      v-if="memberInfo.username && memberInfo.username.length > 15"
                      class="name"
                    >
                      {{ memberInfo.username.slice(0, 15) }}...
                    </div>
                    <div v-else class="name">
                      {{ memberInfo.username }}
                    </div>
                    <div class="phone">
                      {{ memberInfo.mobile }}
                    </div>
                  </div>
                </div>
                <div class="bottom-info">
                  <p>上次登录 {{ memberInfo.lastLoginDate }}&nbsp;</p>
                  <p>
                    <a-space size="large">
                      <a-switch
                        v-model="memberInfo.disabled"
                        checked-color="#F53F3F"
                        @change="memberStatusChange"
                      >
                        <template #checked> 启用 </template>
                        <template #unchecked> 禁用 </template>
                      </a-switch>
                    </a-space>
                  </p>
                </div>
              </div>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="">
              <p class="item">
                <span class="label">昵称：</span>
                <span class="info">{{ memberInfo.nickName }}</span>
              </p>
              <p class="item">
                <span class="label">会员名称：</span>
                <span class="info">{{ memberInfo.username }}</span>
              </p>
              <p class="item">
                <span class="label">性别：</span>
                <span v-if="memberInfo.sex === 1" class="info">男</span>
                <span v-else class="info">女</span>
              </p>
              <p class="item">
                <span class="label">生日：</span>
                <span
                  v-if="
                    memberInfo.birthday == null ||
                    memberInfo.birthday == 'undefined'
                  "
                  >暂未完善</span
                >
                <span v-else class="info">{{ memberInfo.birthday }}</span>
              </p>
              <p class="item">
                <span class="label">地区：</span>
                <span
                  v-if="
                    memberInfo.region == null ||
                    memberInfo.region == '' ||
                    memberInfo.region === 'undefined'
                  "
                  class="info"
                  >暂未完善</span
                >
                <span v-else class="info" style="white-space:nowrap">{{ memberInfo.region }}</span>
              </p>
              <p class="item">
                <span class="label">注册时间：</span>
                <span class="info">{{ memberInfo.createTime }}</span>
              </p>
            </div>
          </a-col>
        </a-row>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { getMemberInfoData, updateMemberStatus } from '@/api/member';
  import { useRoute } from 'vue-router';
  import { memberDetailRules } from '../index';

  const route = useRoute();

  // 会员信息
  const memberInfo = ref<memberDetailRules>({
    username: '',
    mobile: '',
    lastLoginDate: '',
    disabled: false,
    nickName: '',
    sex: 0,
    birthday: null,
    region: null,
    createTime: '',
  });
  // 初始化
  onMounted(() => {
    getMemberInfoData(route.query.id).then((res: any) => {
      memberInfo.value = res.data.result;
    });
  });
  // 会员状态改变事件
  const memberStatusChange = (v: any) => {
    const params = {
      memberIds: route.query.id,
      disabled: v,
    };
    updateMemberStatus(params).then((res: any) => {
      console.log(res);
    });
  };
</script>

<style lang="less" scoped>
  @import '../memberDetail.less';
  .face {
    width: 50px;
    height: 50px;
    border-radius: 50%;
  }
</style>
