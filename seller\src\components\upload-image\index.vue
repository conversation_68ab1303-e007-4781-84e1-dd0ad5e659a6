<template>
  <div style="text-align: left">
    <a-button @click="handleClickUploadImage">点击上传</a-button>
    <a-modal v-model:visible="show" title="上传图片" @ok="handleOk">
      <div class="import-oss" @click="importOSS">从资源库中导入</div>
      <div>
        <a-upload
          list-type="picture-card"
          :action="uploadFile"
          :headers="{ accessToken: accessToken }"
          :on-success="handleSuccessGoodsPicture"
          :on-error="handleError"
          :file-list="images"
          image-preview
          @before-upload="handleBeforeUploadGoodsPicture"
          @before-remove="beforeRemove"
        />
      </div>
    </a-modal>
    <a-modal
      v-model:visible="showOssManager"
      :width="920"
      title="oss资源管理"
      @ok="handleOss"
      @cancel="handleCancel"
    >
      <ossManage :close-model="handleOss" @selected="changOssImage"></ossManage>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import ossManage from '@/components/oss-manage/index.vue';
  import { Message } from '@arco-design/web-vue';
  import uploadFile from '@/api/common';
  import store from '@/utils/storage';

  // 携带toekn
  const accessToken = ref<any>(store.getAccessToken());
  const emit = defineEmits<{
    (e: 'change', val: any, type: string): void;
  }>();
  const show = ref(false); // 是否显示弹框
  const showOssManager = ref<boolean>(false); // oss弹框
  const fileFormat = ref<Array<string>>(['jpg', 'jpeg', 'png', 'gif', 'bmp']);
  const images = ref<Array<any>>([]);

  // 打开模态框
  const handleClickUploadImage = () => {
    show.value = true;
  };
  // 回调给父级
  const handleOk = () => {
    // 先给数据做一下处理 然后将数据传给父级
    const formatImages = images.value.map((item) => {
      return item.url;
    });
    emit('change', formatImages, '');
  };
  // 从资源库导入图片
  const importOSS = () => {
    showOssManager.value = true;
  };
  // 移除图片
  const beforeRemove: any = (file: any) => {
    images.value = images.value.filter((i: any) => i.url !== file.url);
  };
  // oss弹框确定
  const handleOss = () => {
    showOssManager.value = false;
  };
  const handleCancel = () => {
    showOssManager.value = false;
  };
  const changOssImage = (val: any) => {
    images.value = [];
    val.forEach((item: any) => {
      images.value.push({ url: item.url });
    });
  };
  // 图片上传失败
  const handleError = () => {
    Message.error('上传失败');
  };
  // 图片上传会成功回调
  const handleSuccessGoodsPicture = (res: any) => {
    if (res.response.success) {
      Message.success('上传成功');
      images.value.push({ url: res.response.result });
    }
  };
  // 上传前校验
  const handleBeforeUploadGoodsPicture: any = (file: any) => {
    return new Promise((resolve, reject) => {
      if (
        !fileFormat.value.includes(
          file.name.split('.')[file.name.split('.').length - 1]
        )
      ) {
        reject(new Error('上传失败'));
        Message.error(` 请选择 .jpg .jpeg .png .gif .bmp格式文件`);
      } else if (Number((file.size / 1024).toFixed(0))> 10240) {
        reject(new Error('上传失败'));
        Message.error(`所选文件大小过大, 不得超过10M`);
      } else {
        resolve(true);
      }
    });
  };
</script>

<style scoped lang="less">
  .import-oss {
    margin-bottom: 10px;
    text-align: right;
    color: red;
    cursor: pointer;
  }
</style>
