<template>
  <div p-10px>
    <a-alert> <template #title>
        店铺装修
      </template>
      将此模块内容拖入右侧框中
    </a-alert>
    <div my-10px v-for="(block, blockIndex) in list" :key="blockIndex">
      <div text-14px my-20px font-400>
        {{ block.label }}
      </div>
      <div flex gap-7px flex-wrap>
        <drag bg-gray-100 w-70px h-70px cursor-move rounded border-1 text-12px flex flex-a-c justify-center
          v-for="(item, index) in useList[block.value]" :key="index" :result="item" :index="index"></drag>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import drag from './drag.vue'

import goods from '../models/goods'
import base from '../models/base'

import { DragRule } from '../models/types'


import { ref, onMounted, } from 'vue'
const useList: any = {
  ...{
    base,
    goods,
  }
}
onMounted(() => {
  drags.value = goods
})
const drags: any | DragRule = ref([])
const template = ref('base')

const list = ref([
  {
    label: '基础组件',
    value: "base"
  },

  {
    label: '商品组件',
    value: "goods"
  },

])


</script>

<style scoped>
</style>
