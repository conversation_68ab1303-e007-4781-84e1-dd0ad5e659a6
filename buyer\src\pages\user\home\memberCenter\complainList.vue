<template>
  <div>
    <Card _Title="我的投诉" :_Size="16"></Card>

    <div class="order-title border-bg-color">
      <a-row class="pl_20 pr_20">
        <a-col :span="12">商品信息</a-col>
        <a-col :span="3">投诉状态</a-col>
        <a-col :span="3">投诉主题</a-col>
        <a-col :span="6"></a-col>
      </a-row>
    </div>

    <div class="order-item mt_10 mb_10" v-for="(item, index) in complaintList" :key="index">
      <div>
        <div class="header pl_20 pr_20">
          <span>投诉单号：{{item.id}}</span>
          <span class="create-time ml_20">{{item.createTime}}</span>
          <span class="eval-detail hover-pointer hover-color fontsize-12" @click="cancelComplain(item.id)" style="margin-right: 70px;"
                v-if="item.complainStatus === 'APPLYING' || item.complainStatus === 'NEW'">取消投诉</span>
          <span class="eval-detail hover-pointer hover-color fontsize-12" @click="complainDetail(item.id)">投诉详情</span>
        </div>
        <a-row class="body pl_20 pr_20">
          <a-col :span="12" class="body-goods mt_10 mb_10">
            <div class="hover-pointer" @click="goGoodsDetail(item.skuId, item.goodsId)"><img :src="item.goodsImage" alt="" width="60" height="60" /></div>
            <div class="hover-pointer hover-color" @click="goGoodsDetail(item.skuId, item.goodsId)">{{item.goodsName}}</div>
          </a-col>
          <a-col :span="3" class="text-center">{{statusLabel[item.complainStatus]}}</a-col>
          <a-col :span="3" class="text-center light-text-color">{{item.complainTopic}}</a-col>
          <a-col :span="6" class="text-center">
            <a-tooltip :content="item.content" position="bottom">
              <div class="ellipsis ellipsis-1 hover-pointer">{{ item.content }}</div>
            </a-tooltip>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- 分页 -->
    <div class="paginationBox">
      <a-pagination :total="total" :current="apiParams.pageNumber" :page-size="apiParams.pageSize" show-page-size
                    @change="(number) => {apiParams.pageNumber = number;}" @page-size-change="(number) => {apiParams.pageSize = number; apiParams.pageNumber = 1;}" >
      </a-pagination>
    </div>



  </div>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router';
  import { ref, onMounted, watch } from 'vue';
  import {complainList, clearComplain} from '@/api/member';
  import { Message, Modal } from '@arco-design/web-vue';

  const router = useRouter();
  // 投诉状态
  const statusLabel = ref<any>({
    NO_APPLY: '未申请',
    APPLYING: '申请中',
    COMPLETE: '已完成',
    EXPIRED: '已失效',
    CANCEL: '已取消',
    NEW: '新订单'
  });
  // 请求参数
  const apiParams = ref({
    pageNumber: 1,
    pageSize: 10,
  });
  const complaintList = ref<any>([]);
  const total = ref(0);

  // 获取投诉列表
  const getList = () => {
    complainList(apiParams.value).then(res => {
      if (res.data.success) {
        const list = res.data.result.records;
        complaintList.value = list;
        total.value = res.data.result.total;
      }
    })
  };
  // 取消投诉
  const cancelComplain = (id: any) => {
    Modal.confirm({
      title: '取消投诉',
      content: `确定取消投诉吗？`,
      okButtonProps: {
        type: "primary",
        status: "danger"
      },
      onOk: () => {
        clearComplain(id).then(res => {
          if (res.data.success) {
            Message.success('取消投诉成功！');
            getList();
          }
        })
      }
    });
  };
  // 跳转投诉详情
  const complainDetail = (id: any) => {
    router.push({path: '/user/home/<USER>/complainDetail', query: { id }})
  };
  // 跳转商品详情
  const goGoodsDetail = (skuId: any, goodsId: any) => {
    let routeUrl = router.resolve({path: "/goodsDetail", query: { skuId, goodsId },});
    window.open(routeUrl.href, "_blank");
  };

  onMounted(() => {
    getList();
  });
  watch(() => [apiParams],
    (val) => {
      getList();
    }, { deep: true }
  );
</script>

<style scoped lang="less">
  .order-title {
    background-color: @light_background_color;
    height: 38px;
    line-height: 38px;
    text-align: center;
    color: @text_color;
  }
  .order-item {
    border: 1px solid @border_color;
    .header {
      height: 30px;
      line-height: 30px;
      background-color: @light_background_color;
      position: relative;
      .create-time {
        color: @light_text_color;
      }
      .eval-detail {
        position: absolute;
        right: 20px;
      }
    }
    .body {
      align-items: center;
      .body-goods {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        > div:nth-of-type(1) {
          width: 60px;
          height: 60px;
          border: 1px solid @border_color;
          margin-right: 10px;
        }
      }
      .ellipsis {
        color: @light_text_color;
      }
    }
  }
</style>
