import tpl_block from "./tpl_block.vue";
import tpl_one from "./tpl_one.vue";
import tpl_hot_area from "./tpl_hot_area.vue";
import tpl_search from "./tpl_search.vue";
import tpl_banner from "./tpl_banner.vue";
import tpl_goods_only from "./tpl_goods_only.vue";
import tpl_menu from "./tpl_menu.vue";
import tpl_top_nav from "./tpl_top_nav.vue";
import tpl_bottom_nav from "./tpl_bottom_nav.vue"; // 新增底部导航组件
import tpl_left_one_right_two from "./tpl_left_one_right_two.vue";
import tpl_left_two_right_one from "./tpl_left_two_right_one.vue";
import tpl_flex_two from "./tpl_flex_two.vue";
import tpl_flex_three from "./tpl_flex_three.vue";
import tpl_flex_four from "./tpl_flex_four.vue";
import tpl_flex_five from "./tpl_flex_five.vue";
import tpl_goods_category from "./tpl_goods_category.vue";
export default {
  'whiteSpace': tpl_block,
  'flexOne': tpl_one,
  'flexHotArea': tpl_hot_area,
  'search': tpl_search,
  'carousel': tpl_banner,
  'goodsOnly': tpl_goods_only,
  'menu': tpl_menu,
  'topNav': tpl_top_nav,
  'bottomNav': tpl_bottom_nav, // 添加底部导航组件
  'leftOneRightTwo': tpl_left_one_right_two,
  'leftTwoRightOne': tpl_left_two_right_one,
  'flexTwo': tpl_flex_two,
  'flexThree': tpl_flex_three,
  'flexFour': tpl_flex_four,
  'flexFive': tpl_flex_five,
  'goodsCategory': tpl_goods_category
}
