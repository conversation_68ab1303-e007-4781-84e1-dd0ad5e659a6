<template>
  <div py-16px border-b-1>
    <div w-80px>图标大小</div>
    <div mt-20px>
      <a-input-number 
        v-model="props.res.data.iconSize" 
        :min="16" 
        :max="64" 
        :step="2"
        placeholder="请输入图标大小"
        style="width: 200px"
      >
        <template #suffix>px</template>
      </a-input-number>
      <div mt-8px text-12px color-gray-500>
        建议范围：16-64px，默认24px
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DragRule } from '@/views/operation/decoration/app/models/types';

const props = defineProps<{
  res: DragRule
}>()

// 确保iconSize有默认值
if (!props.res.data.iconSize) {
  props.res.data.iconSize = 24;
}
</script>

<style scoped>
</style>
