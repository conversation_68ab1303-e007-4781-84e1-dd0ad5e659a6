<template>
  <a-card class="general-card" title="商品审核" :bordered="false">
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getAuthGoodsListData"
      :api-params="apiParams"
      @detail="JumpProductDetails"
      @refuse="handleRefuse"
      @pass="handlePass"
      :bordered="true"
    />
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { getAuthGoodsListData, authGoods } from '@/api/goods';
  import { authFlag } from '@/utils/tools';
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';
  import useCurrentInstance from '@/hooks/useCurrentInstance';
  import { Message } from '@arco-design/web-vue';

  const tablePageRef = ref<any>();
  const router = useRouter();
  //  获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;
  // 跳转商品详情页
  const JumpProductDetails = (row: any) => {
    router.push({
      name: 'goodsdetail',
      query: { id: row.record.id },
    });
  };
  const columnsSearch: Array<SearchRule> = [
    {
      label: '商品名称',
      model: 'goodsName',
      disabled: false,
      input: true,
    },
    {
      label: '商品编号',
      model: 'id',
      disabled: false,
      input: true,
    },
  ];

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '编号',
      dataIndex: 'id',
    },
    {
      title: '商品',
      dataIndex: 'goodsName',
      slot: true,
      slotData: {
        goods: {
          goodsName: 'goodsName',
          goodsImage: 'original',
        },
      },
    },
    {
      title: '商品价格',
      dataIndex: 'price',
      currency: true,
    },
    {
      title: '审核状态',
      dataIndex: 'authFlag',
      slot: true,
      slotData: {
        badge: authFlag,
      },
    },
    {
      title: '店铺名称',
      dataIndex: 'storeName',
    },
  ];

  const sortMethods: MethodsRule = {
    title: '操作',
    width: 300,
    methods: [
      {
        title: '通过',
        callback: 'pass',
        type:"text" ,
        status:"success"
      },
      {
        title: '拒绝',
        callback: 'refuse',
        type:"text" ,
        status:"danger"
      },
      {
        title: '查看',
        callback: 'detail',
        type:"text" ,
      },
    ],
  };

  const apiParams = ref({
    saveType: 'TEMPLATE',
  });

  // 通过审核
  function handlePass(data: any) {
    const params = {
      memberIds: data.record.id,
      authFlag: 'PASS',
    };
    modal.confirm({
      title: '确认审核',
      content: '您确认要通过审核?',
      alignCenter: false,
      onOk: async () => {
        const res = await authGoods(data.record.id, params);
        if (res.data.success) {
          Message.success('审核成功');
          tablePageRef.value.init();
        }
      },
    });
  }
  // 拒绝审核
  function handleRefuse(data: any) {
    const params = {
      memberIds: data.record.id,
      authFlag: 'REFUSE',
    };
    modal.confirm({
      title: '确认审核',
      content: '您确认要拒绝？',
      alignCenter: false,
      onOk: async () => {
        const res = await authGoods(data.record.id, params);
        if (res.data.success) {
          Message.success('审核成功');
          tablePageRef.value.init();
        }
      },
    });
  }
</script>

<style scoped></style>
