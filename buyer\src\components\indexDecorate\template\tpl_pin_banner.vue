<script setup lang="ts">
import { useRouter } from 'vue-router'
import { navigateTo } from './navigate.ts'

const props = defineProps<{
  res: any
}>()
const router = useRouter()
function handleClickCarousel(params: type) {
  const path = navigateTo(props.res.data.list[0])

  window.open(router.resolve(path).href, '_blank')
}
</script>

<template>
  <div>
    <a-carousel :style="{
      width: '637px',
      height: '334px',
      borderRadius: '10px',
      overflow: 'hidden',
    }">
      <a-carousel-item v-for="(image, index) in props.res.data.list" v-if="props.res" :key="index"
        @click="handleClickCarousel">
        <img :src="image.img" :style="{
          width: '100%',
        }">
      </a-carousel-item>
    </a-carousel>
  </div>
</template>

<style scoped></style>
