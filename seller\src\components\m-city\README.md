### 省市区级联选择器

属性| 说明         | 类型       |必填
---|------------|----------|---
`level`| 几级选择默认4级   | Number   |否
`width`| 级联选择器width | Number   |否
`ids`| 关闭地图       | Array    |否
`callback`| 回调         | function |回调方法


``` vue
  import city from '@/components/m-city/index.vue';
  
  <city :level="4" @callback="val" />
  
  const val = (res) =>{
    /**
    * ids:[]  选择的id
    * cities:[] //选择的城市
    * values:[] // 城市+id
    */
    
  }
```
