<template>
  <div style="background-color: #fff">
    <a-form ref="formRef" style="padding: 30px" :model="form" layout="horizontal" auto-label-width>
      <a-divider orientation="left">短信平台</a-divider>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item field="type" label="平台" :validate-trigger="['change']">
            <a-radio-group type="button" v-model="form.type">
              <a-radio value="KDNIAO">快递鸟</a-radio>
              <a-radio value="KUAIDI100">快递100</a-radio>
              <a-radio value="SHUNFENG">顺丰</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>


      <template v-if="form.type === 'KDNIAO'">
        <a-divider orientation="left">快递鸟</a-divider>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item field="kdniaoEbusinessID" label="快递鸟 商户ID" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.kdniaoEbusinessID" allow-clear/>
            </a-form-item>
            <a-form-item field="kdniaoAppKey" label="快递鸟 AppKey" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.kdniaoAppKey" allow-clear/>
            </a-form-item>
            <a-form-item field="requestType" label="快递鸟 requestType" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.requestType" allow-clear/>
            </a-form-item>
          </a-col>
        </a-row>
      </template>

      <template v-if="form.type === 'KUAIDI100'">
        <a-divider orientation="left">快递100</a-divider>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item field="kuaidi100Customer" label="快递100 授权码" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.kuaidi100Customer" allow-clear/>
            </a-form-item>
            <a-form-item field="kuaidi100Key" label="快递100 Key" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.kuaidi100Key" allow-clear/>
            </a-form-item>
          </a-col>
        </a-row>
      </template>
      <template v-if="form.type === 'SHUNFENG'">
        <a-divider orientation="left">顺丰</a-divider>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item field="clientCode" label="顾客编码" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.clientCode" allow-clear/>
            </a-form-item>
            <a-form-item field="checkWord" label="校验码" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.checkWord" allow-clear/>
            </a-form-item>
            <a-form-item field="callUrl" label="请求地址" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.callUrl" allow-clear/>
            </a-form-item>
            <a-form-item field="templateCode" label="打印模板" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.templateCode" allow-clear/>
            </a-form-item>
            <a-form-item field="monthlyCardNo" label="月结号" :rules="[REQUIRED]" :validate-trigger="['change']">
              <a-input v-model="form.monthlyCardNo" allow-clear/>
            </a-form-item>
          </a-col>
        </a-row>
      </template>

      <a-form-item>
        <a-button type="primary"  @click="handleSubmit">保存</a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import {getSetting, setSetting} from '@/api/operation';
import {onMounted, ref} from 'vue';
import useCurrentInstance from '@/hooks/useCurrentInstance';
import {Message} from '@arco-design/web-vue';
import {FormInstance} from '@arco-design/web-vue/es/form';
import { VARCHAR255, REQUIRED, VARCHAR20 } from '@/utils/validator';

const formRef = ref<FormInstance>();
// 获取modal
const modal = useCurrentInstance().globalProperties?.$modal;

interface formInterface {
  type: string;
  [key: string]: any;
}

// 数据集
const form = ref<formInterface>({
  type: "",
  kdniaoEbusinessID: "",
  kdniaoAppKey: "",
  requestType:"",
  kuaidi100Customer: "",
  kuaidi100Key: "",
});

async function init() {
  const res = await getSetting('LOGISTICS_SETTING');
  form.value = res.data.result;
}

const handleSubmit = async () => {
  const auth = await formRef.value?.validate();
  if (!auth) {
    const result = await setSetting('LOGISTICS_SETTING', form.value);
    if (result.data.success) {
      Message.success('设置成功!');
      init();
    }
  }
};

onMounted(() => {
  init();
});
</script>
