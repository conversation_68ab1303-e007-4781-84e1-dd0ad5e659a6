{"compilerOptions": {"target": "es2016", "jsx": "preserve", "lib": ["DOM", "ESNext"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "bundler", "paths": {"~/*": ["src/*"], "@/*": ["src/*"]}, "resolveJsonModule": true, "types": ["vite/client", "unplugin-vue-macros/macros-global", "unplugin-vue-router/client"], "allowJs": true, "strict": true, "strictNullChecks": true, "noUnusedLocals": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}, "exclude": ["dist", "node_modules", "eslint.config.js"]}