<template>

    <a-card class="general-card" title="隐私协议" :bordered="false">
    <a-alert type="warning" style="margin: 10px 0;">隐私协议在移动端中 设置->关于我们->对应的文章展示</a-alert>
      <a-table ref="tablePageRef" :columns="columnsTable" :data="tableData" :methods="sortMethods" >
        <template #optional="{ record }">
          <a-button @click="editClick(record)" type="text" status="warning">编辑</a-button>
        </template>
      </a-table>
      <!--<tablePage ref="tablePageRef" :columns="columnsTable" :methods="sortMethods" :api="getArticleCategory"></tablePage>-->
      <!--添加文章模态框-->
      <a-modal v-model:visible="articleFormData.modalVisible" :align-center="false" title-align="start" :width="1100" draggable @ok="handleConfirm" @cancel="handleClose">
        <template #title>编辑协议</template>
        <a-row>
          <a-col :span="16">
            <a-form ref="formRef" :model="articleFormData.articleForm" layout="horizontal" auto-label-width>
              <a-form-item field="title" label="文章标题" :rules="[REQUIRED]" :disabled="true">
                <a-input v-model="articleFormData.articleForm.title" :style="{ width: '380px' }"/>
              </a-form-item>
              <a-form-item filed="content" label="文章内容" :rules="[REQUIRED]">
                <Editor :initValue="articleFormData.articleForm.content" @getEditorContent="onEditorChange" :key="editorKey"></Editor>
              </a-form-item>
            </a-form>
          </a-col>
          <a-col :span="8">
            <div class="mobile-effect">
              <div class="title">页面预览</div>
              <div class="content">
                <div v-html="articleFormData.articleForm.content"></div>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-modal>
    </a-card>
  </template>
  
  <script setup lang="ts">
    import tablePage from '@/components/table-pages/index.vue';
    import { MethodsRule, ColumnsDataRule, SearchRule } from '@/types/global';
    import { ref, onMounted, reactive } from 'vue';
    import{ getArticleCategory, getPrivacy, updatePrivacy } from '@/api/operation';
    import { VARCHAR255, REQUIRED, VARCHAR20, MOBILE } from '@/utils/validator';
    import useCurrentInstance from '@/hooks/useCurrentInstance';
    import { Message } from '@arco-design/web-vue';
    import Editor from '@/components/editor/index.vue';

    const editorKey = ref(1);
    const tablePageRef = ref<any>();
    const formRef = ref<any>();
    const tableData = ref([
      { name: '店铺入驻协议', type: 'STORE_REGISTER' },
      { name: '用户协议', type: 'USER_AGREEMENT' },
      { name: '证照信息', type: 'LICENSE_INFORMATION' },
      { name: '关于我们', type: 'ABOUT' },
      { name: '隐私策略', type: 'PRIVACY_POLICY' }
    ]);
    const columnsTable = [
      { title: '协议名称', dataIndex: 'name' },
      { title: '协议类型', dataIndex: 'type' },
      { title: '操作', slotName: 'optional', },
    ];
    const sortMethods: MethodsRule = {
      title: '操作',
      width: 250,
      fixed: 'right',
      methods: [
        {title: '编辑', callback: 'edit', type:'text',status:'success'},
      ],
    };
    interface formInterface {
      modalVisible: boolean,
      articleForm: any,
      id: string | number,
      type: string | number
    }
    const articleFormData :formInterface = reactive({
      modalVisible: false,
      articleForm: {

      },
      id: '',
      type: ''
    });

    // 获取全部文章分类
    const getAllList = (parentId: number | string) => {
      getArticleCategory(parentId).then(res => {
        // console.log('获取全部文章分类', res)
      })
    };
    // 编辑文章modal
    const editClick = (data: any) => {
      formRef.value.resetFields();
      getPrivacy(data.type).then(res => {
        articleFormData.modalVisible = true;
        editorKey.value += 1;
        articleFormData.articleForm.categoryId = res.data.result.categoryId;
        // articleFormData.articleForm.treeValue = data.articleCategoryName;
        articleFormData.id = res.data.result.id;
        articleFormData.articleForm.content = res.data.result.content;
        articleFormData.articleForm.title = res.data.result.title;
        articleFormData.articleForm.sort = res.data.result.sort;
        articleFormData.articleForm.openStatus = res.data.result.openStatus;
        articleFormData.articleForm.type = res.data.result.type;
        articleFormData.type = res.data.result.type;
      })
    };
    // 富文本change事件
    const onEditorChange = (arr: any, html: any) => {
      articleFormData.articleForm.content = html;
    };
    // 添加文章确认
    const handleConfirm = () => {
      updatePrivacy(articleFormData.id, articleFormData.type, articleFormData.articleForm).then(res => {
        if (res.data.success) {
          Message.success('操作成功！');
          // tablePageRef.value.init();
        }
      })
    };
    // 添加文章取消
    const handleClose = (data: any) => {
      // console.log('添加文章取消');
    };

    const passContent = (value: string): void => {
      articleFormData.articleForm.content = value;
    }

    // 初始化
    onMounted(() => {
      // getAllList(0);
    })
  </script>

<style lang="less" scoped>
  .mobile-effect {
    box-sizing: border-box;
    margin: 0 20px;
    border: 2px solid #f1f2f3;
    height: 570px;
    .title {
      align-items: center;
      background: #f9f9fa;
      border-radius: 4px 4px 0 0;
      color: #85878a;
      display: flex;
      font-size: 12px;
      height: 32px;
      line-height: 20px;
      padding: 0 12px;
    }
    .content {
      width: 100%;
      height: 530px;
      padding: 0 14px;
      overflow-y: scroll;
    }
  }
</style>
