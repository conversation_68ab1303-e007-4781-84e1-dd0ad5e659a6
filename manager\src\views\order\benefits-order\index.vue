<template>
  <a-card class="general-card" title="虚拟订单" :bordered="false">
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <tablePage ref="tablePageRef" :columns="columnsTable" :methods="sortMethods" :api="getOrderList"
      :api-params="apiParams" @orderDetail="(val:any)=> $openWindow({
          name: 'order-detail',
          query: {
            id: val.record.sn
          }
        })" :bordered="true">
      <template #Collection="{ data }">
        <a-button :disabled="data.orderStatus == 'UNPAID' ? false : true" @click="confirmPrice(data)"> 收款
        </a-button></template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
import { getOrderList, orderPay, tradeDetail } from '@/api/order';
import searchTable from '@/components/search-column/index.vue';
import tablePage from '@/components/table-pages/index.vue';
import useCurrentInstance from '@/hooks/useCurrentInstance';
import { ColumnsDataRule, MethodsRule, SearchRule } from '@/types/global';
import {
orderClientStatus,
orderClientType,
orderStatusList,
} from '@/utils/tools';
import { Message } from '@arco-design/web-vue';
import { ref } from 'vue';


const modal = useCurrentInstance().globalProperties?.$modal; // 获取modal

const tablePageRef = ref<any>();
const columnsSearch: Array<SearchRule> = [
  {
    label: '订单号',
    model: 'orderSn',
    disabled: false,
    input: true,
  },
  {
    label: '会员名称',
    model: 'buyerName',
    disabled: false,
    input: true,
  },
  {
    label: '订单状态',
    model: 'orderStatus',
    disabled: false,
    select: {
      options: orderStatusList.filter((item) => item.value !== 'UNDELIVERED' && item.value !== 'DELIVERED' && item.value !== 'STAY_PICKED_UP'),
    },
  },
  {
    label: '下单时间',
    disabled: false,
    datePicker: {
      type: 'range',
    },
  },
];

const columnsTable: ColumnsDataRule[] = [
  {
    title: '订单号',
    dataIndex: 'sn',
  },
  {
    title: '订单来源',
    dataIndex: 'clientType',
    slot: true,
    slotData: {
      badge: orderClientType,
    },
  },
  {
    title: '买家名称',
    dataIndex: 'memberName',
  },
  {
    title: '订单金额',
    dataIndex: 'flowPrice',
    currency: true,
  },
  {
    title: '订单状态',
    dataIndex: 'orderStatus',
    slot: true,
    slotData: {
      badge: orderClientStatus,
    },
  },
  {
    title: '下单时间',
    dataIndex: 'createTime',
  },
];

const sortMethods: MethodsRule = {
  title: '操作',
  width: 300,
  fixed: 'right',
  methods: [
    {
      title: '收款',
      callback: 'detail',
      slot: true,
      slotTemplate: 'Collection',
      type:"text" ,
    },
    {
      title: '查看',
      callback: 'orderDetail',
      type:"text" ,
      status:"success"
    },
  ],
};

const apiParams = ref({
  orderType: 'EQUITY',
});
const tradeSn = ref<string>(""); // 交易编号

// 收款
const confirmPrice = (val: any) => {
  tradeDetail(val.sn).then((res)=>{
    tradeSn.value = res.data.result.trade.sn;
    modal.confirm({
      title: '确认收款',
      content: `您确定要收款吗？`,
      alignCenter: false,
      onOk: async () => {
        const res = await orderPay(tradeSn.value);
        if (res.data.code == 200) {
          Message.success('收款成功');
          tablePageRef.value.init()
        }
      },
    });
  });
}
</script>
