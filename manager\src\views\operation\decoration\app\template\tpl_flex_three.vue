<template>
  <div>
    <div flex flex-a-c>
      <div>
        <img h-121px w-121px :src="props.res.data.list[0].img" />
      </div>
      <div>
        <img h-121px w-121px :src="props.res.data.list[1].img" />
      </div>
      <div>
        <img h-121px w-121px :src="props.res.data.list[2].img" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  res: any
}>()
</script>

<style scoped>
img{
  display: block;
}
</style>
