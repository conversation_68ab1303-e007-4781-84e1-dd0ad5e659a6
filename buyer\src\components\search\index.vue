<template>
  <div :class="{'small-search-box':useClass === 'fixed-show'}" class="navbar">
    <div class="container" :class="{'small-container':useClass === 'fixed-show'}">
      <div>
        <img v-if="showLogo" :src="logoImg" alt="" class="logo-img" @click="router.push('/')"/>
      </div>
      <div :class="{'small-search-box':useClass === 'fixed-show'}" class="search-box">
        <a-input-search placeholder="输入你想查找的商品" v-model="searchData" search-button class="input-search" @search="search" @press-enter="search">
          <template #button-icon>
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 1024 1024">
              <path fill="currentColor" d="M1014.64 969.04L703.71 656.207c57.952-69.408 92.88-158.704 92.88-256.208c0-220.912-179.088-400-400-400s-400 179.088-400 400s179.088 400 400 400c100.368 0 192.048-37.056 262.288-98.144l310.496 312.448c12.496 12.497 32.769 12.497 45.265 0c12.48-12.496 12.48-32.752 0-45.263zM396.59 736.527c-185.856 0-336.528-150.672-336.528-336.528S210.734 63.471 396.59 63.471c185.856 0 336.528 150.672 336.528 336.528S582.446 736.527 396.59 736.527"/>
            </svg>
          </template>
        </a-input-search>
        <template v-if="showTag">
          <div v-if="storeId" class="only-store" @click="research()">切换为{{ !onlyStore ? '店铺内' : '平台' }}搜索</div>
          <div class="history-list flex" v-if="promotionTags && promotionTags.length">
            <div v-for="(item, index) in promotionTags" :key="index" class="mr_10">
              <span class="history-item" @click="selectTags(item)">{{ item }}</span>
            </div>
          </div>
          <div v-else></div>
        </template>
      </div>
      <div></div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useRouter, useRoute } from 'vue-router';
  import { ref, computed, onMounted } from 'vue';
  import storage from '@/utils/storage';
  import { hotWords } from '@/api/goods';
  import { useUserStore } from '@/stores/user';
  import { storeToRefs } from 'pinia';



  /**
   * 接收父组件传值
   */
  const props = defineProps({
    showTag: { // 是否展示搜索栏下方热门搜索
      type: Boolean,
      default: true
    },
    showLogo: { // 是否展示左侧logo
      type: Boolean,
      default: true
    },
    storeId: { // 是否为店铺页面
      type: String,
      default: ""
    },
    hover: {
      type: Boolean,
      default: false
    },
    useClass: {
      type: null,
      default: ''
    }
  });

  const route = useRoute();
  const router = useRouter();
  // 状态管理
  const store = useUserStore();
  const { logoImg } = storeToRefs(store);
  // 搜索内容
  const searchData = ref<any>('');
  // 搜索范围
  const onlyStore = ref<boolean>(false);
  // 热门标签
  const promotionTags = computed(() => {
    if (storage.getHotWordsList()) {
      return storage.getHotWordsList()?.split(',');
    } else {
      return [];
    }
  });
  const emits = defineEmits<{ (e: 'search', obj: any): void }>();

  // 选择热门标签
  const selectTags = (item: any) => {
    searchData.value = item;
    search();
  };
  // 全平台搜索商品
  const search = () => {
    const url = route.path;
    if (url === '/goodsList') {
      const newQuery = { ...route.query, keyword: searchData.value };
      router.replace({ query: newQuery });
      emits('search', searchData.value);
    } else {
      const pushData = {
        path: '/goodsList',
        query: {keyword: searchData.value},
      } as any;
      if (props.storeId && onlyStore.value) pushData.query.storeId = props.storeId;
      router.push(pushData);
    }
  };
  // 切换为-店铺内/平台
  const research = () => {
    onlyStore.value = !onlyStore.value;
  };
  //数据监听
  store.$subscribe((mutation, state) => {

  });
  onMounted(() => {
    searchData.value = route.query.keyword;
    // 首页顶部固定搜索栏不调用热词接口
    if (!props.hover) {
      // 搜索热词每5分钟请求一次
      const reloadTime = storage.getHotWordsReloadTime();
      const time = new Date().getTime() - 5 * 60 * 1000;
      if (!reloadTime) {
        hotWords({count: 5}).then(res => {
          if (res.data.success && res.data.result) storage.setHotWordsList(res.data.result.toString());
        });
        storage.setHotWordsReloadTime(new Date().getTime())
      } else if (reloadTime && Number(time) > Number(reloadTime)) {
        hotWords({count: 5}).then(res => {
          if (res.data.success && res.data.result) storage.setHotWordsList(res.data.result.toString());
        });
        storage.setHotWordsReloadTime(new Date().getTime())
      }
    }

  })
</script>

<style scoped lang="less">
  .navbar {
    height: 110px;
    background: #fff;
  }
  .small-search-box {
    height: 60px;
    margin: 0 !important;

  }
  .container {
    width: 1200px;
    height: 110px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .small-container {
    width: 700px;
    height: 60px;
    margin-top: 10px;
  }
  .logo-img {
    /*min-width: 150px;*/
    max-width: 150px;
    max-height: 110px;
    cursor: pointer;
    display: block;
  }
  .input-search {
    width: 700px;
    height: 40px;
    position: relative;
  }
  :deep(.arco-input-wrapper) {
    border: 1.4px solid @theme_color;
    box-sizing: border-box;
    border-radius: 19.6px;
    position: relative;
    padding-left: 26px;
    font-size: 14px;
    font-weight: normal;
    height: 37.8px;
    color: #999;

    &:focus {
      box-shadow: none;
    }
  }
  :deep(.arco-input-append) {
    border-radius: 19.6px !important;
    cursor: pointer;
    box-sizing: border-box;
    border: 1.4px solid #e1251b;
    width: 67.2px;
    height: 37.8px;
    position: absolute;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    top: 0;
    z-index: 99;
    background-color: #e1251b;
    color: #ffffff;
    button {
      font-size: 14px;
      font-weight: 600;
      line-height: 1;
    }
  }

  .history-list {
    display: flex;
    margin-left: 28px;
    margin-top: 4px;
  }
  .history-item {
    font-size: 13px;
    font-weight: normal;
    line-height: 16px;
    letter-spacing: 0px;
    margin-right: 17px;
    color: #666666;
    cursor: pointer;
  }
</style>
