
import { mergeConfig } from 'vite';
import configArcoResolverPlugin from './plugin/arcoResolver';
import configCompressPlugin from './plugin/compress';
import configImageminPlugin from './plugin/imagemin';
import configStyleImportPlugin from './plugin/styleImport';
import configVisualizerPlugin from './plugin/visualizer';
import baseConfig from './vite.config.base';
export default mergeConfig(
  {
    mode: 'production',
    plugins: [
      configCompressPlugin('gzip'),
      configVisualizerPlugin(),
      configArcoResolverPlugin(),
      configStyleImportPlugin(),
      configImageminPlugin(),

    ],
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            arco: ['@arco-design/web-vue'],
            // chart: ['echarts', 'vue-echarts'],
            vue: ['vue', 'vue-router', 'pinia', '@vueuse/core', 'vue-i18n'],
          },
        },
        // 生产环境移除console
        minify: "terser",
        terserOptions: {
          compress: {
            //生产环境时移除console
            drop_console: true,
            drop_debugger: true,
          },
        },
      },
      chunkSizeWarningLimit: 2000,
    },
  },
  baseConfig
);
