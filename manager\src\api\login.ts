import qs from 'query-string';
// eslint-disable-next-line import/no-cycle
import request, { Method } from '@/utils/axios';


interface loginParams {
  username: string | number;
  password: string;
}

/**
 * 登录
 * @returns
 * @param params
 */
export function login(params: loginParams) {
  return request({
    url: '/passport/user/login',
    method: Method.POST,
    needToken: false,
    data: qs.stringify(params),
    headers: {
      'clientType': 'PC',
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 * 登出
 */
export function logoutUser() {
  return request({
    url: '/passport/user/logout',
    method: Method.POST,
    needToken: true,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 * 获取管理员信息
 */
export function getUserInfo() {
  return request({
    url: '/passport/user/info',
    method: Method.GET,
    needToken: true,
  });
}

/**
 * 保存管理员信息
 */
export function setUserInfo(params: any) {
  return request({
    url: '/passport/user/info',
    method: Method.PUT,
    data: qs.stringify(params),
    needToken: true,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 * 刷新token
 */
export function refreshTokenMethod(token: string) {
  return request({
    url: `/passport/login/refresh/${token}`,
    method: Method.GET,
    params: token,
  });
}

/**
 * 
 * 个人中心修改密码
 */
export function changePassword(params: any) {
  return request({
    url: `/passport/user/editPassword`,
    method: Method.PUT,
    data:params,
    needToken: true,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}
