
// 商品部分的
import { DragRule } from './types'
const goodsModel: Array<DragRule> = [
  {
    type: "flexOne",
    name: "图片",
    roles: ['height', 'round', 'img', 'link'],
    border: 'normal',
    data: {
      list: [
        {
          img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/ias_c8d0b1587c24057ea1ec040ddf1dd2da_750x400_80.jpg",
          url: "",
        }
      ],
      height: 200
    }
  },
  {
    type: "flexHotArea",
    name: "热区",
    roles: ['height', 'round', 'img', 'area'],
    border: 'normal',
    data: {
      list: [
        {
          img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/ias_c8d0b1587c24057ea1ec040ddf1dd2da_750x400_80.jpg",
          url: "",
          zoneInfo:[],
        }
      ],
      height: 200
    }
  },
  {
    type: "carousel",
    name: "轮播图",
    roles: ['height', 'round'],
    models: [{
      label: '图片',
      model: 'mix-model'
    }],
    border: 'normal',
    data: {
      list: [
        {
          img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/img.png",
          url: "",
        },
        {
          img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/img.png",
          url: "",
        },
        {
          img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/img.png",
          url: "",
        },
      ],
      height: 200
    }
  },
  {
    type: "goodsOnly",
    name: "商品",
    roles: [],
    models: [{
      'label': '列表样式',
      'model': 'goods-style'
    }, {
      'label': '商品选品',
      'model': 'goods-choice'
    },],
    border: 'normal',
    data: {
      list: [

      ],
      goodsType: "two",
    }
  },
  {
    type: "menu",
    name: "宫格导航",
    roles: ['round', 'height'],
    models: [{
      'label': '文字颜色',
      'model': 'background',
      'bind': 'textColor'
    }, {
      'label': '背景颜色',
      'model': 'background',
      'bind': 'background'
    }, {
      'label': '菜单',
      'model': 'mix-model',
    }],
    border: 'normal',
    data: {
      list: [
        {
          img: "https://lilishop-oss.oss-cn-beijing.aliyuncs.com/ada7d39419554cb1ab52b3d5aae1563a.png",
          url: "",
          link: "",
          title: "菜单",
          size: "88*88 (1:1)",
        },
        {
          img: "https://lilishop-oss.oss-cn-beijing.aliyuncs.com/230f48f024a343c6be9be72597c2dcd0.png",
          url: "",
          link: "",
          title: "菜单",
          size: "88*88 (1:1)",
        },
        {
          img: "https://lilishop-oss.oss-cn-beijing.aliyuncs.com/155c65e778204372ac196ab6cd7cd598.png",
          url: "",
          link: "",
          title: "菜单",
          size: "88*88 (1:1)",
        },
        {
          img: "https://lilishop-oss.oss-cn-beijing.aliyuncs.com/665dd952b54e4911b99b5e1eba4b164f.png",
          url: "",
          link: "",
          title: "菜单",
          size: "88*88 (1:1)",
        },
        {
          img: "https://lilishop-oss.oss-cn-beijing.aliyuncs.com/9bd0d0ed2ec546619d62889f2ae465c7.jpeg",
          url: "",
          link: "",
          title: "菜单",
          size: "88*88 (1:1)",
        },
      ],
      swiper: 'default',
      model: 'default',
      textColor: "#333",
      background: "#ffffff",
      height: 60,

    }
  },

  {
    type: "leftOneRightTwo",
    name: "左一右二",
    roles: [],
    border: 'normal',
    models: [{
      label: '卡片',
      model: 'mix-model'
    }],
    data: {
      list: [
        {
          img: "https://lilishop-oss.oss-cn-beijing.aliyuncs.com/a5eb13650d8244479b123398ab2cfebc.png",
          url: "",
          link: "",
          size: "364*364"
        },
        {
          img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/kdB3AE9ay4c1SnN.png",
          url: "",
          link: "",
          size: "364*168"
        },
        {
          img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/kdB3AE9ay4c1SnN.png",
          url: "",
          link: "",
          size: "364*168"
        },
      ],

    }
  },
  {
    type: "leftTwoRightOne",
    name: "左二右一",
    roles: [],
    border: 'normal',
    models: [{
      label: '卡片',
      model: 'mix-model'
    }],
    data: {
      list: [
        {
          img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/kdB3AE9ay4c1SnN.png",
          url: "",
          link: "",
          size: "364*168"
        },
        {
          img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/kdB3AE9ay4c1SnN.png",
          url: "",
          link: "",
          size: "364*168"
        },
        {
          img: "https://lilishop-oss.oss-cn-beijing.aliyuncs.com/a5eb13650d8244479b123398ab2cfebc.png",
          url: "",
          link: "",
          size: "364*364"
        },
      ],

    }
  },
  {
    type: "flexTwo",
    name: "两张横图",
    roles: [],
    border: 'normal',
    models: [{
      label: '卡片',
      model: 'mix-model'
    }],
    data: {
      list: [
        {
          img: "https://lilishop-oss.oss-cn-beijing.aliyuncs.com/767af000ae5a4310b311aa0674f60b83.png",
          url: "",
          link: "",
          size: "364*120"
        },
        {
          img: "https://lilishop-oss.oss-cn-beijing.aliyuncs.com/2b47d010c3da4b7d987c444234e34986.png",
          url: "",
          link: "",
          size: "364*120"
        },

      ],

    }
  },
  {
    type: "flexThree",
    name: "一行三列",
    roles: [],
    border: 'normal',
    models: [{
      label: '卡片',
      model: 'mix-model'
    }],
    data: {
      list: [
        {
          img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/1699943377009_330x363_90.png",
          url: "",
          link: "",
          size: "364*168"
        },
        {
          img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/1699943111069_330x363_90.png",
          url: "",
          link: "",
          size: "364*168"
        },
        {
          img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/1699942667044_330x363_90.png",
          url: "",
          link: "",
          size: "364*168"
        },

      ],

    }
  },
  {
    type: "flexFour",
    name: "一行四列",
    roles: [],
    border: 'normal',
    models: [{
      label: '卡片',
      model: 'mix-model'
    }],
    data: {
      list: [
        {
          img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/166519837827005655.jpg",
          url: "",
          link: "",
          size: "1:1"
        },
        {
          img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/166519830495916803.jpg",
          url: "",
          link: "",
          size: "1:1"
        },
        {
          img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/9e3b1278663d48bbb60a64b13cb29e7b.png",
          url: "",
          link: "",
          size: "1:1"
        },
        {
          img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/4b13e9f1508c44bf9a35eb9b0cbd5b2c.png",
          url: "",
          link: "",
          size: "1:1"
        },
      ],

    }
  },
  {
    type: "flexFive",
    name: "一行五列",
    roles: [],
    border: 'normal',
    models: [{
      label: '卡片',
      model: 'mix-model'
    }],
    data: {
      list: [
        {
          img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/166519837827005655.jpg",
          url: "",
          link: "",
          size: "1:1"
        },
        {
          img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/166519830495916803.jpg",
          url: "",
          link: "",
          size: "1:1"
        },
        {
          img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/9e3b1278663d48bbb60a64b13cb29e7b.png",
          url: "",
          link: "",
          size: "1:1"
        },
        {
          img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/4b13e9f1508c44bf9a35eb9b0cbd5b2c.png",
          url: "",
          link: "",
          size: "1:1"
        },
        {
          img: "https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/767169e6f7bd4f239acad82db204158b.jpg",
          url: "",
          link: "",
          size: "1:1"
        },

      ],

    }
  },
]
// 基本组件
export default goodsModel

