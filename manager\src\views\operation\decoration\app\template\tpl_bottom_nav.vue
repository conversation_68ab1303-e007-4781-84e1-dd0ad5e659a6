<template>
  <div class="bottom-nav" :style="{
    height: (props.res?.data?.height || 60) + 'px',
    backgroundColor: props.res?.data?.background || '#ffffff',
    borderRadius: (props.res?.data?.round || 0) + 'px'
  }">
    <div class="nav-container" flex flex-a-c flex-j-between>
      <div
        v-for="(item, index) in safeList"
        :key="index"
        class="nav-item"
        flex flex-col flex-a-c flex-j-c
        text-center
        cursor-pointer
        @click="handleNavClick(item, index)"
      >
        <!-- 删除按钮 - 只有中间的菜单可以删除 -->
        <icon-close-circle-fill
          v-if="canDelete(index)"
          size="16"
          color="#e1251b"
          class="delete-icon"
          @click.stop="deleteNavItem(index)"
        />

        <!-- 菜单图标 -->
        <div class="nav-icon" mb-4px>
          <img v-if="item.img" :src="item.img" :style="{
            width: (props.res?.data?.iconSize || 24) + 'px',
            height: (props.res?.data?.iconSize || 24) + 'px'
          }" />
          <div v-else class="default-icon" :style="{
            width: (props.res?.data?.iconSize || 24) + 'px',
            height: (props.res?.data?.iconSize || 24) + 'px',
            backgroundColor: '#f0f0f0',
            borderRadius: '4px'
          }"></div>
        </div>

        <!-- 菜单标题 -->
        <div class="nav-title" :style="{
          color: index === activeIndex ? (props.res?.data?.activeColor || '#1890ff') : (props.res?.data?.textColor || '#999999'),
          fontSize: (props.res?.data?.fontSize || 12) + 'px'
        }">
          {{ item.title }}
        </div>
      </div>

      <!-- 添加按钮 -->
      <div
        v-if="safeList.length < 5"
        class="add-nav-item"
        flex flex-col flex-a-c flex-j-c
        text-center
        cursor-pointer
        @click="addNavItem"
      >
        <icon-plus-circle size="24" color="#1890ff" />
        <div text-12px color-gray-500 mt-4px>添加菜单</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DragRule } from '@/views/operation/decoration/app/models/types';
import { ref, computed } from 'vue';

const props = defineProps<{
  res: DragRule
}>()

// 默认菜单数据
const defaultNavItems = [
  {
    title: '首页',
    img: 'https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/166519837827005655.jpg',
    url: {
      ___value: 'tabbar',
      title: '首页',
      path: '/pages/tabbar/home/<USER>',
      ___key: '首页',
      ___type: 'Tab页面'
    },
    size: "1:1"
  },
  {
    title: '分类',
    img: 'https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/166519830495916803.jpg',
    url: {
      ___value: 'tabbar',
      title: '分类',
      path: '/pages/tabbar/category/category',
      ___key: '分类',
      ___type: 'Tab页面'
    },
    size: "1:1"
  },
  {
    title: '购物车',
    img: 'https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/9e3b1278663d48bbb60a64b13cb29e7b.png',
    url: {
      ___value: 'tabbar',
      title: '购物车',
      path: '/pages/tabbar/cart/cartList',
      ___key: '购物车',
      ___type: 'Tab页面'
    },
    size: "1:1"
  },
  {
    title: '我的',
    img: 'https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/767169e6f7bd4f239acad82db204158b.jpg',
    url: {
      ___value: 'tabbar',
      title: '我的',
      path: '/pages/tabbar/user/my',
      ___key: '我的',
      ___type: 'Tab页面'
    },
    size: "1:1"
  }
];

// 当前激活的菜单索引
const activeIndex = ref(0);

// 确保数据结构存在
if (!props.res.data) {
  props.res.data = { list: [] };
}
if (!props.res.data.list || !Array.isArray(props.res.data.list)) {
  props.res.data.list = [...defaultNavItems];
} else if (props.res.data.list.length === 0) {
  props.res.data.list = [...defaultNavItems];
}

// 设置默认值
if (!props.res.data.height) props.res.data.height = 60;
if (!props.res.data.background) props.res.data.background = '#ffffff';
if (!props.res.data.textColor) props.res.data.textColor = '#999999';
if (!props.res.data.activeColor) props.res.data.activeColor = '#1890ff';
if (!props.res.data.iconSize) props.res.data.iconSize = 24;
if (!props.res.data.fontSize) props.res.data.fontSize = 12;
if (!props.res.data.round) props.res.data.round = 0;



// 确保每个菜单项都有正确的url结构
if (props.res.data.list && Array.isArray(props.res.data.list)) {
  props.res.data.list.forEach((item: any) => {
    if (!item.url) {
      item.url = {
        ___value: 'tabbar',
        title: '首页',
        path: '/pages/tabbar/home/<USER>',
        ___key: '首页',
        ___type: 'Tab页面'
      };
    }
    if (!item.url.___key) {
      item.url.___key = item.url.title || '首页';
    }
    if (!item.url.___type) {
      item.url.___type = 'Tab页面';
    }
    if (!item.url.path && item.url.___value === 'tabbar') {
      // 为tabbar类型的菜单项设置默认路径
      const pathMap: Record<string, string> = {
        '首页': '/pages/tabbar/home/<USER>',
        '分类': '/pages/tabbar/category/category',
        '充值中心': '/pages/tabbar/voucher/voucher',
        '优惠购': '/pages/tabbar/special/tabbar',
        '我的': '/pages/tabbar/user/my',
        '购物车': '/pages/tabbar/cart/cartList'
      };
      item.url.path = pathMap[item.url.title] || '/pages/tabbar/home/<USER>';
    }
  });
}

// 安全的列表访问
const safeList = computed(() => {
  return props.res?.data?.list || [];
});

// 判断是否可以删除（第一个和最后一个不能删除）
function canDelete(index: number): boolean {
  // 添加安全检查，确保list存在且有效
  if (!props.res?.data?.list || !Array.isArray(props.res.data.list)) {
    return false;
  }

  const list = props.res.data.list;
  return index !== 0 && index !== list.length - 1 && list.length > 2;
}

// 删除导航项
function deleteNavItem(index: number) {
  if (canDelete(index) && props.res?.data?.list) {
    props.res.data.list.splice(index, 1);
  }
}

// 添加导航项
function addNavItem() {
  if (!props.res.data) {
    props.res.data = { list: [] };
  }
  if (!props.res.data.list || !Array.isArray(props.res.data.list)) {
    props.res.data.list = [];
  }

  if (props.res.data.list.length < 5) {
    props.res.data.list.push({
      title: '新菜单',
      img: 'https://zzwx-lilishop.oss-cn-beijing.aliyuncs.com/166519837827005655.jpg',
      url: {
        ___value: 'tabbar',
        title: '首页',
        path: '/pages/tabbar/home/<USER>',
        ___key: '首页',
        ___type: 'Tab页面'
      },
      size: "1:1"
    });
  }
}

// 处理导航点击
function handleNavClick(item: any, index: number) {
  activeIndex.value = index;
  console.log('导航点击:', item, index);

  // 处理跳转逻辑
  if (item.url && item.url.___value) {
    const jumpPath = getJumpPath(item.url);
    if (jumpPath) {
      console.log('跳转路径:', jumpPath);
      // 在装修器中只是预览，不实际跳转
      // 实际使用时会在前端页面中处理跳转
    }
  }
}

// 获取跳转路径
function getJumpPath(urlConfig: any) {
  if (!urlConfig) return null;

  switch (urlConfig.___value) {
    case 'other':
      return getOtherPagePath(urlConfig.title);
    case 'goods':
      return {
        path: '/goodsDetail',
        query: {
          skuId: urlConfig.id,
          goodsId: urlConfig.goodsId,
        },
      };
    case 'category':
      return {
        path: '/goodsList',
        query: {
          category: urlConfig.id,
        },
      };
    case 'shops':
      return {
        path: '/Merchant',
        query: {
          id: urlConfig.id,
        },
      };
    case 'special':
      return {
        path: '/topic',
        query: {
          id: urlConfig.id,
        },
      };
    default:
      return null;
  }
}

// 获取其他页面路径
function getOtherPagePath(title: string) {
  const pathMap: Record<string, string> = {
    '首页': '/',
    '分类': '/goodsList',
    '购物车': '/cart',
    '个人中心': '/user/home',
    '收藏商品': '/user/home/<USER>/myFavorites',
    '我的订单': '/user/home/<USER>/myOrder',
    '领券中心': '/coupon',
    '签到': '/user/home/<USER>/signIn',
    '秒杀频道': '/seckill',
    '拼团频道': '/pintuan',
    '砍价': '/bargain',
    '积分商城': '/point',
    '充值中心': '/user/home/<USER>/moneyManagement',
    '优惠购': '/promotion',
  };

  return pathMap[title] ? { path: pathMap[title] } : { path: '/' };
}
</script>

<style scoped lang="less">
.bottom-nav {
  width: 100%;
  position: relative; /* 在装修器中使用相对定位 */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 999;
}

.nav-container {
  height: 100%;
  padding: 0 16px;
}

.nav-item {
  flex: 1;
  position: relative;
  min-width: 60px;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.delete-icon {
  position: absolute;
  top: -2px;
  right: -2px;
  z-index: 10;
  background: white;
  border-radius: 50%;
}

.nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    border-radius: 4px;
    object-fit: cover;
  }
}

.default-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #d9d9d9;
}

.add-nav-item {
  flex: 1;
  min-width: 60px;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}
</style>
