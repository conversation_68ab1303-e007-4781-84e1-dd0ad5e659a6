<template>
  <div v-auto-animate overflow-y-auto>
    <div v-if="app[indexOfApp]" v-auto-animate>
      <!-- 组件名称 -->
      <div p-16px border-b-1>
        {{ app[indexOfApp].name }}

        <a-alert mt-10px v-if="app[indexOfApp].type === 'search'" type="warning">因为手机顶部安全区问题！标题组件建议放在第一位</a-alert>
      </div>
      <div px-16px>
        <!-- 拥有的组件权限 -->
        <div class="role" :key="index" v-for="(item, index) in app[indexOfApp].roles">
          <component :res="app[indexOfApp]" :is="roleTemplate[item]"></component>
        </div>

        <!-- 如果是menu的话 -->
        <menuTpl :res="app[indexOfApp]" v-if="app[indexOfApp].type === 'menu'"></menuTpl>
        <div v-if="app[indexOfApp].models">
          <!-- 拥有的组件 -->
          <div class="role" :key="index" v-for="(item, index) in app[indexOfApp].models">
            <component :res="app[indexOfApp]" v-if="item.bind" :bind="item.bind" :text="item.label"
              :is="modelsTemplate[item.model]"></component>
            <component :res="app[indexOfApp]" v-else  :text="item.label"
              :is="modelsTemplate[item.model]"></component>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import roles from './modules/roles'
import models from './modules/models'
import { useDesign } from '@/store'
import { storeToRefs } from 'pinia'
import menuTpl from './modules/menu.vue'
const userDesign = useDesign();

const { indexOfApp, app }: any = storeToRefs(userDesign)
const roleTemplate: any = roles
const modelsTemplate: any = models

</script>

<style scoped>
</style>
