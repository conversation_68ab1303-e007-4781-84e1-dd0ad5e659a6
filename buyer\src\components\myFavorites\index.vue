<template>
  <div>
    <Card _Title="近期收藏" :_Size="16" :_Tabs="changeWay" @_Change="changeType" _More="更多收藏" _Src="/user/home/<USER>/myFavorites"></Card>
    <div>
      <div class="goodsItem" v-for="(item) in list" :key="item.skuId" v-if="list && list.length">
        <div class="goodsImg hover-pointer" v-if="apiParams.type === 'GOODS'"><img :src="item.image" /></div>
        <div class="goodsImg hover-pointer" v-else><img :src="item.storeLogo" /></div>
        <div class="goodsTitle hover-color" v-if="apiParams.type === 'GOODS'" @click="goGoodsDetail(item.skuId, item.goodsId)">{{item.goodsName}}</div>
        <div v-else class="goodsTitle hover-pointer">{{item.storeName}}</div>
        <div class="goodsPrice">
          <span v-if="apiParams.type === 'GOODS'">{{unitPrice(item.price, '￥')}}</span>
          <a-tag v-if="item.selfOperated" color="red">商家自营</a-tag>
        </div>
        <div class="goodsBuy">
          <template v-if="apiParams.type === 'GOODS'">
            <a-button size="mini" status="danger" type="primary" @click="goGoodsDetail(item.skuId, item.goodsId)">立即购买</a-button>
            <a-button size="mini" @click="cancelGoods(item.skuId)">取消收藏</a-button>
          </template>
          <template v-if="apiParams.type === 'STORE'">
            <a-button size="mini" status="danger" type="primary" @click="goShopPage(item.id)">店铺购买</a-button>
            <a-button size="mini" @click="cancelStore(item.id)">取消收藏</a-button>
          </template>
        </div>
      </div>
      <Empty v-else></Empty>
    </div>

  </div>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router';
  import { ref, onMounted } from 'vue';
  import { unitPrice } from '@/utils/filters';
  import { collectList, cancelCollect, storeCollectList, cancelStoreCollect } from '@/api/member';
  import { Message, Modal } from '@arco-design/web-vue';

  const router = useRouter();
  const changeWay = ref(['收藏商品', '收藏店铺']);
  const list = ref<Array<any>>([]);
  const apiParams = ref({
    pageNumber: 1,
    pageSize: 5,
    type: 'GOODS'
  });

  const changeType = (index: any) => {
    switch (index) {
      case 0:
        apiParams.value.type = 'GOODS';
        getGoodsList();
        break;
      case 1:
        apiParams.value.type = 'STORE';
        getStoreList();
        break;
    }
  };
  // 收藏商品列表
  const getGoodsList = () => {
    collectList(apiParams.value).then(res => {
      if (res.data.success) {
        list.value = res.data.result.records;
      }
    })
  };
  // 收藏店铺列表
  const getStoreList = () => {
    storeCollectList(apiParams.value).then(res => {
      if (res.data.success) {
        list.value = res.data.result.records;
      }
    })
  };
  // 立即购买-跳转商品详情
  const goGoodsDetail = (skuId: any, goodsId: any) => {
    let routeUrl = router.resolve({path: "/goodsDetail", query: { skuId, goodsId },});
    window.open(routeUrl.href, "_blank");
  };
  // 取消收藏商品
  const cancelGoods = (id: any) => {
    Modal.confirm({
      title: '取消收藏',
      content: `确定取消收藏该商品吗？`,
      okButtonProps: {
        type: "primary",
        status: "danger"
      },
      onOk: () => {
        cancelCollect(apiParams.value.type, id).then(res => {
          if (res.data.success) {
            Message.success('操作成功！');
            getGoodsList();
          }
        })
      }
    });
  };
  // 跳转店铺首页
  const goShopPage = (id: any) => {
    let routeUrl = router.resolve({path: "/merchant", query: { id },});
    window.open(routeUrl.href, "_blank");
  };
  // 取消收藏店铺
  const cancelStore = (id: any) => {
    Modal.confirm({
      title: '取消收藏',
      content: `确定取消收藏该店铺吗？`,
      okButtonProps: {type: "primary", status: "danger"},
      onOk: () => {
        cancelStoreCollect(apiParams.value.type, id).then(res => {
          if (res.data.success) {
            Message.success('操作成功！');
            getStoreList();
          }
        })
      }
    });
  };



  onMounted(() => {
    getGoodsList();
  })
</script>

<style scoped lang="less">
  .goodsItem {
    display: flex;
    justify-content: space-between;
    height: 60px;
    line-height: 60px;
    margin-bottom: 10px;

    > .goodsImg {
      width: 60px;
      height: 60px;
      overflow: hidden;

      > img {
        width: 100%;
        height: 100%;
      }
    }

    > .goodsPrice,
    .goodsShop {
      width: 150px;
      text-align: center;
    }

    > .goodsTitle {
      width: 400px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .goodsBuy {
      width: 160px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
</style>
