* {
  box-sizing: border-box;
}

.container {
  max-width: 100% !important;
  padding:  20px 20px 20px;
}

html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-size: 14px;
  background-color: var(--color-bg-1);
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

.echarts-tooltip-diy {
  background: linear-gradient(
    304.17deg,
    rgba(253, 254, 255, 0.6) -6.04%,
    rgba(244, 247, 252, 0.6) 85.2%
  ) !important;
  border: none !important;

  /* Note: backdrop-filter has minimal browser support */

  border-radius: 6px !important;
  backdrop-filter: blur(10px) !important;

  .content-panel {
    display: flex;
    justify-content: space-between;
    width: 164px;
    height: 32px;
    margin-bottom: 4px;
    padding: 0 9px;
    line-height: 32px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 4px;
    box-shadow: 6px 0 20px rgba(34, 87, 188, 0.1);
  }

  .tooltip-title {
    margin: 0 0 10px 0;
  }

  p {
    margin: 0;
  }

  .tooltip-title,
  .tooltip-value {
    display: flex;
    align-items: center;
    color: #1d2129;
    font-weight: bold;
    font-size: 13px;
    line-height: 15px;
    text-align: right;
  }

  .tooltip-item-icon {
    display: inline-block;
    width: 10px;
    height: 10px;
    margin-right: 8px;
    border-radius: 50%;
  }
}

.general-card {
  border: none;
  border-radius: 4px;

  & > .arco-card-header {
    height: auto;
    padding: 20px;
    border: none;
  }

  & > .arco-card-body {
    padding: 0 20px 20px 20px;
  }
}

.split-line {
  border-color: rgb(var(--gray-2));
}

.arco-table-cell {
  .circle {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 4px;
    background-color: rgb(var(--blue-6));
    border-radius: 50%;

    &.pass {
      background-color: rgb(var(--green-6));
    }
  }
}


.wrapper {
  min-height: 580px;
  padding: 20px 0 0 20px;
  background-color: var(--color-bg-2);
  border-radius: 4px;
}

.flex {
  display: flex !important;
}

.flex-j-c {
  justify-content: center;
}

.flex-j-sb {
  justify-content: space-between;
}

.flex-a-c {
  align-items: center;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-20 {
  margin-top: 20px;
}

.ml-20 {
  margin-left: 20px;
}

.mr-20 {
  margin-right: 20px;
}

.mr-10 {
  margin-right: 10px;
}


//自动移滚动条样式
::-webkit-scrollbar {
  width: 1px;
  height: 5px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(50, 50, 50, 0.3);
  border-radius: 1em;
}

::-webkit-scrollbar-track {
  background-color: rgba(50, 50, 50, 0.1);
  border-radius: 1em;
}

