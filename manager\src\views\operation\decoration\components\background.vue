<template>
  <div py-16px border-b-1>
    <div flex flex-a-c>
      <div w-90px>{{props.text}}</div>
      <div ml-4px>
        <t-color-picker  format="HEX" :color-modes="['monochrome']" :show-primary-color-preview="false"
          v-model="props.res.data[bind]" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DragRule } from '~/views/operation/decoration/app/models/types';

const props = defineProps<{
  res: DragRule,
  text:string
  bind:string
}>()


</script>

<style scoped>
</style>
