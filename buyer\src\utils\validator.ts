/* eslint-disable no-useless-escape */
/* eslint-disable no-lonely-if */

/**
 * 对于arco form的rules验证
 * ----------------------------------------------------------------
 */

// 手机号(mobile phone)中国(最宽松), 只要是1开头即可, 如果你的手机号是用来接收短信, 优先建议选择这一条"
export const MOBILE = {
  match: /^(?:(?:\+|00)86)?1\d{10}$/,
  message: '请输入正确电话号码',
};

// 电子邮箱
export const EMAIL = {
  match: /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,
  message: '请输入正确的邮箱格式'
};

// 营业执照号
export const LICENSENUM = {
  match: /(^(?:(?![IOZSV])[\dA-Z]){2}\d{6}(?:(?![IOZSV])[\dA-Z]){10}$)|(^\d{15}$)/,
  message: '请输入正确的营业执照号'
};

// 正整数
export const INTEGER = {
  match: /^[0-9]\d{0,10}|0$/,
  message: '请输入正整数',
};
// 正整数
export const NUMBER = {
  match: /^(\-|\+)?\d{0,10}$/,
  message: '请输入数字',
};
export const VARCHAR5 = {
  match: /^.{1,5}$/,
  message: '长度应该限制在1-5个字符',
};

export const VARCHAR20 = {
  match: /^.{1,20}$/,
  message: '长度应该限制在1-20个字符',
};

export const VARCHAR2TO100 = {
  match: /^.{2,100}$/,
  message: '长度应该限制在2-100个字符',
};

export const VARCHAR255 = {
  match: /^.{1,255}$/,
  message: '超出最大长度限制',
};

export const URL200 = {
  match: /[a-zA-z]+\:\/\/[^\s]{1,190}/,
  message: '请输入长度不超过200的URL地址',
};
export const REQUIRED = {
  required: true,
  message: '请填写参数',
};

export const LEAST6 = {
  match: /^\S{6,}$/,
  message: '密码不能少于6位'
};

export const MONEY = {
  match: /^(0|[1-9][0-9]{0,3})(\.[0-9]{1,5})?$/,
  message: '请输入大于0小于9999的合法提现金额'
};

export const SUM = {
  match: /(^[1-9]([1-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[1-9]\.[0-9]([0-9])?$)/,
  message: '请输入大于1小于9999的合法提现金额'
};

// 身份证
export const IDCARD = {
  match: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
  message: '请输入正确的身份证号'
};

// 正整数【包含0】
export const INTEGER0 = {
  match: /^[0-9]\d*$/,
  message: '只能填写正整数或0'
};


/** ---------------------------------------------------------------- */
