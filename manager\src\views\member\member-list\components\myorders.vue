<template>
  <div>
    <searchTable :columns="columnsSearchOrders" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <!--<tablePage :api-params="apiParams" ref="tablePageRef" :columns="columnsOrderTable" :api="getOrderList"-->
      <!--:methods="sortMethods" @orderDetail="handleMemberDetails">-->
    <!--</tablePage>-->

    <div class="order-box">
      <div class="order-tab">
        <p style="width: 30%">订单详情</p>
        <p style="width: 10%">订单来源</p>
        <p style="width: 10%;">店铺名称</p>
        <p style="width: 10%">应付</p>
        <p style="width: 10%">买家/收货人</p>
        <p style="width: 10%">订单状态</p>
        <p style="width: 10%">支付状态</p>
        <p style="width: 10%">操作</p>
      </div>
      <div class="order-lists">
        <div class="order-item" v-for="(order, orderIndex) in columnsTableList" :key="orderIndex">
          <div class="header">
            <p>订单编号：<a-typography-text copyable bold><span style="color: #333;">{{order.sn}}</span></a-typography-text></p>
            <p>下单时间：<span style="color: #333;">{{order.createTime}}</span></p>
            <!--<p class="delete-order"></p>-->
          </div>
          <div class="body">
            <div style="width: 30%">
              <div class="goods">
                <div v-for="(goods, goodsIndex) in order.orderItems" :key="goodsIndex">
                  <img class="hover-color" :src="goods.image" alt=""/>
                  <div class="goods-info">
                    <div style="width: 100%;" class="hover-color"><a-typography-text copyable bold>{{goods.name}}</a-typography-text></div>
                    <div class="tag" style="font-size: 12px;">
                      <span>售后状态: {{customAfterSaleStatusList(goods.afterSaleStatus)}}</span>
                      <span>投诉状态：{{customComplainStatusList(goods.complainStatus)}}</span>
                    </div>
                  </div>
                  <div class="goods-num">
                    <span class="global_color"> {{unitPrice(goods.goodsPrice, '￥')}} </span>
                    <span style="color: red;"> x{{goods.num}} </span></div>
                </div>
              </div>
            </div>
            <div style="width: 10%;line-height: 32px;">{{customClientTypeList(order.clientType)}}</div>
            <div style="width: 10%;line-height: 32px;">{{order.storeName}}</div>
            <div style="width: 10%;line-height: 32px;">{{unitPrice(order.flowPrice, '￥')}}</div>
            <div style="width: 10%;line-height: 32px;">{{order.memberName}}</div>
            <div style="width: 10%;line-height: 32px;">{{customOrderStatusList(order.orderStatus)}}</div>
            <div style="width: 10%;line-height: 32px;">{{customPayStatusList(order.payStatus)}}</div>
            <div style="width: 10%"><a-button type="text" status="success" @click="handleOrderDetails(order)">查看</a-button></div>
          </div>
        </div>
      </div>
    </div>
    <div class="paginationBox">
      <a-pagination :total="paginationParams.total" show-page-size :current="apiParams.pageNumber" :page-size="apiParams.pageSize"
                    @change="(number) => { apiParams.pageNumber = number; }"
                    @page-size-change="(number) => {apiParams.pageSize = number; apiParams.pageNumber = 1;}"
      ></a-pagination>
    </div>





  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue';
import searchTable from '@/components/search-column/index.vue';
import { ColumnsDataRule, SearchRule, MethodsRule } from '@/types/global';
import { getOrderList } from '@/api/order';
import { useRoute, useRouter } from 'vue-router';
import { orderSource, orderStatus, payStatus, orderType, orderClientType, orderStatusList, paymentStatus, groupComplainStatus } from '@/utils/tools';
import tablePage from '@/components/table-pages/index.vue';
import { unitPrice } from '@/utils/filters';

const route = useRoute();
const router = useRouter();

// TA的订单搜索
const columnsSearchOrders: Array<SearchRule> = [
  {
    label: '订单号',
    model: 'orderSn',
    disabled: false,
    input: true,
  },
  {
    label: '订单状态',
    model: 'orderStatus',
    disabled: false,
    select: {
      options: orderStatus,
    },
  },
  {
    label: '支付状态',
    model: 'payStatus',
    disabled: false,
    select: {
      options: payStatus,
    },
  },
  {
    label: '订单类型',
    model: 'orderType',
    disabled: false,
    select: {
      options: orderType,
    },
  },
  {
    label: '订单来源',
    model: 'clientType',
    disabled: false,
    select: {
      options: orderSource,
    },
  },
  {
    label: '下单时间',
    model: 'payStatus',
    disabled: false,
    datePicker: {
      type: 'range',
    },
  },
];
// 传递的参数
const apiParams = ref<any>({memberId: route.query.id});

// 订单状态枚举
const customOrderStatusList = (type: string) => {
  let result = null as any;
  if (type === 'UNDELIVERED') {
    result = '待发货';
  } else if (type === 'UNPAID') {
    result = '未付款'
  } else if (type === 'PAID') {
    result = '已付款'
  } else if (type === 'DELIVERED') {
    result = '已发货'
  }  else if (type === 'CANCELLED') {
    result = '已取消'
  }  else if (type === 'COMPLETED') {
    result = '已完成'
  }  else if (type === 'TAKE') {
    result = '待核验'
  }  else if (type === 'STAY_PICKED_UP') {
    result = '待自提'
  } else {
    result = ''
  }
  return result;
};
// 订单支付状态枚举
const customPayStatusList = (type: string) => {
  let result = null as any;
  if (type === 'UNPAID') {
    result = '待付款'
  } else if (type === 'PAID') {
    result = '已付款'
  } else if (type === 'CANCEL') {
    result = '已取消'
  } else if (type === 'ACCOUNT') {
    result = '已对账'
  } else if (type === 'ACCOUNT_ERROR') {
    result = '对账失败'
  } else {
    result = ''
  }
  return result;
};
// 订单来源枚举
const customClientTypeList = (type: string) => {
  let result = null as any;
  if (type === 'H5') {
    result = '移动端';
  } else if (type === 'PC') {
    result = 'PC端'
  } else if (type === 'WECHAT_MP') {
    result = '小程序端'
  } else if (type === 'APP') {
    result = '移动应用端'
  } else {
    result = ''
  }
  return result;
}
// 售后状态枚举
const customAfterSaleStatusList = (type: string) => {
  let result = null as any;
  if (type === 'NEW') {
    result = '未申请';
  } else if(type === 'NOT_APPLIED') {
    result = '未申请';
  } else if(type === 'ALREADY_APPLIED') {
    result = '已申请';
  } else if (type === 'EXPIRED') {
    result = '已失效';
  } else {
    result = '';
  }
  return result;
};
// 投诉状态枚举
const customComplainStatusList = (type: string) => {
  let result = null as any;
  if (type === 'NEW') {
    result = '未申请';
  } else if (type === 'NO_APPLY') {
    result = '未申请';
  } else if (type === 'APPLYING') {
    result = '申请中';
  } else if (type === 'COMPLETE') {
    result = '已完成';
  } else if (type === 'EXPIRED') {
    result = '已失效';
  } else if(type === 'CANCEL') {
    result = '取消投诉';
  } else {
    result = '';
  }
  return result;
};
const columnsTableList = ref();
// 分页的属性配置
const paginationParams = ref({
  total: 0
});
// 商品订单列表
const getMyOrder = async () => {
  const res = await getOrderList(apiParams.value);
  if (res.data.success) {
    columnsTableList.value = res.data.result.records;
    paginationParams.value = res.data.result;
  }
};
  // 查看
  const handleOrderDetails = (val: any) => {
    const url = router.resolve({name: 'order-detail', query: {id: val.sn,}});
    window.open(url.href, '_blank');

  };
onMounted(() => {
  getMyOrder();
})
// 初始化监听
watch(() => [apiParams.value],
    (val)=>{
      getMyOrder();
    }, {deep: true}
);
</script>

<style lang="less" scoped>
  .order-box {
    .order-tab {
      width: 100%;
      height: 50px;
      background-color: #f3f4f5;
      color: #252931;
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      p {
        flex-shrink: 0;
        text-align: center;
      }
    }
    .order-lists {
      .order-item {
        box-sizing: border-box;
        border: 1px solid #eeeff0;
        margin-bottom: 10px;
        font-size: 14px;
        .header {
          width: 100%;
          height: 50px;
          background-color: #f8f9fa;
          color: #333;
          display: flex;
          align-items: center;
          box-sizing: border-box;
          padding: 0 20px;
          position: relative;
          font-size: 14px;
          p {
            margin-right: 30px;
          }
          .delete-order {
            position: absolute;
            top: 3px;
            right: 0;
            margin-right: 0;
          }
        }
        .body {
          display: flex;
          align-items: stretch;
          justify-content: space-between;
          text-align: center;
          color: #252931;
          > div {
            flex-shrink: 0;
            box-sizing: border-box;
            padding: 14px 0;
            /*border-left: 1px solid #e5e5e5;*/
          }
          > div:nth-of-type(1) {
            border-left: none;
            padding: 0;
            text-align: left;
          }
          .goods > div {
            width: 100%;
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            /*border-bottom: 1px solid #e5e5e5;*/
            box-sizing: border-box;
            padding: 10px;
            position: relative;
            img {
              width: 48px;
              height: 48px;
              flex-shrink: 0;
              border-radius: 4px;
            }
            .goods-info {
              flex-shrink: 1;
              width: 100%;
              box-sizing: border-box;
              padding: 0 15px;
              color: #252931;
              font-size: 14px;
              line-height: 18px;
              .tag {
                color: #aaaaaa;
                white-space: nowrap;
                margin-top: 12px;
                line-height: 12px;
                width: 100%;
                > span {
                  display: inline-block;
                }
                > span:nth-of-type(1) {
                  width: 126px;
                }
                > span:nth-of-type(3) {
                  color: #e4393c;
                  text-align: end;
                  position: absolute;
                  right: 10px;
                  bottom: 10px;
                }
              }
            }
            .goods-num {
              flex-shrink: 0;
              width: 25%;
              text-align: right;
            }
          }
          .goods > div:nth-last-of-type(1) {
            border-bottom: none;
          }
          .hover-color {
            /*overflow: hidden;*/
            /*-webkit-line-clamp: 2;*/
            /*text-overflow: ellipsis;*/
            /*display: -webkit-box;*/
            /*-webkit-box-orient: vertical;*/
            /*white-space: pre-wrap;*/
          }
        }
      }
    }
  }
  .paginationBox {
    margin-top: 18px;
    display: flex;
    flex-direction: row-reverse;
  }
</style>