<template>
  <div>
    <a-row class="grid-demo" :gutter="24">
      <a-col :span="4">
        <div class="item">
          <a-statistic
            :title="homeData.currentNumberPeopleOnline.label"
            :value="homeData.currentNumberPeopleOnline.value"
            show-group-separator
            class="titles"
          />
        </div>
      </a-col>
      <a-col :span="8">
        <div class="item">
            <div class="title">流量概括</div>
        <div
          class="content"
          v-for="(item, index) in homeData.flowList"
          :key="index"
        >
          <a-statistic
            :title="item.label"
            :value="item.value"
            show-group-separator
          >
          <template #suffix>
            <span class="unit">{{ item.pecs }}</span>
          </template>
        </a-statistic>
        </div>
        </div>
       
      </a-col>
      <a-col :span="12">
        <div class="item">
            <div class="title">今日概括</div>
        <div
          class="content todayGeneralize"
          v-for="(item, index) in homeData.today"
          :key="index"
        >
          <a-statistic
            :title="item.label"
            :value="item.value"
            show-group-separator
          >
          <template #suffix>
            <span class="unit">{{ item.pecs }}</span>
          </template>
        </a-statistic>
         
        </div>
        </div>
        
      </a-col>
    </a-row>
  </div>
</template>
<script lang="ts" setup>
  import { reactive, watch } from 'vue';

  const props = defineProps({
    res: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });
  const homeData = reactive<any>({
    currentNumberPeopleOnline:{ },
    flowList: {},
    today: {},
  });
  const getHomeData = (data: any): void => {
    homeData.currentNumberPeopleOnline = {
      label: '当前在线人数',
      value: data.currentNumberPeopleOnline || 0,
      pecs: '人',
    };
    homeData.flowList = {
      todayUV: {label: '今日访客数', value: data.todayUV || 0, pecs: '人',},
      yesterdayUV: {label: '昨日访客数', value: data.yesterdayUV || 0, pecs: '人',},
      lastSevenUV: {label: '前七日访客数', value: data.lastSevenUV || 0, pecs: '人',},
      lastThirtyUV: {label: '前三十日访客数', value: data.lastThirtyUV || 0, pecs: '人',},
    };
    homeData.today = {
      todayOrderNum: {label: '今日订单数', value: data.todayOrderNum || 0, pecs: '单',},
      todayOrderPrice: {label: '今日交易额', value: data.todayOrderPrice || 0, pecs: '元',},
      todayStoreNum: {label: '今日新增店铺', value: data.todayStoreNum || 0, pecs: '个',},
      todayMemberNum: {label: '今日新增会员数', value: data.todayMemberNum || 0, pecs: '人',},
      todayGoodsNum: {label: '今日上架商品数量', value: data.todayGoodsNum || 0, pecs: '个',},
      todayMemberEvaluation: {label: '今日新增评论', value: data.todayMemberEvaluation || 0, pecs: '条',},
    };

  };
  watch(()=>props.res, (newValue, oldValue) => {
    getHomeData(newValue);
  })
</script>
<style scoped lang="less">
  .grid-demo .arco-col {
    height: 248px;
    //   background-color: var(--color-bg-2);
  }
  .grid-demo .arco-col:nth-child(2n) > div {
    background-color: var(--color-bg-2);
  }
  .grid-demo .arco-col:nth-child(2n + 1) > div {
    background-color: var(--color-bg-2);
  }
  .content {
    width: 45%;
    display: inline-block;
    text-align: center;
    margin-top:15px
  }
  .title {
    padding: 20px;
    font-size: 18px;
  }
  .todayGeneralize{
    width: 33%
  }
  .item{
    height:100%
  }
  .titles{    
    width: 100%;
    text-align: center;
    line-height: 80px;
    :deep(.arco-statistic-title){
    font-size: 18px;
    color: #000;
  }
  }
  
</style>
