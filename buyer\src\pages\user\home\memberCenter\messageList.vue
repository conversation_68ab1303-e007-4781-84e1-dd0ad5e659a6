<template>
  <div>
    <Card _Title="我的消息" :_Size="16" :_Tabs="changeWay" @_Change="changeType"></Card>
    <a-table :columns="messageColumns" :data="messageData.records" :pagination="false">
      <template #content="{ record }">
        <a-tooltip :content="record.content" position="bottom">
          <div class="ellipsis ellipsis-1 hover-pointer">{{ record.content }}</div>
        </a-tooltip>
      </template>
      <template #action="{ record }">
        <a-button status="warning" size="mini" type="primary" @click="setRead(record.id)" class="mr_10" v-if="apiParams.status === 'UN_READY'">已读</a-button>
        <a-button status="danger" size="mini" type="primary" @click="removeMessage(record.id)">删除</a-button>
      </template>
    </a-table>
    <div class="paginationBox">
      <a-pagination :total="messageData.total" :page-size="apiParams.pageSize" :current="apiParams.pageNumber" show-jumper @change="(number) => {apiParams.pageNumber = number;}"></a-pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, watch } from 'vue';
  import  {memberMsgList, readMemberMsg, delMemberMsg } from '@/api/member';
  import { Message, Modal } from '@arco-design/web-vue';

  const loading = ref(false);
  const changeWay = ref(['未读', '已读']);
  // 请求参数
  const apiParams = ref({
    pageNumber: 1,
    pageSize: 15,
    status: 'UN_READY'
  });
  // 消息数据
  const messageData = ref<any>({});
  const messageColumns = [
    {title: '消息标题', width: 200, dataIndex: 'title',},
    {title: '消息内容', minWidth: 300, dataIndex: 'content', slotName: 'content'},
    {title: '发送时间', width: 200, dataIndex: 'createTime'},
    {title: '操作', width: 150, slotName: 'action'},
  ];


  const changeType = (index: number) => {
    switch (index) {
      case 0:
        apiParams.value.status = 'UN_READY';
        break;
      case 1:
        apiParams.value.status = 'ALREADY_READY';
        break;
    }
    apiParams.value.pageNumber = 1;
  };

  // 获取消息列表
  const getList = () => {
    let params = JSON.parse(JSON.stringify(apiParams.value));
    loading.value = true;
    memberMsgList(params).then(res => {
      loading.value = false;
      if (res.data.success) {
        messageData.value = res.data.result;
      }
    });
  };

  // 已读
  const setRead = (id: number) => {
    readMemberMsg(id).then(res => {
      if (res.data.success) {
        getList();
      }
    })
  };

  // 消息放入回收站
  const removeMessage = (id: number) => {
    Modal.confirm({
      title: '确认删除',
      content: `确认要删除此消息？`,
      okButtonProps: {
        type: "primary",
        status: "danger"
      },
      onOk: () => {
        delMemberMsg(id).then(res => {
          if (res.data.success) {
            Message.success('消息已成功放入回收站！');
            getList();
          }
        })
      }
    });
  };

  onMounted(() => {
    getList();
  });

  watch(() => [apiParams],
    (val) => {
      getList();
    }, { deep: true }
  );
</script>

<style scoped lang="less">

</style>
