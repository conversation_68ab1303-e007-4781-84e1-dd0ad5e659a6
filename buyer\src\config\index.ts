const configs: GlobalConfig = {
  title: '车本营',
  icpCard: '', // icp证
  meta: {
    description: 'lilishop官网,开源商城,商城系统,java商城,微服务商城系统,多用户商城商城,新零售商城',
    keywords: 'lilishop商城系统是基于Java技术的企业级网上商城系统,我们致力于提供 安全、稳定、易用的企业级电商平台系统软件。公司的技术团队从事电商行业多年，具备丰富电商开发经验，采用行业前沿开发技术，为客户提供如B2B2C、S2B2C、代理商城、社交电商、跨境电商等多种类型商业模式的解决⽅案和产品服务，帮助客户实现精准营销，提升商业效率。',
  },

  themeColor: '#e1251b', // 主题颜色
  company: {
    href: 'https://m.cizinst.cn',
    name: '中智无线（北京）科技有限公司',
  }, // 公司信息
  icpMessage: '京ICP备2021036926号', // icp备案
  /**
   * 高德地图申请链接
   * https://lbs.amap.com/api/javascript-api/guide/abc/prepare
   * 添加成功后，可获取到key值和安全密钥jscode（自2021年12月02日升级，升级之后所申请的 key 必须配备安全密钥 jscode 一起使用)
   */
  // FIXME 请检查当前高德key创建的日期，如果2021年12月02日之前申请的 无需填写安全密钥
  aMapSecurityJsCode: '2bd0fbf621881f4c77be74f0e76495f3', // 高德web端js申请的安全密钥
  aMapKey: '7f11113750315d8543daaf5c3ba353ca', // 高德web端js申请的api key
  aMapSwitch: false, // 是否开启高德定位
  enableCDN: true, // 生产环境 是否启用cdn加载 vue等js
  port: 10000, // 端口
  inputMaxLength: '140', // 全局输入框默认最大输入长度字
  PC_DOMAIN: 'https://pc.cizinst.cn', // PC端域名
  WAP_DOMAIN: 'https://m.cizinst.cn', // WAP端域名
}
interface GlobalConfig {
  title: string // 网站昵称
  icpCard: string // ICP证件地址
  meta: {
    description: string // SEO 描述
    keywords: string // SEO 关键字
  }
  themeColor: string // 网站主题色
  company: {
    href: string // 公司网站href
    name: string // 公司名称
  }
  icpMessage: string // icp备案
  /**
   * 高德地图申请链接
   * https://lbs.amap.com/api/javascript-api/guide/abc/prepare
   * 添加成功后，可获取到key值和安全密钥jscode（自2021年12月02日升级，升级之后所申请的 key 必须配备安全密钥 jscode 一起使用)
   */
  // FIXME 请检查当前高德key创建的日期，如果2021年12月02日之前申请的 无需填写安全密钥
  aMapSecurityJsCode: string // 高德web端js申请的安全密钥
  aMapKey: string // 高德web端js申请的api key
  aMapSwitch: boolean // 是否开启高德定位
  enableCDN: boolean // 生产环境 是否启用cdn加载 vue等js
  port: number // 端口
  inputMaxLength: string // 全局输入框默认最大输入长度字
  PC_DOMAIN: string // PC端域名
  WAP_DOMAIN: string // WAP端域名
}

export default configs
