import { defineStore } from 'pinia'
import URL from '@/config/index';

const usePathJumpStore = defineStore('path', {
  actions: {
    viewGoodsDetail(goodsId: any, skuId?: any) { // 跳转买家端商品
      if(skuId){
        
        // window.open(`${URL.BASE.PC_URL}/goodsDetail?skuId=${skuId}&goodsId=${goodsId}`, '_blank')

      }else{
        // window.open(`${URL.BASE.PC_URL}/goodsDetail?goodsId=${goodsId}`, '_blank')
      }
    }
  }
})
export default usePathJumpStore
