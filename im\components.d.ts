/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AAvatar: typeof import('@arco-design/web-vue')['Avatar']
    ABadge: typeof import('@arco-design/web-vue')['Badge']
    AButton: typeof import('@arco-design/web-vue')['Button']
    APopover: typeof import('@arco-design/web-vue')['Popover']
    AResizeBox: typeof import('@arco-design/web-vue')['ResizeBox']
    AScrollbar: typeof import('@arco-design/web-vue')['Scrollbar']
    ASkeleton: typeof import('@arco-design/web-vue')['Skeleton']
    ASkeletonLine: typeof import('@arco-design/web-vue')['SkeletonLine']
    ASkeletonShape: typeof import('@arco-design/web-vue')['SkeletonShape']
    ASpace: typeof import('@arco-design/web-vue')['Space']
    ATag: typeof import('@arco-design/web-vue')['Tag']
    ATextarea: typeof import('@arco-design/web-vue')['Textarea']
    ATooltip: typeof import('@arco-design/web-vue')['Tooltip']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TheCounter: typeof import('./src/components/TheCounter.vue')['default']
    TheFooter: typeof import('./src/components/TheFooter.vue')['default']
    TheInput: typeof import('./src/components/TheInput.vue')['default']
  }
}
