<template>
  <a-card class="general-card" title="ES分词" :bordered="false">
    <a-row style="margin-bottom: 16px">
      <a-col :span="16">
        <a-space>
          <a-button @click="handleAdd" type="primary">
            添加
          </a-button>
        </a-space>
      </a-col>
    </a-row>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getCustomWordsPage"
      @editor="handleEdit"
      @delete="handleDelete"
      :bordered="true"
    >
    </tablePage>
    <a-modal
      v-model:visible="sensitiveData.enableAddModal"
      :align-center="false"
      :footer="false"
    >
      <template #title> {{ title }} </template>
      <a-form ref="formRef" :model="sensitiveData.form" @submit="handleAddOk">
        <a-form-item
          field="name"
          label="自定义分词"
          :rules="[REQUIRED, VARCHAR20]"
          :validate-trigger="['change']"
        >
          <a-input v-model="sensitiveData.form.name" />
        </a-form-item>
        <a-form-item label="操作">
          <a-button :loading="sensitiveData.formLoading" html-type="submit"
          type="primary">保存</a-button
          >
        </a-form-item>
      </a-form>
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">
  import tablePage from '@/components/table-pages/index.vue';
  import { MethodsRule, ColumnsDataRule, SearchRule } from '@/types/global';
  import {
    getCustomWordsPage,
    insertCustomWords,
    updateCustomWords,
    delCustom,
  } from '@/api/operation';
  import { ref, reactive } from 'vue';
  import { REQUIRED, VARCHAR20 } from '@/utils/validator';
  import useCurrentInstance from '@/hooks/useCurrentInstance';
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';

  const tablePageRef = ref<any>();
  // 获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;
  const formRef = ref<FormInstance>();
  const title = ref<string>('');
  interface formInterface {
    enableAddModal: boolean;
    formLoading: boolean;
    fid: string | number;
    form: {
      name: string;
      id: string;
      [key:string]: any;
    };
    [key:string]: any;
  }
  const columnsTable: ColumnsDataRule[] = [
    {
      title: '自定义分词',
      dataIndex: 'name',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
    },
    {
      title: '操作人',
      dataIndex: 'createBy',
    },
  ];

  const sortMethods: MethodsRule = {
    title: '操作',
    width: 250,
    fixed: 'right',
    methods: [
      {
        title: '修改',
        callback: 'editor',
        type:'text',
        status:'warning'
      },
      {
        title: '删除',
        callback: 'delete',
        type:'text',
        status:'danger'
      },
    ],
  };
  // 数据集
  const sensitiveData = reactive<formInterface>({
    enableAddModal: false,
    formLoading: false,
    fid: '', // 当前form的ids
    form: {
      name: '',
      id: '',
    }, // 表单提交数据
  });
  // 点击添加
  function handleAdd() {
    sensitiveData.enableAddModal = true;
    title.value = '添加';
    sensitiveData.fid = '';
    Object.keys(sensitiveData.form).forEach((key) => {
      sensitiveData.form[key] = '';
    });
  }
  // 添加/修改地址
  async function handleAddOk() {
    // sensitiveData.form.password = this.md5(sensitiveData.form.password);
    const auth = await formRef.value?.validate();
    if (!auth) {
      let res;
      !sensitiveData.fid
        ? (res = await insertCustomWords(sensitiveData.form))
        : (res = await updateCustomWords(sensitiveData.form));

      if (res.data.success) {
        Message.success(`${sensitiveData.fid ? '修改' : '添加'}成功!`);
        sensitiveData.enableAddModal = false;
        tablePageRef.value.init();
      }
    }
  }
  // 点击修改地址
  function handleEdit(val: any) {
    title.value = '编辑';
    if (val) {
      Object.keys(val.record).forEach((key) => {
        // eslint-disable-next-line no-unused-expressions,no-prototype-builtins
        sensitiveData.form.hasOwnProperty(key)
          ? (sensitiveData.form[key] = val.record[key])
          : '';
      });
      sensitiveData.fid = val.record.id;
      sensitiveData.enableAddModal = true;
    }
  }
  // 回调删除
  function handleDelete(data: any) {
    modal.confirm({
      title: '确认删除',
      content: `您确认要删除${data.record.name}?`,
      alignCenter: false,
      onOk: async () => {
        const res = await delCustom(data.record.id);
        if (res.data.success) {
          Message.success('删除成功');
          tablePageRef.value.init();
        }
      },
    });
  }
</script>

<script lang="ts">
  // eslint-disable-next-line import/export
  // export default {
  //   name: 'GoodsList',
  // };
</script>
