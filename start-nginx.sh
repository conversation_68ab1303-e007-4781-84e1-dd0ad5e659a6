#!/bin/bash

ROOT_DIR=/usr/share/nginx/html

for file in $ROOT_DIR/*/*/api.js*;

do
  echo "Processing $file ...";

  if [ -n "$LILISHOP_GATEWAY_URL_ENV" ]; then
      sed -i '/gateway:/s|gateway:.*|gateway: "'$LILISHOP_GATEWAY_URL_ENV'",|g' $file
  fi

  if [ -n "$LILISHOP_PC_URL_ENV" ]; then
      sed -i '/PC_URL:/s|PC_URL:.*|PC_URL: "'$LILISHOP_PC_URL_ENV'",|g' $file
  fi

  if [ -n "$LILISHOP_WAP_URL_ENV" ]; then
      sed -i '/WAP_URL:/s|WAP_URL:.*|WAP_URL: "'$LILISHOP_WAP_URL_ENV'",|g' $file
  fi

  if [ -n "$LILISHOP_SELLER_URL_ENV" ]; then
      sed -i '/SELLER_URL:/s|SELLER_URL:.*|SELLER_URL: "'$LILISHOP_SELLER_URL_ENV'",|g' $file
  fi

  if [ -n "$LILISHOP_WEB_SOCKET_URL_ENV" ]; then
      sed -i '/im:/s|im:.*|im: "'$LILISHOP_WEB_SOCKET_URL_ENV'",|g' $file
  fi

done

nginx -g 'daemon off;'
