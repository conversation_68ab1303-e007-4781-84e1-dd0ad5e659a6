<template>
  <a-card class="general-card" title="会员资金"  :bordered="false">
    <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
    </searchTable>
    <tablePage ref="tablePageRef" :columns="columnsTable"  :api="getUserWallet" :api-params="apiParams" :bordered="true">
      <template #money="{ data }">
        <span style="color:green" v-if="data.money > 0">
          {{ unitPrice(data.money, '￥') }}
        </span>
        <span v-else style="color:red"> {{ unitPrice(data.money, '￥') }}</span>
      </template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
import searchTable from '@/components/search-column/index.vue';
import tablePage from '@/components/table-pages/index.vue';
import { SearchRule, ColumnsDataRule } from '@/types/global';
import { serviceType } from '@/utils/tools';
import { getUserWallet } from '@/api/member';
import { ref } from 'vue';
import { unitPrice } from '@/utils/filters';

const apiParams = ref({});
const tablePageRef = ref('');
const columnsSearch: Array<SearchRule> = [
  {
    label: '会员名称',
    model: 'memberName',
    disabled: false,
    input: true,
  },
  {
    label: '支付时间',
    disabled: false,
    datePicker: {
      type: 'range',
    },
  },
];

const columnsTable: ColumnsDataRule[] = [
  {
    title: '会员名称',
    dataIndex: 'memberName',
  },
  {
    title: '变动金额',
    dataIndex: 'money',
    // currency: true,
    slot: true,
    slotTemplate: 'money',
  },

  {
    title: '变更时间',
    dataIndex: 'createTime',
  },
  {
    title: '业务类型',
    dataIndex: 'serviceType',
    slot: true,
    slotData: {
      badge: serviceType,
    },
    width: 150
  },
  {
    title: '详细',
    dataIndex: 'detail',
    width: 550
  },
];


</script>
