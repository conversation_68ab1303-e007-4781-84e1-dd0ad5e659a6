<template>
  <div class="flex">
    <a-spin style="display: block; width: 75%" :loading="loading">
      <div>
        <div>
          <div class="title">转换</div>
          <div class="transaction-card">
            <div class="card-item">
              <div class="card-item-label">访客数UV</div>
              <div class="card-item-value">{{ overViewList.uvNum || 0 }}</div>
            </div>
            <div class="card-item">
              <div class="card-item-label">下单转化率</div>
              <div class="card-item-value">{{
                overViewList.orderConversionRate || 0
              }}</div>
            </div>
            <div class="card-item">
              <div class="card-item-label">付款转化率</div>
              <div class="card-item-value">{{
                overViewList.paymentsConversionRate || 0
              }}</div>
            </div>
            <div class="card-item">
              <div class="card-item-label">全店转化率</div>
              <div class="card-item-value">{{
                overViewList.overallConversionRate || 0
              }}</div>
            </div>
          </div>
        </div>
        <div>
          <div class="title">订单</div>
          <div class="transaction-card">
            <div class="card-item">
              <div class="card-item-label">下单笔数</div>
              <div class="card-item-value">{{ overViewList.uvNum || 0 }}</div>
            </div>
            <div class="card-item">
              <div class="card-item-label">下单人数</div>
              <div class="card-item-value">{{
                overViewList.orderMemberNum || 0
              }}</div>
            </div>
            <div class="card-item">
              <div class="card-item-label">下单金额</div>
              <div class="card-item-value">{{
                overViewList.orderAmount || 0
              }}</div>
            </div>
            <div class="card-item">
              <div class="card-item-label">付款笔数</div>
              <div class="card-item-value">{{
                overViewList.paymentOrderNum || 0
              }}</div>
            </div>
            <div class="card-item">
              <div class="card-item-label">付款人数</div>
              <div class="card-item-value">{{
                overViewList.paymentsNum || 0
              }}</div>
            </div>
            <div class="card-item">
              <div class="card-item-label">付款金额</div>
              <div class="card-item-value">{{
                overViewList.paymentAmount || 0
              }}</div>
            </div>
          </div>
        </div>
        <div>
          <div class="title">退单</div>
          <div class="transaction-card">
            <div class="card-item">
              <div class="card-item-label">退单笔数</div>
              <div class="card-item-value">{{
                overViewList.refundOrderNum || 0
              }}</div>
            </div>
            <div class="card-item">
              <div class="card-item-label">退单金额</div>
              <div class="card-item-value">{{
                overViewList.refundOrderPrice || 0
              }}</div>
            </div>
          </div>
        </div>
      </div>
    </a-spin>
    <div class="shap">
      <div id="overViewChart">
        <!-- 上 -->
        <div class="block">
          <div class="box">
            <span>访客数</span>
            <span>{{ overViewList.uvNum || 0 }}</span>
          </div>
        </div>
        <!-- 中 -->
        <div class="block">
          <div class="box">
            <span>下单笔数</span>
            <span>{{ overViewList.orderNum || 0 }}</span>
          </div>
        </div>
        <!-- 下 -->
        <div class="block">
          <div class="box">
            <span>付款笔数</span>
            <span>{{ overViewList.paymentOrderNum || 0 }}</span>
          </div>
        </div>

        <!-- 线 -->
        <div class="rightBorder"></div>
        <div class="leftTopBorder"></div>
        <div class="leftBottomBorder"></div>
        <!--数据 -->
        <div class="leftTopTips">
          <div>下单转化率</div>
          <div>{{ overViewList.orderConversionRate || '0%' }}</div>
        </div>
        <div class="leftBottomTips">
          <div>付款转化率</div>
          <div>{{ overViewList.paymentsConversionRate || '0%' }}</div>
        </div>
        <div class="rightTips">
          <div>整体转换率</div>
          <div>{{ overViewList.overallConversionRate || '0%' }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, reactive, watch, ref } from 'vue';
  import { Chart } from '@antv/g2';
  import useLoading from '@/hooks/loading';
  import { PreViewParamsRule } from '@/types/global';
  import { getOrderOverView } from '@/api/statisitics';
  import { useUserStore } from '@/store';

  const overViewList = ref({}) as any;
  const { loading, setLoading } = useLoading();
  const props = defineProps({
    dateType: {
      type: Object,
      default: () => {
        return {
          recent: 'LAST_SEVEN',
          month: '',
        };
      },
    },
  });

  // 订单请求参数
  const orderParams = reactive<PreViewParamsRule>({
    searchType: props.dateType.recent, // TODAY ,  YESTERDAY , LAST_SEVEN , LAST_THIRTY
    year: props.dateType.month || new Date().getFullYear(),
    storeId: useUserStore().userInfo.id,
  });

  // 初始化订单的概括
  const initOrderOverViewList = async () => {
    setLoading(true);
    const res = await getOrderOverView(orderParams);
    if (res.data.success) {
      overViewList.value = res.data.result;
    }
    setLoading(false);
  };
  onMounted(() => {
    initOrderOverViewList();
  });
  // 监听值的改变 父级值改变
  watch(
    () => props.dateType,
    (val) => {
      orderParams.searchType = val.recent;
      if (val.month) {
        // eslint-disable-next-line prefer-destructuring,no-nested-ternary
        orderParams.month = val.month.split('-')[1]
          ? val.month.split('-')[1].indexOf('0') != -1
            ? val.month.split('-')[1].substr(1)
            : ''
          : '';

        // eslint-disable-next-line prefer-destructuring
        orderParams.year = val.month.split('-')[0];
      }

      initOrderOverViewList();
    },
    {
      deep: true,
      immediate: true,
    }
  );
</script>

<style scoped lang="less">
  .orderChart {
    width: 100%;
  }
  .transaction-card {
    height: 120px;
    border-radius: 0.4em;
    display: flex;
    background: #f3f5f7;
  }
  .card-item-label {
    font-weight: bold;
    font-size: #666;
    font-size: 15px;
    margin-bottom: 10px;
  }
  .card-item-value {
    font-size: 15px;
    font-weight: bold;
    color: #f31947;
  }
  .card-item {
    height: 100%;

    width: 20%;
    align-items: center;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }
  .transaction-item {
    margin: 10px 0;
  }
  .title {
    margin: 10px 0;
    font-size: 15px;
  }
  .shap {
    width: 400px;
    margin-left: 20px;
    margin-top: 50px;
  }
  #overViewChart {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    position: relative;
    margin-left: 20px;
    > .leftTopTips {
      position: absolute;
      top: 68px;
      left: -2px;
      width: 85px;
      text-align: center;
      background: rgb(255, 255, 255);
      z-index: 1;
      padding: 5px 0px;
    }
    > .leftBottomTips {
      position: absolute;
      bottom: 100px;
      left: -2px;
      width: 85px;
      text-align: center;
      background: rgb(255, 255, 255);
      z-index: 1;
      padding: 5px 0px;
    }
    > .rightTips {
      position: absolute;
      bottom: 240px;
      right: 0px;
      width: 85px;
      text-align: center;
      background: rgb(255, 255, 255);
      z-index: 1;
      padding: 5px 0px;
    }
    > .rightBorder {
      width: 110px;
      position: absolute;
      top: 30px;
      right: 40px;
      border: 2px solid #d9d9d9;
      border-left: 0;
      height: 280px;
    }
    > .leftTopBorder {
      border: 2px solid #d9d9d9;
      height: 118px;
      width: 56px;
      position: absolute;
      left: 40px;
      top: 30px;
      border-right: 0;
    }
    > .leftBottomBorder {
      width: 108px;
      border: 2px solid #d9d9d9;
      height: 150px;
      position: absolute;
      bottom: 45px;
      left: 40px;
      border-right: 0;
    }
    > .block {
      height: 0px;
      border-left: 30px solid transparent;
      border-right: 30px solid transparent;
      position: relative;
      background: rgb(255, 255, 255);
      z-index: 1;
      position: relative;
    }
    > .block:nth-of-type(1) {
      width: 240px;
      border-top: 90px solid #ff4646;
      > .box {
        left: -30px;
        top: -90px;
        width: 240px;
        height: 90px;
      }
    }
    > .block:nth-of-type(2) {
      width: 172px;
      border-top: 100px solid #ff8585;
      margin-top: 10px;
      > .box {
        left: -29px;
        top: -100px;
        width: 172px;
        height: 100px;
      }
    }
    > .block:nth-of-type(3) {
      width: 100px;
      margin-top: 10px;
      border-top: 150px solid #ffb396;
      border-left: 50px solid transparent;
      border-right: 50px solid transparent;

      > .box {
        left: -50px;
        top: -150px;
        width: 100px;
        height: 120px;
        z-index: 2;
      }
    }
    :deep(.box) {
      color: #fff;
      position: absolute;

      z-index: 2;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      > span {
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
</style>
