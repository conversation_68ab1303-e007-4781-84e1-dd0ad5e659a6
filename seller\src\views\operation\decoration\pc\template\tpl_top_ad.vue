<template>
  <div w-1200px v-if="resolved.data" @click="handleClickTopAd()"
    :style="{ backgroundColor: resolved.data.background, height: (resolved.data.height || 80) + 'px' }">
    <img :style="{ height: resolved.data.height + 'px' }" w-1184px block h-full @click="handleClickTopAd"
      :src="resolved.data.list[0].img" />
  </div>
</template>

<script setup lang="ts">
import { useDesign } from '@/store'
import { watch, ref } from 'vue';
const design = useDesign()
const props = defineProps<{
  res: any
}>()
const resolved = ref<any>("")

watch(() => design.pinPc[0], (val: any) => {
  resolved.value = { ...val };
}, { deep: true, immediate: true })

function handleClickTopAd() {
  design.setCurrentPcDesign(props.res)
}
</script>

<style scoped>
</style>
