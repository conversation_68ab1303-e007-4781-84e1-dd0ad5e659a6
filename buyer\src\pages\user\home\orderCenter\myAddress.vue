<template>
  <div>
    <Card _Title="收货地址" :_Size="16" _More="添加新地址" _Src="/user/home/<USER>/addAddress"></Card>

    <div class="address-list">
      <div class="address-item" v-for="(item, index) in list" :key="index">
        <div class="header">
          <div>
            <div class="name">{{ item.name }}</div>
            <a-tag class="mr_10" v-if="item.isDefault" color="red" bordered>默认地址</a-tag>
            <a-tag class="mr_10" v-if="item.alias" color="orange" bordered>{{item.alias}}</a-tag>
          </div>
          <div>
            <a-button @click="editAddress(item.id)" type="text" status="danger">修改</a-button>
            <a-button @click="delAddress(item.id)" type="text" status="danger">删除</a-button>
          </div>
        </div>
        <div class="body">
          <div><span>收货人：</span>{{ item.name }}</div>
          <div><span>收货地区：</span>{{ item.consigneeAddressPath }}</div>
          <div><span>详细地址：</span>{{ item.detail }}</div>
          <div><span>手机号码：</span>{{ item.mobile }}</div>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router';
  import { ref, onMounted } from 'vue';
  import { Message, Modal } from '@arco-design/web-vue';
  import { memberAddress, memberAddressList, delMemberAddress } from '@/api/address';

  const router = useRouter();
  const list = ref<Array<any>>([]);

  // 获取地址列表
  const getAddrList = () => {
    memberAddressList().then((res) => {
      console.log(res);
      if (res.data.success) {
        list.value = res.data.result;
      }
    });
  };

  // 修改地址
  const editAddress = (id: any) => {
    router.push({ path: '/user/home/<USER>/addAddress', query: { id: id } });
  };
  // 删除地址
  const delAddress = (id: any) => {
    Modal.confirm({
      title: '提示',
      content: `你确定删除这个收货地址吗？`,
      okButtonProps: {
        type: "primary",
        status: "danger"
      },
      onOk: () => {
        delMemberAddress(id).then((res) => {
          if (res.data.success) {
            Message.success('删除成功');
            getAddrList();
          }
        });
      }
    });
  };


  onMounted(() => {
    getAddrList();
  })
</script>

<style scoped lang="less">
  .address-list {
    .address-item {
      border-bottom: 1px solid @light_border_color;
      padding: 20px;
      .header {
        height: 36px;
        line-height: 36px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        > div:nth-of-type(1) {
          display: flex;
          align-items: center;
          .name {
            /*font-weight: bold;*/
            font-size: 18px;
            color: #000;
            margin-right: 20px;
          }
        }
      }
      .body {
        > div {
          height: 30px;
          line-height: 30px;
          color: @text_color;
          > span {
            display: inline-block;
            width: 90px;
          }
        }
      }
    }
  }
</style>
