<template>
  <div v-if="templateShow">
    <a-form
      :model="form"
      :style="{ width: '100%' }"
      layout="horizontal"
      auto-label-width
    >
      <a-form-item label="每日场次设置">
        <a-row class="row" :gutter="16">
          <a-col
            v-for="(item, index) in times"
            :key="index"
            class="time-item"
            :span="3"
            @click="handleClickTime(item, index)"
          >
            <div class="time" :class="{ active: item.check }"
              >{{ item.time }}:00</div
            >
          </a-col>
        </a-row>
      </a-form-item>
      <a-form-item label="秒杀规则">
        <a-textarea
          v-model="form.seckillRule"
          allow-clear
          style="width: 360px; margin-left: 10px"
        />
      </a-form-item>
      <a-form-item>
        <div class="foot-btn">
          <a-button style="margin-right: 5px" @click="closeCurrentPage">
            返回</a-button
          >
          <a-button
            type="primary"
            style="margin-right: 5px"
            @click="handleSubmit"
          >
            提交</a-button
          >
        </div>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { getSetting, setSetting } from '@/api/promotion';
  import { Message } from '@arco-design/web-vue';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const templateShow = ref<boolean>(false); // 设置是否显示
  const form = ref({
    seckillRule: '',
  });
  const times = ref([]) as any; // 时间集合 1-24点
  const init = async () => {
    const result = await getSetting('SECKILL_SETTING');
    if (result.data.success) {
      templateShow.value = true;
      form.value.seckillRule = result.data.result.seckillRule;
      times.value = [];
      for (let i = 0; i < 24; i += 1) {
        // 将数据拆出
        if (result.data.result.hours) {
          const way = result.data.result.hours.split(',');
          way.forEach((hours: any) => {
            if (hours == i) {
              times.value.push({
                time: i,
                check: true,
              });
            }
          });
        }
        if (!times.value[i]) {
          times.value.push({
            time: i,
            check: false,
          });
        }
      }
    }
  };
  onMounted(() => {
    init();
  });
  // 选中时间
  const handleClickTime = (val: any, index: any) => {
    val.check = !val.check;
  };
  // 关闭当前页面
  const closeCurrentPage = () => {
    router.push({ name: 'seckill' });
  };
  // 提交
  const handleSubmit = async () => {
    const hours = times.value
      .filter((item: any) => {
        return item.check;
      })
      .map((item: any) => {
        return item.time;
      })
      .join(',');
    const result = await setSetting('SECKILL_SETTING', {
      seckillRule: form.value.seckillRule,
      hours,
    });
    if (result.data.success) {
      Message.success('设置成功!');
      init();
    }
  };
</script>

<style lang="less" scoped>
  .row {
    width: 50%;
  }

  .foot-btn {
    margin-left: 10px;
  }

  .time-list {
    display: flex;
    flex-wrap: wrap;
  }

  .active {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
    color: #fff;
    background: #165dff !important;
  }

  .time {
    width: 100%;
    cursor: pointer;
    transition: 0.35s;
    border-radius: 0.8em;
    justify-content: center;
    align-items: center;
    display: flex;
    background: #f3f5f7;
    height: 100%;
  }

  .time-item {
    height: 50px;
    margin: 8px 0;
    font-size: 15px;
  }
</style>
