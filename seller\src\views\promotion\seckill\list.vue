<template>
  <a-card class="general-card" title="秒杀活动" :bordered="false">
    <a-tabs @change="(val)=>{apiParams.promotionStatus = val}" :default-active-key="seckillStatus">
      <a-tab-pane key="NEW" title="未开始"></a-tab-pane>
      <a-tab-pane key="START" title="已开始"></a-tab-pane>
      <a-tab-pane key="END" title="已结束"></a-tab-pane>
      <a-tab-pane key="CLOSE" title="已关闭"></a-tab-pane>
    </a-tabs>
    <searchTable
      :columns="columnsSearch"
      time-type="timestamp"
      @reset="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
      @search="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
    ></searchTable>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getSeckillList"
      :api-params="apiParams"
      :bordered="true"
    >
      <template #hours="{ record }">
        <a-tag
          v-for="(item, index) in record.split(',')"
          :key="index"
          class="item-hour"
          >{{ item + ':00' }}</a-tag
        >
      </template>
      <template #btnList="{ data }">
        <a-button
          v-if="data?.promotionStatus === 'NEW'"
          type="text"
          @click="manage(data)"
        >
          管理
        </a-button>
        <a-button v-else type="text" status="success" @click="manage(data)">
          查看
        </a-button>
      </template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { getSeckillList } from '@/api/promotion';
  import { promotionStatus, promotionStatusSelect } from '@/utils/tools';
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';
  const tablePageRef = ref('');
  const seckillStatus = ref<string>('START');
  const apiParams = ref<any>({ sort: 'startTime',promotionStatus:seckillStatus.value });
  const router = useRouter();
  const columnsSearch: Array<SearchRule> = [
    {
      label: '活动名称',
      model: 'promotionName',
      disabled: false,
      input: true,
    },
    {
      label: '活动时间',
      model: 'selectDate',
      disabled: false,
      datePicker: {
        type: 'range',
      },
    },
  ];

  const columnsTable: ColumnsDataRule[] = [
    {
      title: '活动名称',
      dataIndex: 'promotionName',
      width: 300,
    },
    {
      title: '活动开始时间',
      dataIndex: 'startTime',
      width: 300,
    },

    {
      title: '报名截止时间',
      dataIndex: 'applyEndTime',
      width: 300,
    },
    {
      title: '时间场次',
      dataIndex: 'hours',
      slot: true,
      ellipsis: false,
      width: 300,
      slotTemplate: 'hours',
    },
    {
      title: '状态',
      dataIndex: 'promotionStatus',
      width: 300,
      slot: true,
      slotData: {
        badge: promotionStatus,
      },
    },
  ];

  const sortMethods: MethodsRule = {
    title: '操作',
    width: 200,
    fixed: 'right',
    methods: [
      {
        slot: true,
        slotTemplate: 'btnList',
      },
    ],
  };
  // 管理/查看
  const manage = (val: any) => {
    router.push({
      name: 'seckill-goods',
      query: {
        id: val.id,
      },
    });
  };
</script>

<style lang="less" scoped>
  .item-hour {
    margin: 0 2px 2px 0;
  }
</style>
