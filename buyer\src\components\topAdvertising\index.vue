<template>
  <!-- 头部广告 -->
  <div class="advertising" v-if="show && advertising" :style="{'background-color': advertising.background}">
    <img v-if="advertising.list && advertising.list.length" :src="advertising.list[0].img" class="hover-pointer" @click="linkTo(advertising.list[0].url)"/>
    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" @click="show = false">
      <path fill="#666666" d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10m0-11.414L9.172 7.757L7.757 9.172L10.586 12l-2.829 2.828l1.415 1.415L12 13.414l2.828 2.829l1.415-1.415L13.414 12l2.829-2.828l-1.415-1.415z"/>
    </svg>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';
  import { useRouter } from 'vue-router';

  /**
   * 接收父组件传值
   */
  const props = defineProps({
    // 验证码类型
    data: { // 传入的广告信息
      type: Object,
      default: null
    }
  });
  const show = ref(true); // 是否显示头部广告
  const router = useRouter();
  const advertising = ref<any>();

  const linkTo = (url: any) => {
    let routeUrl = router.resolve(url);
    window.open(routeUrl.href, "_blank");
  }


  watch(() => [props.data],
    (val: any) => {
      if(val && val.length) {
        if (val[0].data.list && val[0].data.list.length) {
          advertising.value = val[0].data;
        }
      }
    }, { deep: true }
  );
</script>

<style scoped lang="less">
  .advertising {
    height: 80px;
    width: 100%;
    background-color: @theme_color;
    text-align: center;
    overflow: hidden;
    position: relative;
    > img {
      width: 1200px;
      height: 100%;
    }
    *:nth-child(2){
      position: relative;
      right: 30px;
      top: -50px;
      cursor: pointer;
    }
  }
</style>
