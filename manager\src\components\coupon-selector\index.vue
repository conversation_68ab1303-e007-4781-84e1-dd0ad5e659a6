<template>
  <div>
    <a-modal v-model:visible="visible" width="1300px" :align-center="false">
      <template #title>优惠券</template>
      <searchTable :columns="columnsSearch" @reset="(val) => {apiParams = {...apiParams, ...val}}" @search="(val) => {apiParams = {...apiParams, ...val}}">
      </searchTable>
      <tablePage
        ref="tablePageRef"
        :columns="columnsTable"
        :api="getShopCouponList"
        :api-params="apiParams"
        :checkbox="true"
        @selectTableChange="selectTableChange"
      >
        <template #price="{ data }">
          <span v-if="data.price">{{ unitPrice(data.price, '¥') }}</span>
          <span v-else>{{ `${data.couponDiscount}折` }}</span>
        </template>
        <template #publishNum="{ data }">
          <span>
            {{ `${data.receivedNum}/` }}
            {{ `${data.publishNum == 0 ? '不限制' : ` ${data.publishNum}`}` }}
          </span>
        </template>
        <template #usedNum="{ data }">
          {{ `${data.usedNum}/${data.receivedNum}` }}
        </template>
        <template #couponType="{ data }">
          <span v-if="data.couponType == 'DISCOUNT'">
            <a-tag color="blue">打折</a-tag>
          </span>
          <span v-else-if="data.couponType == 'PRICE'">
            <a-tag color="purple">减免现金</a-tag>
          </span>
          <span v-else>
            <a-tag color="purple">未知</a-tag>
          </span>
        </template>
        <template #time="{ data }">
          <span
            v-if="
              data?.getType == 'ACTIVITY' && data?.rangeDayType == 'DYNAMICTIME'
            "
            >长期有效</span
          >
          <span v-else>{{
            `${data.startTime}
          ${data.endTime}`
          }}</span>
        </template>
      </tablePage>
      <template #footer>
        <div style="text-align: right">
          <a-button @click="visible = false">取消</a-button>
          <a-button
            style="margin-left: 10px"
            type="primary"
            @click="handleOk"
            >确定</a-button
          >
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import { ref, reactive, onMounted } from 'vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { getShopCouponList } from '@/api/promotion';
  import { unitPrice } from '@/utils/filters';
  import { promotionsScopeTypeRender } from '@/utils/tools';

  const emit = defineEmits<{ (e: 'couponlist', obj: Array<any>): void }>();
  const couponlist = ref([]);
  // 传递的参数
  const apiParams = ref({
    //getType: 'ACTIVITY',
    promotionStatus: 'START',
  });
  const columnsTable: ColumnsDataRule[] = [
    {
      title: '活动名称',
      dataIndex: 'promotionName',
    },
    {
      title: '优惠券名称',
      dataIndex: 'couponName',
    },
    {
      title: '面额/折扣',
      dataIndex: 'price',
      slot: true,
      slotTemplate: 'price',
    },
    {
      title: '已领取数量/总数量',
      dataIndex: 'publishNum',
      slot: true,
      slotTemplate: 'publishNum',
    },
    {
      title: '已被使用的数量/已领取数量',
      dataIndex: 'usedNum',
      slot: true,
      slotTemplate: 'usedNum',
    },
    {
      title: '优惠券类型',
      dataIndex: 'couponType',
      slot: true,
      slotTemplate: 'couponType',
    },
    {
      title: '品类描述',
      dataIndex: 'scopeType',
      slot: true,
      slotData: {
        tag: promotionsScopeTypeRender,
      },
    },
    {
      title: '活动时间',
      dataIndex: 'time',
      slot: true,
      slotTemplate: 'time',
    },
  ];
  const columnsSearch: Array<SearchRule> = [
    {
      label: '优惠券名称',
      model: 'couponName',
      disabled: false,
      input: true,
    },
    {
      label: '活动状态',
      model: 'promotionStatus',
      disabled: false,
      input: true,
    },
    {
      label: '活动时间',
      model: 'selectDate',
      disabled: false,
      datePicker: {
        type: 'range',
      },
    },
  ];
  const tablePageRef = ref('');
  const visible = ref<boolean>(false); // 弹框是否显示
  const selectTableChange = (val: any) => {
    // 选择的行
    couponlist.value = val;
  };
  // 确定
  const handleOk = () => {
    emit('couponlist', couponlist.value);
    visible.value = false;
  };

  defineExpose({
    visible,
  });
</script>

<style lang="scss" scoped></style>
