<template>
  <div style="background-color: #fff">
    <a-form ref="formRef" style="padding: 30px" :model="form" layout="horizontal" auto-label-width>
      <a-divider orientation="left">IM设置</a-divider>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item field="httpUrl" label="云IM地址" :rules="[REQUIRED]" :validate-trigger="['change']">
            <div>
              <a-input v-model="form.httpUrl" placeholder="配置买家端联系客服以及商家端登录客服跳转的路径" allow-clear/>
              <div style="margin-left: 10px;color: #999;font-size: 12px;margin-top: 10px;">配置买家端联系客服以及商家端登录客服跳转的路径</div>
            </div>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleSubmit">保存</a-button>
          </a-form-item>
        </a-col>
      </a-row>

    </a-form>
  </div>
</template>

<script setup lang="ts">
  import { getSetting, setSetting } from '@/api/operation';
  import { onMounted, ref } from 'vue';
  import useCurrentInstance from '@/hooks/useCurrentInstance';
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { VARCHAR255, REQUIRED, VARCHAR20 } from '@/utils/validator';

  const formRef = ref<FormInstance>();
  // 获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;

  interface formInterface {
      httpUrl: string;
  }

  // 数据集
  const form = ref<formInterface>({
      httpUrl: '',
  });

  async function init() {
    const res = await getSetting('IM_SETTING');
    form.value = res.data.result;
  }

  const handleSubmit = async () => {
    const auth = await formRef.value?.validate();
    if (!auth) {
      const result = await setSetting('IM_SETTING', form.value);
      if (result.data.success) {
        Message.success('设置成功!');
        init();
      }
    }
  };

  onMounted(() => {
    init();
  });
</script>
