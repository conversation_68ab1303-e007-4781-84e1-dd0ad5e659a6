<template>
  <a-spin style="display: block" :loading="loading">
    <a-tabs
      v-model:activeKey="storeData.params.status"
      type="rounded"
      destroy-on-hide
    >
      <a-tab-pane v-for="item in tabList" :key="item.key">
        <template #title>
          <span> {{ item.title }} </span>
        </template>
        <a-result v-if="!storeData.renderList.length" status="404">
          <template #subtitle>暂无消息</template>
        </a-result>
        <List
          :render-list="storeData.renderList"
          :unread-count="0"
          @item-click="handleItemClick"
        />
      </a-tab-pane>
    </a-tabs>
  </a-spin>
</template>

<script setup lang="ts">
  import { reactive, onMounted, watch } from 'vue';
  import useLoading from '@/hooks/loading';
  import {
    deleteMessage,
    getStoreMessage,
    // readMessage,
    resetMessage,
    clearMessage,
  } from '@/api/message';
  import resultTips from '@/hooks/tips';
  import List from './list.vue';

  const { loading, setLoading } = useLoading(true);

  interface FormInterface {
    params: {
      pageNumber: number;
      pageSize: number;
      sort?: string;
      order?: string;
      [key: string]: any;
    };
    [key: string]: any;
  }
  interface TabItem {
    key: string;
    title: string;
  }

  const storeData = reactive<FormInterface>({
    params: {
      pageNumber: 1,
      pageSize: 10,
      status: 'UN_READY',
      sort: 'createTime',
      order: 'desc',
    },
    renderList: [],
  });

  const tabList: TabItem[] = [
    {
      key: 'UN_READY',
      title: '未读消息',
    },
    {
      key: 'ALREADY_READY',
      title: '已读消息',
    },
    {
      key: 'ALREADY_REMOVE',
      title: '回收站',
    },
  ];

  // 请求消息列表
  async function fetchStoreMessage() {
    setLoading(true);
    try {
      const res = await getStoreMessage(storeData.params);
      storeData.renderList = res.data.result.records;
    } catch (err) {
      // you can report use errorHandler or other
    } finally {
      setLoading(false);
    }
  }
  interface ItemClickRule {
    id: string | number;
    type: string;
  }

  // 消息处理
  const handleTargetClick = async (id: string | number, type: string) => {
    let res: any;
    switch (type) {
      case 'read':
        // res = await readMessage(id);
        break;
      case 'deleted':
        res = await deleteMessage(id);
        break;
      case 'reset':
        res = await resetMessage(id);
        break;
      case 'clear':
        res = await clearMessage(id);
        break;
      default:
        break;
    }
    resultTips(res);
    await fetchStoreMessage();
  };
  // 回调消息处理
  const handleItemClick: any = (val: ItemClickRule) => {
    handleTargetClick(val.id, val.type);
  };

  // 监听status变化一次请求接口一次
  watch(
    () => storeData.params.status,
    () => {
      fetchStoreMessage();
    }
  );
  onMounted(() => {
    fetchStoreMessage();
  });
</script>

<style scoped lang="less">
  :deep(.arco-popover-popup-content) {
    padding: 0;
  }

  :deep(.arco-list-item-meta) {
    align-items: flex-start;
  }

  :deep(.arco-tabs-nav) {
    padding: 14px 0 12px 16px;
    border-bottom: 1px solid var(--color-neutral-3);
  }

  :deep(.arco-tabs-content) {
    padding-top: 0;

    .arco-result-subtitle {
      color: rgb(var(--gray-6));
    }
  }
</style>
