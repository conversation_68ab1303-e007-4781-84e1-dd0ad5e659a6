<template>
  <a-carousel :style="{
    height: props.res.data.height + 'px',
    borderRadius: props.res.data.round + 'px'
  }" indicator-type="line" show-arrow="never" @change="(index) => current = index">
    <a-carousel-item indicator-class="dot" v-for="(key) in blockList">
      <div text-12px flex  text-center flex-wrap :class="{ 'scroll': props.res.data.swiper === 'scroll' }"
        :style="{ backgroundColor: props.res.data.background, height: props.res.data.height + 'px', borderRadius: props.res.data.round + 'px' }">
        <div v-for="(item, index) in props.res.data.list?.slice((current - 1) * 10, current * 10)" class="menu-item"
          :key="index">
          <img v-if="props.res.data.model === 'default'" :src="item.img" />
          <div :style="{ color: props.res.data.textColor }">{{ item.title }}</div>
        </div>
      </div>
    </a-carousel-item>
  </a-carousel>
</template>

<script setup lang="ts">
import { DragRule } from '@/views/operation/decoration/app/models/types';

const current = ref<number>(1)
const props = defineProps<{
  res: DragRule,
}>()


const blockList: any = computed(() => {
  console.log(props.res!.data!.list!.length, Math.floor(props.res!.data!.list!.length / 10))
  return Math.ceil(props.res!.data!.list!.length / 10)
})

watch(() => props.res.data.row, (val) => {
  if (val === 'two') {
    props.res.data.height = 140
  } else {
    props.res.data.height = 70

  }
})
</script>

<style scoped lang="less">
.menu-item {
  width: 20%;

  >img {
    width: 44px;
    height: 44px;
  }
}

:deep(.arco-carousel-indicator-wrapper-bottom){
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 13%, rgba(0, 0, 0, 0.35) 100%);
}

// .scroll {
//   overflow-x: auto;
//   display: -webkit-box !important;
//   flex-wrap: nowrap !important;

// }
</style>
