import qs from 'query-string';
import request, { Method } from '@/utils/axios';
import { ParamsRule } from '@/types/global';

/**
 * 修改im商户id
 */
export function setMerchantId(params: ParamsRule) {
  return request({
    url: '/settings/storeSettings/merchantEuid',
    method: Method.PUT,
    data: qs.stringify(params),
    needToken: true,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 * 修改保存库存预警数
 */
export function setStockWarning(params: ParamsRule) {
  return request({
    url: '/settings/updateStockWarning',
    method: Method.PUT,
    data: qs.stringify(params),
    needToken: true,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 * 获取商家退货地址
 */
export function getRefundGoodsAddress() {
  return request({
    url: '/settings/storeSettings/storeAfterSaleAddress',
    method: Method.GET,
    needToken: true,
  });
}

/**
 * 获取商家自提点
 */
export function getShopAddress(params: ParamsRule) {
  return request({
    url: '/member/storeAddress',
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 * 保存商家自提点
 */
export function addShopAddress(params: ParamsRule) {
  return request({
    url: '/member/storeAddress',
    method: Method.POST,
    needToken: true,
    params,
  });
}

// 修改商家自提点
export function editShopAddress(id: string | number, params: ParamsRule) {
  return request({
    url: `/member/storeAddress/${id}`,
    method: Method.PUT,
    needToken: true,
    data: qs.stringify(params),
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 *  删除商家自提点
 */
export function delShopAddress(id: string | number) {
  return request({
    url: `/member/storeAddress/${id}`,
    method: Method.DELETE,
    needToken: true,
  });
}

/**
 *  获取商家公告列表数据
 */
export function getSellerArticleList() {
  return request({
    url: `/other/article/getByPage?type=STORE_ARTICLE&pageSize=15`,
    method: Method.GET,
    needToken: true,
  });
}

/**
 *  获取商家公告列表数据
 */
export function getSellerArticleDetail(id: string | number) {
  return request({
    url: `/other/article/${id}`,
    method: Method.GET,
    needToken: true,
  });
}

/**
 *  查询店铺列表
 */
export function getShopListData(params: ParamsRule) {
  return request({
    url: `/store/store`,
    method: Method.GET,
    needToken: true,
    params,
  });
}

/**
 *  获取结算单分页
 */
export function getBuyBillPage(params: ParamsRule) {
  return request({
    url: `/payment/bill/getByPage`,
    method: Method.GET,
    needToken: true,
    params,
  });
}
// 根据id获取店铺信息
export function getShopByMemberId(id: string | number) {
  return request({
    url: `/store/store/${id}/member`,
    method: Method.GET,
    needToken: true,
  });
}
// 查询店铺详情
export function shopDetail(id: any) {
  return request({
    url: `/store/store/get/detail/${id}`,
    method: Method.GET,
    needToken: true,
  });
}
// 增加店铺列表
export function shopAdd(params: ParamsRule) {
  return request({
    url: '/store/store/add',
    method: Method.POST,
    needToken: true,
    data: qs.stringify(params),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded', },
  });
}
// 修改店铺列表
export function shopEdit(id: any, params: ParamsRule) {
  return request({
    url: `/store/store/edit/${id}`,
    method: Method.PUT,
    data: qs.stringify(params),
    needToken: true,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
}
// 禁用店铺
export function disableShop(id: any) {
  return request({
    url: `/store/store/disable/${id}`,
    method: Method.PUT,
    needToken: true,
  });
}
// 开启店铺
export function enableBrand(id: any) {
  return request({
    url: `/store/store/enable/${id}`,
    method: Method.PUT,
    needToken: true,
  });
}
// 审核店铺
export function shopAudit(id: string | number, passed: any) {
  return request({
    url: `/store/store/audit/${id}/${passed}`,
    method: Method.PUT,
    needToken: true,
  });
}
// 查询店铺详细
export function getShopDetailData(id: any) {
  return request({
    url: `/store/store/get/detail/${id}`,
    method: Method.GET,
    needToken: true,
  });
}
// 获取结算单详情
export function getBuyBillDetail(id: any) {
  return request({
    url: `/payment/bill/get/${id}`,
    method: Method.GET,
    needToken: true,
  });
}
// 获取商家结算单流水分页
export function getStoreFlow(params: ParamsRule) {
  return request({
    url: `/payment/bill/${localStorage.getItem('settleID')}/getStoreFlow`,
    method: Method.GET,
    needToken: true,
    params
  });
}
// 审核结算单
export function pay(id: any) {
  return request({
    url: `/payment/bill/pay/${id}`,
    method: Method.PUT,
    needToken: true,
  });
}

// 获取所有商家
export function getShopList() {
  return request({
    url: '/store/store/all',
    method: Method.GET,
    needToken: true
  })
}

// 进件-申请单列表
export function getConstruction(params:ParamsRule) {
  return request({
    url: `/payment/wechatApplyment`,
    method: Method.GET,
    needToken: true,
    params
  });
}

// 同步进件详情
export function syncStatus(id: string | number) {
  return request({
    url: `/payment/wechatApplyment/applyments/${id}`,
    method: Method.POST,
    needToken: true,
  });
}

// 获取进件-对公银行信息
export const getBankInfo = (params: any) => {
  return request({
    url: `/payment/wechatApplyment/getBankInfo`,
    method: Method.GET,
    needToken: true,
    params
  });
};

// 获取进件-对私银行信息
export function getPersonalBankInfo(params:ParamsRule) {
  return request({
    url: `/payment/wechatApplyment/getPersonalBank`,
    method: Method.GET,
    needToken: true,
    params
  });
}

// 获取支行信息
export function getBranches(params:ParamsRule) {
  return request({
    url: `/payment/wechatApplyment/branches`,
    method: Method.GET,
    needToken: true,
    params
  });
}

// 用户选择地区
export function getProvinces(params:ParamsRule) {
  return request({
    url: `/payment/wechatApplyment/provinces`,
    method: Method.GET,
    needToken: true,
    params
  });
}

// 保存进件-草稿
export function draftSave(params: any) {
  return request({
    url: `/payment/wechatApplyment/save`,
    method: Method.POST,
    needToken: true,
    data: params,
    headers: { "Content-type": "application/json" }
  });
}

// 获取进件-二级商户
export function editConstruction(id: any) {
  return request({
    url: `/payment/wechatApplyment/get/${id}`,
    method: Method.GET,
    needToken: true
  });
}

// 提交申请单
export function postConstruction(params: any) {
  return request({
    url: `/payment/wechatApplyment/create`,
    method: Method.POST,
    needToken: true,
    data: params,
    headers: { "Content-type": "application/json" }
  });
}

// 编辑进件信息
export function editputConstruction(id: any, params: any) {
  return request({
    url: `/payment/wechatApplyment/update/${id}`,
    method: Method.PUT,
    needToken: true,
    data: params,
    headers: { "Content-type": "application/json" }
  });
}