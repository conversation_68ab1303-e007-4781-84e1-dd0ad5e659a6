<template>
  <a-card class="general-card" title="发票管理" :bordered="false">
    <searchTable
      :columns="columnsSearch"
      @reset="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
      @search="
        (val) => {
          apiParams = { ...apiParams, ...val };
        }
      "
    ></searchTable>
    <tablePage
      ref="tablePageRef"
      :columns="columnsTable"
      :methods="sortMethods"
      :api="getReceiptPage"
      :api-params="apiParams"
      :bordered="true"
    >
      <template #invoicingTemplate="{ data }">
        <a-button
          :disabled="
            !(
              (data.orderStatus === 'COMPLETED' ||
                data.orderStatus === 'DELIVERED') &&
              data.receiptStatus === 0
            )
          "
          @click="invoicingClick(data)"
          >开票</a-button
        >
      </template>
    </tablePage>
  </a-card>
</template>

<script setup lang="ts">
  import searchTable from '@/components/search-column/index.vue';
  import tablePage from '@/components/table-pages/index.vue';
  import { SearchRule, MethodsRule, ColumnsDataRule } from '@/types/global';
  import { getReceiptPage, invoicing } from '@/api/order';
  import { receiptStatus, orderStatusList } from '@/utils/tools';
  import { ref } from 'vue';
  import useCurrentInstance from '@/hooks/useCurrentInstance';
  import { Message } from '@arco-design/web-vue';

  // 获取modal
  const modal = useCurrentInstance().globalProperties?.$modal;
  const tablePageRef = ref<any>();
  const columnsSearch: Array<SearchRule> = [
    {
      label: '订单编号',
      model: 'orderSn',
      disabled: false,
      input: true,
    },
    {
      label: '会员名称',
      model: 'memberName',
      disabled: false,
      input: true,
    },
    {
      label: '发票抬头',
      model: 'receiptTitle',
      disabled: false,
      input: true,
    },
    {
      label: '状态',
      model: 'receiptStatus',
      disabled: false,
      select: {
        options: receiptStatus,
      },
    },
  ];
  const columnsTable: ColumnsDataRule[] = [
    {
      title: '订单号',
      dataIndex: 'orderSn',
    },
    {
      title: '会员名称',
      dataIndex: 'memberName',
    },
    {
      title: '发票抬头',
      dataIndex: 'receiptTitle',
      empty: '暂未填写',
    },
    {
      title: '纳税人识别号',
      dataIndex: 'taxpayerId',
      empty: '暂未填写',
    },
    {
      title: '发票内容',
      dataIndex: 'receiptContent',
    },
    {
      title: '发票金额',
      dataIndex: 'billPrice',
      currency: true,
    },
    {
      title: '发票状态',
      dataIndex: 'receiptStatus',
      slot: true,
      slotData: {
        badge: receiptStatus,
      },
    },
    {
      title: '订单状态',
      dataIndex: 'orderStatus',
      slot: true,
      slotData: {
        badge: orderStatusList,
      },
    },
  ];
  const sortMethods: MethodsRule = {
    title: '操作',
    width: 100,
    methods: [
      {
        title: '开票',
        callback: 'invoicingTemplate',
        slot: true,
        slotTemplate: 'invoicingTemplate',
      },
    ],
  };
  const apiParams = ref({
    billStatus: 'OUT',
  });
  // 开票
  const invoicingClick = async (data: any) => {
    modal.confirm({
      title: '确认开票',
      content: `您确认已经开具发票 ?`,
      alignCenter: false,
      onOk: async () => {
        const res = await invoicing(data.id);
        if (res.data.success) {
          Message.success('开票成功');
          tablePageRef.value.init();
        }
      },
    });
  };
</script>

<style scoped></style>
